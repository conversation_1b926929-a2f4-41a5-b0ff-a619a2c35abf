package com.ruoyi.web.controller.manager.crm.dsp.landpage;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.Result;
import com.ruoyi.common.core.domain.ResultBuilder;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.bo.landpage.article.ArticleDataBo;
import com.ruoyi.system.bo.landpage.article.ArticleDataExcelBo;
import com.ruoyi.system.bo.landpage.article.ArticleDataParamBo;
import com.ruoyi.system.bo.landpage.article.ArticleListBo;
import com.ruoyi.system.bo.landpage.article.ArticleListParamBo;
import com.ruoyi.system.entity.landpage.article.ArticleAggrLinkEntity;
import com.ruoyi.system.entity.landpage.article.ArticleEntity;
import com.ruoyi.system.req.common.IdReq;
import com.ruoyi.system.req.landpage.article.ArticleAddReq;
import com.ruoyi.system.req.landpage.article.ArticleBatchAddReq;
import com.ruoyi.system.req.landpage.article.ArticleBatchEditReq;
import com.ruoyi.system.req.landpage.article.ArticleDataExportReq;
import com.ruoyi.system.req.landpage.article.ArticleEditReq;
import com.ruoyi.system.req.landpage.article.ArticleListReq;
import com.ruoyi.system.req.landpage.article.ArticleSyRefreshReq;
import com.ruoyi.system.service.antiblock.AntiBlockBizUrlGenerator;
import com.ruoyi.system.service.fc.FcLinkArticleAggrRelService;
import com.ruoyi.system.service.fc.FcLinkService;
import com.ruoyi.system.service.landpage.article.ArticleAggrLinkService;
import com.ruoyi.system.service.landpage.article.ArticleApiService;
import com.ruoyi.system.service.landpage.article.ArticleEditHistoryService;
import com.ruoyi.system.service.landpage.article.ArticleHourDataService;
import com.ruoyi.system.service.landpage.article.ArticleService;
import com.ruoyi.system.vo.landpage.article.ArticleBatchAddResultItemVO;
import com.ruoyi.system.vo.landpage.article.ArticleBatchAddResultVO;
import com.ruoyi.system.vo.landpage.article.ArticleVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * [CRM后台]文章管理
 *
 * <AUTHOR>
 * @date 2023-12-01
 */
@Slf4j
@RestController
@PreAuthorize("@ss.hasCrmPermi()")
@RequestMapping("/crm/dsp/landpage/article")
public class ArticleController extends BaseController {

    @Autowired
    public TransactionTemplate transactionTemplate;

    @Autowired
    private ArticleService articleService;

    @Autowired
    private ArticleAggrLinkService articleAggrLinkService;

    @Autowired
    private ArticleHourDataService articleHourDataService;

    @Autowired
    private ArticleEditHistoryService articleEditHistoryService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private ArticleApiService articleApiService;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private FcLinkArticleAggrRelService fcLinkArticleAggrRelService;

    @Autowired
    private FcLinkService fcLinkService;
    @Autowired
    private AntiBlockBizUrlGenerator antiBlockBizUrlGenerator;

    /**
     * 查询文章列表
     */
    @GetMapping("/list")
    public TableDataInfo<ArticleVO> list(ArticleListReq req) {
        startPage();
        List<ArticleListBo> list = articleService.selectListWithData(buildQueryParam(req));

        Integer totalWeight = getOnlineWeightSum(req.getLinkId());
        boolean isPvDegrade = degradeJudge();

        boolean isFc = fcLinkArticleAggrRelService.existsByArticleAggrLinkId(req.getLinkId());
        return getDataTable(PageInfoUtils.dto2Vo(list, entity -> {
            ArticleVO vo = BeanUtil.copyProperties(entity, ArticleVO.class);
            vo.setTotalWeight(totalWeight);
            if (!isFc) {
                vo.setFcCheckStatus(null);
                vo.setFcSyncStatus(null);
            }
            if (null != entity.getActualRequestPv() && null != entity.getInitRequestPv()) {
                vo.setActualIncrRequestPv(entity.getActualRequestPv() - entity.getInitRequestPv());
            }
            if (null != entity.getSyRequestPv() && null != entity.getInitRequestPv()) {
                vo.setSyIncrRequestPv(entity.getSyRequestPv() - entity.getInitRequestPv());
            }
            // 如果高并发导致更新延迟，暂时用redis的pv数据展示
            if (isPvDegrade && Objects.equals(entity.getOnline(), 1)) {
                Integer requestPv = redisCache.getCacheMapValue(EngineRedisKeyFactory.K111.join(DateUtil.today(), entity.getLinkId()), String.valueOf(entity.getId()));
                if (null != requestPv && requestPv > 0) {
                    vo.setRequestPv(requestPv);
                    vo.setRequestUv(requestPv);
                }
            }
            return vo;
        }));
    }

    /**
     * 新增文章
     */
    @Log(title = "文章", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public Result<Void> add(@RequestBody ArticleAddReq req) {
        if (StringUtils.isBlank(req.getName())) {
            return ResultBuilder.fail("名称不能为空");
        }
        if (StringUtils.isBlank(req.getUrl())) {
            return ResultBuilder.fail("链接不能为空");
        }
        if (!StrUtil.startWith(req.getUrl(), "http")) {
            return ResultBuilder.fail("无效的链接格式");
        }
        if (null != req.getInitRequestPv() && req.getInitRequestPv() < 0) {
            return ResultBuilder.fail("初始阅读量不能为负数");
        }

        ArticleAggrLinkEntity link = articleAggrLinkService.selectById(req.getLinkId());
        if (null == link) {
            return ResultBuilder.fail("无效的链接ID");
        }

        List<ArticleEntity> existArticleList = articleService.selectListByLinkId(req.getLinkId());
        for (ArticleEntity article : existArticleList) {
            if (Objects.equals(article.getName(), req.getName())) {
                return ResultBuilder.fail("该名称已存在");
            }
            if (Objects.equals(article.getUrl(), req.getUrl())) {
                return ResultBuilder.fail("该文章链接已存在");

            }
        }
        LoginUser operator = SecurityUtils.getLoginUser();

        ArticleEntity article = new ArticleEntity();
        article.setLinkId(req.getLinkId());
        article.setName(req.getName());
        article.setUrl(req.getUrl());

        // 通过antiBlockBizUrlGenerator的generateDirectEntryUrl生成jump_url
        try {
            String jumpUrl = antiBlockBizUrlGenerator.generateDirectEntryUrl(req.getUrl(), false);
            article.setJumpUrl(jumpUrl);
        } catch (Exception e) {
            log.error("生成跳转链接失败, url={}", req.getUrl(), e);
            // 如果生成失败，jumpUrl设置为null，不影响文章创建
            article.setJumpUrl(null);
        }

        article.setTargetRequestPv(NumberUtils.defaultInt(req.getTargetRequestPv()));
        article.setWeight(NumberUtils.defaultInt(req.getWeight(), 1));
        article.setInitRequestPv(req.getInitRequestPv());
        article.setOperatorId(operator.getCrmAccountId());
        article.setOperatorName(operator.getUserName());
        articleService.insert(article);
        redisCache.deleteObject(EngineRedisKeyFactory.K110.join(DateUtil.today(), req.getLinkId()));
        articleService.updateArticleProfileAsync(article.getId(), article.getUrl());
        return ResultBuilder.success();
    }

    /**
     * 批量导入文章
     */
    @Log(title = "文章", businessType = BusinessType.INSERT)
    @PostMapping("/batchAdd")
    public Result<ArticleBatchAddResultVO> batchAdd(@RequestBody ArticleBatchAddReq req) {
        ArticleAggrLinkEntity link = articleAggrLinkService.selectById(req.getLinkId());
        if (null == link) {
            return ResultBuilder.fail("无效的链接ID");
        }
        if (CollectionUtils.isEmpty(req.getArticles())) {
            return ResultBuilder.fail("文章列表不能为空");
        }

        List<ArticleEntity> existArticleList = articleService.selectListByLinkId(req.getLinkId());
        Set<String> existArticleNameSet = existArticleList.stream().map(ArticleEntity::getName).collect(Collectors.toSet());
        Set<String> existArticleUrlSet = existArticleList.stream().map(ArticleEntity::getUrl).collect(Collectors.toSet());
        Set<String> addArticleNameSet = new HashSet<>();
        Set<String> addArticleUrlSet = new HashSet<>();

        LoginUser operator = SecurityUtils.getLoginUser();
        List<ArticleBatchAddResultItemVO> errList = new ArrayList<>();
        List<ArticleEntity> insertList = new ArrayList<>();
        for (ArticleAddReq article : req.getArticles()) {
            String errMsg = null;
            if (StringUtils.isBlank(article.getName())) {
                errMsg = "名称不能为空";
            } else if (StringUtils.isBlank(article.getUrl())) {
                errMsg = "链接不能为空";
            } else if (!StrUtil.startWith(article.getUrl(), "http")) {
                errMsg = "无效的链接格式";
            } else if (existArticleNameSet.contains(article.getName())) {
                errMsg = "名称已存在";
            } else if (existArticleUrlSet.contains(article.getUrl())) {
                errMsg = "文章链接已存在";
            } else if (addArticleNameSet.contains(article.getName())) {
                errMsg = "名称已存在";
            } else if (addArticleUrlSet.contains(article.getUrl())) {
                errMsg = "文章链接已存在";
            } else if (null != article.getInitRequestPv() && article.getInitRequestPv() < 0) {
                errMsg = "初始阅读量不能为负数";
            }
            if (StringUtils.isBlank(errMsg)) {
                ArticleEntity entity = BeanUtil.copyProperties(article, ArticleEntity.class);
                entity.setWeight(NumberUtils.defaultInt(entity.getWeight(), 1));
                entity.setTargetRequestPv(NumberUtils.defaultInt(entity.getTargetRequestPv()));
                entity.setOperatorId(operator.getCrmAccountId());
                entity.setOperatorName(operator.getUserName());

                // 通过antiBlockBizUrlGenerator的generateDirectEntryUrl生成jump_url
                try {
                    String jumpUrl = antiBlockBizUrlGenerator.generateDirectEntryUrl(article.getUrl(), false);
                    entity.setJumpUrl(jumpUrl);
                } catch (Exception e) {
                    log.error("批量添加时生成跳转链接失败, url={}", article.getUrl(), e);
                    // 如果生成失败，jumpUrl设置为null，不影响文章创建
                    entity.setJumpUrl(null);
                }

                insertList.add(entity);
                addArticleNameSet.add(entity.getName());
                addArticleUrlSet.add(entity.getUrl());
            } else {
                ArticleBatchAddResultItemVO errItem = BeanUtil.copyProperties(article, ArticleBatchAddResultItemVO.class);
                errItem.setErrMsg(errMsg);
                errList.add(errItem);
            }
        }
        articleService.batchInsert(insertList);
        redisCache.deleteObject(EngineRedisKeyFactory.K110.join(DateUtil.today(), req.getLinkId()));
        return ResultBuilder.success(new ArticleBatchAddResultVO(insertList.size(), errList.size(), errList));
    }

    /**
     * 批量修改
     */
    @Log(title = "文章", businessType = BusinessType.UPDATE)
    @PostMapping("/batchEdit")
    public Result<Void> batchEdit(@RequestBody ArticleBatchEditReq req) {
        ArticleAggrLinkEntity link = articleAggrLinkService.selectById(req.getLinkId());
        if (null == link) {
            return ResultBuilder.fail("无效的链接ID");
        }
        if (CollectionUtils.isEmpty(req.getArticles())) {
            return ResultBuilder.fail("文章列表不能为空");
        }
        List<String> articleNameList = req.getArticles().stream().map(ArticleEditReq::getName).filter(Objects::nonNull).collect(Collectors.toList());
        if (articleNameList.size() != new HashSet<>(articleNameList).size()) {
            return ResultBuilder.fail("文章名称不能重复");
        }

        Date now = new Date();
        List<ArticleEntity> existArticleList = articleService.selectListByLinkId(req.getLinkId());
        Map<String, Long> articleNameMap = existArticleList.stream().collect(Collectors.toMap(ArticleEntity::getName, ArticleEntity::getId, (v1, v2) -> v2));
        Map<Long, Integer> fcSyncStatusMap = existArticleList.stream().collect(Collectors.toMap(ArticleEntity::getId, ArticleEntity::getFcSyncStatus, (v1, v2) -> v2));
        LoginUser operator = SecurityUtils.getLoginUser();
        for (ArticleEditReq article : req.getArticles()) {
            if (null == article.getId()) {
                return ResultBuilder.fail("文章ID不能为空");
            }
            if (StringUtils.isNotBlank(article.getName()) && articleNameMap.containsKey(article.getName())
                    && !Objects.equals(articleNameMap.get(article.getName()), article.getId())) {
                return ResultBuilder.fail("文章「" + article.getName() +"」已存在");
            }
            if (null != article.getInitRequestPv() && article.getInitRequestPv() < 0) {
                return ResultBuilder.fail("文章「" + article.getName() +"」初始阅读量不能为负数");
            }
            ArticleEntity updateRecord = new ArticleEntity();
            updateRecord.setId(article.getId());
            updateRecord.setName(article.getName());
            updateRecord.setWeight(article.getWeight());
            updateRecord.setTargetRequestPv(article.getTargetRequestPv());
            updateRecord.setCompensateRequestPv(article.getCompensateRequestPv());
            updateRecord.setInitRequestPv(article.getInitRequestPv());
            updateRecord.setOperatorId(operator.getCrmAccountId());
            updateRecord.setOperatorName(operator.getUserName());
            updateRecord.setOperatorTime(now);
            articleEditHistoryService.add(updateRecord);
            articleService.updateById(updateRecord);

            // 修改丰巢同步成功的文章需要删除相应的缓存
            if ( Objects.equals(1, fcSyncStatusMap.get(article.getId())) ) {
                fcLinkService.deleteFcLinkArticleCacheByArticleId(article.getId());
            }
        }
        redisCache.deleteObject(EngineRedisKeyFactory.K110.join(DateUtil.today(), req.getLinkId()));
        return ResultBuilder.success();
    }

    /**
     * 导出文章数据
     */
    @Log(title = "文章", businessType = BusinessType.EXPORT)
    @GetMapping("/dataExport")
    public AjaxResult dataExport(ArticleDataExportReq req) {
        if (null == req.getLinkId()) {
            return AjaxResult.error("链接不存在");
        }

        ArticleDataParamBo param = new ArticleDataParamBo();
        param.setLinkId(req.getLinkId());
        param.setStartDate(DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), -92)));
        List<ArticleDataBo> dataList = articleHourDataService.selectDataBy(param);

        Map<Long, ArticleEntity> articleMap = articleService.selectMapByLinkId(req.getLinkId());

        List<ArticleDataExcelBo> excels = dataList.stream().map(data -> {
            ArticleDataExcelBo excelBo = BeanUtil.copyProperties(data, ArticleDataExcelBo.class);
            ArticleEntity article = articleMap.get(data.getArticleId());
            if (null != article) {
                excelBo.setArticleName(article.getName());
                excelBo.setArticleUrl(article.getUrl());
                excelBo.setTargetRequestPv(article.getTargetRequestPv());
                excelBo.setInitRequestPv(article.getInitRequestPv());
                excelBo.setCompensateRequestPv(article.getCompensateRequestPv());
                if (null != article.getActualRequestPv() && null != article.getInitRequestPv()) {
                    excelBo.setActualIncrRequestPv(article.getActualRequestPv() - article.getInitRequestPv());
                }
                if (null != article.getSyRequestPv() && null != article.getInitRequestPv()) {
                    excelBo.setSyIncrRequestPv(article.getSyRequestPv() - article.getInitRequestPv());
                }
            }
            return excelBo;
        }).collect(Collectors.toList());
        ExcelUtil<ArticleDataExcelBo> util = new ExcelUtil<>(ArticleDataExcelBo.class);
        return util.exportExcel(excels, "文章数据");
    }

    /**
     * 移除文章
     */
    @Log(title = "文章", businessType = BusinessType.UPDATE)
    @PostMapping("/remove")
    public Result<Void> remove(@RequestBody IdReq req) {
        ArticleEntity article = articleService.selectById(req.getId());
        if (null == article || Objects.equals(article.getStatus(), 1)) {
            return ResultBuilder.success();
        }
        Date now = new Date();
        ArticleDataParamBo param = new ArticleDataParamBo();
        param.setStartDate(DateUtil.beginOfDay(DateUtil.offsetDay(now, -30)));
        param.setArticleIds(Collections.singletonList(req.getId()));
        ArticleDataBo data = articleHourDataService.selectSumBy(param);
        if (null != data && data.getRequestPv() > 0) {
            return ResultBuilder.fail("该文章近30天有数据，暂时不能移除");
        }

        LoginUser operator = SecurityUtils.getLoginUser();
        ArticleEntity updateRecord = new ArticleEntity();
        updateRecord.setId(article.getId());
        updateRecord.setStatus(1);
        updateRecord.setOperatorId(operator.getCrmAccountId());
        updateRecord.setOperatorName(operator.getUserName());
        updateRecord.setOperatorTime(now);
        articleService.updateById(updateRecord);
        redisCache.deleteObject(EngineRedisKeyFactory.K110.join(DateUtil.today(), article.getLinkId()));

        // 删除丰巢同步成功的文章需要删除相应的缓存
        if ( Objects.equals(article.getFcSyncStatus(), 1) ) {
            fcLinkService.deleteFcLinkArticleCacheByArticleId(article.getId());
        }
        return ResultBuilder.success();
    }

    /**
     * 手动更新文章私域阅读量
     */
    @Log(title = "文章", businessType = BusinessType.UPDATE)
    @PostMapping("/manualSyArticleRefresh")
    public Result<Boolean> manualSyArticleRefresh(@RequestBody ArticleSyRefreshReq req) {
        ArticleAggrLinkEntity link = articleAggrLinkService.selectById(req.getLinkId());
        if (StringUtils.isBlank(link.getSySlot())) {
            return ResultBuilder.fail("设置私域广告位之后才能使用");
        }
        List<ArticleEntity> articles = articleService.selectListByLinkId(req.getLinkId());
        if (CollectionUtils.isEmpty(articles)) {
            return ResultBuilder.success(true);
        }
        Date today = DateUtil.beginOfDay(new Date());
        for (ArticleEntity article : articles) {
            if (article.getGmtCreate().before(today) && article.getTargetRequestPv() > 0) {
                continue;
            }
            // 十分钟内不会重复查询，避免误操作
            if (null == redisAtomicClient.getLock(EngineRedisKeyFactory.K067.join("manualSyArticleRefresh", article.getId()), 600)) {
                continue;
            }
            GlobalThreadPool.longTimeExecutorService.execute(() -> {
                Integer requestPv = articleApiService.getArticleRealRequestPv(article.getUrl());
                if (requestPv > 0) {
                    ArticleEntity updateArticle = new ArticleEntity();
                    updateArticle.setId(article.getId());
                    updateArticle.setSyRequestPv(requestPv);
                    articleService.updateById(updateArticle);
                }
            });
        }
        return ResultBuilder.success(true);
    }

    /**
     * 获取链接的在线文章权重总和
     *
     * @param linkId 链接ID
     * @return 权重总和
     */
    private Integer getOnlineWeightSum(Long linkId) {
        Integer weightSum = 0;
        List<ArticleEntity> articles = articleService.selectListByLinkId(linkId);
        if (CollectionUtils.isEmpty(articles)) {
            return weightSum;
        }
        Map<String, Integer> articleRequestMap = MapUtil.defaultIfEmpty(redisCache.getCacheMap(EngineRedisKeyFactory.K111.join(DateUtil.today(), linkId)), Collections.emptyMap());
        for (ArticleEntity article : articles) {
            if (!article.isOnline()) {
                continue;
            }
            Integer requestPv = articleRequestMap.getOrDefault(String.valueOf(article.getId()), 0);
            if (requestPv >= article.getTargetRequestPv() + article.getCompensateRequestPv()) {
                continue;
            }
            weightSum += article.getWeight();
        }
        return weightSum;
    }

    /**
     * 构造查询参数
     *
     * @param req 请求参数
     * @return 查询参数
     */
    private ArticleListParamBo buildQueryParam(ArticleListReq req) {
        ArticleListParamBo param = new ArticleListParamBo();
        param.setSearchKey(StrUtil.trim(req.getSearchKey()));
        param.setLinkId(NumberUtils.defaultLong(req.getLinkId()));
        return param;
    }

    /**
     * 根据埋点线程池判断是否降级
     */
    private boolean degradeJudge() {
        try {
            ThreadPoolExecutor te = (ThreadPoolExecutor) GlobalThreadPool.statExecutorService;
            return te.getQueue().size() >= 1000;
        } catch (Exception e) {
            logger.error("degradeJudge error", e);
        }
        return false;
    }

    /**
     * 批量更新指定时间范围内、指定域名开头的文章jump_url
     *
     * @param startDate 开始日期，格式：2025-07-01
     * @param endDate 结束日期，格式：2025-07-01
     * @param domain 域名，匹配jump_url开头的域名
     * @return 更新结果
     * <AUTHOR>
     */
    @GetMapping("/updateJumpUrl")
    public AjaxResult updateArticleJumpUrl(String startDate, String endDate, String domain, boolean encrypt) {
        // 参数校验
        if (StringUtils.isBlank(startDate)) {
            return AjaxResult.error("开始日期不能为空，格式：2025-07-01");
        }
        if (StringUtils.isBlank(endDate)) {
            return AjaxResult.error("结束日期不能为空，格式：2025-07-01");
        }
        if (StringUtils.isBlank(domain)) {
            return AjaxResult.error("域名不能为空");
        }

        Date startDateTime = DateUtils.parseDate(startDate);
        Date endDateTime = DateUtils.parseDate(endDate);

        // 结束日期设置为当天的23:59:59
        endDateTime = DateUtil.endOfDay(endDateTime);
        startDateTime = DateUtil.beginOfDay(startDateTime);

        if (startDateTime.after(endDateTime)) {
            return AjaxResult.error("开始日期不能大于结束日期");
        }

        log.info("开始批量更新文章jump_url, startDate={}, endDate={}, domain={}", startDate, endDate, domain);

        try {
            // 查询指定时间范围内的所有文章
            List<ArticleEntity> articles = articleService.selectListByDateRange(startDateTime, endDateTime);

            if (CollectionUtils.isEmpty(articles)) {
                return AjaxResult.success("未找到需要更新的文章");
            }

            // 过滤符合条件的文章（域名过滤）
            List<ArticleEntity> targetArticles = articles.stream()
                .filter(article -> {
                    // 域名过滤
                    String jumpUrl = article.getJumpUrl();
                    if (StringUtils.isBlank(jumpUrl)) {
                        return false;
                    }

                    // 检查jump_url是否以指定域名开头
                    return jumpUrl.startsWith("http://" + domain) || jumpUrl.startsWith("https://" + domain);
                })
                .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(targetArticles)) {
                return AjaxResult.success("未找到符合条件的文章，条件：时间范围[" + startDate + " ~ " + endDate + "]，域名[" + domain + "]");
            }

            log.info("找到符合条件的文章数量：{}", targetArticles.size());

            // 同步批量更新
            int successCount = 0;
            int failCount = 0;

            for (ArticleEntity article : targetArticles) {
                try {
                    // 重新生成jump_url
                    String newJumpUrl = antiBlockBizUrlGenerator.generateDirectEntryUrl(article.getUrl(), encrypt);

                    // 更新文章
                    ArticleEntity updateEntity = new ArticleEntity();
                    updateEntity.setId(article.getId());
                    updateEntity.setJumpUrl(newJumpUrl);

                    boolean updateResult = articleService.updateById(updateEntity);
                    if (updateResult) {
                        successCount++;
                        log.debug("更新文章jump_url成功，文章ID：{}，原jump_url：{}，新jump_url：{}",
                            article.getId(), article.getJumpUrl(), newJumpUrl);
                    } else {
                        failCount++;
                        log.error("更新文章jump_url失败，文章ID：{}", article.getId());
                    }
                } catch (Exception e) {
                    failCount++;
                    log.error("更新文章jump_url异常，文章ID：{}，原URL：{}", article.getId(), article.getUrl(), e);
                }
            }

            log.info("批量更新文章jump_url完成，总数：{}，成功：{}，失败：{}",
                targetArticles.size(), successCount, failCount);

            return AjaxResult.success("批量更新完成，总数：" + targetArticles.size() + "，成功：" + successCount + "，失败：" + failCount);

        } catch (Exception e) {
            log.error("批量更新文章jump_url异常", e);
            return AjaxResult.error("批量更新失败：" + e.getMessage());
        }
    }
}
