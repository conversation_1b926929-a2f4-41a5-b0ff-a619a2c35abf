<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.contract.ContractMapper">

    <resultMap type="com.ruoyi.system.entity.contract.ContractEntity" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="planAName" column="plan_a_name"/>
        <result property="accountId" column="account_id"/>
        <result property="contractCode" column="contract_code"/>
        <result property="startDate" column="start_date"/>
        <result property="endDate" column="end_date"/>
        <result property="remark" column="remark"/>
        <result property="operatorId" column="operator_id"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
            plan_a_name,
            account_id,
            contract_code,
            start_date,
            end_date,
            remark,
            operator_id,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.contract.ContractEntity">
        INSERT INTO tb_contract
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="planAName != null">
                plan_a_name,
            </if>
            <if test="accountId != null">
                account_id,
            </if>
            <if test="contractCode != null">
                contract_code,
            </if>
            <if test="startDate != null">
                start_date,
            </if>
            <if test="endDate != null">
                end_date,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="operatorId != null">
                operator_id
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="planAName != null">
                #{planAName},
            </if>
            <if test="accountId != null">
                #{accountId},
            </if>
            <if test="contractCode != null">
                #{contractCode},
            </if>
            <if test="startDate != null">
                #{startDate},
            </if>
            <if test="endDate != null">
                #{endDate},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="operatorId != null">
                #{operatorId}
            </if>
        </trim>
    </insert>

    <delete id="deleteById">
        DELETE
        FROM tb_contract
        WHERE id = #{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.contract.ContractEntity">
        UPDATE tb_contract
        <set>
            <if test="startDate != null">
                start_date = #{startDate},
            </if>
            <if test="endDate != null">
                end_date = #{endDate},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="operatorId != null">
                operator_id = #{operatorId},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_contract
        WHERE id = #{id}
    </select>

    <select id="selectListByAccountId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_contract
        WHERE account_id = #{accountId}
        order by gmt_create desc
    </select>

    <select id="selectLatestContractByAccountIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_contract
        WHERE id in (select max(id) from tb_contract where account_id in
        <foreach collection="accountIds" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
        group by account_id)
    </select>

    <select id="selectAccountIdsByLatestContractStatus" resultType="java.lang.Long">
        SELECT account_id
        FROM tb_contract
        <where>
            id in (select max(id) from tb_contract group by account_id)
            <choose>
                <when test="status == 1">
                    and datediff(end_date,now()) > 30
                </when>
                <when test="status == 2">
                    and datediff(end_date,now()) &gt;= 0 and datediff(end_date,now()) &lt;= 30
                </when>
                <when test="status == 3">
                    and datediff(now(),end_date) > 0
                </when>
            </choose>
        </where>
    </select>
    <select id="selectByContractCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tb_contract
        where contract_code = #{contractCode}
        limit 1
    </select>

</mapper>