<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.tagmanager.TagManagerMapper">

    <resultMap type="com.ruoyi.system.entity.tagmanager.TagManagerEntity" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="tagName" column="tag_name"/>
        <result property="parentId" column="parent_id"/>
        <result property="tagSort" column="tag_sort"/>
        <result property="tagType" column="tag_type"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
            tag_name,
            parent_id,
            tag_sort,
            tag_type,
            is_deleted,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.tagmanager.TagManagerEntity">
        INSERT INTO tb_tag_manager
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tagName != null">
                tag_name,
            </if>
            <if test="parentId != null">
                parent_id,
            </if>
            <if test="tagSort != null">
                tag_sort,
            </if>
            <if test="tagType != null">
                tag_type,
            </if>
            <if test="isDeleted != null">
                is_deleted
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tagName != null">
                #{tagName},
            </if>
            <if test="parentId != null">
                #{parentId},
            </if>
            <if test="tagSort != null">
                #{tagSort},
            </if>
            <if test="tagType != null">
                #{tagType},
            </if>
            <if test="isDeleted != null">
                #{isDeleted}
            </if>
        </trim>
    </insert>

    <delete id="deleteById">
        DELETE
        FROM tb_tag_manager
        WHERE id = #{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.tagmanager.TagManagerEntity">
        UPDATE tb_tag_manager
        <set>
            <if test="tagName != null">
                tag_name = #{tagName},
            </if>
            <if test="parentId != null">
                parent_id = #{parentId},
            </if>
            <if test="tagSort != null">
                tag_sort = #{tagSort},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_tag_manager
        WHERE id = #{id}
    </select>

    <select id="selectAllTag" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_tag_manager
        where is_deleted = 0
    </select>
    <select id="selectAllTagByType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_tag_manager
        where is_deleted = 0 and tag_type = #{type}
    </select>

    <update id="deleteByIds">
        update tb_tag_manager set is_deleted = 1 where id in
        <foreach collection="ids" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </update>
    <insert id="batchInsert">
        insert into tb_tag_manager(`tag_name`,`parent_id`,`tag_sort`,`tag_type`)
        values
        <foreach collection="insertList" item="tag" separator=",">
            (#{tag.tagName}, #{tag.parentId}, #{tag.tagSort}, #{tag.tagType})
        </foreach>
    </insert>
    <insert id="batchUpdate">
        <foreach collection="updateList" separator=";" item="tag">
            update tb_tag_manager set tag_name = #{tag.tagName},tag_sort = #{tag.tagSort} where id = #{tag.id}
        </foreach>
    </insert>

    <select id="selectByIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_tag_manager
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectTagIdsByFirstTagName" resultType="java.lang.Long">
        select id
        from tb_tag_manager
        where tag_type = #{type} and tag_name = #{tagName}
    </select>

    <select id="selectTagIdsBySecondTagNames" resultType="java.lang.Long">
        select id
        from tb_tag_manager
        where tag_type = #{type} and parent_id > 0 and tag_name in
        <foreach collection="tagNames" item="tagName" open="(" separator="," close=")">
            #{tagName}
        </foreach>
    </select>

    <select id="selectIdsByParentIds" resultType="java.lang.Long">
        select id
        from tb_tag_manager
        where parent_id in
        <foreach collection="parentIds" item="parentId" open="(" separator="," close=")">
            #{parentId}
        </foreach>
    </select>

</mapper>
