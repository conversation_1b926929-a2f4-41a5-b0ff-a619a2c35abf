<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.tagmanager.DomainTagRelationMapper">

    <resultMap type="com.ruoyi.system.entity.tagmanager.DomainTagRelationEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="domainId" column="domain_id"/>
            <result property="tagId" column="tag_id"/>
            <result property="isDeleted" column="is_deleted"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            domain_id,
            tag_id,
            is_deleted,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.tagmanager.DomainTagRelationEntity">
        INSERT INTO tb_domain_tag_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="domainId != null">
                    domain_id,
                </if>
                <if test="tagId != null">
                    tag_id,
                </if>
                <if test="isDeleted != null">
                    is_deleted
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="domainId != null">
                    #{domainId},
                </if>
                <if test="tagId != null">
                    #{tagId},
                </if>
                <if test="isDeleted != null">
                    #{isDeleted}
                </if>
        </trim>
    </insert>

    <delete id="deleteById">
        DELETE FROM tb_domain_tag_relation WHERE id=#{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.tagmanager.DomainTagRelationEntity">
        UPDATE tb_domain_tag_relation
        <set>
                    <if test="domainId != null">
                        domain_id = #{domainId},
                    </if>
                    <if test="tagId != null">
                        tag_id = #{tagId},
                    </if>
                    <if test="isDeleted != null">
                        is_deleted = #{isDeleted},
                    </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_domain_tag_relation
        WHERE id = #{id}
    </select>

    <select id="countByTagId" resultType="java.lang.Integer">
        SELECT count(*)
        FROM tb_domain_tag_relation
        WHERE is_deleted = 0 and tag_id = #{tagId}
    </select>

    <select id="selectTagIdsByTagIds" resultType="java.lang.Long">
        SELECT distinct tag_id
        FROM tb_domain_tag_relation
        WHERE is_deleted = 0 and tag_id in
        <foreach collection="tagIds" item="tagId" open="(" separator="," close=")">
            #{tagId}
        </foreach>
    </select>

    <select id="selectTagIdsByDomainId" resultType="java.lang.Long">
        SELECT distinct tag_id
        FROM tb_domain_tag_relation
        WHERE is_deleted = 0 and domain_id = #{domainId}
    </select>

    <update id="deleteByDomainId">
        update tb_domain_tag_relation
        set is_deleted = 1 where domain_id = #{domainId}
    </update>

    <insert id="batchInsert">
        insert into tb_domain_tag_relation(`domain_id`,`tag_id`)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.domainId}, #{entity.tagId})
        </foreach>
    </insert>
</mapper>