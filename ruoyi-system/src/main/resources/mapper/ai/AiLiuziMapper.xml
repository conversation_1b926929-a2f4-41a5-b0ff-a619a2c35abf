<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.ai.AiLiuziMapper">

    <resultMap type="com.ruoyi.system.entity.ai.AiLiuziEntity" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="userName" column="user_name"/>
        <result property="phone" column="phone"/>
        <result property="useScene" column="use_scene"/>
        <result property="status" column="status"/>
        <result property="operatorName" column="operator_name"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        user_name,
        phone,
        use_scene,
        status,
        operator_name,
        gmt_create,
        gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.ai.AiLiuziEntity">
        INSERT INTO tb_ai_liuzi
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userName != null">
                user_name,
            </if>
            <if test="phone != null">
                phone,
            </if>
            <if test="useScene != null">
                use_scene,
            </if>
            <if test="status != null">
                status
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userName != null">
                #{userName},
            </if>
            <if test="phone != null">
                #{phone},
            </if>
            <if test="useScene != null">
                #{useScene},
            </if>
            <if test="status != null">
                #{status}
            </if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.ruoyi.system.entity.ai.AiLiuziEntity">
        UPDATE tb_ai_liuzi
        <set>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="operatorName != null">
                operator_name = #{operatorName},
            </if>
        </set>
        WHERE id=#{id}
    </update>
    <select id="selectList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_ai_liuzi
        <where>
            <if test="req.startDate != null and req.endDate != null">
                gmt_create &gt;= #{req.startDate} and gmt_create &lt;= #{req.endDate}
            </if>
            <if test="req.status != null">
                and status = #{req.status}
            </if>
        </where>
        order by id desc
    </select>
</mapper>
