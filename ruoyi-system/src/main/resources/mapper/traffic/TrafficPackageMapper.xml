<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.traffic.TrafficPackageMapper">

    <resultMap type="com.ruoyi.system.entity.traffic.TrafficPackageEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="name" column="name"/>
            <result property="desc" column="desc"/>
            <result property="operatorId" column="operator_id"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            `name`,
            `desc`,
            operator_id,
            gmt_create,
            gmt_modified
    </sql>

    <select id="selectList" resultType="com.ruoyi.system.bo.traffic.TrafficPackageListBo"  parameterType="com.ruoyi.system.req.traffic.TrafficPackageListReq">
        SELECT
            id,
            `name`,
            `desc`,
            appCount,
            slotCount,
            operator_id as operatorId,
            gmt_create as gmtCreate
        FROM tb_traffic_package p
        LEFT JOIN (
            select traffic_package_id,  count(distinct app_id) as appCount, COUNT(distinct slot_id) as slotCount
            from tb_traffic_package_item
            <where>
                <if test="ids != null and ids.size > 0">
                    traffic_package_id in
                    <foreach collection="ids" open="(" separator="," close=")" item="id">
                        #{id}
                    </foreach>
                </if>
            </where>
            group by traffic_package_id
        ) i on p.id = i.traffic_package_id
        <where>
            <if test="name != null and name != ''"> and `name` like concat('%', #{name}, '%')</if>
            <if test="desc != null and desc != ''"> and `desc` like concat('%', #{desc}, '%')</if>
            <if test="ids != null and ids.size > 0">
                p.id in
                <foreach collection="ids" open="(" separator="," close=")" item="id">
                    #{id}
                </foreach>
            </if>
        </where>
        ORDER BY id desc
    </select>


    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.traffic.TrafficPackageEntity">
        INSERT INTO tb_traffic_package
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="name != null">
                    `name`,
                </if>
                <if test="desc != null">
                    `desc`,
                </if>
                <if test="operatorId != null">
                    operator_id
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="name != null">
                    #{name},
                </if>
                <if test="desc != null">
                    #{desc},
                </if>
                <if test="operatorId != null">
                    #{operatorId}
                </if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.ruoyi.system.entity.traffic.TrafficPackageEntity">
        UPDATE tb_traffic_package
        <set>
            <if test="desc != null">
                `desc` = #{desc},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_traffic_package
        WHERE id = #{id}
    </select>

    <select id="existByName" resultType="Integer">
        SELECT 1
        FROM tb_traffic_package
        WHERE `name` = #{name}
        LIMIT 1
    </select>
</mapper>
