<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.traffic.AdvertOrientTrafficMapper">

    <resultMap type="com.ruoyi.system.entity.traffic.AdvertOrientTrafficEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="advertId" column="advert_id"/>
            <result property="orientId" column="orient_id"/>
            <result property="trafficPackageId" column="traffic_package_id"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            advert_id,
            orient_id,
            traffic_package_id,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="batchInsert" parameterType="com.ruoyi.system.entity.traffic.TrafficPackageItemEntity">
        insert into tb_advert_orient_traffic(`advert_id`,`orient_id`,`traffic_package_id`)
        values
        <foreach collection="list" item="entity" separator=",">
            (#{entity.advertId},#{entity.orientId},#{entity.trafficPackageId})
        </foreach>
    </insert>

    <delete id="deleteByOrientId">
        DELETE FROM tb_advert_orient_traffic WHERE orient_id=#{orientId}
    </delete>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_advert_orient_traffic
        WHERE id = #{id}
    </select>

    <select id="countByTrafficPackageId" resultType="com.ruoyi.system.bo.traffic.TrafficPackageAdvertCountBo">
        SELECT traffic_package_id as trafficPackageId, count(distinct advert_id) as advertCount
        FROM tb_advert_orient_traffic
        <where>
            <if test="trafficPackageIds != null and trafficPackageIds.size() > 0">
                traffic_package_id in
                <foreach collection="trafficPackageIds" open="(" separator="," close=")" item="trafficPackageId">
                    #{trafficPackageId}
                </foreach>
            </if>
        </where>
        GROUP BY traffic_package_id
    </select>

    <select id="selectAdvertIdByTrafficPackageId" resultType="Long">
        SELECT distinct advert_id
        FROM tb_advert_orient_traffic
        WHERE traffic_package_id = #{trafficPackageId}
    </select>

    <select id="selectTrafficPackageIdByOrientId" resultType="Long">
        SELECT distinct traffic_package_id
        FROM tb_advert_orient_traffic
        WHERE orient_id = #{orientId}
    </select>

    <select id="selectByAdvertIds" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tb_advert_orient_traffic
        <where>
            <if test="advertIds != null and advertIds.size > 0">
                advert_id in
                <foreach collection="advertIds" open="(" separator="," close=")" item="advertId">
                    #{advertId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectByOrientIds" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tb_advert_orient_traffic
        <where>
            <if test="orientIds != null and orientIds.size > 0">
                orient_id in
                <foreach collection="orientIds" open="(" separator="," close=")" item="orientId">
                    #{orientId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectTotalAdvertIds" resultType="Long">
        SELECT distinct advert_id
        FROM tb_advert_orient_traffic
    </select>
</mapper>
