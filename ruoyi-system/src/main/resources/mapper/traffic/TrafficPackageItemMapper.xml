<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.traffic.TrafficPackageItemMapper">
    <resultMap type="com.ruoyi.system.entity.traffic.TrafficPackageItemEntity" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="trafficPackageId" column="traffic_package_id"/>
        <result property="appId" column="app_id"/>
        <result property="slotId" column="slot_id"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            traffic_package_id,
            app_id,
            slot_id,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="batchInsert" parameterType="com.ruoyi.system.entity.traffic.TrafficPackageItemEntity">
        insert into tb_traffic_package_item(`traffic_package_id`,`app_id`,`slot_id`)
        values
        <foreach collection="list" item="entity" separator=",">
            (#{entity.trafficPackageId},#{entity.appId},#{entity.slotId})
        </foreach>
    </insert>

    <delete id="deleteBy">
        DELETE
        FROM tb_traffic_package_item
        WHERE traffic_package_id = #{trafficPackageId}
    </delete>

    <select id="selectByTrafficPackageIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_traffic_package_item
        <where>
            <if test="trafficPackageIds != null and trafficPackageIds.size > 0">
                traffic_package_id in
                <foreach collection="trafficPackageIds" open="(" separator="," close=")" item="trafficPackageId">
                    #{trafficPackageId}
                </foreach>
            </if>
        </where>
        ORDER BY id DESC
    </select>

    <select id="selectTrafficPackageIdsByAppIds" resultType="Long">
        SELECT traffic_package_id
        FROM tb_traffic_package_item
        <where>
            <if test="appIds != null and appIds.size > 0">
                app_id in
                <foreach collection="appIds" open="(" separator="," close=")" item="appId">
                    #{appId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectTotalAppIds" resultType="Long">
        SELECT distinct app_id
        FROM tb_traffic_package_item
    </select>
</mapper>
