<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.slot.SlotBiddingConfigMapper">

    <resultMap type="com.ruoyi.system.entity.slot.SlotBiddingConfigEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="slotId" column="slot_id"/>
            <result property="convPrice" column="conv_price"/>
            <result property="coldStart" column="cold_start"/>
            <result property="consumeType" column="consume_type"/>
            <result property="isEnable" column="is_enable"/>
            <result property="operatorId" column="operator_id"/>
            <result property="operatorName" column="operator_name"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            slot_id,
            conv_price,
            cold_start,
            consume_type,
            is_enable,
            operator_id,
            operator_name,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.slot.SlotBiddingConfigEntity">
        INSERT INTO tb_slot_bidding_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="slotId != null">
                    slot_id,
                </if>
                <if test="convPrice != null">
                    conv_price,
                </if>
                <if test="coldStart != null">
                    cold_start,
                </if>
                <if test="consumeType != null">
                    consume_type,
                </if>
                <if test="isEnable != null">
                    is_enable,
                </if>
                <if test="operatorId != null">
                    operator_id,
                </if>
                <if test="operatorName != null">
                    operator_name
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="slotId != null">
                    #{slotId},
                </if>
                <if test="convPrice != null">
                    #{convPrice},
                </if>
                <if test="coldStart != null">
                    #{coldStart},
                </if>
                <if test="consumeType != null">
                    #{consumeType},
                </if>
                <if test="isEnable != null">
                    #{isEnable},
                </if>
                <if test="operatorId != null">
                    #{operatorId},
                </if>
                <if test="operatorName != null">
                    #{operatorName}
                </if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.ruoyi.system.entity.slot.SlotBiddingConfigEntity">
        UPDATE tb_slot_bidding_config
        <set>
                    <if test="slotId != null">
                        slot_id = #{slotId},
                    </if>
                    <if test="convPrice != null">
                        conv_price = #{convPrice},
                    </if>
                    <if test="coldStart != null">
                        cold_start = #{coldStart},
                    </if>
                    <if test="consumeType != null">
                        consume_type = #{consumeType},
                    </if>
                    <if test="isEnable != null">
                        is_enable = #{isEnable},
                    </if>
                    <if test="operatorId != null">
                        operator_id = #{operatorId},
                    </if>
                    <if test="operatorName != null">
                        operator_name = #{operatorName},
                    </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_slot_bidding_config
        WHERE id = #{id}
    </select>

    <select id="selectBySlotId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_slot_bidding_config
        WHERE slot_id = #{slotId}
    </select>
</mapper>
