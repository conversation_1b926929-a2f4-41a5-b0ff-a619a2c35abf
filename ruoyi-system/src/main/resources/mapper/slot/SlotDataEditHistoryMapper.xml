<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.slot.SlotDataEditHistoryMapper">

    <resultMap type="com.ruoyi.system.entity.slot.SlotDataEditHistory" id="SlotDataEditHistoryResult">
        <result property="id"    column="id"    />
        <result property="curDate"    column="cur_date"    />
        <result property="slotId"    column="slot_id"    />
        <result property="beforeEdit"    column="before_edit"    />
        <result property="afterEdit"    column="after_edit"    />
        <result property="source"    column="source"    />
        <result property="operatorId"    column="operator_id"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectSlotDataVo">
        select id, cur_date, slot_id, before_edit, after_edit, source, operator_id, gmt_create, gmt_modified from tb_slot_data_edit_history
    </sql>

    <insert id="insert" parameterType="com.ruoyi.system.entity.slot.SlotDataEditHistory" useGeneratedKeys="true" keyProperty="id">
        insert into tb_slot_data_edit_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="curDate != null">cur_date,</if>
            <if test="slotId != null">slot_id,</if>
            <if test="beforeEdit != null and beforeEdit != ''">before_edit,</if>
            <if test="afterEdit != null and afterEdit != ''">after_edit,</if>
            <if test="source != null">`source`,</if>
            <if test="operatorId != null">operator_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="curDate != null">#{curDate},</if>
            <if test="slotId != null">#{slotId},</if>
            <if test="beforeEdit != null and beforeEdit != ''">#{beforeEdit},</if>
            <if test="afterEdit != null and afterEdit != ''">#{afterEdit},</if>
            <if test="source != null">#{source},</if>
            <if test="operatorId != null">#{operatorId},</if>
         </trim>
    </insert>
</mapper>
