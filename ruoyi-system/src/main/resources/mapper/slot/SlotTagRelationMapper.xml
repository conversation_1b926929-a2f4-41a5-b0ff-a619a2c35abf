<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.slot.SlotTagRelationMapper">
    <resultMap type="com.ruoyi.system.entity.slot.SlotTagRelationEntity" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="slotId" column="slot_id"/>
        <result property="tagId" column="tag_id"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        slot_id,
        tag_id,
        gmt_create,
        gmt_modified
    </sql>

    <delete id="deleteBySlotId">
        DELETE FROM tb_slot_tag_relation
        WHERE slot_id = #{slotId}
    </delete>

    <select id="selectTagCountListBySlotIds" resultType="com.ruoyi.system.bo.slot.SlotTagCountBo">
        SELECT slot_id as slotId, count(tag_id) as count
        FROM tb_slot_tag_relation
        GROUP BY slot_id
    </select>

    <select id="selectTagIdsBySlotId" resultType="Long">
        SELECT distinct tag_id
        FROM tb_slot_tag_relation
        WHERE slot_id = #{slotId}
    </select>

    <select id="selectSlotIdsByTagId" resultType="Long">
        SELECT distinct slot_id
        FROM tb_slot_tag_relation
        WHERE tag_id = #{tagId}
    </select>

    <insert id="batchInsert">
        insert into tb_slot_tag_relation(`slot_id`,`tag_id`)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.slotId}, #{entity.tagId})
        </foreach>
    </insert>
</mapper>
