<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.slot.SlotRedirectHourDataMapper">

    <resultMap type="com.ruoyi.system.entity.slot.SlotRedirectHourData" id="SlotRedirectHourDataResult">
        <result property="id"    column="id"    />
        <result property="curDate"    column="cur_date"    />
        <result property="curHour"    column="cur_hour"    />
        <result property="slotId"    column="slot_id"    />
        <result property="redirectType"    column="redirect_type"    />
        <result property="redirectValue"    column="redirect_value"    />
        <result property="redirectValueMd5"    column="redirect_value_md5"    />
        <result property="slotRequestPv"    column="slot_request_pv"    />
        <result property="slotRequestUv"    column="slot_request_uv"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectSlotRedirectHourDataVo">
        select id, cur_date, cur_hour, slot_id, redirect_type, redirect_value, redirect_value_md5, slot_request_pv, slot_request_uv, gmt_create, gmt_modified from tb_slot_redirect_hour_data
    </sql>

    <select id="selectBy" parameterType="com.ruoyi.system.entity.slot.SlotRedirectHourData" resultMap="SlotRedirectHourDataResult">
        <include refid="selectSlotRedirectHourDataVo"/>
        <where>
            <if test="curDate != null "> and cur_date = #{curDate}</if>
            <if test="curHour != null "> and cur_hour = #{curHour}</if>
            <if test="slotId != null">and slot_id = #{slotId}</if>
            <if test="redirectType != null">and redirect_type = #{redirectType}</if>
            <if test="redirectValueMd5 != null">and redirect_value_md5 = #{redirectValueMd5}</if>
        </where>
    </select>

    <select id="groupBy" parameterType="com.ruoyi.system.req.slot.data.SlotRedirectDataParam" resultMap="SlotRedirectHourDataResult">
        select slot_id, cur_date, redirect_type, redirect_value, sum(slot_request_pv) as slot_request_pv, sum(slot_request_uv) as slot_request_uv
        from tb_slot_redirect_hour_data
        <where>
            <if test="slotId != null">and slot_id = #{slotId}</if>
            <if test="startDate != null "> and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null "> and cur_date &lt;= #{endDate}</if>
        </where>
        group by `slot_id` , `cur_date`, redirect_type, redirect_value
        order by cur_date desc, slot_request_pv desc
    </select>

    <insert id="insert" parameterType="com.ruoyi.system.entity.slot.SlotShuntData" useGeneratedKeys="true" keyProperty="id">
        insert ignore into tb_slot_redirect_hour_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="curDate != null">cur_date,</if>
            <if test="curHour != null">cur_hour,</if>
            <if test="slotId != null">slot_id,</if>
            <if test="redirectType != null">redirect_type,</if>
            <if test="redirectValue != null">redirect_value,</if>
            <if test="redirectValueMd5 != null">redirect_value_md5,</if>
            <if test="slotRequestPv != null">slot_request_pv,</if>
            <if test="slotRequestUv != null">slot_request_uv,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="curDate != null">#{curDate},</if>
            <if test="curHour != null">#{curHour},</if>
            <if test="slotId != null">#{slotId},</if>
            <if test="redirectType != null">#{redirectType},</if>
            <if test="redirectValue != null">#{redirectValue},</if>
            <if test="redirectValueMd5 != null">#{redirectValueMd5},</if>
            <if test="slotRequestPv != null">#{slotRequestPv},</if>
            <if test="slotRequestUv != null">#{slotRequestUv},</if>
         </trim>
    </insert>

    <update id="update">
        update tb_slot_redirect_hour_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="pvAdd != null">slot_request_pv = slot_request_pv + #{pvAdd},</if>
            <if test="uvAdd != null">slot_request_uv = slot_request_uv + #{uvAdd},</if>
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>
</mapper>
