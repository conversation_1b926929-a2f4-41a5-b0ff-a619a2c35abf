<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.slot.SlotShuntDataMapper">

    <resultMap type="com.ruoyi.system.entity.slot.SlotShuntData" id="SlotShuntDataResult">
        <result property="id"    column="id"    />
        <result property="curDate"    column="cur_date"    />
        <result property="slotId"    column="slot_id"    />
        <result property="taskId"    column="task_id"    />
        <result property="slotRequestPv"    column="slot_request_pv"    />
        <result property="slotRequestUv"    column="slot_request_uv"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectSlotShuntDataVo">
        select id, cur_date, slot_id, task_id, slot_request_pv, slot_request_uv, gmt_create, gmt_modified from tb_slot_shunt_data
    </sql>

    <select id="selectList" parameterType="com.ruoyi.system.req.slot.shunt.SlotShuntDataParam" resultMap="SlotShuntDataResult">
        <include refid="selectSlotShuntDataVo"/>
        <where>
            <if test="curDate != null "> and cur_date = #{curDate}</if>
            <if test="slotId != null">and slot_id = #{slotId}</if>
            <if test="taskIds != null and taskIds.size()>0">
                and task_id in
                <foreach collection="taskIds" item="taskId" open="(" separator="," close=")">
                    #{taskId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectByTaskId" resultMap="SlotShuntDataResult">
        <include refid="selectSlotShuntDataVo"/>
        where task_id = #{taskId} and cur_date = #{curDate}
    </select>

    <select id="selectById" parameterType="Long" resultMap="SlotShuntDataResult">
        <include refid="selectSlotShuntDataVo"/>
        where id = #{id}
    </select>

    <insert id="insert" parameterType="com.ruoyi.system.entity.slot.SlotShuntData" useGeneratedKeys="true" keyProperty="id">
        insert ignore into tb_slot_shunt_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="curDate != null">cur_date,</if>
            <if test="slotId != null">slot_id,</if>
            <if test="taskId != null">task_id,</if>
            <if test="slotRequestPv != null">slot_request_pv,</if>
            <if test="slotRequestUv != null">slot_request_uv,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="curDate != null">#{curDate},</if>
            <if test="slotId != null">#{slotId},</if>
            <if test="taskId != null">#{taskId},</if>
            <if test="slotRequestPv != null">#{slotRequestPv},</if>
            <if test="slotRequestUv != null">#{slotRequestUv},</if>
         </trim>
    </insert>

    <update id="update" parameterType="com.ruoyi.system.req.slot.shunt.SlotShuntDataUpdateParam">
        update tb_slot_shunt_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="slotRequestPvAdd != null">slot_request_pv = slot_request_pv + #{slotRequestPvAdd},</if>
            <if test="slotRequestUvAdd != null">slot_request_uv = slot_request_uv + #{slotRequestUvAdd},</if>
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>
</mapper>
