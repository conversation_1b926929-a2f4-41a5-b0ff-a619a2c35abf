<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.datashow.BiddingDayDataMapper">

    <resultMap type="com.ruoyi.system.entity.slot.BiddingDayDataEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="curDate" column="cur_date"/>
            <result property="slotId" column="slot_id"/>
            <result property="advertiserId" column="advertiser_id"/>
            <result property="consume" column="consume"/>
            <result property="operatorId" column="operator_id"/>
            <result property="operatorName" column="operator_name"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            cur_date,
            slot_id,
            advertiser_id,
            consume,
            operator_id,
            operator_name,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.slot.BiddingDayDataEntity">
        INSERT IGNORE INTO tb_bidding_day_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="curDate != null">
                    cur_date,
                </if>
                <if test="slotId != null">
                    slot_id,
                </if>
                <if test="advertiserId != null">
                    advertiser_id,
                </if>
                <if test="consume != null">
                    consume,
                </if>
                <if test="operatorId != null">
                    operator_id,
                </if>
                <if test="operatorName != null">
                    operator_name
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="curDate != null">
                    #{curDate},
                </if>
                <if test="slotId != null">
                    #{slotId},
                </if>
                <if test="advertiserId != null">
                    #{advertiserId},
                </if>
                <if test="consume != null">
                    #{consume},
                </if>
                <if test="operatorId != null">
                    #{operatorId},
                </if>
                <if test="operatorName != null">
                    #{operatorName}
                </if>
        </trim>
    </insert>

    <delete id="deleteById">
        DELETE FROM tb_bidding_day_data WHERE id=#{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.slot.BiddingDayDataEntity">
        UPDATE tb_bidding_day_data
        <set>
            <if test="slotId != null">
                slot_id = #{slotId},
            </if>
            <if test="consume != null">
                consume = #{consume},
            </if>
            <if test="operatorId != null">
                operator_id = #{operatorId},
            </if>
            <if test="operatorName != null">
                operator_name = #{operatorName},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <update id="incrConv">
        UPDATE tb_bidding_day_data
        SET conv = conv + 1
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_bidding_day_data
        WHERE id = #{id}
    </select>

    <select id="selectBy" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_bidding_day_data
        <where>
            cur_date = #{curDate} and advertiser_id = #{advertiserId}
            <if test="slotId != null"> and slot_id = #{slotId}</if>
        </where>
        limit 1
    </select>

    <select id="countByDateAndSlotIds" resultMap="BaseResultMap">
        SELECT cur_date, slot_id, ifnull(sum(consume), 0) as consume, ifnull(sum(conv), 0) as conv
        FROM tb_bidding_day_data
        <where>
            <if test="startDate != null "> and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null "> and cur_date &lt;= #{endDate}</if>
            <if test="slotIds != null and slotIds.size() > 0">
                and slot_id in
                <foreach collection="slotIds" item="slotId" open="(" separator="," close=")">
                    #{slotId}
                </foreach>
            </if>
        </where>
        group by cur_date, slot_id
    </select>

    <select id="sumByDateAndSlotIds" resultMap="BaseResultMap">
        SELECT ifnull(sum(consume), 0) as consume, ifnull(sum(conv), 0) as conv
        FROM tb_bidding_day_data
        <where>
            <if test="startDate != null "> and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null "> and cur_date &lt;= #{endDate}</if>
            <if test="slotIds != null and slotIds.size() > 0">
                and slot_id in
                <foreach collection="slotIds" item="slotId" open="(" separator="," close=")">
                    #{slotId}
                </foreach>
            </if>
        </where>
    </select>
</mapper>
