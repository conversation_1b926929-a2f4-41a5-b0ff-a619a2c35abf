<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.qualification.AccountQualificationMapper">

    <resultMap type="com.ruoyi.system.entity.qualification.AccountQualificationEntity" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="accountId" column="account_id"/>
        <result property="companyName" column="company_name"/>
        <result property="businessLicense" column="business_license"/>
        <result property="businessLicenseImg" column="business_license_img"/>
        <result property="bankName" column="bank_name"/>
        <result property="bankAccount" column="bank_account"/>
        <result property="bankAccountName" column="bank_account_name"/>
        <result property="remarkText" column="remark_text"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        account_id,
        company_name,
        business_license,
        business_license_img,
        bank_name,
        bank_account,
        bank_account_name,
        remark_text,
        gmt_create,
        gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.qualification.AccountQualificationEntity">
        INSERT INTO tb_account_qualification
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountId != null">
                account_id,
            </if>
            <if test="companyName != null">
                company_name,
            </if>
            <if test="businessLicense != null">
                business_license,
            </if>
            <if test="businessLicenseImg != null">
                business_license_img,
            </if>
            <if test="bankName != null">
                bank_name,
            </if>
            <if test="bankAccount != null">
                bank_account,
            </if>
            <if test="bankAccountName != null">
                bank_account_name,
            </if>
            <if test="remarkText != null">
                remark_text
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountId != null">
                #{accountId},
            </if>
            <if test="companyName != null">
                #{companyName},
            </if>
            <if test="businessLicense != null">
                #{businessLicense},
            </if>
            <if test="businessLicenseImg != null">
                #{businessLicenseImg},
            </if>
            <if test="bankName != null">
                #{bankName},
            </if>
            <if test="bankAccount != null">
                #{bankAccount},
            </if>
            <if test="bankAccountName != null">
                #{bankAccountName},
            </if>
            <if test="remarkText != null">
                #{remarkText}
            </if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.ruoyi.system.entity.qualification.AccountQualificationEntity">
        UPDATE tb_account_qualification
        <set>
            <if test="accountId != null">
                account_id = #{accountId},
            </if>
            <if test="companyName != null">
                company_name = #{companyName},
            </if>
            <if test="businessLicense != null">
                business_license = #{businessLicense},
            </if>
            <if test="businessLicenseImg != null">
                business_license_img = #{businessLicenseImg},
            </if>
            <if test="bankName != null">
                bank_name = #{bankName},
            </if>
            <if test="bankAccount != null">
                bank_account = #{bankAccount},
            </if>
            <if test="bankAccountName != null">
                bank_account_name = #{bankAccountName},
            </if>
            <if test="remarkText != null">
                remark_text = #{remarkText},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tb_account_qualification
        WHERE id = #{id}
    </select>
    <select id="selectByAccountId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_account_qualification
        where account_id = #{accountId}
    </select>
    <select id="selectQualificationList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_account_qualification
        <where>
            <if test="startDate != null">
                gmt_create &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                and gmt_create &lt;= #{endDate}
            </if>
            <if test="accountIds != null">
                and account_id in
                <foreach collection="accountIds" item="accountId" close=")" separator="," open="(">
                    #{accountId}
                </foreach>
            </if>
        </where>
        order by gmt_create desc
    </select>
    <select id="selectAccountIdsByCompanyName" resultType="Long">
        select account_id
        from tb_account_qualification
        where company_name like concat('%', #{company}, '%')
    </select>

</mapper>