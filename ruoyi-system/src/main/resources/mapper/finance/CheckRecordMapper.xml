<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.finance.CheckRecordMapper">

    <resultMap type="com.ruoyi.system.entity.checkrecord.CheckRecordEntity" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="withdrawRecordId" column="withdraw_record_id"/>
        <result property="checkAccountId" column="check_account_id"/>
        <result property="checkStatus" column="check_status"/>
        <result property="complexAuditStatus" column="complex_audit_status"/>
        <result property="refuseReason" column="refuse_reason"/>
        <result property="auditTime" column="audit_time"/>
        <result property="leaderAuditorId" column="leader_auditor_id"/>
        <result property="leaderAuditTime" column="leader_audit_time"/>
        <result property="leaderAuditReason" column="leader_audit_reason"/>
        <result property="ceoAuditorId" column="ceo_auditor_id"/>
        <result property="ceoAuditTime" column="ceo_audit_time"/>
        <result property="ceoAuditReason" column="ceo_audit_reason"/>
        <result property="financeAuditorId" column="finance_auditor_id"/>
        <result property="financeAuditTime" column="finance_audit_time"/>
        <result property="financeAuditReason" column="finance_audit_reason"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        withdraw_record_id,
        check_account_id,
        check_status,
        complex_audit_status,
        refuse_reason,
        audit_time,
        leader_auditor_id,
        leader_audit_time,
        leader_audit_reason,
        ceo_auditor_id,
        ceo_audit_time,
        ceo_audit_reason,
        finance_auditor_id,
        finance_audit_time,
        finance_audit_reason,
        gmt_create,
        gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.checkrecord.CheckRecordEntity">
        INSERT INTO tb_check_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="withdrawRecordId != null">
                withdraw_record_id,
            </if>
            <if test="checkAccountId != null">
                check_account_id,
            </if>
            <if test="complexAuditStatus != null">
                complex_audit_status,
            </if>
            <if test="checkStatus != null">
                check_status,
            </if>
            <if test="refuseReason != null">
                refuse_reason,
            </if>
            <if test="auditTime != null">
                audit_time,
            </if>
            <if test="leaderAuditorId != null">
                leader_auditor_id,
            </if>
            <if test="leaderAuditTime != null">
                leader_audit_time,
            </if>
            <if test="leaderAuditReason != null">
                leader_audit_reason,
            </if>
            <if test="ceoAuditorId != null">
                ceo_auditor_id,
            </if>
            <if test="ceoAuditTime != null">
                ceo_audit_time,
            </if>
            <if test="ceoAuditReason != null">
                ceo_audit_reason,
            </if>
            <if test="financeAuditorId != null">
                finance_auditor_id,
            </if>
            <if test="financeAuditTime != null">
                finance_audit_time,
            </if>
            <if test="financeAuditReason != null">
                finance_audit_reason,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="withdrawRecordId != null">
                #{withdrawRecordId},
            </if>
            <if test="checkAccountId != null">
                #{checkAccountId},
            </if>
            <if test="complexAuditStatus != null">
                #{complexAuditStatus},
            </if>
            <if test="checkStatus != null">
                #{checkStatus},
            </if>
            <if test="refuseReason != null">
                #{refuseReason},
            </if>
            <if test="auditTime != null">
                #{auditTime},
            </if>
            <if test="leaderAuditorId != null">
                #{leaderAuditorId},
            </if>
            <if test="leaderAuditTime != null">
                #{leaderAuditTime},
            </if>
            <if test="leaderAuditReason != null">
                #{leaderAuditReason},
            </if>
            <if test="ceoAuditorId != null">
                #{ceoAuditorId},
            </if>
            <if test="ceoAuditTime != null">
                #{ceoAuditTime},
            </if>
            <if test="ceoAuditReason != null">
                #{ceoAuditReason},
            </if>
            <if test="financeAuditorId != null">
                #{financeAuditorId},
            </if>
            <if test="financeAuditTime != null">
                #{financeAuditTime},
            </if>
            <if test="financeAuditReason != null">
                #{financeAuditReason},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.ruoyi.system.entity.checkrecord.CheckRecordEntity">
        UPDATE tb_check_record
        <set>
            <if test="checkAccountId != null">
                check_account_id = #{checkAccountId},
            </if>
            <if test="checkStatus != null">
                check_status = #{checkStatus},
            </if>
            <if test="complexAuditStatus != null">
                complex_audit_status = #{complexAuditStatus},
            </if>
            <if test="refuseReason != null">
                refuse_reason = #{refuseReason},
            </if>
            <if test="auditTime != null">
                audit_time = #{auditTime},
            </if>
            <if test="leaderAuditorId != null">
                leader_auditor_id = #{leaderAuditorId},
            </if>
            <if test="leaderAuditTime != null">
                leader_audit_time = #{leaderAuditTime},
            </if>
            <if test="leaderAuditReason != null">
                leader_audit_reason = #{leaderAuditReason},
            </if>
            <if test="ceoAuditorId != null">
                ceo_auditor_id = #{ceoAuditorId},
            </if>
            <if test="ceoAuditTime != null">
                ceo_audit_time = #{ceoAuditTime},
            </if>
            <if test="ceoAuditReason != null">
                ceo_audit_reason = #{ceoAuditReason},
            </if>
            <if test="financeAuditorId != null">
                finance_auditor_id = #{financeAuditorId},
            </if>
            <if test="financeAuditTime != null">
                finance_audit_time = #{financeAuditTime},
            </if>
            <if test="financeAuditReason != null">
                finance_audit_reason = #{financeAuditReason},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectByWithdrawId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tb_check_record
        WHERE withdraw_record_id = #{withdrawId}
        ORDER BY id desc
        LIMIT 1
    </select>

    <select id="selectListByWithdrawIds" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tb_check_record
        WHERE withdraw_record_id in
          <foreach collection="withdrawIds" item="withdrawId" close=")" separator="," open="(">
            #{withdrawId}
          </foreach>
        order by id
    </select>

</mapper>
