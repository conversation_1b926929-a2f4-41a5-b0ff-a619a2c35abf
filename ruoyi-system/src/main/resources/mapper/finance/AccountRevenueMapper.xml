<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.finance.AccountRevenueMapper">

    <resultMap type="com.ruoyi.system.entity.account.finance.AccountRevenueEntity" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="accountId" column="account_id"/>
        <result property="totalRevenue" column="total_revenue"/>
        <result property="withdrawableAmount" column="withdrawable_amount"/>
        <result property="payType" column="pay_type"/>
        <result property="prepayAmount" column="prepay_amount"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        account_id,
        total_revenue,
        withdrawable_amount,
        pay_type,
        prepay_amount,
        gmt_create,
        gmt_modified
    </sql>

    <insert id="addRevenue">
        insert into tb_account_revenue(account_id,total_revenue,withdrawable_amount)
        VALUES(#{accountId},#{revenue},#{withdrawableAmount})
            ON DUPLICATE KEY UPDATE total_revenue = total_revenue + #{revenue} , withdrawable_amount = withdrawable_amount + #{withdrawableAmount}
    </insert>

    <insert id="addPrepayAmount">
        insert into tb_account_revenue(account_id, prepay_amount)
        VALUES(#{accountId}, #{prepayAmount})
        ON DUPLICATE KEY UPDATE prepay_amount = prepay_amount + #{prepayAmount}
    </insert>

    <select id="selectByAccountId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_account_revenue
        where account_id = #{accountId}
    </select>

    <update id="deductionWithdrawAmount">
        update tb_account_revenue
        set withdrawable_amount = withdrawable_amount - #{amount}
        where account_id = #{accountId} and withdrawable_amount >= #{amount}
    </update>

    <insert id="updatePayType">
        insert into tb_account_revenue(account_id, pay_type)
        VALUES(#{accountId}, #{payType})
        ON DUPLICATE KEY UPDATE pay_type = #{payType}
    </insert>

    <select id="selectPrepayAccountRevenue" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_account_revenue
        where pay_type = 1
    </select>

    <select id="selectPrepayAccountRevenueByAccountIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_account_revenue
        where pay_type = 1
        <if test="accountIds != null and accountIds.size() > 0">
            and account_id in
            <foreach collection="accountIds" separator="," item="accountId" open="(" close=")">
                #{accountId}
            </foreach>
        </if>
    </select>
</mapper>
