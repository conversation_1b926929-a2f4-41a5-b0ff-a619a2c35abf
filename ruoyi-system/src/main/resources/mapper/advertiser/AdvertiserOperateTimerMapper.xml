<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.advertiser.AdvertiserOperateTimerMapper">

    <resultMap type="com.ruoyi.system.entity.advertiser.AdvertiserOperateTimerEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="advertiserId" column="advertiser_id"/>
            <result property="operType" column="oper_type"/>
            <result property="planTime" column="plan_time"/>
            <result property="execTime" column="exec_time"/>
            <result property="operatorId" column="operator_id"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            advertiser_id,
            oper_type,
            plan_time,
            exec_time,
            operator_id,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.advertiser.AdvertiserOperateTimerEntity">
        INSERT INTO tb_advertiser_operate_timer
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="advertiserId != null">
                    advertiser_id,
                </if>
                <if test="operType != null">
                    oper_type,
                </if>
                <if test="planTime != null">
                    plan_time,
                </if>
                <if test="operatorId != null">
                    operator_id
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="advertiserId != null">
                    #{advertiserId},
                </if>
                <if test="operType != null">
                    #{operType},
                </if>
                <if test="planTime != null">
                    #{planTime},
                </if>
                <if test="operatorId != null">
                    #{operatorId}
                </if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.ruoyi.system.entity.advertiser.AdvertiserOperateTimerEntity">
        UPDATE tb_advertiser_operate_timer
        <set>
            <if test="planTime != null">
                plan_time = #{planTime},
            </if>
            <if test="execTime != null">
                exec_time = #{execTime},
            </if>
            <if test="operatorId != null">
                operator_id = #{operatorId},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <update id="execTimer" parameterType="Long">
        UPDATE tb_advertiser_operate_timer
        SET exec_time = now()
        WHERE id = #{id} and exec_time is null
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_advertiser_operate_timer
        WHERE id = #{id}
    </select>

    <select id="selectPlanTimerBy" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_advertiser_operate_timer
        WHERE advertiser_id = #{advertiserId} and oper_type = #{operType} and exec_time is null
        LIMIT 1
    </select>

    <select id="selectPlanTimerByAdvertiserIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_advertiser_operate_timer
        <where>
            exec_time is null
            <if test="advertiserIds != null and advertiserIds.size() > 0">
                and advertiser_id in
                <foreach collection="advertiserIds" separator="," item="advertiserId" close=")" open="(">
                    #{advertiserId}
                </foreach>
            </if>
        </where>
        order by gmt_create
    </select>

    <select id="selectReadyTimer" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_advertiser_operate_timer
        WHERE plan_time &lt;= now() and exec_time is null
        ORDER BY plan_time
    </select>
</mapper>
