<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.advertiser.finance.AdvertiserFianceStatisticsRecordMapper">

    <resultMap type="com.ruoyi.system.entity.advertiser.finance.AdvertiserFianceStatisticsRecordEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="accountId" column="account_id"/>
            <result property="curDate" column="cur_date"/>
            <result property="rechargeAmount" column="recharge_amount"/>
            <result property="consumeAmount" column="consume_amount"/>
            <result property="balanceAmount" column="balance_amount"/>
            <result property="cashBalance" column="cash_balance"/>
            <result property="rebateBalance" column="rebate_balance"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            account_id,
            cur_date,
            recharge_amount,
            consume_amount,
            (cash_balance + rebate_balance) as balance_amount,
            cash_balance,
            rebate_balance,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.advertiser.finance.AdvertiserFianceStatisticsRecordEntity">
        INSERT INTO tb_advertiser_fiance_statistics_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="accountId != null">
                    account_id,
                </if>
                <if test="curDate != null">
                    cur_date,
                </if>
                <if test="rechargeAmount != null">
                    recharge_amount,
                </if>
                <if test="consumeAmount != null">
                    consume_amount,
                </if>
                <if test="cashBalance != null">
                    cash_balance,
                </if>
                <if test="rebateBalance != null">
                    rebate_balance
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="accountId != null">
                    #{accountId},
                </if>
                <if test="curDate != null">
                    #{curDate},
                </if>
                <if test="rechargeAmount != null">
                    #{rechargeAmount},
                </if>
                <if test="consumeAmount != null">
                    #{consumeAmount},
                </if>
                <if test="cashBalance != null">
                    #{cashBalance},
                </if>
                <if test="rebateBalance != null">
                    #{rebateBalance}
                </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.ruoyi.system.req.advertiser.finance.AdvertiserFianceStatisticsRecordUpdateReq">
        UPDATE tb_advertiser_fiance_statistics_record
        <set>
            <if test="rechargeAmountAdd != null">
                recharge_amount = recharge_amount + #{rechargeAmountAdd},
            </if>
            <if test="consumeAmountAdd != null">
                consume_amount = consume_amount + #{consumeAmountAdd},
            </if>
            <if test="cashBalanceAdd != null">
                cash_balance = cash_balance + #{cashBalanceAdd},
            </if>
            <if test="rebateBalanceAdd != null">
                rebate_balance = rebate_balance + #{rebateBalanceAdd},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectList" parameterType="com.ruoyi.system.req.advertiser.finance.AdvertiserFianceStatisticsRecordListReq" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_advertiser_fiance_statistics_record
        <where>
            <if test="accountId != null"> and account_id = #{accountId}</if>
            <if test="accountIds != null and accountIds.size() > 0">
                and account_id in
                <foreach collection="accountIds" separator="," item="accountId" close=")" open="(">
                    #{accountId}
                </foreach>
            </if>
            <if test="startDate != null"> and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null"> and cur_date &lt;= #{endDate}</if>
            <if test="excludeAccountIds != null and excludeAccountIds.size() > 0">
                and account_id not in
                <foreach collection="excludeAccountIds" separator="," item="excludeAccountId" close=")" open="(">
                    #{excludeAccountId}
                </foreach>
            </if>
        </where>
        ORDER BY cur_date desc, consume_amount desc, balance_amount desc, id desc
    </select>

    <select id="selectByAccountIdAndDate" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_advertiser_fiance_statistics_record
        WHERE account_id = #{accountId} and cur_date = #{curDate}
    </select>

    <select id="selectLatestByAccountId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_advertiser_fiance_statistics_record
        WHERE account_id = #{accountId}
        ORDER BY cur_date DESC
        LIMIT 1
    </select>
</mapper>
