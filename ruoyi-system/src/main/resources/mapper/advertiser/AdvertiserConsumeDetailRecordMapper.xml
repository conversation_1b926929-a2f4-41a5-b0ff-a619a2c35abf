<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.advertiser.finance.AdvertiserConsumeDetailRecordMapper">

    <resultMap type="com.ruoyi.system.entity.advertiser.finance.AdvertiserConsumeDetailRecordEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="accountId" column="account_id"/>
            <result property="consumeType" column="consume_type"/>
            <result property="consumeAmount" column="consume_amount"/>
            <result property="recordId" column="record_id"/>
            <result property="remark" column="remark"/>
            <result property="isDone" column="is_done"/>
            <result property="doneTime" column="done_time"/>
            <result property="retryTimes" column="retry_times"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            account_id,
            consume_type,
            consume_amount,
            record_id,
            remark,
            is_done,
            done_time,
            retry_times,
            gmt_create,
            gmt_modified
    </sql>


    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.advertiser.finance.AdvertiserConsumeDetailRecordEntity">
        INSERT INTO tb_advertiser_consume_detail_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="accountId != null">
                    account_id,
                </if>
                <if test="consumeType != null">
                    consume_type,
                </if>
                <if test="consumeAmount != null">
                    consume_amount,
                </if>
                <if test="recordId != null">
                    record_id,
                </if>
                <if test="remark != null">
                    remark,
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="accountId != null">
                    #{accountId},
                </if>
                <if test="consumeType != null">
                    #{consumeType},
                </if>
                <if test="consumeAmount != null">
                    #{consumeAmount},
                </if>
                <if test="recordId != null">
                    #{recordId},
                </if>
                <if test="remark != null">
                    #{remark},
                </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.ruoyi.system.entity.advertiser.finance.AdvertiserConsumeDetailRecordEntity">
        UPDATE tb_advertiser_consume_detail_record
        <set>
            <if test="isDone != null">
                is_done = #{isDone},
            </if>
            <if test="doneTime != null">
                done_time = #{doneTime},
            </if>
            <if test="retryTimes != null and retryTimes > 0">
                retry_times = retry_times + 1,
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectList" parameterType="com.ruoyi.system.entity.advertiser.finance.AdvertiserConsumeDetailRecordEntity" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_advertiser_consume_detail_record
        <where>
            <if test="accountId != null"> and account_id = #{accountId}</if>
            <if test="isDone != null"> and is_done = #{isDone}</if>
            <if test="retryTimes != null"> and retry_times &lt; #{retryTimes}</if>
            <if test="gmtCreate != null"> and gmt_create &lt; #{gmtCreate}</if>
        </where>
        order by id
    </select>
</mapper>
