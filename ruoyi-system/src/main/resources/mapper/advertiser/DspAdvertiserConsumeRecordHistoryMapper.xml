<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.advertiser.DspAdvertiserConsumeRecordHistoryMapper">

    <resultMap type="com.ruoyi.system.entity.advertiser.DspAdvertiserConsumeRecordHistoryEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="curDate" column="cur_date"/>
            <result property="advertiserId" column="advertiser_id"/>
            <result property="beforeEdit" column="before_edit"/>
            <result property="afterEdit" column="after_edit"/>
            <result property="source" column="source"/>
            <result property="operatorId" column="operator_id"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            cur_date,
            advertiser_id,
            before_edit,
            after_edit,
            source,
            operator_id,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.advertiser.DspAdvertiserConsumeRecordHistoryEntity">
        INSERT INTO tb_dsp_advertiser_consume_record_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="curDate != null">
                    cur_date,
                </if>
                <if test="advertiserId != null">
                    advertiser_id,
                </if>
                <if test="beforeEdit != null">
                    before_edit,
                </if>
                <if test="afterEdit != null">
                    after_edit,
                </if>
                <if test="source != null">
                    source,
                </if>
                <if test="operatorId != null">
                    operator_id
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="curDate != null">
                    #{curDate},
                </if>
                <if test="advertiserId != null">
                    #{advertiserId},
                </if>
                <if test="beforeEdit != null">
                    #{beforeEdit},
                </if>
                <if test="afterEdit != null">
                    #{afterEdit},
                </if>
                <if test="source != null">
                    #{source},
                </if>
                <if test="operatorId != null">
                    #{operatorId}
                </if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.ruoyi.system.entity.advertiser.DspAdvertiserConsumeRecordHistoryEntity">
        UPDATE tb_dsp_advertiser_consume_record_history
        <set>
                    <if test="curDate != null">
                        cur_date = #{curDate},
                    </if>
                    <if test="advertiserId != null">
                        advertiser_id = #{advertiserId},
                    </if>
                    <if test="beforeEdit != null">
                        before_edit = #{beforeEdit},
                    </if>
                    <if test="afterEdit != null">
                        after_edit = #{afterEdit},
                    </if>
                    <if test="source != null">
                        source = #{source},
                    </if>
                    <if test="operatorId != null">
                        operator_id = #{operatorId},
                    </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_dsp_advertiser_consume_record_history
        WHERE id = #{id}
    </select>

</mapper>
