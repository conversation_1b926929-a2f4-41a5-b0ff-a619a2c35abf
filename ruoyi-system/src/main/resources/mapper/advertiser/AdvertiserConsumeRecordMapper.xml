<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.advertiser.finance.AdvertiserConsumeRecordMapper">

    <resultMap type="com.ruoyi.system.entity.advertiser.finance.AdvertiserConsumeRecordEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="accountId" column="account_id"/>
            <result property="curDate" column="cur_date"/>
            <result property="consumeAmount" column="consume_amount"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            account_id,
            cur_date,
            consume_amount,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.advertiser.finance.AdvertiserConsumeRecordEntity">
        INSERT INTO tb_advertiser_consume_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="accountId != null">
                    account_id,
                </if>
                <if test="curDate != null">
                    cur_date,
                </if>
                <if test="consumeAmount != null">
                    consume_amount
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="accountId != null">
                    #{accountId},
                </if>
                <if test="curDate != null">
                    #{curDate},
                </if>
                <if test="consumeAmount != null">
                    #{consumeAmount}
                </if>
        </trim>
    </insert>

    <update id="updateByReq" parameterType="com.ruoyi.system.req.advertiser.finance.AdvertiserConsumeRecordUpdateReq">
        UPDATE tb_advertiser_consume_record
        <set>
            <if test="consumeAmountAdd != null">
                consume_amount = consume_amount + #{consumeAmountAdd},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_advertiser_consume_record
        WHERE id = #{id}
    </select>

    <select id="selectByAccountIdAndDate" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_advertiser_consume_record
        WHERE account_id = #{accountId} and cur_date = #{curDate}
    </select>

    <select id="selectList" parameterType="com.ruoyi.system.req.advertiser.finance.AdvertiserConsumeListReq" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_advertiser_consume_record
        <where>
            <if test="accountId != null"> and account_id = #{accountId}</if>
            <if test="accountIds != null and accountIds.size() > 0">
                and account_id in
                <foreach collection="accountIds" separator="," item="accountId" close=")" open="(">
                    #{accountId}
                </foreach>
            </if>
            <if test="startDate != null"> and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null"> and cur_date &lt;= #{endDate}</if>
            <if test="invisibleDateList != null and invisibleDateList.size() > 0">
                and cur_date not in
                <foreach collection="invisibleDateList" separator="," item="date" close=")" open="(">
                    #{date}
                </foreach>
            </if>
        </where>
        order by cur_date desc, id desc
    </select>

    <select id="sumConsumeAmount" parameterType="com.ruoyi.system.req.advertiser.finance.AdvertiserConsumeListReq" resultType="Integer">
        SELECT sum(consume_amount) as consume_amount
        FROM tb_advertiser_consume_record
        <where>
            <if test="accountId != null"> and account_id = #{accountId}</if>
        </where>
    </select>

    <select id="sumConsumeAmountByAccountIds" resultMap="BaseResultMap">
        SELECT account_id, sum(consume_amount) as consume_amount
        FROM tb_advertiser_consume_record
        <where>
            <if test="accountIds != null and accountIds.size() > 0">
                and account_id in
                <foreach collection="accountIds" separator="," item="accountId" close=")" open="(">
                    #{accountId}
                </foreach>
            </if>
        </where>
        group by account_id
    </select>

    <select id="sumConsumeAmountByAccountIdsAndDate" resultMap="BaseResultMap">
        SELECT account_id, sum(consume_amount) as consume_amount
        FROM tb_advertiser_consume_record
        <where>
            <if test="date != null"> and cur_date = #{date}</if>
            <if test="accountIds != null and accountIds.size() > 0">
                and account_id in
                <foreach collection="accountIds" separator="," item="accountId" close=")" open="(">
                    #{accountId}
                </foreach>
            </if>
        </where>
        group by account_id
    </select>
</mapper>
