<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.advertiser.finance.AdvertiserBalanceMapper">

    <resultMap type="com.ruoyi.system.entity.advertiser.finance.AdvertiserBalanceEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="accountId" column="account_id"/>
            <result property="totalAmount" column="total_amount"/>
            <result property="cashAmount" column="cash_amount"/>
            <result property="rebateAmount" column="rebate_amount"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            account_id,
            (cash_amount + rebate_amount) as total_amount,
            cash_amount,
            rebate_amount,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.advertiser.finance.AdvertiserBalanceEntity">
        INSERT INTO tb_advertiser_balance
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="accountId != null">
                    account_id,
                </if>
                <if test="cashAmount != null">
                    cash_amount,
                </if>
                <if test="rebateAmount != null">
                    rebate_amount
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="accountId != null">
                    #{accountId},
                </if>
                <if test="cashAmount != null">
                    #{cashAmount},
                </if>
                <if test="rebateAmount != null">
                    #{rebateAmount}
                </if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.ruoyi.system.entity.advertiser.finance.AdvertiserBalanceEntity">
        UPDATE tb_advertiser_balance
        <set>
            <if test="accountId != null">
                account_id = #{accountId},
            </if>
            <if test="cashAmount != null">
                cash_amount = #{cashAmount},
            </if>
            <if test="rebateAmount != null">
                rebate_amount = #{rebateAmount},
            </if>
            <if test="cashAmountAdd != null and cashAmountAdd > 0">
                cash_amount = cash_amount + #{cashAmountAdd},
            </if>
            <if test="rebateAmountAdd != null and rebateAmountAdd > 0">
                rebate_amount = rebate_amount + #{rebateAmountAdd},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <update id="updateBalance" parameterType="com.ruoyi.system.req.advertiser.finance.AdvertiserBalanceUpdateReq">
        UPDATE tb_advertiser_balance
        <set>
            <if test="cashAmountAdd != null">
                cash_amount = cash_amount + #{cashAmountAdd},
            </if>
            <if test="rebateAmountAdd != null">
                rebate_amount = rebate_amount + #{rebateAmountAdd},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <select id="selectByAccountId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_advertiser_balance
        WHERE account_id = #{accountId}
    </select>

    <select id="sumByAccountIds" resultType="Integer">
        SELECT ifnull(sum(cash_amount + rebate_amount), 0)
        FROM tb_advertiser_balance
        <where>
            <if test="accountIds != null and accountIds.size() > 0">
                and account_id in
                <foreach collection="accountIds" separator="," item="accountId" close=")" open="(">
                    #{accountId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_advertiser_balance
        <where>
            <if test="accountIds != null and accountIds.size() > 0">
                and account_id in
                <foreach collection="accountIds" separator="," item="accountId" close=")" open="(">
                    #{accountId}
                </foreach>
            </if>
        </where>
        ORDER BY total_amount desc, cash_amount desc, id desc
    </select>

    <select id="selectStatisticBalance" resultType="com.ruoyi.system.bo.advertiser.finance.AdvertiserBalanceStatisticBo">
        SELECT ifnull(sum(cash_amount + rebate_amount), 0) as totalAmount, ifnull(sum(cash_amount), 0) as cashAmount,
            ifnull(sum(rebate_amount), 0) as rebateAmount
        FROM tb_advertiser_balance
        <where>
            <if test="accountIds != null and accountIds.size() > 0">
                and account_id in
                <foreach collection="accountIds" separator="," item="accountId" close=")" open="(">
                    #{accountId}
                </foreach>
            </if>
            <if test="excludeAccountIds != null and excludeAccountIds.size() > 0">
                and account_id not in
                <foreach collection="excludeAccountIds" separator="," item="excludeAccountId" close=")" open="(">
                    #{excludeAccountId}
                </foreach>
            </if>
        </where>
    </select>
</mapper>
