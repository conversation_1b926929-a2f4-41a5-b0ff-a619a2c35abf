<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.advertiser.finance.AdvertiserConsumeCategoryRecordMapper">

    <resultMap type="com.ruoyi.system.entity.advertiser.finance.AdvertiserConsumeCategoryRecordEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="accountId" column="account_id"/>
            <result property="curDate" column="cur_date"/>
            <result property="billingType" column="billing_type"/>
            <result property="consumeAmount" column="consume_amount"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            account_id,
            cur_date,
            billing_type,
            consume_amount,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.advertiser.finance.AdvertiserConsumeCategoryRecordEntity">
        INSERT INTO tb_advertiser_consume_category_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="accountId != null">
                    account_id,
                </if>
                <if test="curDate != null">
                    cur_date,
                </if>
                <if test="billingType != null">
                    billing_type,
                </if>
                <if test="consumeAmount != null">
                    consume_amount
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="accountId != null">
                    #{accountId},
                </if>
                <if test="curDate != null">
                    #{curDate},
                </if>
                <if test="billingType != null">
                    #{billingType},
                </if>
                <if test="consumeAmount != null">
                    #{consumeAmount}
                </if>
        </trim>
    </insert>

    <update id="updateByReq" parameterType="com.ruoyi.system.req.advertiser.finance.AdvertiserConsumeRecordUpdateReq">
        UPDATE tb_advertiser_consume_category_record
        <set>
            <if test="consumeAmountAdd != null">
                consume_amount = consume_amount + #{consumeAmountAdd},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectBy" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_advertiser_consume_category_record
        WHERE account_id = #{accountId} and cur_date = #{curDate} and billing_type = #{billingType}
    </select>

    <select id="selectSumByDateAndAccountId" resultType="com.ruoyi.system.bo.advertiser.finance.AdvertiserConsumeCategorySumBo">
        SELECT cur_date as curDate , consume_amount as consumeAmount
        FROM tb_advertiser_consume_category_record
        WHERE account_id = #{accountId}
            <if test="startDate != null and endDate != null">
                and cur_date &gt;= #{startDate} and cur_date &lt;= #{endDate}
            </if>
            <if test="billingType != null">
                and billing_type = #{billingType}
            </if>
            group by cur_date
    </select>
</mapper>
