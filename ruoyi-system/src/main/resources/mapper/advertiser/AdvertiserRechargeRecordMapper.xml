<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.advertiser.finance.AdvertiserRechargeRecordMapper">

    <resultMap type="com.ruoyi.system.entity.advertiser.finance.AdvertiserRechargeRecordEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="accountId" column="account_id"/>
            <result property="sourceAccountId" column="source_account_id"/>
            <result property="rechargeType" column="recharge_type"/>
            <result property="rechargeAmount" column="recharge_amount"/>
            <result property="operatorId" column="operator_id"/>
            <result property="remark" column="remark"/>
            <result property="auditStatus" column="audit_status"/>
            <result property="auditReason" column="audit_reason"/>
            <result property="auditorId" column="auditor_id"/>
            <result property="auditTime" column="audit_time"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            account_id,
            source_account_id,
            recharge_type,
            recharge_amount,
            operator_id,
            remark,
            audit_status,
            audit_reason,
            auditor_id,
            audit_time,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.advertiser.finance.AdvertiserRechargeRecordEntity">
        INSERT INTO tb_advertiser_recharge_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="accountId != null">
                    account_id,
                </if>
                <if test="sourceAccountId != null">
                    source_account_id,
                </if>
                <if test="rechargeType != null">
                    recharge_type,
                </if>
                <if test="rechargeAmount != null">
                    recharge_amount,
                </if>
                <if test="remark != null">
                    remark,
                </if>
                <if test="operatorId != null">
                    operator_id,
                </if>
                <if test="auditStatus != null">
                    audit_status
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="accountId != null">
                    #{accountId},
                </if>
                <if test="sourceAccountId != null">
                    #{sourceAccountId},
                </if>
                <if test="rechargeType != null">
                    #{rechargeType},
                </if>
                <if test="rechargeAmount != null">
                    #{rechargeAmount},
                </if>
                <if test="remark != null">
                    #{remark},
                </if>
                <if test="operatorId != null">
                    #{operatorId},
                </if>
                <if test="auditStatus != null">
                    #{auditStatus}
                </if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.ruoyi.system.entity.advertiser.finance.AdvertiserRechargeRecordEntity">
        UPDATE tb_advertiser_recharge_record
        <set>
            <if test="auditStatus != null">
                audit_status = #{auditStatus},
            </if>
            <if test="auditReason != null">
                audit_reason = #{auditReason},
            </if>
            <if test="auditorId != null">
                auditor_id = #{auditorId},
            </if>
            <if test="auditTime != null">
                audit_time = #{auditTime},
            </if>
        </set>
        WHERE id=#{id} and audit_status = 0
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_advertiser_recharge_record
        WHERE id = #{id}
    </select>

    <select id="selectListByAccountIdAndDate" parameterType="com.ruoyi.system.req.advertiser.finance.AdvertiserRechargeListReq" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_advertiser_recharge_record
        <where>
            <if test="accountId != null"> and account_id = #{accountId}</if>
            <if test="sourceAccountId != null"> and source_account_id = #{sourceAccountId}</if>
            <if test="startDate != null"> and gmt_create &gt;= #{startDate}</if>
            <if test="endDate != null"> and gmt_create &lt;= #{endDate}</if>
            <if test="auditStatus != null"> and audit_status = #{auditStatus}</if>
            <if test="accountIds != null and accountIds.size() > 0">
                and account_id in
                <foreach collection="accountIds" separator="," item="accountId" close=")" open="(">
                    #{accountId}
                </foreach>
            </if>
            <if test="rechargeType != null"> and recharge_type = #{rechargeType}</if>
            <if test="rechargeTypes != null and rechargeTypes.size() > 0">
                and recharge_type in
                <foreach collection="rechargeTypes" separator="," item="rechargeType" close=")" open="(">
                    #{rechargeType}
                </foreach>
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectStatisticRecharge" parameterType="com.ruoyi.system.req.advertiser.finance.AdvertiserRechargeListReq" resultType="Long">
        SELECT ifnull(sum(recharge_amount), 0)
        FROM tb_advertiser_recharge_record
        <where>
            <if test="accountId != null"> and account_id = #{accountId}</if>
            <if test="sourceAccountId != null"> and source_account_id = #{sourceAccountId}</if>
            <if test="startDate != null"> and gmt_create &gt;= #{startDate}</if>
            <if test="endDate != null"> and gmt_create &lt;= #{endDate}</if>
            <if test="auditStatus != null"> and audit_status = #{auditStatus}</if>
            <if test="accountIds != null and accountIds.size() > 0">
                and account_id in
                <foreach collection="accountIds" separator="," item="accountId" close=")" open="(">
                    #{accountId}
                </foreach>
            </if>
            <if test="rechargeType != null"> and recharge_type = #{rechargeType}</if>
            <if test="rechargeTypes != null and rechargeTypes.size() > 0">
                and recharge_type in
                <foreach collection="rechargeTypes" separator="," item="rechargeType" close=")" open="(">
                    #{rechargeType}
                </foreach>
            </if>
            <if test="excludeAccountIds != null and excludeAccountIds.size() > 0">
                and account_id not in
                <foreach collection="excludeAccountIds" separator="," item="excludeAccountId" close=")" open="(">
                    #{excludeAccountId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="sumBySourceAccountId" resultType="Integer">
        SELECT sum(recharge_amount)
        FROM tb_advertiser_recharge_record
        WHERE source_account_id = #{sourceAccountId} and recharge_type in (3, 4)
    </select>

    <select id="sumAccountBySourceAccountId" resultMap="BaseResultMap">
        SELECT account_id, sum(recharge_amount) as recharge_amount
        FROM tb_advertiser_recharge_record
        WHERE source_account_id = #{sourceAccountId} and recharge_type in (3, 4)
        <if test="accountIds != null and accountIds.size() > 0">
            and account_id in
            <foreach collection="accountIds" separator="," item="accountId" close=")" open="(">
                #{accountId}
            </foreach>
        </if>
        GROUP BY account_id
        ORDER BY recharge_amount desc
    </select>
</mapper>
