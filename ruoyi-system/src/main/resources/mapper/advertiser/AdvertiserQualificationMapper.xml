<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.advertiser.AdvertiserQualificationMapper">

    <resultMap type="com.ruoyi.system.entity.advertiser.AdvertiserQualificationEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="accountId" column="account_id"/>
            <result property="qualificationName" column="qualification_name"/>
            <result property="qualificationImg" column="qualification_img"/>
            <result property="expireTime" column="expire_time"/>
            <result property="remarkText" column="remark_text"/>
            <result property="qualificationType" column="qualification_type"/>
            <result property="industryId" column="industry_id"/>
            <result property="qualificationRequireId" column="qualification_require_id"/>
            <result property="applicationTime" column="application_time"/>
            <result property="auditStatus" column="audit_status"/>
            <result property="auditor" column="auditor"/>
            <result property="auditTime" column="audit_time"/>
            <result property="auditReason" column="audit_reason"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            account_id,
            qualification_name,
            qualification_img,
            expire_time,
            remark_text,
            qualification_type,
            industry_id,
            qualification_require_id,
            application_time,
            audit_status,
            auditor,
            audit_time,
            audit_reason,
            gmt_create,
            gmt_modified
    </sql>

    <select id="selectById" parameterType="Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_advertiser_qualification
        WHERE id =  #{id}
    </select>

    <select id="selectBy" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_advertiser_qualification
        <where>
            <if test="accountId != null"> and account_id = #{accountId}</if>
            <if test="industryId != null"> and industry_id = #{industryId}</if>
            <if test="qualificationName != null"> and qualification_name = #{qualificationName}</if>
        </where>
        order by id
        limit 1
    </select>

    <update id="updateById" parameterType="com.ruoyi.system.entity.advertiser.AdvertiserQualificationEntity">
        UPDATE tb_advertiser_qualification
        <set>
            <if test="qualificationImg != null">
                qualification_img = #{qualificationImg},
            </if>
            <if test="expireTime != null">
                expire_time = #{expireTime},
            </if>
            <if test="applicationTime != null">
                application_time = #{applicationTime},
            </if>
            <if test="auditStatus != null">
                audit_status = #{auditStatus},
            </if>
            <if test="auditor != null">
                auditor = #{auditor},
            </if>
            <if test="auditTime != null">
                audit_time = #{auditTime},
            </if>
            <if test="auditReason != null">
                audit_reason = #{auditReason},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <insert id="batchInsert" parameterType="com.ruoyi.system.entity.advertiser.AdvertiserQualificationEntity">
        insert into tb_advertiser_qualification(`account_id`,`qualification_name`,`qualification_img`,`expire_time`,
            `qualification_type`, `industry_id`, `qualification_require_id`, `application_time`)
        values
        <foreach collection="list" item="entity" separator=",">
            (#{entity.accountId},#{entity.qualificationName},#{entity.qualificationImg},#{entity.expireTime},
            #{entity.qualificationType},#{entity.industryId},#{entity.qualificationRequireId}, #{entity.applicationTime})
        </foreach>
    </insert>

    <delete id="batchDelete">
        DELETE FROM tb_advertiser_qualification WHERE account_id = #{accountId}
        and id in
        <foreach collection="ids" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
    </delete>

    <update id="batchUpdate" parameterType="com.ruoyi.system.entity.advertiser.AdvertiserQualificationEntity">
        <foreach collection="list" item="entity" separator=";">
            UPDATE tb_advertiser_qualification
            <set>
                <if test="entity.qualificationName != null">
                    qualification_name = #{entity.qualificationName},
                </if>
                <if test="entity.qualificationImg != null">
                    qualification_img = #{entity.qualificationImg},
                </if>
                <if test="entity.expireTime != null">
                    expire_time = #{entity.expireTime},
                </if>
                <if test="entity.applicationTime != null">
                    application_time = #{entity.applicationTime},
                </if>
                <if test="entity.auditStatus != null">
                    audit_status = #{entity.auditStatus},
                </if>
            </set>
            WHERE id=#{entity.id}
        </foreach>

    </update>

    <select id="selectIdsByAccountId" resultType="Long">
        SELECT id
        FROM tb_advertiser_qualification
        WHERE account_id = #{accountId}
    </select>

    <select id="selectListByAccountId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_advertiser_qualification
        WHERE account_id = #{accountId}
        order by industry_id, qualification_type
    </select>

    <select id="selectListByAccountIdAndIndustryId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_advertiser_qualification
        WHERE account_id = #{accountId} and industry_id = #{industryId}
    </select>

    <select id="selectListByAccountIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_advertiser_qualification
        <where>
            <if test="accountIds != null and accountIds.size() > 0">
                and account_id in
                <foreach collection="accountIds" separator="," item="accountId" close=")" open="(">
                    #{accountId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectIndustryListByAccountIds" resultType="com.ruoyi.system.bo.advertiser.AdvertiserIndustryBo">
        SELECT q.account_id as advertiserId, q.industry_id as industryId, i.industry_name as industryName
        from (
            select distinct account_id, industry_id
            from tb_advertiser_qualification
            WHERE account_id in
            <foreach collection="accountIds" separator="," item="accountId" close=")" open="(">
                #{accountId}
            </foreach>
            and industry_id > 0
        ) q left join tb_industry i on q.industry_id= i.id
    </select>

    <select id="selectIndustryIdsByAccountId" parameterType="Long" resultType="Long">
        SELECT distinct industry_id
        FROM tb_advertiser_qualification
        WHERE account_id = #{accountId}
    </select>
</mapper>
