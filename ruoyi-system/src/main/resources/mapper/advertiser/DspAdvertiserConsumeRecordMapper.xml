<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.advertiser.DspAdvertiserConsumeRecordMapper">

    <resultMap type="com.ruoyi.system.entity.advertiser.DspAdvertiserConsumeRecordEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="curDate" column="cur_date"/>
            <result property="advertiserId" column="advertiser_id"/>
            <result property="billingClickPv" column="billing_click_pv"/>
            <result property="billingClickUv" column="billing_click_uv"/>
            <result property="consumeAmount" column="consume_amount"/>
            <result property="isVisible" column="is_visible"/>
            <result property="operatorId" column="operator_id"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            cur_date,
            advertiser_id,
            billing_click_pv,
            billing_click_uv,
            consume_amount,
            is_visible,
            operator_id,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.advertiser.DspAdvertiserConsumeRecordEntity">
        INSERT INTO tb_dsp_advertiser_consume_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="curDate != null">
                    cur_date,
                </if>
                <if test="advertiserId != null">
                    advertiser_id,
                </if>
                <if test="billingClickPv != null">
                    billing_click_pv,
                </if>
                <if test="billingClickUv != null">
                    billing_click_uv,
                </if>
                <if test="consumeAmount != null">
                    consume_amount,
                </if>
                <if test="isVisible != null">
                    is_visible,
                </if>
                <if test="operatorId != null">
                    operator_id
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="curDate != null">
                    #{curDate},
                </if>
                <if test="advertiserId != null">
                    #{advertiserId},
                </if>
                <if test="billingClickPv != null">
                    #{billingClickPv},
                </if>
                <if test="billingClickUv != null">
                    #{billingClickUv},
                </if>
                <if test="consumeAmount != null">
                    #{consumeAmount},
                </if>
                <if test="isVisible != null">
                    #{isVisible},
                </if>
                <if test="operatorId != null">
                    #{operatorId}
                </if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.ruoyi.system.entity.advertiser.DspAdvertiserConsumeRecordEntity">
        UPDATE tb_dsp_advertiser_consume_record
        <set>
            <if test="curDate != null">
                cur_date = #{curDate},
            </if>
            <if test="advertiserId != null">
                advertiser_id = #{advertiserId},
            </if>
            <if test="billingClickPv != null">
                billing_click_pv = #{billingClickPv},
            </if>
            <if test="billingClickUv != null">
                billing_click_uv = #{billingClickUv},
            </if>
            <if test="consumeAmount != null">
                consume_amount = #{consumeAmount},
            </if>
            <if test="isVisible != null">
                is_visible = #{isVisible},
            </if>
            <if test="operatorId != null">
                operator_id = #{operatorId},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectByAdvertiserIdAndDate" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_dsp_advertiser_consume_record
        WHERE cur_date = #{curDate} and advertiser_id = #{advertiserId}
    </select>

    <select id="selectInvisibleDateList" resultType="java.util.Date">
        SELECT cur_date
        FROM tb_dsp_advertiser_consume_record
        WHERE advertiser_id = #{advertiserId} and is_visible = 0
    </select>

    <select id="batchSelectInvisibleDateList" resultMap="BaseResultMap">
        SELECT advertiser_id, cur_date
        FROM tb_dsp_advertiser_consume_record
        <where>
            is_visible = 0
            <if test="advertiserIds != null and advertiserIds.size() > 0">
                and advertiser_id in
                <foreach collection="advertiserIds" separator="," item="advertiserId" close=")" open="(">
                    #{advertiserId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_dsp_advertiser_consume_record
        <where>
            <if test="advertiserIds != null and advertiserIds.size() > 0">
                and advertiser_id in
                <foreach collection="advertiserIds" separator="," item="advertiserId" close=")" open="(">
                    #{advertiserId}
                </foreach>
            </if>
            <if test="startDate != null"> and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null"> and cur_date &lt;= #{endDate}</if>
            <if test="isVisible != null"> and is_visible = #{isVisible}</if>
        </where>
    </select>

    <select id="consumeOffset" resultType="java.lang.Integer">
        SELECT ifnull(sum(case
                when d.is_visible = 0 then -a.consume_amount
                when d.is_visible = 1 then d.consume_amount - a.consume_amount
                end), 0)
        FROM tb_dsp_advertiser_consume_record d
        LEFT JOIN tb_advertiser_consume_record a on d.cur_date = a.cur_date and d.advertiser_id = a.account_id
        WHERE d.advertiser_id = #{advertiserId} and d.cur_date &lt; curdate()
    </select>

    <select id="consumeOffsetForCrm" resultType="java.lang.Integer">
        SELECT ifnull(sum(d.consume_amount - a.consume_amount), 0)
        FROM tb_dsp_advertiser_consume_record d
        LEFT JOIN tb_advertiser_consume_record a on d.cur_date = a.cur_date and d.advertiser_id = a.account_id
        WHERE d.advertiser_id = #{advertiserId} and d.cur_date &lt;= curdate()
    </select>

    <select id="batchConsumeOffsetForCrm" resultType="com.ruoyi.system.bo.advertiser.finance.AdvertiserConsumeOffsetBo">
        SELECT d.advertiser_id as advertiserId, sum(ifnull(d.consume_amount, 0) - ifnull(a.consume_amount, 0)) as offset
        FROM tb_dsp_advertiser_consume_record d
        LEFT JOIN tb_advertiser_consume_record a on d.cur_date = a.cur_date and d.advertiser_id = a.account_id
        <where>
            d.cur_date &lt;= curdate()
            <if test="advertiserIds != null and advertiserIds.size() > 0">
                and d.advertiser_id in
                <foreach collection="advertiserIds" separator="," item="advertiserId" close=")" open="(">
                    #{advertiserId}
                </foreach>
            </if>
        </where>
        GROUP BY d.advertiser_id
    </select>

    <select id="batchConsumeOffset" resultType="com.ruoyi.system.bo.advertiser.finance.AdvertiserConsumeOffsetBo">
        SELECT d.cur_date as curDate , d.advertiser_id as advertiserId, sum(ifnull(d.consume_amount, 0) - ifnull(a.consume_amount, 0)) as offset
        FROM tb_dsp_advertiser_consume_record d
        LEFT JOIN tb_advertiser_consume_record a on d.cur_date = a.cur_date and d.advertiser_id = a.account_id
        <where>
            <if test="advertiserIds != null and advertiserIds.size() > 0">
                and d.advertiser_id in
                <foreach collection="advertiserIds" separator="," item="advertiserId" close=")" open="(">
                    #{advertiserId}
                </foreach>
            </if>
            <if test="startDate != null"> and d.cur_date &gt;= #{startDate}</if>
            <if test="endDate != null"> and d.cur_date &lt;= #{endDate}</if>
        </where>
        group by d.cur_date, d.advertiser_id
    </select>

    <select id="advertiserConsumeOffsetForCrm" resultType="com.ruoyi.system.bo.advertiser.finance.AdvertiserConsumeOffsetBo">
        SELECT d.advertiser_id as advertiserId, ifnull(sum(ifnull(d.consume_amount, 0) - a.consume_amount),0) as offset
        FROM tb_dsp_advertiser_consume_record d
        LEFT JOIN tb_advertiser_consume_record a on d.cur_date = a.cur_date and d.advertiser_id = a.account_id
        <where>
            <if test="advertiserIds != null and advertiserIds.size() > 0">
                and d.advertiser_id in
                <foreach collection="advertiserIds" separator="," item="advertiserId" close=")" open="(">
                    #{advertiserId}
                </foreach>
            </if>
        </where>
        group by d.advertiser_id
    </select>

    <select id="sumAdvertiserConsumeOffsetForCrm" resultType="Long">
        SELECT ifnull(sum(ifnull(d.consume_amount, 0) - a.consume_amount), 0)
        FROM tb_dsp_advertiser_consume_record d
        LEFT JOIN tb_advertiser_consume_record a on d.cur_date = a.cur_date and d.advertiser_id = a.account_id
        <where>
            <if test="advertiserIds != null and advertiserIds.size() > 0">
                and d.advertiser_id in
                <foreach collection="advertiserIds" separator="," item="advertiserId" close=")" open="(">
                    #{advertiserId}
                </foreach>
            </if>
            <if test="excludeAccountIds != null and excludeAccountIds.size() > 0">
                and account_id not in
                <foreach collection="excludeAccountIds" separator="," item="excludeAccountId" close=")" open="(">
                    #{excludeAccountId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="advertiserConsumeOffset" resultType="com.ruoyi.system.bo.advertiser.finance.AdvertiserConsumeOffsetBo">
        SELECT d.advertiser_id as advertiserId,
                ifnull(sum(case when d.is_visible = 0 then -a.consume_amount
                         when d.is_visible = 1 then d.consume_amount - a.consume_amount
                    end),0) as offset
        FROM tb_dsp_advertiser_consume_record d
        LEFT JOIN tb_advertiser_consume_record a on d.cur_date = a.cur_date and d.advertiser_id = a.account_id
        <where>
            <if test="advertiserIds != null and advertiserIds.size() > 0">
                and d.advertiser_id in
                <foreach collection="advertiserIds" separator="," item="advertiserId" close=")" open="(">
                    #{advertiserId}
                </foreach>
            </if>
        </where>
        group by d.advertiser_id
    </select>

    <select id="advertiserConsumeOffsetSum" resultType="Integer">
        SELECT ifnull(sum(case
            when d.is_visible = 0 then -a.consume_amount
            when d.is_visible = 1 then d.consume_amount - a.consume_amount
            end), 0)
        FROM tb_dsp_advertiser_consume_record d
        LEFT JOIN tb_advertiser_consume_record a on d.cur_date = a.cur_date and d.advertiser_id = a.account_id
        <where>
            <if test="advertiserIds != null and advertiserIds.size() > 0">
                and d.advertiser_id in
                <foreach collection="advertiserIds" separator="," item="advertiserId" close=")" open="(">
                    #{advertiserId}
                </foreach>
            </if>
        </where>
    </select>
</mapper>
