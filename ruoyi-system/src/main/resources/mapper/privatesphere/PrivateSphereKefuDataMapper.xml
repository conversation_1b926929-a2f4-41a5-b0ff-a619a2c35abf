<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.privatesphere.PrivateSphereKefuDataMapper">

    <resultMap type="com.ruoyi.system.entity.privatesphere.PrivateSphereKefuDataEntity" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="curDate" column="cur_date"/>
        <result property="kefuChannelId" column="kefu_channel_id"/>
        <result property="accountId" column="account_id"/>
        <result property="sspAccountId" column="ssp_account_id"/>
        <result property="productId" column="product_id"/>
        <result property="callCount" column="call_count"/>
        <result property="connectCount" column="connect_count"/>
        <result property="personCount" column="person_count"/>
        <result property="entryCount" column="entry_count"/>
        <result property="feedbackEntryCount" column="feedback_entry_count"/>
        <result property="dataCost" column="data_cost"/>
        <result property="dataCostCount" column="data_cost_count"/>
        <result property="lineCost" column="line_cost"/>
        <result property="personCost" column="person_cost"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
            cur_date,
            kefu_channel_id,
            account_id,
            ssp_account_id,
            product_id,
            call_count,
            connect_count,
            person_count,
            entry_count,
            feedback_entry_count,
            data_cost,
            data_cost_count,
            line_cost,
            person_cost,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.privatesphere.PrivateSphereKefuDataEntity">
        INSERT INTO tb_private_sphere_kefu_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="curDate != null">
                cur_date,
            </if>
            <if test="kefuChannelId != null">
                kefu_channel_id,
            </if>
            <if test="accountId != null">
                account_id,
            </if>
            <if test="sspAccountId != null">
                ssp_account_id,
            </if>
            <if test="productId != null">
                product_id,
            </if>
            <if test="callCount != null">
                call_count,
            </if>
            <if test="connectCount != null">
                connect_count,
            </if>
            <if test="personCount != null">
                person_count,
            </if>
            <if test="entryCount != null">
                entry_count,
            </if>
            <if test="feedbackEntryCount != null">
                feedback_entry_count,
            </if>
            <if test="dataCost != null">
                data_cost,
            </if>
            <if test="dataCostCount != null">
                data_cost_count,
            </if>
            <if test="lineCost != null">
                line_cost,
            </if>
            <if test="personCost != null">
                person_cost
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="curDate != null">
                #{curDate},
            </if>
            <if test="kefuChannelId != null">
                #{kefuChannelId},
            </if>
            <if test="accountId != null">
                #{accountId},
            </if>
            <if test="sspAccountId != null">
                #{sspAccountId},
            </if>
            <if test="productId != null">
                #{productId},
            </if>
            <if test="callCount != null">
                #{callCount},
            </if>
            <if test="connectCount != null">
                #{connectCount},
            </if>
            <if test="personCount != null">
                #{personCount},
            </if>
            <if test="entryCount != null">
                #{entryCount},
            </if>
            <if test="feedbackEntryCount != null">
                #{feedbackEntryCount},
            </if>
            <if test="dataCost != null">
                #{dataCost},
            </if>
            <if test="dataCostCount != null">
                #{dataCostCount},
            </if>
            <if test="lineCost != null">
                #{lineCost},
            </if>
            <if test="personCost != null">
                #{personCost}
            </if>
        </trim>
    </insert>

    <delete id="deleteById">
        DELETE
        FROM tb_private_sphere_kefu_data
        WHERE id = #{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.privatesphere.PrivateSphereKefuDataEntity">
        UPDATE tb_private_sphere_kefu_data
        <set>
            <if test="curDate != null">
                cur_date = #{curDate},
            </if>
            <if test="kefuChannelId != null">
                kefu_channel_id = #{kefuChannelId},
            </if>
            <if test="productId != null">
                product_id = #{productId},
            </if>
            <if test="callCount != null">
                call_count = #{callCount},
            </if>
            <if test="connectCount != null">
                connect_count = #{connectCount},
            </if>
            <if test="personCount != null">
                person_count = #{personCount},
            </if>
            <if test="entryCount != null">
                entry_count = #{entryCount},
            </if>
            <if test="feedbackEntryCount != null">
                feedback_entry_count = #{feedbackEntryCount},
            </if>
            <if test="dataCost != null">
                data_cost = #{dataCost},
            </if>
            <if test="dataCostCount != null">
                data_cost_count = #{dataCostCount},
            </if>
            <if test="lineCost != null">
                line_cost = #{lineCost},
            </if>
            <if test="personCost != null">
                person_cost = #{personCost},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_private_sphere_kefu_data
        WHERE id = #{id}
    </select>

    <select id="selectByProductAndKefuChannelId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_private_sphere_kefu_data
        WHERE cur_date = #{curDate} and kefu_channel_id = #{kefuChannelId} and product_id = #{productId}
    </select>

    <select id="selectListByParam" parameterType="com.ruoyi.system.bo.privatesphere.PrivateSphereKefuDataListBO" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        FROM tb_private_sphere_kefu_data
        <where>
            <if test="kefuChannelIds != null and kefuChannelIds.size > 0">
                kefu_channel_id in
                <foreach collection="kefuChannelIds" item="channelId" open="(" close=")" separator=",">
                    #{channelId}
                </foreach>
            </if>
            <if test="productIds != null and productIds.size > 0">
                and product_id in
                <foreach collection="productIds" item="productId" open="(" close=")" separator=",">
                    #{productId}
                </foreach>
            </if>
            <if test="startDate != null" >
                and cur_date &gt;= #{startDate}
            </if>
            <if test="endDate != null" >
                and cur_date &lt;= #{endDate}
            </if>
        </where>
        order by cur_date desc
    </select>

    <select id="statisticsByParam" parameterType="com.ruoyi.system.bo.privatesphere.PrivateSphereKefuDataListBO" resultType="com.ruoyi.system.entity.privatesphere.PrivateSphereKefuDataEntity">
        select sum(call_count) as callCount,sum(connect_count) as connectCount, sum(person_count) as personCount, sum(entry_count) as entryCount,sum(feedback_entry_count) as feedbackEntryCount,sum(data_cost * data_cost_count) as dataCost,sum(line_cost) as lineCost,sum(person_cost * entry_count) as personCost
        FROM tb_private_sphere_kefu_data
        <where>
            <if test="kefuChannelIds != null and kefuChannelIds.size > 0">
                kefu_channel_id in
                <foreach collection="kefuChannelIds" item="channelId" open="(" close=")" separator=",">
                    #{channelId}
                </foreach>
            </if>
            <if test="productIds != null and productIds.size > 0">
                and product_id in
                <foreach collection="productIds" item="productId" open="(" close=")" separator=",">
                    #{productId}
                </foreach>
            </if>
            <if test="startDate != null" >
                and cur_date &gt;= #{startDate}
            </if>
            <if test="endDate != null" >
                and cur_date &lt;= #{endDate}
            </if>
        </where>
    </select>
    <select id="selectIdsByParam" parameterType="com.ruoyi.system.bo.privatesphere.PrivateSphereKefuDataListBO" resultType="java.lang.Long">
        select id
        FROM tb_private_sphere_kefu_data
        <where>
            <if test="kefuChannelIds != null and kefuChannelIds.size > 0">
                kefu_channel_id in
                <foreach collection="kefuChannelIds" item="channelId" open="(" close=")" separator=",">
                    #{channelId}
                </foreach>
            </if>
            <if test="productIds != null and productIds.size > 0">
                and product_id in
                <foreach collection="productIds" item="productId" open="(" close=")" separator=",">
                    #{productId}
                </foreach>
            </if>
            <if test="startDate != null" >
                and cur_date &gt;= #{startDate}
            </if>
            <if test="endDate != null" >
                and cur_date &lt;= #{endDate}
            </if>
        </where>
    </select>

    <select id="countByKefuChannelId" resultType="java.lang.Integer">
        select count(*) from tb_private_sphere_kefu_data where kefu_channel_id = #{kefuChannelId}
    </select>

</mapper>