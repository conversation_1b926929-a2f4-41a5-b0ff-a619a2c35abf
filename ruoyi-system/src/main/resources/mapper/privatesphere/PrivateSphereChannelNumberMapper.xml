<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.privatesphere.PrivateSphereChannelNumberMapper">

    <resultMap type="com.ruoyi.system.entity.privatesphere.PrivateSphereChannelNumberEntity" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="channelId" column="channel_id"/>
        <result property="channelNumber" column="channel_number"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
            channel_id,
            channel_number,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.privatesphere.PrivateSphereChannelNumberEntity">
        INSERT INTO tb_private_sphere_channel_number
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="channelId != null">
                channel_id,
            </if>
            <if test="channelNumber != null">
                channel_number
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="channelId != null">
                #{channelId},
            </if>
            <if test="channelNumber != null">
                #{channelNumber}
            </if>
        </trim>
    </insert>

    <delete id="deleteById">
        DELETE
        FROM tb_private_sphere_channel_number
        WHERE id = #{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.privatesphere.PrivateSphereChannelNumberEntity">
        UPDATE tb_private_sphere_channel_number
        <set>
            <if test="channelId != null">
                channel_id = #{channelId},
            </if>
            <if test="channelNumber != null">
                channel_number = #{channelNumber},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_private_sphere_channel_number
        WHERE id = #{id}
    </select>

    <insert id="batchInsertOrUpdate">
        INSERT INTO tb_private_sphere_channel_number
        (channel_id,channel_number)
        values
        <foreach collection="entities" item="entity" separator=",">
            ( #{entity.channelId},#{entity.channelNumber})
        </foreach>
        ON DUPLICATE KEY UPDATE
        gmt_modified = now()
    </insert>

    <select id="selectByChannelIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_private_sphere_channel_number
        where channel_id in
        <foreach collection="channelIds" item="channelId" close=")" separator="," open="(">
            #{channelId}
        </foreach>
    </select>

</mapper>