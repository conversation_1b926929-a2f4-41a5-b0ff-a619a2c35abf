<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.privatesphere.PrivateSphereProductMapper">

    <resultMap type="com.ruoyi.system.entity.privatesphere.PrivateSphereProductEntity" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="accountId" column="account_id"/>
        <result property="productName" column="product_name"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
            account_id,
            product_name,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.privatesphere.PrivateSphereProductEntity">
        INSERT INTO tb_private_sphere_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountId != null">
                account_id,
            </if>
            <if test="productName != null">
                product_name
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountId != null">
                #{accountId},
            </if>
            <if test="productName != null">
                #{productName}
            </if>
        </trim>
    </insert>

    <delete id="deleteById">
        DELETE
        FROM tb_private_sphere_product
        WHERE id = #{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.privatesphere.PrivateSphereProductEntity">
        UPDATE tb_private_sphere_product
        <set>
            <if test="accountId != null">
                account_id = #{accountId},
            </if>
            <if test="productName != null">
                product_name = #{productName},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_private_sphere_product
        WHERE id = #{id}
    </select>

    <select id="selectByAccountIdAndName" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_private_sphere_product
        WHERE account_id = #{accountId} and product_name = #{productName}
    </select>

    <select id="selectListByIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_private_sphere_product
        WHERE id in
        <foreach collection="ids" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </select>

    <select id="selectListByAccountIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_private_sphere_product
        WHERE account_id in
        <foreach collection="accountIds" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </select>

    <select id="selectProductIdsByName" resultType="java.lang.Long">
        SELECT id
        FROM tb_private_sphere_product
        <where>
            <if test="productName != null and productName != ''">
                product_name like concat('%', #{productName}, '%')
            </if>
        </where>
    </select>

    <select id="selectProductList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_private_sphere_product
        order by gmt_create desc
    </select>

</mapper>