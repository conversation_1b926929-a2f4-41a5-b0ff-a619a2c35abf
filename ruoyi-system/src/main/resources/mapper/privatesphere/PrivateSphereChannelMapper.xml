<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.privatesphere.PrivateSphereChannelMapper">

    <resultMap type="com.ruoyi.system.entity.privatesphere.PrivateSphereChannelEntity" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="accountId" column="account_id"/>
        <result property="productId" column="product_id"/>
        <result property="channel" column="channel"/>
        <result property="operAccountId" column="oper_account_id"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
            account_id,
            product_id,
            channel,
            oper_account_id,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.privatesphere.PrivateSphereChannelEntity">
        INSERT INTO tb_private_sphere_channel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountId != null">
                account_id,
            </if>
            <if test="productId != null">
                product_id,
            </if>
            <if test="channel != null">
                channel,
            </if>
            <if test="operAccountId != null">
                oper_account_id
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountId != null">
                #{accountId},
            </if>
            <if test="productId != null">
                #{productId},
            </if>
            <if test="channel != null">
                #{channel},
            </if>
            <if test="operAccountId != null">
                #{operAccountId}
            </if>
        </trim>
    </insert>

    <delete id="deleteById">
        DELETE
        FROM tb_private_sphere_channel
        WHERE id = #{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.privatesphere.PrivateSphereChannelEntity">
        UPDATE tb_private_sphere_channel
        <set>
            <if test="accountId != null">
                account_id = #{accountId},
            </if>
            <if test="productId != null">
                product_id = #{productId},
            </if>
            <if test="channel != null">
                channel = #{channel},
            </if>
            <if test="operAccountId != null">
                oper_account_id = #{operAccountId},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_private_sphere_channel
        WHERE id = #{id}
    </select>

    <select id="selectByProductAndChannelName" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_private_sphere_channel
        WHERE product_id = #{productId} and channel = #{channel}
    </select>

    <select id="selectListByParam" parameterType="com.ruoyi.system.bo.privatesphere.PrivateSphereChannelListBO" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_private_sphere_channel
        <where>
            <if test="accountIds != null and accountIds.size > 0">
                account_id in
                <foreach collection="accountIds" open="(" separator="," close=")" item="accountId">
                    #{accountId}
                </foreach>
            </if>
            <if test="channel != null and channel != ''">
                and channel like concat('%', #{channel}, '%')
            </if>
            <if test="startDate != null">
                and gmt_create &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                and gmt_create &lt;= #{endDate}
            </if>
            <if test="productIds != null and productIds.size > 0">
                and product_id in
                <foreach collection="productIds" open="(" separator="," close=")" item="productId">
                    #{productId}
                </foreach>
            </if>
        </where>
        order by id desc
    </select>

</mapper>
