<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.privatesphere.PrivateSphereDataMapper">

    <resultMap type="com.ruoyi.system.entity.privatesphere.PrivateSphereDataEntity" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="curDate" column="cur_date"/>
        <result property="accountId" column="account_id"/>
        <result property="productId" column="product_id"/>
        <result property="callCount" column="call_count"/>
        <result property="connectCount" column="connect_count"/>
        <result property="personCount" column="person_count"/>
        <result property="entryCount" column="entry_count"/>
        <result property="dataCost" column="data_cost"/>
        <result property="dataCostCount" column="data_cost_count"/>
        <result property="lineCost" column="line_cost"/>
        <result property="personCost" column="person_cost"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
            cur_date,
            account_id,
            product_id,
            call_count,
            connect_count,
            person_count,
            entry_count,
            data_cost,
            data_cost_count,
            line_cost,
            person_cost,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.privatesphere.PrivateSphereDataEntity">
        INSERT INTO tb_private_sphere_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="curDate != null">
                cur_date,
            </if>
            <if test="accountId != null">
                account_id,
            </if>
            <if test="productId != null">
                product_id,
            </if>
            <if test="callCount != null">
                call_count,
            </if>
            <if test="connectCount != null">
                connect_count,
            </if>
            <if test="personCount != null">
                person_count,
            </if>
            <if test="entryCount != null">
                entry_count,
            </if>
            <if test="dataCost != null">
                data_cost,
            </if>
            <if test="dataCostCount != null">
                data_cost_count,
            </if>
            <if test="lineCost != null">
                line_cost,
            </if>
            <if test="personCost != null">
                person_cost
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="curDate != null">
                #{curDate},
            </if>
            <if test="accountId != null">
                #{accountId},
            </if>
            <if test="productId != null">
                #{productId},
            </if>
            <if test="callCount != null">
                #{callCount},
            </if>
            <if test="connectCount != null">
                #{connectCount},
            </if>
            <if test="personCount != null">
                #{personCount},
            </if>
            <if test="entryCount != null">
                #{entryCount},
            </if>
            <if test="dataCost != null">
                #{dataCost},
            </if>
            <if test="dataCostCount != null">
                #{dataCostCount},
            </if>
            <if test="lineCost != null">
                #{lineCost},
            </if>
            <if test="personCost != null">
                #{personCost}
            </if>
        </trim>
    </insert>

    <delete id="deleteById">
        DELETE
        FROM tb_private_sphere_data
        WHERE id = #{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.privatesphere.PrivateSphereDataEntity">
        UPDATE tb_private_sphere_data
        <set>
            <if test="curDate != null">
                cur_date = #{curDate},
            </if>
            <if test="accountId != null">
                account_id = #{accountId},
            </if>
            <if test="productId != null">
                product_id = #{productId},
            </if>
            <if test="callCount != null">
                call_count = #{callCount},
            </if>
            <if test="connectCount != null">
                connect_count = #{connectCount},
            </if>
            <if test="personCount != null">
                person_count = #{personCount},
            </if>
            <if test="entryCount != null">
                entry_count = #{entryCount},
            </if>
            <if test="dataCost != null">
                data_cost = #{dataCost},
            </if>
            <if test="dataCostCount != null">
                data_cost_count = #{dataCostCount},
            </if>
            <if test="lineCost != null">
                line_cost = #{lineCost},
            </if>
            <if test="personCost != null">
                person_cost = #{personCost},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_private_sphere_data
        WHERE id = #{id}
    </select>

    <select id="selectByDateAndProduct" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_private_sphere_data
        WHERE cur_date = #{curDate} and account_id = #{accountId} and product_id = #{productId}
    </select>

    <select id="selectListByParam" parameterType="com.ruoyi.system.bo.privatesphere.PrivateSphereDataListBO" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        FROM tb_private_sphere_data
        <where>
            <if test="productIds != null and productIds.size > 0">
                product_id in
                <foreach collection="productIds" item="productId" open="(" close=")" separator=",">
                    #{productId}
                </foreach>
            </if>
            <if test="accountIds != null and accountIds.size > 0">
                and account_id in
                <foreach collection="accountIds" item="accountId" open="(" close=")" separator=",">
                    #{accountId}
                </foreach>
            </if>

            <if test="startDate != null" >
                and cur_date &gt;= #{startDate}
            </if>
            <if test="endDate != null" >
                and cur_date &lt;= #{endDate}
            </if>
        </where>
        order by cur_date desc
    </select>

    <select id="statisticsByParam" parameterType="com.ruoyi.system.bo.privatesphere.PrivateSphereDataListBO" resultType="com.ruoyi.system.entity.privatesphere.PrivateSphereDataEntity">
        select sum(call_count) as callCount,sum(connect_count) as connectCount, sum(person_count) as personCount, sum(entry_count) as entryCount,sum(data_cost * data_cost_count) as dataCost,sum(line_cost) as lineCost,sum(person_cost * entry_count) as personCost
        FROM tb_private_sphere_data
        <where>
            <if test="accountIds != null and accountIds.size > 0">
                account_id in
                <foreach collection="accountIds" item="accountId" open="(" close=")" separator=",">
                    #{accountId}
                </foreach>
            </if>
            <if test="productIds != null and productIds.size > 0">
                and product_id in
                <foreach collection="productIds" item="productId" open="(" close=")" separator=",">
                    #{productId}
                </foreach>
            </if>
            <if test="startDate != null" >
                and cur_date &gt;= #{startDate}
            </if>
            <if test="endDate != null" >
                and cur_date &lt;= #{endDate}
            </if>
        </where>
    </select>
    <select id="selectIdsByParam" parameterType="com.ruoyi.system.bo.privatesphere.PrivateSphereDataListBO" resultType="java.lang.Long">
        select id
        FROM tb_private_sphere_data
        <where>
            <if test="accountIds != null and accountIds.size > 0">
                account_id in
                <foreach collection="accountIds" item="accountId" open="(" close=")" separator=",">
                    #{accountId}
                </foreach>
            </if>
            <if test="productIds != null and productIds.size > 0">
                and product_id in
                <foreach collection="productIds" item="productId" open="(" close=")" separator=",">
                    #{productId}
                </foreach>
            </if>
            <if test="startDate != null" >
                and cur_date &gt;= #{startDate}
            </if>
            <if test="endDate != null" >
                and cur_date &lt;= #{endDate}
            </if>
        </where>
    </select>

    <select id="countByProductId" resultType="java.lang.Integer">
        select count(*) from tb_private_sphere_data where product_id = #{productId}
    </select>

</mapper>