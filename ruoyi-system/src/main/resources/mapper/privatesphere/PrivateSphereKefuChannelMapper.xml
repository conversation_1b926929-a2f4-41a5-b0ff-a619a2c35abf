<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.privatesphere.PrivateSphereKefuChannelMapper">

    <resultMap type="com.ruoyi.system.entity.privatesphere.PrivateSphereKefuChannelEntity" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="accountId" column="account_id"/>
        <result property="productId" column="product_id"/>
        <result property="sspAccountId" column="ssp_account_id"/>
        <result property="channel" column="channel"/>
        <result property="operAccountId" column="oper_account_id"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
            account_id,
            product_id,
            ssp_account_id,
            channel,
            oper_account_id,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.privatesphere.PrivateSphereKefuChannelEntity">
        INSERT INTO tb_private_sphere_kefu_channel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountId != null">
                account_id,
            </if>
            <if test="productId != null">
                product_id,
            </if>
            <if test="sspAccountId != null">
                ssp_account_id,
            </if>
            <if test="channel != null">
                channel,
            </if>
            <if test="operAccountId != null">
                oper_account_id
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountId != null">
                #{accountId},
            </if>
            <if test="productId != null">
                #{productId},
            </if>
            <if test="sspAccountId != null">
                #{sspAccountId},
            </if>
            <if test="channel != null">
                #{channel},
            </if>
            <if test="operAccountId != null">
                #{operAccountId}
            </if>
        </trim>
    </insert>

    <delete id="deleteById">
        DELETE
        FROM tb_private_sphere_kefu_channel
        WHERE id = #{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.privatesphere.PrivateSphereKefuChannelEntity">
        UPDATE tb_private_sphere_kefu_channel
        <set>
            <if test="accountId != null">
                account_id = #{accountId},
            </if>
            <if test="productId != null">
                product_id = #{productId},
            </if>
            <if test="sspAccountId != null">
                ssp_account_id = #{sspAccountId},
            </if>
            <if test="channel != null">
                channel = #{channel},
            </if>
            <if test="operAccountId != null">
                oper_account_id = #{operAccountId},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_private_sphere_kefu_channel
        WHERE id = #{id}
    </select>

    <select id="selectByChannel" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_private_sphere_kefu_channel
        where channel = #{channel}
    </select>

    <select id="selectAllList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_private_sphere_kefu_channel
        order by gmt_create desc
    </select>

    <select id="selectIdListByChannel" resultType="java.lang.Long">
        SELECT id
        FROM tb_private_sphere_kefu_channel
        <where>
            <if test="channel != null and channel != ''">
                channel like concat('%', #{channel}, '%')
            </if>
        </where>
    </select>

    <select id="selectListByIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_private_sphere_kefu_channel
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

</mapper>