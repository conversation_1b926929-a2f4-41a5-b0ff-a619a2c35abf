<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.privatesphere.PrivateSphereKefuChannelDataMapper">

    <resultMap type="com.ruoyi.system.entity.privatesphere.PrivateSphereKefuChannelDataEntity" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="kefuDataId" column="kefu_data_id"/>
        <result property="channelId" column="channel_id"/>
        <result property="entryGroupCount" column="entry_group_count"/>
        <result property="refundAmount" column="refund_amount"/>
        <result property="converAmount" column="conver_amount"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
            kefu_data_id,
            channel_id,
            entry_group_count,
            refund_amount,
            conver_amount,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.privatesphere.PrivateSphereKefuChannelDataEntity">
        INSERT INTO tb_private_sphere_kefu_channel_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="kefuDataId != null">
                kefu_data_id,
            </if>
            <if test="channelId != null">
                channel_id,
            </if>
            <if test="entryGroupCount != null">
                entry_group_count,
            </if>
            <if test="refundAmount != null">
                refund_amount,
            </if>
            <if test="converAmount != null">
                conver_amount
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="kefuDataId != null">
                #{kefuDataId},
            </if>
            <if test="channelId != null">
                #{channelId},
            </if>
            <if test="entryGroupCount != null">
                #{entryGroupCount},
            </if>
            <if test="refundAmount != null">
                #{refundAmount},
            </if>
            <if test="converAmount != null">
                #{converAmount}
            </if>
        </trim>
    </insert>

    <delete id="deleteById">
        DELETE
        FROM tb_private_sphere_kefu_channel_data
        WHERE id = #{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.privatesphere.PrivateSphereKefuChannelDataEntity">
        UPDATE tb_private_sphere_kefu_channel_data
        <set>
            <if test="kefuDataId != null">
                kefu_data_id = #{kefuDataId},
            </if>
            <if test="channelId != null">
                channel_id = #{channelId},
            </if>
            <if test="entryGroupCount != null">
                entry_group_count = #{entryGroupCount},
            </if>
            <if test="refundAmount != null">
                refund_amount = #{refundAmount},
            </if>
            <if test="converAmount != null">
                conver_amount = #{converAmount},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_private_sphere_kefu_channel_data
        WHERE id = #{id}
    </select>

    <insert id="batchInsertOrUpdate">
        insert into
        tb_private_sphere_kefu_channel_data(kefu_data_id,channel_id,entry_group_count,refund_amount,conver_amount)
        values
        <foreach collection="entities" item="entity" separator=",">
            (
            #{entity.kefuDataId},#{entity.channelId},#{entity.entryGroupCount},#{entity.refundAmount},#{entity.converAmount})
        </foreach>
        ON DUPLICATE KEY UPDATE
        entry_group_count = values(entry_group_count) ,
        refund_amount = values(refund_amount),
        conver_amount = values(conver_amount)
    </insert>

    <select id="selectListByDataIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_private_sphere_kefu_channel_data
        where kefu_data_id in
        <foreach collection="dataIds" open="(" separator="," close=")" item="dataId">
            #{dataId}
        </foreach>
    </select>

    <select id="statisticsByDataIds" resultType="com.ruoyi.system.entity.privatesphere.PrivateSphereKefuChannelDataEntity">
        select channel_id as channelId, sum(entry_group_count) as entryGroupCount,sum(refund_amount) as refundAmount,sum(conver_amount) as converAmount
        from tb_private_sphere_kefu_channel_data
        where kefu_data_id in
        <foreach collection="dataIds" open="(" separator="," close=")" item="dataId">
            #{dataId}
        </foreach>
        group by channel_id
    </select>

</mapper>