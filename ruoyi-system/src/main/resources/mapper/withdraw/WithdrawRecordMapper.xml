<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.withdraw.WithdrawRecordMapper">

    <resultMap type="com.ruoyi.system.entity.withdraw.WithdrawRecordEntity" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="accountId" column="account_id"/>
        <result property="withdrawAmount" column="withdraw_amount"/>
        <result property="withdrawStatus" column="withdraw_status"/>
        <result property="withdrawFile" column="withdraw_file"/>
        <result property="extraInfo" column="extra_info"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        account_id,
        withdraw_amount,
        withdraw_status,
        withdraw_file,
        extra_info,
        gmt_create,
        gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.withdraw.WithdrawRecordEntity">
        INSERT INTO tb_withdraw_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountId != null">
                account_id,
            </if>
            <if test="withdrawAmount != null">
                withdraw_amount,
            </if>
            <if test="withdrawStatus != null">
                withdraw_status,
            </if>
            <if test="extraInfo != null">
                extra_info
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountId != null">
                #{accountId},
            </if>
            <if test="withdrawAmount != null">
                #{withdrawAmount},
            </if>
            <if test="withdrawStatus != null">
                #{withdrawStatus},
            </if>
            <if test="extraInfo != null">
                #{extraInfo}
            </if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.ruoyi.system.entity.withdraw.WithdrawRecordEntity">
        UPDATE tb_withdraw_record
        <set>
            <if test="accountId != null">
                account_id = #{accountId},
            </if>
            <if test="withdrawAmount != null">
                withdraw_amount = #{withdrawAmount},
            </if>
            <if test="withdrawStatus != null">
                withdraw_status = #{withdrawStatus},
            </if>
            <if test="withdrawFile != null">
                withdraw_file = #{withdrawFile},
            </if>
            <if test="extraInfo != null">
                extra_info = #{extraInfo},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tb_withdraw_record
        WHERE id = #{id}
    </select>
    <select id="selectListByCondition" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_withdraw_record
        <where>
            <if test="accountIds != null and accountIds.size > 0">
                account_id in
                <foreach collection="accountIds" open="(" separator="," close=")" item="accountId">
                    #{accountId}
                </foreach>
            </if>
            <if test="startDate != null">
                and gmt_create &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                and gmt_create &lt;= #{endDate}
            </if>
            <if test="withdrawStatus != null">
                and withdraw_status = #{withdrawStatus}
            </if>
        </where>
        order by gmt_create desc
    </select>
    <update id="updateWithdrawStatus">
        update tb_withdraw_record
        set withdraw_status = #{withdrawStatus}
        where id = #{id}
    </update>
    <update id="updateWithdrawFile">
        update tb_withdraw_record
        set withdraw_file = #{withdrawFile}
        where id = #{id}
    </update>

    <select id="sumWithdrawAmountByAccountId" resultMap="BaseResultMap">
        select account_id, sum(withdraw_amount) as withdraw_amount
        from tb_withdraw_record
        <where>
            withdraw_status in (1, 2)
            <if test="accountIds != null and accountIds.size > 0">
                and account_id in
                <foreach collection="accountIds" open="(" separator="," close=")" item="accountId">
                    #{accountId}
                </foreach>
            </if>
        </where>
        group by account_id
    </select>
</mapper>
