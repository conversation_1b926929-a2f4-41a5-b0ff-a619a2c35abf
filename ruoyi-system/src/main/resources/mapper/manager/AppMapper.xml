<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.manager.AppMapper">

    <resultMap type="com.ruoyi.system.entity.app.App" id="AppResult">
        <result property="id"    column="id"    />
        <result property="accountId"    column="account_id"    />
        <result property="appName"    column="app_name"    />
        <result property="appType"    column="app_type"    />
        <result property="appKey"    column="app_key"    />
        <result property="appSecret"    column="app_secret"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectAppVo">
        select id, account_id, app_name, app_type, app_key, app_secret, gmt_create, gmt_modified from tb_app
    </sql>

    <select id="selectAppList" parameterType="com.ruoyi.system.entity.app.App" resultMap="AppResult">
        <include refid="selectAppVo"/>
        <where>
            <if test="id != null "> and id = #{id}</if>
            <if test="accountId != null "> and account_id = #{accountId}</if>
            <if test="appName != null  and appName != ''"> and app_name like concat('%', #{appName}, '%')</if>
            <if test="appType != null "> and app_type = #{appType}</if>
            <if test="appKey != null  and appKey != ''"> and app_key = #{appKey}</if>
            <if test="appSecret != null  and appSecret != ''"> and app_secret = #{appSecret}</if>
            <if test="searchValue != null and searchValue != ''"> and (app_name like concat('%', #{searchValue}, '%') or id = #{searchValue})</if>
            <if test="startDate != null "> and gmt_create &gt;= #{startDate}</if>
            <if test="endDate != null "> and gmt_create &lt;= #{endDate}</if>
            <if test="appIds != null and appIds.size() > 0">
                and id in
                <foreach collection="appIds" item="appId" open="(" separator="," close=")">
                    #{appId}
                </foreach>
            </if>
            <if test="accountIds != null and accountIds.size() > 0">
                and account_id in
                <foreach collection="accountIds" item="accountId" open="(" separator="," close=")">
                    #{accountId}
                </foreach>
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectAppIdList" parameterType="com.ruoyi.system.entity.app.App" resultType="Long">
        select id from tb_app
        <where>
            <if test="accountId != null "> and account_id = #{accountId}</if>
            <if test="searchValue != null and searchValue != ''"> and (app_name like concat('%', #{searchValue}, '%') or id = #{searchValue})</if>
            <if test="appName != null  and appName != ''"> and app_name like concat('%', #{appName}, '%')</if>
        </where>
    </select>

    <select id="selectAccountIdList" parameterType="com.ruoyi.system.entity.app.App" resultType="Long">
        select distinct account_id from tb_app
        <where>
            <if test="accountId != null "> and account_id = #{accountId}</if>
            <if test="searchValue != null and searchValue != ''"> and (app_name like concat('%', #{searchValue}, '%') or id = #{searchValue})</if>
            <if test="appName != null  and appName != ''"> and app_name like concat('%', #{appName}, '%')</if>
        </where>
    </select>

    <select id="selectSimpleAppList" parameterType="com.ruoyi.system.entity.app.App" resultMap="AppResult">
        select id, app_name from tb_app
        <where>
            <if test="accountId != null "> and account_id = #{accountId}</if>
        </where>
    </select>

    <select id="selectAppById" parameterType="Long" resultMap="AppResult">
        <include refid="selectAppVo"/>
        where id = #{id}
    </select>

    <select id="selectAppByAppKey" parameterType="String" resultMap="AppResult">
        <include refid="selectAppVo"/>
        where app_key = #{appKey}
    </select>

    <insert id="insertApp" parameterType="com.ruoyi.system.entity.app.App" useGeneratedKeys="true" keyProperty="id">
        insert into tb_app
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountId != null">account_id,</if>
            <if test="appName != null and appName != ''">app_name,</if>
            <if test="appType != null">app_type,</if>
            <if test="appKey != null and appKey != ''">app_key,</if>
            <if test="appSecret != null and appSecret != ''">app_secret,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountId != null">#{accountId},</if>
            <if test="appName != null and appName != ''">#{appName},</if>
            <if test="appType != null">#{appType},</if>
            <if test="appKey != null and appKey != ''">#{appKey},</if>
            <if test="appSecret != null and appSecret != ''">#{appSecret},</if>
         </trim>
    </insert>

    <update id="updateApp" parameterType="com.ruoyi.system.entity.app.App">
        update tb_app
        <trim prefix="SET" suffixOverrides=",">
            <if test="appName != null and appName != ''">app_name = #{appName},</if>
            <if test="appKey != null and appKey != ''">app_key = #{appKey},</if>
            <if test="appSecret != null and appSecret != ''">app_secret = #{appSecret},</if>
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>

    <select id="groupByAccountId" parameterType="List" resultType="Map">
        select account_id as accountId, COUNT(*) as count
        from tb_app
        <where>
            account_id in
            <foreach collection="accountIds" item="accountId" open="(" separator="," close=")">
                #{accountId}
            </foreach>
        </where>
        GROUP BY account_id
    </select>

    <select id="selectAppIdByAccountIds" resultType="Long">
        select id from tb_app
        <where>
            account_id in
            <foreach collection="accountIds" item="accountId" open="(" separator="," close=")">
                #{accountId}
            </foreach>
        </where>
    </select>

    <select id="selectSimpleInfoByIds" parameterType="List" resultMap="AppResult">
        select id, app_name ,account_id from tb_app
        <where>
            and id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </select>

    <select id="selectAppIdAndName" resultMap="AppResult">
        select id, app_name from tb_app
    </select>
</mapper>
