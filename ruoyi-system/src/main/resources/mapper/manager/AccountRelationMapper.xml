<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.manager.AccountRelationMapper">

    <resultMap type="com.ruoyi.system.entity.account.AccountRelation" id="AccountRelationResult">
        <result property="id"    column="id"    />
        <result property="srcAccount"    column="src_account"    />
        <result property="destAccount"    column="dest_account"    />
        <result property="relationType"    column="relation_type"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectAccountRelationVo">
        select id, src_account, dest_account, relation_type, gmt_create, gmt_modified from tb_account_relation
    </sql>

    <select id="selectBySrcAccountIds" parameterType="com.ruoyi.system.entity.account.AccountRelation" resultMap="AccountRelationResult">
        <include refid="selectAccountRelationVo"/>
        <where>
            relation_type in
            <foreach collection="relationTypes" item="relationType" open="(" separator="," close=")">
                #{relationType}
            </foreach>
            and src_account in
            <foreach collection="accountIds" item="accountId" open="(" separator="," close=")">
                #{accountId}
            </foreach>
        </where>
    </select>

    <select id="selectByDestAccountIds" parameterType="com.ruoyi.system.entity.account.AccountRelation" resultMap="AccountRelationResult">
        <include refid="selectAccountRelationVo"/>
        <where>
            relation_type in
            <foreach collection="relationTypes" item="relationType" open="(" separator="," close=")">
                #{relationType}
            </foreach>
            and dest_account in
            <foreach collection="accountIds" item="accountId" open="(" separator="," close=")">
                #{accountId}
            </foreach>
        </where>
        order by relation_type
    </select>

    <select id="selectListByDestAccountAndRelationType" resultMap="AccountRelationResult">
        <include refid="selectAccountRelationVo"/>
        <where>
            <if test="destAccountIds != null and destAccountIds.size() > 0">
                dest_account in
                <foreach collection="destAccountIds" item="destAccountId" open="(" separator="," close=")">
                    #{destAccountId}
                </foreach>
            </if>
            <if test="relationType != null"> and relation_type = #{relationType}</if>
        </where>
    </select>

    <insert id="insertAccountRelation" parameterType="com.ruoyi.system.entity.account.AccountRelation" useGeneratedKeys="true" keyProperty="id">
        insert into tb_account_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="srcAccount != null">src_account,</if>
            <if test="destAccount != null">dest_account,</if>
            <if test="relationType != null">relation_type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="srcAccount != null">#{srcAccount},</if>
            <if test="destAccount != null">#{destAccount},</if>
            <if test="relationType != null">#{relationType},</if>
        </trim>
    </insert>

    <delete id="deleteAccountRelation">
        delete from tb_account_relation where dest_account = #{destAccountId} and relation_type = #{relationType}
    </delete>
</mapper>
