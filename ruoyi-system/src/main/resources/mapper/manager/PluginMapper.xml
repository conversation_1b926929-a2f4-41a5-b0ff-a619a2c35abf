<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.manager.PluginMapper">

    <resultMap type="com.ruoyi.system.entity.plugin.Plugin" id="PluginResult">
        <result property="id"    column="id"    />
        <result property="pluginType"    column="plugin_type"    />
        <result property="pluginInfo"    column="plugin_info"    />
        <result property="isDeleted"    column="is_deleted"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectPluginVo">
        select id, plugin_type, plugin_info, is_deleted, gmt_create, gmt_modified from tb_plugin
    </sql>

    <select id="selectById" parameterType="Long" resultMap="PluginResult">
        <include refid="selectPluginVo"/>
        where id = #{id}
    </select>

    <select id="selectList" parameterType="com.ruoyi.system.entity.plugin.Plugin" resultMap="PluginResult">
        <include refid="selectPluginVo"/>
        <where>
            is_deleted = 0
            <if test="pluginType != null"> and plugin_type = #{pluginType}</if>
        </where>
    </select>

    <insert id="insertPlugin" parameterType="com.ruoyi.system.entity.plugin.Plugin" useGeneratedKeys="true" keyProperty="id">
        insert into tb_plugin
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="pluginType != null">plugin_type,</if>
            <if test="pluginInfo != null">plugin_info,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="pluginType != null">#{pluginType},</if>
            <if test="pluginInfo != null">#{pluginInfo},</if>
         </trim>
    </insert>

    <update id="updatePlugin" parameterType="com.ruoyi.system.entity.plugin.Plugin">
        update tb_plugin
        <trim prefix="SET" suffixOverrides=",">
            <if test="pluginType != null">plugin_type = #{pluginType},</if>
            <if test="pluginInfo != null">plugin_info = #{pluginInfo},</if>
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>
</mapper>
