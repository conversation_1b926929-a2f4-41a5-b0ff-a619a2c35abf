<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.manager.ActivitySkinMapper">

    <resultMap type="com.ruoyi.system.entity.activity.ActivitySkin" id="ActivitySkinResult">
        <result property="id"    column="id"    />
        <result property="skinCode"    column="skin_code"    />
        <result property="skinName"    column="skin_name"    />
        <result property="skinType"    column="skin_type"    />
        <result property="activityType"    column="activity_type"    />
        <result property="thumbnailImage"    column="thumbnail_image"    />
        <result property="jsTemplate"    column="js_template"    />
        <result property="skinConfig"    column="skin_config"    />
        <result property="globalConfig"    column="global_config"    />
        <result property="redirectPath"    column="redirect_path"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectActivitySkinVo">
        select id, skin_code, skin_name, skin_type, activity_type, thumbnail_image, js_template, skin_config,global_config,
               redirect_path, gmt_create, gmt_modified from tb_activity_skin
    </sql>

    <select id="selectActivitySkinList" parameterType="com.ruoyi.system.entity.activity.ActivitySkin" resultMap="ActivitySkinResult">
        <include refid="selectActivitySkinVo"/>
        <where>
            skin_name not like '%已废弃%'
            <if test="skinCode != null  and skinCode != ''"> and skin_code = #{skinCode}</if>
            <if test="skinName != null  and skinName != ''"> and skin_name like concat('%', #{skinName}, '%')</if>
            <if test="skinType != null"> and skin_type = #{skinType}</if>
            <if test="activityType != null"> and activity_type = #{activityType}</if>
        </where>
        order by id desc
    </select>

    <select id="selectSimpleActivitySkinList" parameterType="com.ruoyi.system.entity.activity.ActivitySkin" resultMap="ActivitySkinResult">
        select skin_code, skin_name, redirect_path
        from tb_activity_skin
        <where>
            <if test="skinCode != null"> and skin_code = #{skinCode}</if>
            <if test="skinName != null"> and skin_name like concat('%', #{skinName}, '%')</if>
            <if test="skinType != null"> and skin_type = #{skinType}</if>
            <if test="activityType != null"> and activity_type = #{activityType}</if>
        </where>
    </select>

    <select id="selectActivitySkinById" parameterType="Long" resultMap="ActivitySkinResult">
        <include refid="selectActivitySkinVo"/>
        where id = #{id}
    </select>

    <select id="selectBySkinCode" parameterType="String" resultMap="ActivitySkinResult">
        <include refid="selectActivitySkinVo"/>
        where skin_code = #{skinCode}
    </select>

    <select id="selectSkinNameBySkinCode" parameterType="String" resultType="String">
        select skin_name from tb_activity_skin
        where skin_code = #{skinCode}
    </select>

    <insert id="insertActivitySkin" parameterType="com.ruoyi.system.entity.activity.ActivitySkin" useGeneratedKeys="true" keyProperty="id">
        insert into tb_activity_skin
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="skinCode != null and skinCode != ''">skin_code,</if>
            <if test="skinName != null and skinName != ''">skin_name,</if>
            <if test="skinType != null">skin_type,</if>
            <if test="activityType != null">activity_type,</if>
            <if test="thumbnailImage != null and thumbnailImage != ''">thumbnail_image,</if>
            <if test="jsTemplate != null and jsTemplate != ''">js_template,</if>
            <if test="skinConfig != null and skinConfig != ''">skin_config,</if>
            <if test="globalConfig != null and globalConfig != ''">global_config,</if>
            <if test="redirectPath != null and redirectPath != ''">redirect_path,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="skinCode != null and skinCode != ''">#{skinCode},</if>
            <if test="skinName != null and skinName != ''">#{skinName},</if>
            <if test="skinType != null">#{skinType},</if>
            <if test="activityType != null">#{activityType},</if>
            <if test="thumbnailImage != null and thumbnailImage != ''">#{thumbnailImage},</if>
            <if test="jsTemplate != null and jsTemplate != ''">#{jsTemplate},</if>
            <if test="skinConfig != null and skinConfig != ''">#{skinConfig},</if>
            <if test="globalConfig != null and globalConfig != ''">#{globalConfig},</if>
            <if test="redirectPath != null and redirectPath != ''">#{redirectPath},</if>
         </trim>
    </insert>

    <update id="updateActivitySkin" parameterType="com.ruoyi.system.entity.activity.ActivitySkin">
        update tb_activity_skin
        <trim prefix="SET" suffixOverrides=",">
            <if test="skinCode != null and skinCode != ''">skin_code = #{skinCode},</if>
            <if test="skinName != null and skinName != ''">skin_name = #{skinName},</if>
            <if test="skinType != null">skin_type = #{skinType},</if>
            <if test="activityType != null">activity_type = #{activityType},</if>
            <if test="thumbnailImage != null and thumbnailImage != ''">thumbnail_image = #{thumbnailImage},</if>
            <if test="jsTemplate != null and jsTemplate != ''">js_template = #{jsTemplate},</if>
            <if test="skinConfig != null and skinConfig != ''">skin_config = #{skinConfig},</if>
            <if test="globalConfig != null and globalConfig != ''">global_config = #{globalConfig},</if>
            <if test="redirectPath != null and redirectPath != ''">redirect_path = #{redirectPath},</if>
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>
</mapper>
