<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.manager.SlotConfigMapper">

    <resultMap type="com.ruoyi.system.entity.slot.SlotConfig" id="SlotConfigResult">
        <result property="id"    column="id"    />
        <result property="slotId"    column="slot_id"    />
        <result property="redirectType"    column="redirect_type"    />
        <result property="redirectValue"    column="redirect_value"    />
        <result property="domainConfig"    column="domain_config"    />
        <result property="retConfig"    column="ret_config"    />
        <result property="channel"    column="channel"    />
        <result property="switchConfig"    column="switch_config"    />
        <result property="degradedOrientIds"    column="degraded_orient_ids"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectSlotConfigVo">
        select id, slot_id, redirect_type, redirect_value,
               domain_config, ret_config, channel, switch_config,
               degraded_orient_ids, gmt_create, gmt_modified
        from tb_slot_config
    </sql>

    <select id="selectSlotConfigList" parameterType="com.ruoyi.system.entity.slot.SlotConfig" resultMap="SlotConfigResult">
        <include refid="selectSlotConfigVo"/>
        <where>
            <if test="slotId != null "> and slot_id = #{slotId}</if>
            <if test="redirectType != null  and redirectType != ''"> and redirect_type = #{redirectType}</if>
            <if test="redirectValue != null  and redirectValue != ''"> and redirect_value = #{redirectValue}</if>
            <if test="domainConfig != null "> and domain_config like concat('%', #{domainConfig}, '%')</if>
            <if test="switchConfig != null "> and switch_config like concat('%', #{switchConfig}, '%')</if>
            <if test="channel != null "> and channel = #{channel}</if>
        </where>
    </select>

    <select id="selectBySlotId" parameterType="Long" resultMap="SlotConfigResult">
        <include refid="selectSlotConfigVo"/>
        where slot_id = #{slotId}
    </select>

    <select id="selectSlotConfigById" parameterType="String" resultMap="SlotConfigResult">
        <include refid="selectSlotConfigVo"/>
        where id = #{id}
    </select>

    <select id="selectListBySlotIds" parameterType="List" resultMap="SlotConfigResult">
        <include refid="selectSlotConfigVo"/>
        where slot_id in
        <foreach collection="slotIds" item="slotId" open="(" separator="," close=")">
            #{slotId}
        </foreach>
    </select>

    <insert id="insertSlotConfig" parameterType="com.ruoyi.system.entity.slot.SlotConfig" useGeneratedKeys="true" keyProperty="id">
        insert into tb_slot_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="slotId != null">slot_id,</if>
            <if test="redirectType != null">redirect_type,</if>
            <if test="redirectValue != null">redirect_value,</if>
            <if test="domainConfig != null">domain_config,</if>
            <if test="retConfig != null">ret_config,</if>
            <if test="channel != null">channel,</if>
            <if test="switchConfig != null">switch_config ,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="slotId != null">#{slotId},</if>
            <if test="redirectType != null">#{redirectType},</if>
            <if test="redirectValue != null">#{redirectValue},</if>
            <if test="domainConfig != null">#{domainConfig},</if>
            <if test="retConfig != null">#{retConfig},</if>
            <if test="channel != null">#{channel},</if>
            <if test="switchConfig != null">#{switchConfig},</if>
        </trim>
    </insert>

    <update id="updateSlotConfig" parameterType="com.ruoyi.system.entity.slot.SlotConfig">
        update tb_slot_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="redirectType != null">redirect_type = #{redirectType},</if>
            <if test="redirectValue != null">redirect_value = #{redirectValue},</if>
            <if test="domainConfig != null">domain_config = #{domainConfig},</if>
            <if test="retConfig != null">ret_config = #{retConfig},</if>
            <if test="channel != null">channel = #{channel},</if>
            <if test="switchConfig != null">switch_config = #{switchConfig},</if>
            <if test="degradedOrientIds != null">degraded_orient_ids = #{degradedOrientIds},</if>
            gmt_modified = now()
        </trim>
        where slot_id = #{slotId}
    </update>

    <update id="updateById" parameterType="com.ruoyi.system.entity.slot.SlotConfig">
        update tb_slot_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="redirectType != null">redirect_type = #{redirectType},</if>
            <if test="redirectValue != null">redirect_value = #{redirectValue},</if>
            <if test="domainConfig != null">domain_config = #{domainConfig},</if>
            <if test="retConfig != null">ret_config = #{retConfig},</if>
            <if test="channel != null">`channel` = #{channel},</if>
            <if test="switchConfig != null">switch_config = #{switchConfig},</if>
            <if test="degradedOrientIds != null">degraded_orient_ids = #{degradedOrientIds},</if>
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>
</mapper>
