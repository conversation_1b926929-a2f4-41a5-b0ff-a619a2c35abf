<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.manager.ActivityMapper">

    <resultMap type="com.ruoyi.system.entity.activity.Activity" id="ActivityResult">
        <result property="id"    column="id"    />
        <result property="skinCode"    column="skin_code"    />
        <result property="activityName"    column="activity_name"    />
        <result property="autoJoin"    column="auto_join"    />
        <result property="joinTimes"    column="join_times"    />
        <result property="prizes"    column="prizes"    />
        <result property="ruleDesc"    column="rule_desc"    />
        <result property="icpNo"    column="icp_no"    />
        <result property="jsTemplate"    column="js_template"    />
        <result property="operatorId"    column="operator_id"    />
        <result property="operatorName"    column="operator_name"    />
        <result property="customerConfig"    column="customer_config"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectActivityVo">
        select id, skin_code, activity_name, auto_join, join_times, prizes, rule_desc, icp_no, js_template,
               operator_id, operator_name, customer_config,gmt_create, gmt_modified from tb_activity
    </sql>

    <select id="selectActivityList" parameterType="com.ruoyi.system.entity.activity.Activity" resultMap="ActivityResult">
        <include refid="selectActivityVo"/>
        <where>
            <if test="id != null  and id != ''"> and id = #{id}</if>
            <if test="skinCode != null  and skinCode != ''"> and skin_code = #{skinCode}</if>
            <if test="activityName != null  and activityName != ''"> and activity_name like concat('%', #{activityName}, '%')</if>
        </where>
        order by id desc
    </select>

    <select id="selectSimpleActivityList" parameterType="com.ruoyi.system.entity.activity.Activity" resultMap="ActivityResult">
        select id, skin_code, activity_name from tb_activity
        <where>
            <if test="id != null  and id != ''"> and id = #{id}</if>
            <if test="skinCode != null  and skinCode != ''"> and skin_code = #{skinCode}</if>
            <if test="activityName != null  and activityName != ''"> and activity_name like concat('%', #{activityName}, '%')</if>
            <if test="ids != null and ids.size() > 0">
                and id in
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectActivityById" parameterType="Long" resultMap="ActivityResult">
        <include refid="selectActivityVo"/>
        where id = #{id}
    </select>

    <select id="selectSkinCodeById" parameterType="Long" resultType="java.lang.String">
        select skin_code
        from tb_activity
        where id = #{id}
    </select>

    <insert id="insertActivity" parameterType="com.ruoyi.system.entity.activity.Activity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_activity
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="skinCode != null and skinCode != ''">skin_code,</if>
            <if test="activityName != null and activityName != ''">activity_name,</if>
            <if test="autoJoin != null">auto_join,</if>
            <if test="prizes != null and prizes != ''">prizes,</if>
            <if test="ruleDesc != null and ruleDesc != ''">rule_desc,</if>
            <if test="icpNo != null">icp_no,</if>
            <if test="jsTemplate != null and jsTemplate != ''">js_template,</if>
            <if test="operatorId != null and operatorId != ''">operator_id,</if>
            <if test="operatorName != null and operatorName != ''">operator_name,</if>
            <if test="customerConfig != null and customerConfig != ''">customer_config,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="skinCode != null and skinCode != ''">#{skinCode},</if>
            <if test="activityName != null and activityName != ''">#{activityName},</if>
            <if test="autoJoin != null">#{autoJoin},</if>
            <if test="prizes != null and prizes != ''">#{prizes},</if>
            <if test="ruleDesc != null and ruleDesc != ''">#{ruleDesc},</if>
            <if test="icpNo != null">#{icpNo},</if>
            <if test="jsTemplate != null and jsTemplate != ''">#{jsTemplate},</if>
            <if test="operatorId != null and operatorId != ''">#{operatorId},</if>
            <if test="operatorName != null and operatorName != ''">#{operatorName},</if>
            <if test="customerConfig != null and customerConfig != ''">#{customerConfig},</if>
         </trim>
    </insert>

    <update id="updateActivity" parameterType="com.ruoyi.system.entity.activity.Activity">
        update tb_activity
        <trim prefix="SET" suffixOverrides=",">
            <if test="activityName != null and activityName != ''">activity_name = #{activityName},</if>
            <if test="autoJoin != null">auto_join = #{autoJoin},</if>
            <if test="prizes != null and prizes != ''">prizes = #{prizes},</if>
            <if test="ruleDesc != null and ruleDesc != ''">rule_desc = #{ruleDesc},</if>
            <if test="icpNo != null">icp_no = #{icpNo},</if>
            <if test="jsTemplate != null">js_template = #{jsTemplate},</if>
            <if test="customerConfig != null">customer_config = #{customerConfig},</if>
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>

    <select id="selectTotalOpenActivity" resultMap="ActivityResult">
        select a.id, a.activity_name
        from tb_activity a
        left join tb_activity_plan p on a.id = p.activity_id
        where p.status = 0
        order by a.id desc
    </select>
</mapper>
