<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.manager.AccountMapper">

    <resultMap type="com.ruoyi.system.entity.account.Account" id="AccountResult">
        <result property="id"    column="id"    />
        <result property="mainType"    column="main_type"    />
        <result property="email"    column="email"    />
        <result property="passwd"    column="passwd"    />
        <result property="companyName"    column="company_name"    />
        <result property="contact"    column="contact"    />
        <result property="phone"    column="phone"    />
        <result property="extInfo"    column="ext_info"    />
        <result property="status"    column="status"    />
        <result property="adminType"    column="admin_type"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectAccountVo">
        select id, main_type, email, passwd, company_name, contact, phone, ext_info, status, admin_type, gmt_create, gmt_modified from tb_account
    </sql>

    <select id="selectAccountForLogin" parameterType="com.ruoyi.system.entity.account.Account" resultMap="AccountResult">
        select id, main_type, email, passwd, company_name, contact, phone,admin_type,`status` from tb_account
        where email = #{email} and main_type = #{mainType}
        <if test="companyName != null ">
            union all
            select id, main_type, email, passwd, company_name, contact, phone, admin_type,status from tb_account
            where company_name = #{companyName} and main_type = #{mainType}
        </if>
        <if test="phone != null ">
            union all
            select id, main_type, email, passwd, company_name, contact, phone,admin_type ,status from tb_account
            where phone = #{phone} and main_type = #{mainType}
        </if>
        limit 1
    </select>

    <select id="selectAccountList" parameterType="com.ruoyi.system.entity.account.Account" resultMap="AccountResult">
        <include refid="selectAccountVo"/>
        <where>
            <if test="id != null "> and id = #{id}</if>
            <if test="status != null "> and `status` = #{status}</if>
            <if test="mainType != null "> and main_type = #{mainType}</if>
            <if test="companyName != null  and companyName != ''"> and company_name like concat('%', #{companyName}, '%')</if>
            <if test="startDate != null "> and gmt_create &gt;= #{startDate}</if>
            <if test="endDate != null "> and gmt_create &lt;= #{endDate}</if>
            <if test="ids != null and ids.size() > 0">
                and id in
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="email != null and email != ''">and email like concat('%', #{email}, '%')</if>
        </where>
        order by id desc
    </select>

    <select id="selectAgentAdvertiserList" parameterType="com.ruoyi.system.entity.account.Account" resultMap="AccountResult">
        <include refid="selectAccountVo"/>
        <where>
            main_type in (2, 4)
            <if test="mainType != null "> and main_type = #{mainType}</if>
            <if test="status != null "> and `status` = #{status}</if>
            <if test="startDate != null "> and gmt_create &gt;= #{startDate}</if>
            <if test="endDate != null "> and gmt_create &lt;= #{endDate}</if>
            <if test="email != null and email != ''">and email like concat('%', #{email}, '%')</if>
            <if test="excludeIds != null and excludeIds.size() != 0">
                and id not in
                <foreach collection="excludeIds" item="excludeId" open="(" separator="," close=")">
                    #{excludeId}
                </foreach>
            </if>
            <if test="(agentIds != null and agentIds.size() > 0) or (advertiserIds != null and advertiserIds.size() > 0)">
                and (
                    1 = 0
                    <if test="agentIds != null and agentIds.size() > 0">
                        or ( main_type = 4 and id in
                        <foreach collection="agentIds" item="agentId" open="(" separator="," close=")">
                            #{agentId}
                        </foreach>
                        )
                    </if>
                    <if test="advertiserIds != null and advertiserIds.size() > 0">
                        or ( main_type = 2 and id in
                        <foreach collection="advertiserIds" item="advertiserId" open="(" separator="," close=")">
                            #{advertiserId}
                        </foreach>
                        )
                    </if>
                )
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectAccountExtInfo" parameterType="com.ruoyi.system.entity.account.Account" resultMap="AccountResult">
        <include refid="selectAccountVo"/>
        <where>
            <if test="mainType != null "> and main_type = #{mainType}</if>
            <if test="extInfo != null and extInfo != ''"> and ext_info like concat('%', #{extInfo}, '%')</if>
        </where>
    </select>

    <select id="selectByIds" parameterType="List" resultMap="AccountResult">
        <include refid="selectAccountVo"/>
        <where>
            id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </select>

    <select id="selectAccountById" parameterType="Long" resultMap="AccountResult">
        <include refid="selectAccountVo"/>
        where id = #{id}
    </select>

    <insert id="insertAccount" parameterType="com.ruoyi.system.entity.account.Account" useGeneratedKeys="true" keyProperty="id">
        insert into tb_account
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="mainType != null">main_type,</if>
            <if test="email != null and email != ''">email,</if>
            <if test="passwd != null and passwd != ''">passwd,</if>
            <if test="companyName != null and companyName != ''">company_name,</if>
            <if test="contact != null">contact,</if>
            <if test="phone != null">phone,</if>
            <if test="extInfo != null and extInfo != ''">ext_info,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="mainType != null">#{mainType},</if>
            <if test="email != null and email != ''">#{email},</if>
            <if test="passwd != null and passwd != ''">#{passwd},</if>
            <if test="companyName != null and companyName != ''">#{companyName},</if>
            <if test="contact != null">#{contact},</if>
            <if test="phone != null">#{phone},</if>
            <if test="extInfo != null and extInfo != ''">#{extInfo},</if>
         </trim>
    </insert>

    <update id="updateAccount" parameterType="com.ruoyi.system.entity.account.Account">
        update tb_account
        <trim prefix="SET" suffixOverrides=",">
            <if test="status != null">`status` = #{status},</if>
            <if test="mainType != null">main_type = #{mainType},</if>
            <if test="email != null and email != ''">email = #{email},</if>
            <if test="passwd != null and passwd != ''">passwd = #{passwd},</if>
            <if test="companyName != null and companyName != ''">company_name = #{companyName},</if>
            <if test="contact != null and contact != ''">contact = #{contact},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="extInfo != null and extInfo != ''">ext_info = #{extInfo},</if>
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>

    <select id="checkCompanyNameUnique"  resultType="int">
        select count(1) from tb_account where company_name = #{companyName} and main_type = #{mainType}  limit 1
    </select>

    <select id="checkPhoneUnique" resultType="int">
        select count(1) from tb_account where phone = #{phone} and main_type = #{mainType} limit 1
    </select>

    <select id="checkEmailUnique" resultType="int">
        select count(1) from tb_account where email = #{email} and main_type = #{mainType} limit 1
    </select>

    <select id="selectSimpleAccountList" parameterType="com.ruoyi.system.entity.account.Account" resultMap="AccountResult">
        select id, company_name, ext_info from tb_account
        <where>
            main_type = #{mainType}
            <if test="id != null "> and id = #{id}</if>
            <if test="companyName != null  and companyName != ''"> and company_name like concat('%', #{companyName}, '%')</if>
        </where>
        order by id desc
    </select>

    <select id="selectIdByCompanyName"  resultType="Long">
        select id
        from tb_account
        <where>
            main_type = #{mainType}
            and company_name like concat('%', #{companyName}, '%')
        </where>
        order by id desc
    </select>

    <select id="selectIdByEmail"  resultType="Long">
        select id
        from tb_account
        <where>
            main_type = #{mainType}
            and email like concat('%', #{email}, '%')
        </where>
        order by id desc
    </select>

    <select id="selectIdsByEmailOrCompanyName" resultType="Long">
        select id
        from tb_account
        <where>
            <if test="mainType != null "> and main_type = #{mainType}</if>
            <if test="accountSearch != null and accountSearch != ''">and email like concat('%', #{accountSearch}, '%')</if>
        </where>
        union all
        select id
        from tb_account
        <where>
            <if test="mainType != null "> and main_type = #{mainType}</if>
            <if test="accountSearch != null and accountSearch != ''">and company_name like concat('%', #{accountSearch}, '%')</if>
        </where>
    </select>

    <select id="selectIdsByIdOrEmail" resultType="Long">
        select id
        from tb_account
        where email like concat('%', #{accountSearch}, '%') or id = #{accountSearch}
    </select>

    <select id="selectIdsByIdOrEmailOrCompanyName" resultType="Long">
        select id
        from tb_account
        where email like concat('%', #{accountSearch}, '%') or id = #{accountSearch} or company_name like concat('%', #{accountSearch}, '%')
    </select>

    <select id="selectIdsByIdOrEmailAndCompany" resultType="Long">
        select id
        from tb_account
        <where>
            <if test="accountSearch != null and accountSearch != ''">
                (email like concat('%', #{accountSearch}, '%') or id = #{accountSearch})
            </if>
            <if test="companyName != null  and companyName != ''">
                and company_name like concat('%', #{companyName}, '%')
            </if>
        </where>
    </select>

    <select id="filterNormalAccountByIds" parameterType="List" resultType="Long">
        select id
        from tb_account
        where `status` = 0 and id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
    </select>

    <select id="selectCrmUserByEmail" resultMap="AccountResult">
        <include refid="selectAccountVo"/>
        where email = #{email} and main_type = 3
    </select>

    <select id="selectCrmUserIdsByEmail" parameterType="List" resultType="Long">
        select id
        from tb_account
        where main_type = 3 and email in
        <foreach collection="emails" item="email" open="(" separator="," close=")">
            #{email}
        </foreach>
    </select>
</mapper>
