<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.manager.InnovateLayerMapper">

    <resultMap type="com.ruoyi.system.entity.advert.InnovateLayer" id="InnovateLayerResult">
        <result property="id"    column="id"    />
        <result property="layerName"    column="layer_name"    />
        <result property="skinCode"    column="skin_code"    />
        <result property="bgImg" column="bg_img"    />
        <result property="gifImg" column="gif_img"    />
        <result property="layerInfo" column="layer_info"    />
        <result property="operatorId"    column="operator_id"    />
        <result property="operatorName"    column="operator_name"    />
        <result property="isDeleted"    column="is_deleted"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectInnovateLayerVo">
        select id, layer_name, skin_code, bg_img, gif_img, layer_info,
               operator_id, operator_name, is_deleted, gmt_create, gmt_modified from tb_innovate_layer
    </sql>

    <select id="selectInnovateLayerList" parameterType="com.ruoyi.system.entity.advert.InnovateLayer" resultMap="InnovateLayerResult">
        <include refid="selectInnovateLayerVo"/>
        <where>
            is_deleted = 0
            <if test="id != null"> and id = #{id}</if>
            <if test="layerName != null  and layerName != ''"> and layer_name like concat('%', #{layerName}, '%')</if>
            <if test="skinCode != null  and skinCode != ''"> and skin_code = #{skinCode}</if>
            <if test="bgImg != null and bgImg != ''"> and bg_img = #{bgImg}</if>
            <if test="gifImg != null and gifImg != ''"> and gif_img = #{gifImg}</if>
        </where>
        order by id desc
    </select>

    <select id="selectInnovateLayerById" parameterType="Long" resultMap="InnovateLayerResult">
        <include refid="selectInnovateLayerVo"/>
        where id = #{id}
    </select>

    <insert id="insertInnovateLayer" parameterType="com.ruoyi.system.entity.advert.InnovateLayer" useGeneratedKeys="true" keyProperty="id">
        insert into tb_innovate_layer
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="layerName != null and layerName != ''">layer_name,</if>
            <if test="skinCode != null and skinCode != ''">skin_code,</if>
            <if test="bgImg != null and bgImg != ''">bg_img,</if>
            <if test="gifImg != null and gifImg != ''">gif_img,</if>
            <if test="layerInfo != null and layerInfo != ''">layer_info,</if>
            <if test="operatorId != null">operator_id,</if>
            <if test="operatorName != null and operatorName != ''">operator_name,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="layerName != null and layerName != ''">#{layerName},</if>
            <if test="skinCode != null and skinCode != ''">#{skinCode},</if>
            <if test="bgImg != null and bgImg != ''">#{bgImg},</if>
            <if test="gifImg != null and gifImg != ''">#{gifImg},</if>
            <if test="layerInfo != null and layerInfo != ''">#{layerInfo},</if>
            <if test="operatorId != null">#{operatorId},</if>
            <if test="operatorName != null and operatorName != ''">#{operatorName},</if>
         </trim>
    </insert>

    <update id="updateInnovateLayer" parameterType="com.ruoyi.system.entity.advert.InnovateLayer">
        update tb_innovate_layer
        <trim prefix="SET" suffixOverrides=",">
            <if test="layerName != null and layerName != ''">layer_name = #{layerName},</if>
            <if test="skinCode != null and skinCode != ''">skin_code = #{skinCode},</if>
            <if test="bgImg != null and bgImg != ''">bg_img = #{bgImg},</if>
            <if test="gifImg != null and gifImg != ''">gif_img = #{gifImg},</if>
            <if test="layerInfo != null and layerInfo != ''">layer_info = #{layerInfo},</if>
            <if test="operatorId != null">operator_id = #{operatorId},</if>
            <if test="operatorName != null">operator_name = #{operatorName},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>
</mapper>
