<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.manager.SlotShuntTaskMapper">

    <resultMap type="com.ruoyi.system.entity.slot.SlotShuntTask" id="SlotShuntTaskResult">
        <result property="id"    column="id"    />
        <result property="slotId"    column="slot_id"    />
        <result property="shuntType"    column="shunt_type"    />
        <result property="taskName"    column="task_name"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="redirectType"    column="redirect_type"    />
        <result property="redirectValue"    column="redirect_value"    />
        <result property="shuntRatio"    column="shunt_ratio"    />
        <result property="taskStatus"    column="task_status"    />
        <result property="threshold"    column="threshold"    />
        <result property="executeTime"    column="execute_time"    />
        <result property="finishTime"    column="finish_time"    />
        <result property="cancelTime"    column="cancel_time"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectSlotShuntTaskVo">
        select id, slot_id, shunt_type, task_name, start_time, end_time, redirect_type, redirect_value,
               shunt_ratio, task_status, threshold, execute_time, finish_time,  cancel_time,
               gmt_create, gmt_modified
        from tb_slot_shunt_task
    </sql>

    <select id="selectList" parameterType="com.ruoyi.system.req.slot.shunt.SlotShuntTaskParam" resultMap="SlotShuntTaskResult">
        <include refid="selectSlotShuntTaskVo"/>
        <where>
            task_status != 3
            <if test="slotId != null "> and slot_id = #{slotId}</if>
            <if test="taskName != null and taskName != ''"> and task_name like concat('%', #{taskName}, '%')</if>
            <if test="taskStatus != null "> and task_status = #{taskStatus}</if>
            <if test="startDate != null "> and start_time &gt;= #{startDate}</if>
            <if test="endDate != null "> and start_time &lt;= #{endDate}</if>
            <if test="slotIds != null and slotIds.size() > 0">
                and slot_id in
                <foreach collection="slotIds" item="slotId" open="(" separator="," close=")">
                    #{slotId}
                </foreach>
            </if>
            <if test="taskStatusList != null and taskStatusList.size() > 0">
                and task_status in
                <foreach collection="taskStatusList" item="taskStatus" open="(" separator="," close=")">
                    #{taskStatus}
                </foreach>
            </if>
        </where>
        order by id desc
    </select>

    <select id="countByParam" parameterType="com.ruoyi.system.req.slot.shunt.SlotShuntTaskParam"  resultType="int">
        select count(1)
        from tb_slot_shunt_task
        <where>
            task_status != 3
            <if test="slotId != null "> and slot_id = #{slotId}</if>
            <if test="taskName != null and taskName != ''"> and task_name like concat('%', #{taskName}, '%')</if>
            <if test="taskStatus != null "> and task_status = #{taskStatus}</if>
            <if test="startDate != null "> and start_time &gt;= #{startDate}</if>
            <if test="endDate != null "> and start_time &lt;= #{endDate}</if>
            <if test="slotIds != null and slotIds.size() > 0">
                and id in
                <foreach collection="slotIds" item="slotId" open="(" separator="," close=")">
                    #{slotId}
                </foreach>
            </if>
            <if test="taskStatusList != null and taskStatusList.size() > 0">
                and task_status in
                <foreach collection="taskStatusList" item="taskStatus" open="(" separator="," close=")">
                    #{taskStatus}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectTotalValidList" resultMap="SlotShuntTaskResult">
        <include refid="selectSlotShuntTaskVo"/>
        <where>
            task_status in (0, 1)
        </where>
    </select>

    <select id="selectExecuteList" resultMap="SlotShuntTaskResult">
        <include refid="selectSlotShuntTaskVo"/>
        <where>
            task_status = 1
            and start_time &lt;= now()
            and end_time &gt;= now()
            <if test="slotId != null "> and slot_id = #{slotId}</if>
        </where>
    </select>

    <select id="selectById" parameterType="Long" resultMap="SlotShuntTaskResult">
        <include refid="selectSlotShuntTaskVo"/>
        where id = #{id}
    </select>

    <insert id="insert" parameterType="com.ruoyi.system.entity.slot.SlotShuntTask"
            useGeneratedKeys="true" keyProperty="id">
        insert into tb_slot_shunt_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="slotId != null">slot_id,</if>
            <if test="shuntType != null">shunt_type,</if>
            <if test="taskName != null">task_name,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="redirectType != null">redirect_type,</if>
            <if test="redirectValue != null">redirect_value,</if>
            <if test="shuntRatio != null">shunt_ratio,</if>
            <if test="threshold != null">threshold,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="slotId != null">#{slotId},</if>
            <if test="shuntType != null">#{shuntType},</if>
            <if test="taskName != null">#{taskName},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="redirectType != null">#{redirectType},</if>
            <if test="redirectValue != null">#{redirectValue},</if>
            <if test="shuntRatio != null">#{shuntRatio},</if>
            <if test="threshold != null">#{threshold},</if>
        </trim>
    </insert>

    <update id="update" parameterType="com.ruoyi.system.entity.slot.SlotShuntTask">
        update tb_slot_shunt_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="shuntType != null">shunt_type = #{shuntType},</if>
            <if test="taskName != null">task_name = #{taskName},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="redirectType != null">redirect_type = #{redirectType},</if>
            <if test="redirectValue != null">redirect_value = #{redirectValue},</if>
            <if test="shuntRatio != null">shunt_ratio = #{shuntRatio},</if>
            <if test="threshold != null">threshold = #{threshold},</if>
            <if test="taskStatus != null">task_status = #{taskStatus},</if>
            <if test="cancelTime != null">cancel_time = #{cancelTime},</if>
            <if test="finishTime != null">finish_time = #{finishTime},</if>
            <if test="executeTime != null">execute_time = #{executeTime},</if>
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>
</mapper>
