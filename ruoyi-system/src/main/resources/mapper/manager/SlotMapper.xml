<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.manager.SlotMapper">

    <resultMap type="com.ruoyi.system.entity.slot.Slot" id="SlotResult">
        <result property="id"    column="id"    />
        <result property="accountId"    column="account_id"    />
        <result property="appId"    column="app_id"    />
        <result property="slotUrl"    column="slot_url"    />
        <result property="slotSpecId"    column="slot_spec_id"    />
        <result property="slotName"    column="slot_name"    />
        <result property="sckType"    column="sck_type"    />
        <result property="status"    column="status"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectSlotVo">
        select id, account_id, app_id, slot_url, slot_spec_id, slot_name, sck_type, status, gmt_create, gmt_modified from tb_slot
    </sql>

    <select id="selectSlotList" parameterType="com.ruoyi.system.entity.slot.Slot" resultMap="SlotResult">
        <include refid="selectSlotVo"/>
        <where>
            <if test="id != null "> and id = #{id}</if>
            <if test="accountId != null "> and account_id = #{accountId}</if>
            <if test="appId != null "> and app_id = #{appId}</if>
            <if test="slotUrl != null"> and slot_url = #{slotUrl}</if>
            <if test="slotSpecId != null"> and slot_spec_id = #{slotSpecId}</if>
            <if test="slotName != null"> and slot_name like concat('%', #{slotName}, '%')</if>
            <if test="searchValue != null and searchValue != ''"> and (slot_name like concat('%', #{searchValue}, '%') or id = #{searchValue})</if>
            <if test="sckType != null "> and sck_type = #{sckType}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="startDate != null "> and gmt_create &gt;= #{startDate}</if>
            <if test="endDate != null "> and gmt_create &lt;= #{endDate}</if>
            <if test="appIds != null and appIds.size() > 0">
                and app_id in
                <foreach collection="appIds" item="appId" open="(" separator="," close=")">
                    #{appId}
                </foreach>
            </if>
            <if test="slotIds != null and slotIds.size() > 0">
                and id in
                <foreach collection="slotIds" item="slotId" open="(" separator="," close=")">
                    #{slotId}
                </foreach>
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectSlotListBySlotDataSort" parameterType="com.ruoyi.system.entity.slot.Slot" resultMap="SlotResult">
        select id, account_id, app_id, slot_url, slot_spec_id, slot_name, sck_type, status, gmt_create, gmt_modified from tb_slot slot
        LEFT JOIN (select slot_id, slot_request_uv from tb_slot_data where `cur_date` = #{curDate})  data on slot.`id` = data.`slot_id`
        <where>
            <if test="id != null "> and id = #{id}</if>
            <if test="accountId != null "> and slot.account_id = #{accountId}</if>
            <if test="appId != null "> and slot.app_id = #{appId}</if>
            <if test="slotName != null  and slotName != ''"> and slot.slot_name like concat('%', #{slotName}, '%')</if>
            <if test="status != null "> and slot.status = #{status}</if>
            <if test="appIds != null and appIds.size() > 0">
                and slot.app_id in
                <foreach collection="appIds" item="appId" open="(" separator="," close=")">
                    #{appId}
                </foreach>
            </if>
            <if test="slotIds != null and slotIds.size() > 0">
                and id in
                <foreach collection="slotIds" item="slotId" open="(" separator="," close=")">
                    #{slotId}
                </foreach>
            </if>
            <if test="notSlotIds != null and notSlotIds.size() > 0">
                and id not in
                <foreach collection="notSlotIds" item="slotId" open="(" separator="," close=")">
                    #{slotId}
                </foreach>
            </if>
            <if test="accountIds != null and accountIds.size() > 0">
                and account_id in
                <foreach collection="accountIds" item="accountId" open="(" separator="," close=")">
                    #{accountId}
                </foreach>
            </if>
        </where>
        order by
        <choose>
            <when test="orderColumn != null and orderColumn == 'gmtCreate'">
                slot.`gmt_create`
            </when>
            <otherwise>
                data.`slot_request_uv`
            </otherwise>
        </choose>

        <choose>
            <when test="isAsc != null and isAsc == true">
                asc
            </when>
            <otherwise>
                desc
            </otherwise>
        </choose>
        ,id desc
    </select>

    <select id="selectSlotIdList" parameterType="com.ruoyi.system.entity.slot.Slot" resultType="Long">
        select id from tb_slot
        <where>
            <if test="accountId != null "> and account_id = #{accountId}</if>
            <if test="appId != null "> and app_id = #{appId}</if>
            <if test="searchValue != null and searchValue != ''"> and (slot_name like concat('%', #{searchValue}, '%') or id = #{searchValue})</if>
        </where>
    </select>

    <select id="selectSimpleSlotList" parameterType="com.ruoyi.system.entity.slot.Slot" resultMap="SlotResult">
        select id, slot_name from tb_slot
        <where>
            <if test="accountId != null "> and account_id = #{accountId}</if>
        </where>
    </select>

    <select id="selectSlotById" parameterType="Long" resultMap="SlotResult">
        <include refid="selectSlotVo"/>
        where id = #{id}
    </select>

    <insert id="insertSlot" parameterType="com.ruoyi.system.entity.slot.Slot" useGeneratedKeys="true" keyProperty="id">
        insert into tb_slot
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountId != null">account_id,</if>
            <if test="appId != null">app_id,</if>
            <if test="slotUrl != null">slot_url,</if>
            <if test="slotSpecId != null">slot_spec_id,</if>
            <if test="slotName != null">slot_name,</if>
            <if test="sckType != null">sck_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountId != null">#{accountId},</if>
            <if test="appId != null">#{appId},</if>
            <if test="slotUrl != null">#{slotUrl},</if>
            <if test="slotSpecId != null">#{slotSpecId},</if>
            <if test="slotName != null">#{slotName},</if>
            <if test="sckType != null">#{sckType},</if>
         </trim>
    </insert>

    <update id="updateSlot" parameterType="com.ruoyi.system.entity.slot.Slot">
        update tb_slot
        <trim prefix="SET" suffixOverrides=",">
            <if test="slotUrl != null">slot_url = #{slotUrl},</if>
            <if test="slotName != null">slot_name = #{slotName},</if>
            <if test="status != null">status = #{status},</if>
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>

    <select id="groupByAccountId" parameterType="List" resultType="Map">
        select account_id as accountId, COUNT(*) as count
        from tb_slot
        <where>
            account_id in
            <foreach collection="accountIds" item="accountId" open="(" separator="," close=")">
                #{accountId}
            </foreach>
        </where>
        GROUP BY account_id
    </select>

    <select id="groupByAppId" parameterType="List" resultType="Map">
        select app_id as appId, COUNT(*) as count
        from tb_slot
        <where>
            app_id in
            <foreach collection="appIds" item="appId" open="(" separator="," close=")">
                #{appId}
            </foreach>
        </where>
        GROUP BY app_id
    </select>
    <select id="selectSimpleSlotByIds" resultMap="SlotResult">
        select id, slot_name ,account_id ,app_id from tb_slot
        <where>
            id in
            <foreach collection="ids" open="(" item="id" separator=","  close=")">
                #{id}
            </foreach>
        </where>
    </select>
    <select id="selectSlotByAppId" resultMap="SlotResult">
        select id, slot_name,status,app_id from tb_slot
        <where>
            app_id = #{appId}
        </where>
    </select>

    <select id="selectSlotIdByAccountIds" resultType="Long">
        select id from tb_slot
        <where>
            account_id in
            <foreach collection="accountIds" item="accountId" open="(" separator="," close=")">
                #{accountId}
            </foreach>
        </where>
    </select>

    <select id="selectSlotIdAndName" resultMap="SlotResult">
        select id, slot_name from tb_slot
    </select>

    <select id="selectSlotIdAndSlotUrl" resultMap="SlotResult">
        select id, slot_url from tb_slot
    </select>
    <select id="selectByAppIds" resultMap="SlotResult">
        select id, slot_name ,account_id ,app_id from tb_slot
        <where>
            app_id in
            <foreach collection="appIds" open="(" item="id" separator=","  close=")">
                #{id}
            </foreach>
        </where>
    </select>
</mapper>
