<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.manager.LayerSkinMapper">

    <resultMap type="com.ruoyi.system.entity.advert.LayerSkin" id="LayerSkinResult">
        <result property="id"    column="id"    />
        <result property="skinCode"    column="skin_code"    />
        <result property="skinName"    column="skin_name"    />
        <result property="skinType"    column="skin_type"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectLayerSkinVo">
        select id, skin_code, skin_name, skin_type, gmt_create, gmt_modified from tb_layer_skin
    </sql>

    <select id="selectLayerSkinList" parameterType="com.ruoyi.system.entity.advert.LayerSkin" resultMap="LayerSkinResult">
        <include refid="selectLayerSkinVo"/>
        <where>
            <if test="skinCode != null  and skinCode != ''"> and skin_code = #{skinCode}</if>
            <if test="skinName != null  and skinName != ''"> and skin_name like concat('%', #{skinName}, '%')</if>
        </where>
        order by id desc
    </select>

    <select id="selectLayerSkinById" parameterType="Long" resultMap="LayerSkinResult">
        <include refid="selectLayerSkinVo"/>
        where id = #{id}
    </select>

    <select id="selectBySkinCode" parameterType="String" resultMap="LayerSkinResult">
        <include refid="selectLayerSkinVo"/>
        where skin_code = #{skinCode}
    </select>

    <select id="selectBySkinCodeList" resultMap="LayerSkinResult">
        <include refid="selectLayerSkinVo"/>
        where skin_code in
        <foreach collection="list" item="skinCode" open="(" separator="," close=")">
            #{skinCode}
        </foreach>
    </select>

    <insert id="insertLayerSkin" parameterType="com.ruoyi.system.entity.advert.LayerSkin"
            useGeneratedKeys="true" keyProperty="id">
        insert into tb_layer_skin
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="skinCode != null and skinCode != ''">skin_code,</if>
            <if test="skinName != null and skinName != ''">skin_name,</if>
            <if test="skinType != null and skinType != ''">skin_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="skinCode != null and skinCode != ''">#{skinCode},</if>
            <if test="skinName != null and skinName != ''">#{skinName},</if>
            <if test="skinType != null and skinType != ''">#{skinType},</if>
         </trim>
    </insert>

    <update id="updateLayerSkin" parameterType="com.ruoyi.system.entity.advert.LayerSkin">
        update tb_layer_skin
        <trim prefix="SET" suffixOverrides=",">
            <if test="skinCode != null and skinCode != ''">skin_code = #{skinCode},</if>
            <if test="skinName != null and skinName != ''">skin_name = #{skinName},</if>
            <if test="skinType != null and skinType != ''">skin_type = #{skinType},</if>
        </trim>
        where id = #{id}
    </update>
</mapper>
