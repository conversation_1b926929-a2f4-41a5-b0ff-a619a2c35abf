<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.manager.MaterialMapper">

    <resultMap type="com.ruoyi.system.entity.advert.Material" id="MaterialResult">
        <result property="id"    column="id"    />
        <result property="advertId"    column="advert_id"    />
        <result property="advertTitle"    column="advert_title"    />
        <result property="materialImg"    column="material_img"    />
        <result property="buttonText"    column="button_text"    />
        <result property="layerType"    column="layer_type"    />
        <result property="layerId"    column="layer_id"    />
        <result property="isDefault"    column="is_default"    />
        <result property="weight"    column="weight"    />
        <result property="status"    column="status"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectMaterialVo">
        select id, advert_id, advert_title, material_img, button_text, layer_type, layer_id, is_default, weight, status, gmt_create, gmt_modified from tb_material
    </sql>

    <select id="selectMaterialList" parameterType="com.ruoyi.system.entity.advert.Material" resultMap="MaterialResult">
        <include refid="selectMaterialVo"/>
        <where>
            `status` != 2
            <if test="advertId != null  and advertId != ''"> and advert_id = #{advertId}</if>
            <if test="advertTitle != null  and advertTitle != ''"> and advert_title like concat('%', #{advertTitle}, '%')</if>
            <if test="materialImg != null  and materialImg != ''"> and material_img = #{materialImg}</if>
            <if test="buttonText != null  and buttonText != ''"> and button_text = #{buttonText}</if>
            <if test="layerType != null "> and layer_type = #{layerType}</if>
            <if test="layerId != null"> and layer_id = #{layerId}</if>
            <if test="isDefault != null "> and is_default = #{isDefault}</if>
            <if test="weight != null "> and weight = #{weight}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="advertIds != null and advertIds.size() > 0">
                and advert_id in
                <foreach collection="advertIds" item="advertId" open="(" separator="," close=")">
                    #{advertId}
                </foreach>
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectMaterialListByAdvertId" resultMap="MaterialResult">
        <include refid="selectMaterialVo"/>
        <where>
            advert_id = #{advertId} and `status` != 2
        </where>
    </select>

    <select id="selectMaterialById" parameterType="Long" resultMap="MaterialResult">
        <include refid="selectMaterialVo"/>
        where id = #{id}
    </select>

    <insert id="insertMaterial" parameterType="com.ruoyi.system.entity.advert.Material" useGeneratedKeys="true" keyProperty="id">
        insert into tb_material
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="advertId != null and advertId != ''">advert_id,</if>
            <if test="advertTitle != null and advertTitle != ''">advert_title,</if>
            <if test="materialImg != null and materialImg != ''">material_img,</if>
            <if test="buttonText != null and buttonText != ''">button_text,</if>
            <if test="layerType != null">layer_type,</if>
            <if test="layerId != null">layer_id,</if>
            <if test="isDefault != null">is_default,</if>
            <if test="weight != null">weight,</if>
            <if test="status != null">status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="advertId != null and advertId != ''">#{advertId},</if>
            <if test="advertTitle != null and advertTitle != ''">#{advertTitle},</if>
            <if test="materialImg != null and materialImg != ''">#{materialImg},</if>
            <if test="buttonText != null and buttonText != ''">#{buttonText},</if>
            <if test="layerType != null">#{layerType},</if>
            <if test="layerId != null">#{layerId},</if>
            <if test="isDefault != null">#{isDefault},</if>
            <if test="weight != null">#{weight},</if>
            <if test="status != null">#{status},</if>
         </trim>
    </insert>

    <update id="updateMaterial" parameterType="com.ruoyi.system.entity.advert.Material">
        update tb_material
        <trim prefix="SET" suffixOverrides=",">
            <if test="advertTitle != null and advertTitle != ''">advert_title = #{advertTitle},</if>
            <if test="materialImg != null and materialImg != ''">material_img = #{materialImg},</if>
            <if test="buttonText != null and buttonText != ''">button_text = #{buttonText},</if>
            <if test="layerType != null">layer_type = #{layerType},</if>
            <if test="layerId != null">layer_id = #{layerId},</if>
            <if test="isDefault != null">is_default = #{isDefault},</if>
            <if test="weight != null">weight = #{weight},</if>
            <if test="status != null">status = #{status},</if>
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>

    <insert id="batchInsertMaterial">
        insert into tb_material (advert_id,advert_title,material_img,button_text,layer_type,layer_id,is_default,weight,status)
        values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.advertId},#{item.advertTitle},#{item.materialImg},#{item.buttonText},#{item.layerType},#{item.layerId},#{item.isDefault},#{item.weight},#{item.status})
        </foreach>
    </insert>

    <select id="selectUsedLayerId" resultType="Long">
        select distinct layer_id
        from tb_material m
        left join tb_advert a on m.advert_id = a.id
        <where> a.is_invalid = 0
            and m.status != 2
            and m.layer_id in
            <foreach collection="layerIds" item="layerId" open="(" separator="," close=")">
                #{layerId}
            </foreach>
        </where>
    </select>
</mapper>
