<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.manager.ActivityPlanMapper">

    <resultMap type="com.ruoyi.system.entity.activity.ActivityPlan" id="ActivityPlanResult">
        <result property="id"    column="id"    />
        <result property="activityId"    column="activity_id"    />
        <result property="status"    column="status"    />
        <result property="operatorId"    column="operator_id"    />
        <result property="operatorName"    column="operator_name"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectActivityPlanVo">
        select id, activity_id, status, operator_id, operator_name, gmt_create, gmt_modified from tb_activity_plan
    </sql>

    <select id="selectActivityPlanList" parameterType="com.ruoyi.system.entity.activity.ActivityPlan" resultMap="ActivityPlanResult">
        <include refid="selectActivityPlanVo"/>
        <where>
            <if test="status != null "> and status = #{status}</if>
            <if test="activityIds != null and activityIds.size() != 0">
                and activity_id in
                <foreach collection="activityIds" item="activityId" open="(" separator="," close=")">
                    #{activityId}
                </foreach>
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectActivityPlanById" parameterType="Long" resultMap="ActivityPlanResult">
        <include refid="selectActivityPlanVo"/>
        where id = #{id}
    </select>

    <select id="selectByActivityId" parameterType="Long" resultMap="ActivityPlanResult">
        <include refid="selectActivityPlanVo"/>
        where activity_id = #{activityId}
    </select>

    <insert id="insertActivityPlan" parameterType="com.ruoyi.system.entity.activity.ActivityPlan" useGeneratedKeys="true" keyProperty="id">
        insert into tb_activity_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="status != null">status,</if>
            <if test="activityId != null">activity_id,</if>
            <if test="operatorId != null">operator_id,</if>
            <if test="operatorName != null">operator_name,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="status != null">#{status},</if>
            <if test="activityId != null">#{activityId},</if>
            <if test="operatorId != null">#{operatorId},</if>
            <if test="operatorName != null">#{operatorName},</if>
         </trim>
    </insert>

    <update id="updateActivityPlan" parameterType="com.ruoyi.system.entity.activity.ActivityPlan">
        update tb_activity_plan
        <trim prefix="SET" suffixOverrides=",">
            <if test="status != null">status = #{status},</if>
            <if test="activityId != null">activity_id = #{activityId},</if>
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>

    <select id="selectByActivityIds" parameterType="List" resultMap="ActivityPlanResult">
        <include refid="selectActivityPlanVo"/>
        where activity_id in
        <foreach collection="activityIds" item="activityId" open="(" separator="," close=")">
            #{activityId}
        </foreach>
    </select>

</mapper>
