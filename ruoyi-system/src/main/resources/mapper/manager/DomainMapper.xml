<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.manager.DomainMapper">

    <resultMap type="com.ruoyi.system.entity.manager.Domain" id="DomainResult">
        <result property="id"    column="id"    />
        <result property="domain"    column="domain"    />
        <result property="domainType"    column="domain_type"    />
        <result property="httpsEnable"    column="https_enable"    />
        <result property="domainStatus"    column="domain_status"    />
        <result property="icpNo"    column="icp_no"    />
        <result property="icpSubject"    column="icp_subject"    />
        <result property="domainExpire"    column="domain_expire"    />
        <result property="remark"    column="remark"    />
        <result property="wxStatus"    column="wx_status"    />
        <result property="alipayStatus"    column="alipay_status"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectDomainVo">
        select id, domain, domain_type, https_enable, domain_status, icp_no, icp_subject, domain_expire, remark,wx_status,alipay_status, gmt_create, gmt_modified from tb_domain
    </sql>

    <select id="selectDomainList" parameterType="com.ruoyi.system.entity.manager.Domain" resultMap="DomainResult">
        <include refid="selectDomainVo"/>
        <where>
            <if test="domain != null and domain != ''"> and domain like concat('%', #{domain}, '%')</if>
            <if test="domainType != null "> and domain_type = #{domainType}</if>
            <if test="httpsEnable != null "> and https_enable = #{httpsEnable}</if>
            <if test="domainStatus != null "> and domain_status = #{domainStatus}</if>
            <if test="alipayStatus != null "> and alipay_status = #{alipayStatus}</if>
            <if test="wxStatus != null "> and wx_status = #{wxStatus}</if>
            <if test="icpNo != null"> and icp_no = #{icpNo}</if>
            <if test="icpSubject != null and icpSubject != ''"> and icp_subject like concat('%', #{icpSubject}, '%')</if>
            <if test="remark != null and remark != ''"> and remark like concat('%', #{remark}, '%')</if>
            <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
            <if test="domains != null and domains.size() > 0">
                and domain in
                <foreach collection="domains" item="domain" open="(" separator="," close=")">
                    #{domain}
                </foreach>
            </if>
        </where>
        order by
        <choose>
            <when test="orderColumn != null and orderColumn == 'domainExpire'">
                `domain_expire`
            </when>
            <otherwise>
                `id`
            </otherwise>
        </choose>
        <choose>
            <when test="isAsc != null and isAsc == true">
                asc
            </when>
            <otherwise>
                desc
            </otherwise>
        </choose>
    </select>

    <select id="selectDomainById" parameterType="Long" resultMap="DomainResult">
        <include refid="selectDomainVo"/>
        where id = #{id}
    </select>

    <select id="selectByDomain" parameterType="String" resultMap="DomainResult">
        <include refid="selectDomainVo"/>
        where domain = #{domain}
    </select>

    <insert id="insertDomain" parameterType="com.ruoyi.system.entity.manager.Domain" useGeneratedKeys="true" keyProperty="id">
        insert into tb_domain
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="domain != null and domain != ''">domain,</if>
            <if test="domainType != null">domain_type,</if>
            <if test="httpsEnable != null">https_enable,</if>
            <if test="domainStatus != null">domain_status,</if>
            <if test="icpNo != null">icp_no,</if>
            <if test="icpSubject != null">icp_subject,</if>
            <if test="remark != null">remark,</if>
            <if test="domainExpire != null">domain_expire,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="domain != null and domain != ''">#{domain},</if>
            <if test="domainType != null">#{domainType},</if>
            <if test="httpsEnable != null">#{httpsEnable},</if>
            <if test="domainStatus != null">#{domainStatus},</if>
            <if test="icpNo != null and icpNo != ''">#{icpNo},</if>
            <if test="icpSubject != null">#{icpSubject},</if>
            <if test="remark != null">#{remark},</if>
            <if test="domainExpire != null">#{domainExpire},</if>
         </trim>
    </insert>

    <update id="updateDomain" parameterType="com.ruoyi.system.entity.manager.Domain">
        update tb_domain
        <trim prefix="SET" suffixOverrides=",">
            <if test="httpsEnable != null">https_enable = #{httpsEnable},</if>
            <if test="domainStatus != null">domain_status = #{domainStatus},</if>
            <if test="icpNo != null and icpNo != ''">icp_no = #{icpNo},</if>
            <if test="icpSubject != null">icp_subject = #{icpSubject},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="domainExpire != null">domain_expire = #{domainExpire},</if>
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>
    <update id="updateWxAlipayStatus" parameterType="com.ruoyi.system.req.manager.DomainUpdateStatusReq">
        update tb_domain
        <trim prefix="SET" suffixOverrides=",">
            <if test="wxStatus != null">wx_status = #{wxStatus},</if>
            <if test="alipayStatus != null">alipay_status = #{alipayStatus},</if>
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDomainById" parameterType="Long">
        delete from tb_domain where id = #{id}
    </delete>
</mapper>
