<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.manager.AdvertCostMapper">

    <resultMap type="com.ruoyi.system.entity.advert.AdvertCost" id="Base_result">
        <result property="id"    column="id"    />
        <result property="advertId" column="advert_id"    />
        <result property="billingType"    column="billing_type"    />
        <result property="cost"    column="cost"    />
        <result property="curDate"    column="cur_date"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectAdvertVo">
        select id, advert_id, billing_type, cost, cur_date, gmt_create, gmt_modified from tb_advert_cost
    </sql>

    <insert id="insertOrUpdateAdvertCost" useGeneratedKeys="true" keyProperty="id">
        insert into tb_advert_cost
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="advertId != null">advert_id,</if>
            <if test="billingType != null">billing_type,</if>
            <if test="cost != null">cost,</if>
            cur_date
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="advertId != null">#{advertId},</if>
            <if test="billingType != null">#{billingType},</if>
            <if test="cost != null">#{cost},</if>
            now()
         </trim>
        ON DUPLICATE KEY UPDATE
        billing_type = values(billing_type),
        cost = values(cost)
    </insert>


    <insert id="batchInsertOrUpdate" useGeneratedKeys="true" keyProperty="id">
        insert into tb_advert_cost(advert_id,billing_type,cost,cur_date)
        values
        <foreach collection="advertCosts" item="entity" separator=",">
            ( #{entity.advertId},#{entity.billingType},#{entity.cost}, #{entity.curDate})
        </foreach>
        ON DUPLICATE KEY UPDATE
        billing_type = values(billing_type),
        cost = values(cost)
    </insert>

    <select id="selectById" resultMap="Base_result">
        <include refid="selectAdvertVo"/>
        where advert_id = #{advertId} and cur_date = #{curDate}
    </select>

    <select id="selectList" parameterType="com.ruoyi.system.entity.advert.AdvertCost" resultMap="Base_result">
        <include refid="selectAdvertVo"/>
        <where>
            <if test="curDate != null "> and cur_date = #{curDate}</if>
            <if test="billingType != null "> and billing_type = #{billingType}</if>
        </where>
        order by id desc
    </select>

    <select id="selectListByYesterday" resultMap="Base_result">
        <include refid="selectAdvertVo"/>
        where id > #{id} and cur_date = #{date}
        order by id asc
        limit #{pageSize}
    </select>

</mapper>
