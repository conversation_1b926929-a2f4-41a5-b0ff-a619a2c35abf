<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.manager.SlotSpecMapper">

    <resultMap type="com.ruoyi.system.entity.slot.SlotSpec" id="SlotSpecResult">
        <result property="id"    column="id"    />
        <result property="specName"    column="spec_name"    />
        <result property="length"    column="length"    />
        <result property="width"    column="width"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectSlotSpecVo">
        select id, spec_name, length, width, gmt_create, gmt_modified from tb_slot_spec
    </sql>

    <select id="selectSlotSpecList" parameterType="com.ruoyi.system.entity.slot.SlotSpec" resultMap="SlotSpecResult">
        <include refid="selectSlotSpecVo"/>
        <where>
            <if test="specName != null  and specName != ''"> and spec_name like concat('%', #{specName}, '%')</if>
            <if test="length != null  and length != ''"> and length = #{length}</if>
            <if test="width != null  and width != ''"> and width = #{width}</if>
            <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
            <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
        </where>
    </select>

    <select id="selectSimpleSlotSpecList" parameterType="com.ruoyi.system.entity.slot.SlotSpec" resultMap="SlotSpecResult">
        select id, spec_name from tb_slot_spec
        <where>
            <if test="specName != null  and specName != ''"> and spec_name like concat('%', #{specName}, '%')</if>
            <if test="length != null  and length != ''"> and length = #{length}</if>
            <if test="width != null  and width != ''"> and width = #{width}</if>
        </where>
    </select>

    <select id="selectSlotSpecById" parameterType="Long" resultMap="SlotSpecResult">
        <include refid="selectSlotSpecVo"/>
        where id = #{id}
    </select>

    <insert id="insertSlotSpec" parameterType="com.ruoyi.system.entity.slot.SlotSpec" useGeneratedKeys="true" keyProperty="id">
        insert into tb_slot_spec
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="specName != null and specName != ''">spec_name,</if>
            <if test="length != null and length != ''">length,</if>
            <if test="width != null and width != ''">width,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="specName != null and specName != ''">#{specName},</if>
            <if test="length != null and length != ''">#{length},</if>
            <if test="width != null and width != ''">#{width},</if>
         </trim>
    </insert>

    <update id="updateSlotSpec" parameterType="com.ruoyi.system.entity.slot.SlotSpec">
        update tb_slot_spec
        <trim prefix="SET" suffixOverrides=",">
            <if test="specName != null and specName != ''">spec_name = #{specName},</if>
            <if test="length != null and length != ''">length = #{length},</if>
            <if test="width != null and width != ''">width = #{width},</if>
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>
</mapper>
