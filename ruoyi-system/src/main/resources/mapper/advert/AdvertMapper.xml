<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.manager.AdvertMapper">

    <resultMap type="com.ruoyi.system.entity.advert.Advert" id="AdvertResult">
        <result property="id"    column="id"    />
        <result property="advertCategory" column="advert_category"    />
        <result property="advertName"    column="advert_name"    />
        <result property="thumbnailImg"    column="thumbnail_img"    />
        <result property="advertiserId"    column="advertiser_id"    />
        <result property="assessType"    column="assess_type"    />
        <result property="assessCost"    column="assess_cost"    />
        <result property="landpageUrl"    column="landpage_url"    />
        <result property="clickCallbackUrl"    column="click_callback_url"    />
        <result property="lpCallbackUrl"    column="lp_callback_url"    />
        <result property="dailyBudget"    column="daily_budget"    />
        <result property="startServingDate"    column="start_serving_date"    />
        <result property="stopServingDate"    column="stop_serving_date"    />
        <result property="servingSwitch"    column="serving_switch"    />
        <result property="advertStatus"    column="advert_status"    />
        <result property="extInfo" column="ext_info"    />
        <result property="operatorId" column="operator_id"    />
        <result property="operatorName" column="operator_name"    />
        <result property="isInvalid" column="is_invalid"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectAdvertVo">
        select id, advert_category, advert_name, thumbnail_img, advertiser_id, assess_type, assess_cost, landpage_url,
               click_callback_url, lp_callback_url, daily_budget, start_serving_date, stop_serving_date, serving_switch,
               advert_status, ext_info, operator_id, operator_name, is_invalid, gmt_create, gmt_modified from tb_advert
    </sql>

    <select id="selectAdvertList" parameterType="com.ruoyi.system.entity.advert.Advert" resultMap="AdvertResult">
        <include refid="selectAdvertVo"/>
        <where>
            <if test="id != null">and id = #{id}</if>
            <if test="isInvalid != null">and is_invalid = #{isInvalid}</if>
            <if test="advertCategory != null">and advert_category = #{advertCategory}</if>
            <if test="advertName != null  and advertName != ''"> and advert_name like concat('%', #{advertName}, '%')</if>
            <if test="thumbnailImg != null  and thumbnailImg != ''"> and thumbnail_img = #{thumbnailImg}</if>
            <if test="advertiserId != null"> and advertiser_id = #{advertiserId}</if>
            <if test="assessType != null"> and assess_type = #{assessType}</if>
            <if test="assessCost != null "> and assess_cost = #{assessCost}</if>
            <if test="landpageUrl != null  and landpageUrl != ''"> and landpage_url like concat('%', #{landpageUrl}, '%')</if>
            <if test="clickCallbackUrl != null  and clickCallbackUrl != ''"> and click_callback_url = #{clickCallbackUrl}</if>
            <if test="dailyBudget != null "> and daily_budget = #{dailyBudget}</if>
            <if test="startServingDate != null "> and start_serving_date = #{startServingDate}</if>
            <if test="stopServingDate != null "> and stop_serving_date = #{stopServingDate}</if>
            <if test="servingSwitch != null "> and serving_switch = #{servingSwitch}</if>
            <if test="advertStatus != null "> and advert_status = #{advertStatus}</if>
            <if test="operatorId != null "> and operator_id = #{operatorId}</if>
            <if test="operatorName != null  and operatorName != ''"> and operator_name like concat('%', #{operatorName}, '%')</if>
            <if test="gmtCreateStart != null "> and gmt_create &gt;= #{gmtCreateStart}</if>
            <if test="gmtCreateEnd != null"> and gmt_create &lt;= #{gmtCreateEnd}</if>
            <if test="searchValue != null and searchValue != ''"> and (advert_name like concat('%', #{searchValue}, '%') or id = #{searchValue})</if>
            <if test="advertiserIds != null and advertiserIds.size() > 0">
                and advertiser_id in
                <foreach collection="advertiserIds" item="advertiserId" open="(" separator="," close=")">
                    #{advertiserId}
                </foreach>
            </if>
            <if test="ids != null and ids.size() > 0">
                and id in
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectAdvertIdsBySearchValue" resultType="Long">
        select id
        from tb_advert
        <where>
            <if test="searchValue != null and searchValue != ''"> and (advert_name like concat('%', #{searchValue}, '%') or id = #{searchValue})</if>
        </where>
    </select>

    <select id="selectAdvertIdNameList" resultMap="AdvertResult">
        select id, advert_name
        from tb_advert
        <where>
            <if test="ids != null and ids.size() > 0">
                id in
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectValidAdvertIdList" resultType="Long">
        select id
        from tb_advert
        <where>
            advert_status = 0 and serving_switch = 1 and stop_serving_date &gt;= curdate() and is_invalid = 0
        </where>
    </select>

    <select id="selectAdvertById" parameterType="Long" resultMap="AdvertResult">
        <include refid="selectAdvertVo"/>
        where id = #{id}
    </select>

    <insert id="insertAdvert" parameterType="com.ruoyi.system.entity.advert.Advert" useGeneratedKeys="true" keyProperty="id">
        insert into tb_advert
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="advertCategory != null">advert_category,</if>
            <if test="advertName != null">advert_name,</if>
            <if test="thumbnailImg != null">thumbnail_img,</if>
            <if test="advertiserId != null">advertiser_id,</if>
            <if test="assessType != null">assess_type,</if>
            <if test="assessCost != null">assess_cost,</if>
            <if test="landpageUrl != null">landpage_url,</if>
            <if test="clickCallbackUrl != null">click_callback_url,</if>
            <if test="dailyBudget != null">daily_budget,</if>
            <if test="startServingDate != null">start_serving_date,</if>
            <if test="stopServingDate != null">stop_serving_date,</if>
            <if test="servingSwitch != null">serving_switch,</if>
            <if test="advertStatus != null">advert_status,</if>
            <if test="extInfo != null">ext_info,</if>
            <if test="operatorId != null">operator_id,</if>
            <if test="operatorName != null">operator_name,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="advertCategory != null">#{advertCategory},</if>
            <if test="advertName != null">#{advertName},</if>
            <if test="thumbnailImg != null">#{thumbnailImg},</if>
            <if test="advertiserId != null">#{advertiserId},</if>
            <if test="assessType != null">#{assessType},</if>
            <if test="assessCost != null">#{assessCost},</if>
            <if test="landpageUrl != null">#{landpageUrl},</if>
            <if test="clickCallbackUrl != null">#{clickCallbackUrl},</if>
            <if test="dailyBudget != null">#{dailyBudget},</if>
            <if test="startServingDate != null">#{startServingDate},</if>
            <if test="stopServingDate != null">#{stopServingDate},</if>
            <if test="servingSwitch != null">#{servingSwitch},</if>
            <if test="advertStatus != null">#{advertStatus},</if>
            <if test="extInfo != null">#{extInfo},</if>
            <if test="operatorId != null">#{operatorId},</if>
            <if test="operatorName != null">#{operatorName},</if>
         </trim>
    </insert>

    <update id="updateAdvert" parameterType="com.ruoyi.system.entity.advert.Advert">
        update tb_advert
        <trim prefix="SET" suffixOverrides=",">
            <if test="advertCategory != null">advert_category = #{advertCategory},</if>
            <if test="advertName != null">advert_name = #{advertName},</if>
            <if test="thumbnailImg != null">thumbnail_img = #{thumbnailImg},</if>
            <if test="advertiserId != null">advertiser_id = #{advertiserId},</if>
            <if test="assessType != null">assess_type = #{assessType},</if>
            <if test="assessCost != null">assess_cost = #{assessCost},</if>
            <if test="landpageUrl != null">landpage_url = #{landpageUrl},</if>
            <if test="clickCallbackUrl != null">click_callback_url = #{clickCallbackUrl},</if>
            <if test="startServingDate != null">start_serving_date = #{startServingDate},</if>
            <if test="stopServingDate != null">stop_serving_date = #{stopServingDate},</if>
            <if test="servingSwitch != null">serving_switch = #{servingSwitch},</if>
            <if test="advertStatus != null">advert_status = #{advertStatus},</if>
            <if test="extInfo != null">ext_info = #{extInfo},</if>
            <if test="operatorId != null">operator_id = #{operatorId},</if>
            <if test="operatorName != null">operator_name = #{operatorName},</if>
            daily_budget = #{dailyBudget},
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>

    <update id="updateLandPageUrl" >
        update tb_advert
        <trim prefix="SET" suffixOverrides=",">
            <if test="landpageUrl != null">landpage_url = #{landpageUrl},</if>
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>

    <update id="updateAdvertStatus" parameterType="com.ruoyi.system.entity.advert.Advert">
        update tb_advert
        <trim prefix="SET" suffixOverrides=",">
            <if test="servingSwitch != null">serving_switch = #{servingSwitch},</if>
            <if test="advertStatus != null">advert_status = #{advertStatus},</if>
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>

    <update id="resetAdvertStatus">
        update tb_advert
        <trim prefix="SET" suffixOverrides=",">
            advert_status = 0,
            gmt_modified = now()
        </trim>
        where advert_status in (2, 3, 5)
            and id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
    </update>

    <select id="selectAdvertIdByAdvertiserIds" resultType="Long">
        select id from tb_advert
        <where>
            advertiser_id in
            <foreach collection="advertiserIds" item="advertiserId" open="(" separator="," close=")">
                #{advertiserId}
            </foreach>
            and is_invalid = 0
        </where>
    </select>

    <update id="invalidateAdvert">
        update tb_advert set is_invalid = 1
        where id = #{id}
        limit 1
    </update>

    <update id="disableAdvertByAdvertiserId">
        update tb_advert
        set serving_switch = 0, advert_status = 4
        where advertiser_id = #{advertiserId} and is_invalid = 0
    </update>

    <update id="enableAdvertByAdvertiserId">
        update tb_advert
        set advert_status = 0
        where advertiser_id = #{advertiserId} and advert_status = 4 and is_invalid = 0
    </update>

    <select id="selectAllOpenOrCloseAdvertNameByAdvertiserId" resultMap="AdvertResult">
        select id,advert_name from tb_advert
        where advertiser_id = #{advertiserId} and serving_switch = #{status} and is_invalid = 0
    </select>

    <update id="updateSwitchByIds">
        update tb_advert set serving_switch = #{servingSwitch}
        where id in
        <foreach collection="ids" open="(" item="id" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <select id="selectAdvertIdByAdvertiserIdAndStatus" resultType="Long">
        select id
        from tb_advert
        <where>
            advertiser_id = #{advertiserId}
            and is_invalid = 0
            and advert_status in
            <foreach collection="statusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </where>
    </select>
</mapper>
