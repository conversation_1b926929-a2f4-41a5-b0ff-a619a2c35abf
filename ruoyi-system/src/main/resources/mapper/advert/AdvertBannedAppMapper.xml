<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.advert.AdvertBannedAppMapper">

    <resultMap type="com.ruoyi.system.domain.advert.AdvertBannedApp" id="AdvertBannedAppResult">
        <result property="id"    column="id"    />
        <result property="advertId"    column="advert_id"    />
        <result property="orientId"    column="orient_Id"    />
        <result property="appId"    column="app_id"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectAdvertAppRelationVo">
        select id, advert_id, orient_Id, app_id, gmt_create, gmt_modified from tb_advert_banned_app
    </sql>

    <select id="selectListByOrientId" parameterType="Long" resultType="com.ruoyi.system.bo.advert.AdvertBannedAppBo">
        select b.advert_id as advertId, b.orient_Id as orientId, b.app_id as appId, a.app_name as appName
        from tb_advert_banned_app b
        left join tb_app a on b.app_id = a.id
        where b.orient_Id = #{orientId}
    </select>

    <select id="selectByOrientId" parameterType="Long" resultMap="AdvertBannedAppResult">
        <include refid="selectAdvertAppRelationVo"/>
        where orient_Id = #{orientId}
    </select>

    <select id="selectByAppId" parameterType="Long" resultMap="AdvertBannedAppResult">
        <include refid="selectAdvertAppRelationVo"/>
        where app_id = #{appId}
    </select>

    <select id="selectByAdvertIds" parameterType="Long" resultMap="AdvertBannedAppResult">
        <include refid="selectAdvertAppRelationVo"/>
        <where>
            advert_id in
            <foreach collection="advertIds" item="advertId" open="(" separator="," close=")">
                #{advertId}
            </foreach>
        </where>
    </select>

    <select id="selectByAdvertId" parameterType="Long" resultMap="AdvertBannedAppResult">
        <include refid="selectAdvertAppRelationVo"/>
        <where>
            advert_id =#{advertId}
        </where>
    </select>

    <select id="selectListByAdvertId" parameterType="Long" resultType="com.ruoyi.system.bo.advert.AdvertBannedAppBo">
        select b.advert_id as advertId, b.orient_Id as orientId, b.app_id as appId, a.app_name as appName
        from tb_advert_banned_app b
        left join tb_app a on b.app_id = a.id
        where b.advert_id = #{advertId}
    </select>

    <delete id="deleteByOrientId" parameterType="Long">
        delete from tb_advert_banned_app where orient_id = #{orientId}
    </delete>

    <insert id="batchInsert">
        insert into tb_advert_banned_app(advert_id, orient_id, app_id) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.advertId},#{item.orientId},#{item.appId})
        </foreach>
    </insert>

</mapper>
