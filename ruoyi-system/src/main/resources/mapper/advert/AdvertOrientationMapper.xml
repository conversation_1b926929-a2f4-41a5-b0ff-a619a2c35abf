<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.manager.AdvertOrientationMapper">

    <resultMap type="com.ruoyi.system.entity.advert.AdvertOrientation" id="AdvertOrientationResult">
        <result property="id"    column="id"    />
        <result property="orientName"    column="orient_name"    />
        <result property="advertId"    column="advert_id"    />
        <result property="servingSwitch"    column="serving_switch"    />
        <result property="landpageType"    column="landpage_type"    />
        <result property="landpageUrl"    column="landpage_url"    />
        <result property="chargeType"    column="charge_type"    />
        <result property="unitPrice"    column="unit_price"    />
        <result property="milliUnitPrice"    column="milli_unit_price"    />
        <result property="ocpcConvType"    column="ocpc_conv_type"    />
        <result property="ocpcConvCost"    column="ocpc_conv_cost"    />
        <result property="dailyBudget"    column="daily_budget"    />
        <result property="areaTarget"    column="area_target"    />
        <result property="deviceTarget"    column="device_target"    />
        <result property="flowTarget"    column="flow_target"    />
        <result property="osTarget"    column="os_target"    />
        <result property="ispTarget"    column="isp_target"    />
        <result property="servingHour"    column="serving_hour"    />
        <result property="platform"    column="platform"    />
        <result property="isDefault"    column="is_default"    />
        <result property="weight"    column="weight"    />
        <result property="operatorId"    column="operator_id"    />
        <result property="operatorName"    column="operator_name"    />
        <result property="isDeleted"    column="is_deleted"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectAdvertOrientationVo">
        select id, orient_name, advert_id, serving_switch, landpage_type, landpage_url, charge_type,
               unit_price, ifnull(milli_unit_price, unit_price * 100) as milli_unit_price,
               ocpc_conv_type, ocpc_conv_cost, daily_budget, area_target, isp_target,
               device_target, flow_target, os_target, serving_hour, platform, is_default, weight,
               operator_id, operator_name, is_deleted, gmt_create, gmt_modified
        from tb_advert_orientation
    </sql>

    <select id="selectDefaultOrientationByAdvertId" parameterType="Long" resultMap="AdvertOrientationResult">
        <include refid="selectAdvertOrientationVo"/>
        <where>
            advert_id = #{advertId} and is_default = 1
        </where>
        LIMIT 1
    </select>

    <select id="selectIdsByAdvertId" parameterType="Long" resultType="Long">
        select id
        from tb_advert_orientation
        <where>
            advert_id = #{advertId} and is_deleted = 0
        </where>
    </select>

    <select id="selectAdvertOrientationList" parameterType="com.ruoyi.system.entity.advert.AdvertOrientation" resultMap="AdvertOrientationResult">
        <include refid="selectAdvertOrientationVo"/>
        <where>
            is_deleted = 0
            <if test="advertId != null"> and advert_id = #{advertId}</if>
            <if test="servingSwitch != null"> and serving_switch = #{servingSwitch}</if>
            <if test="landpageUrl != null and landpageUrl != ''"> and landpage_url like concat('%', #{landpageUrl}, '%')</if>
            <if test="chargeType != null"> and charge_type = #{chargeType}</if>
            <if test="unitPrice != null"> and unit_price = #{unitPrice}</if>
            <if test="dailyBudget != null"> and daily_budget = #{dailyBudget}</if>
            <if test="servingHour != null"> and serving_hour = #{servingHour}</if>
            <if test="platform != null and platform != ''"> and platform = #{platform}</if>
            <if test="isDefault != null "> and is_default = #{isDefault}</if>
            <if test="operatorId != null "> and operator_id = #{operatorId}</if>
            <if test="operatorName != null and operatorName != ''"> and operator_name like concat('%', #{operatorName}, '%')</if>
            <if test="gmtCreateStart != null"> and gmt_create &gt;= #{gmtCreateStart}</if>
            <if test="gmtCreateEnd != null"> and gmt_create &lt;= #{gmtCreateEnd}</if>
            <if test="orientSearch != null and orientSearch != ''"> and (orient_name like concat('%', #{orientSearch}, '%') or id = #{orientSearch})</if>
            <if test="advertIds != null and advertIds.size() != 0">
                and advert_id in
                <foreach collection="advertIds" item="advertId" open="(" separator="," close=")">
                    #{advertId}
                </foreach>
            </if>
            <if test="ids != null and ids.size() != 0">
                and id in
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectIdsWithAreaTargetByAdvertIds" resultType="Long">
        select id
        from tb_advert_orientation
        <where>
            area_target != '[]'
            and is_deleted = 0
            <if test="advertIds != null and advertIds.size() != 0">
                and advert_id in
                <foreach collection="advertIds" item="advertId" open="(" separator="," close=")">
                    #{advertId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectMaxMilliUnitPriceByAdvertId" parameterType="Long" resultType="Integer">
        select ifnull(max(ifnull(milli_unit_price, unit_price * 100)), 0)
        from tb_advert_orientation
        <where>
            advert_id = #{advertId} and serving_switch = 1 and is_deleted = 0
        </where>
    </select>

    <select id="selectAdvertOrientationById" parameterType="Long" resultMap="AdvertOrientationResult">
        <include refid="selectAdvertOrientationVo"/>
        where id = #{id}
    </select>

    <insert id="insertAdvertOrientation" parameterType="com.ruoyi.system.entity.advert.AdvertOrientation" useGeneratedKeys="true" keyProperty="id">
        insert into tb_advert_orientation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orientName != null">orient_name,</if>
            <if test="advertId != null">advert_id,</if>
            <if test="servingSwitch != null">serving_switch,</if>
            <if test="landpageType != null">landpage_type,</if>
            <if test="landpageUrl != null and landpageUrl != ''">landpage_url,</if>
            <if test="weight != null">weight,</if>
            <if test="chargeType != null">charge_type,</if>
            <if test="unitPrice != null">unit_price,</if>
            <if test="milliUnitPrice != null">milli_unit_price,</if>
            <if test="ocpcConvType != null">ocpc_conv_type,</if>
            <if test="ocpcConvCost != null">ocpc_conv_cost,</if>
            <if test="dailyBudget != null">daily_budget,</if>
            <if test="areaTarget != null">area_target,</if>
            <if test="deviceTarget != null">device_target,</if>
            <if test="flowTarget != null">flow_target,</if>
            <if test="osTarget != null">os_target,</if>
            <if test="ispTarget != null">isp_target,</if>
            <if test="servingHour != null">serving_hour,</if>
            <if test="platform != null and platform != ''">platform,</if>
            <if test="isDefault != null">is_default,</if>
            <if test="operatorId != null">operator_id,</if>
            <if test="operatorName != null">operator_name,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orientName != null">#{orientName},</if>
            <if test="advertId != null">#{advertId},</if>
            <if test="servingSwitch != null">#{servingSwitch},</if>
            <if test="landpageType != null">#{landpageType},</if>
            <if test="landpageUrl != null and landpageUrl != ''">#{landpageUrl},</if>
            <if test="weight != null">#{weight},</if>
            <if test="chargeType != null">#{chargeType},</if>
            <if test="unitPrice != null">#{unitPrice},</if>
            <if test="milliUnitPrice != null">#{milliUnitPrice},</if>
            <if test="ocpcConvType != null">#{ocpcConvType},</if>
            <if test="ocpcConvCost != null">#{ocpcConvCost},</if>
            <if test="dailyBudget != null">#{dailyBudget},</if>
            <if test="areaTarget != null">#{areaTarget},</if>
            <if test="deviceTarget != null">#{deviceTarget},</if>
            <if test="flowTarget != null">#{flowTarget},</if>
            <if test="osTarget != null">#{osTarget},</if>
            <if test="ispTarget != null">#{ispTarget},</if>
            <if test="servingHour != null">#{servingHour},</if>
            <if test="platform != null and platform != ''">#{platform},</if>
            <if test="isDefault != null">#{isDefault},</if>
            <if test="operatorId != null">#{operatorId},</if>
            <if test="operatorName != null">#{operatorName},</if>
         </trim>
    </insert>

    <update id="updateAdvertOrientation" parameterType="com.ruoyi.system.entity.advert.AdvertOrientation">
        update tb_advert_orientation
        <trim prefix="SET" suffixOverrides=",">
            <if test="orientName != null">orient_name = #{orientName},</if>
            <if test="landpageType != null">landpage_type = #{landpageType},</if>
            <if test="landpageUrl != null">landpage_url = #{landpageUrl},</if>
            <if test="chargeType != null">charge_type = #{chargeType},</if>
            <if test="unitPrice != null">unit_price = #{unitPrice},</if>
            <if test="milliUnitPrice != null">milli_unit_price = #{milliUnitPrice},</if>
            <if test="ocpcConvType != null">ocpc_conv_type = #{ocpcConvType},</if>
            <if test="ocpcConvCost != null">ocpc_conv_cost = #{ocpcConvCost},</if>
            <if test="areaTarget != null">area_target = #{areaTarget},</if>
            <if test="deviceTarget != null">device_target = #{deviceTarget},</if>
            <if test="flowTarget != null">flow_target = #{flowTarget},</if>
            <if test="osTarget != null">os_target = #{osTarget},</if>
            <if test="ispTarget != null">isp_target = #{ispTarget},</if>
            <if test="servingHour != null">serving_hour = #{servingHour},</if>
            <if test="platform != null">platform = #{platform},</if>
            <if test="weight != null">weight = #{weight},</if>
            <if test="servingSwitch != null">serving_switch = #{servingSwitch},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateWithDailyBudget" parameterType="com.ruoyi.system.entity.advert.AdvertOrientation">
        update tb_advert_orientation
        <trim prefix="SET" suffixOverrides=",">
            <if test="orientName != null">orient_name = #{orientName},</if>
            <if test="landpageType != null">landpage_type = #{landpageType},</if>
            <if test="landpageUrl != null">landpage_url = #{landpageUrl},</if>
            <if test="chargeType != null">charge_type = #{chargeType},</if>
            <if test="unitPrice != null">unit_price = #{unitPrice},</if>
            <if test="milliUnitPrice != null">milli_unit_price = #{milliUnitPrice},</if>
            <if test="ocpcConvType != null">ocpc_conv_type = #{ocpcConvType},</if>
            <if test="ocpcConvCost != null">ocpc_conv_cost = #{ocpcConvCost},</if>
            <if test="areaTarget != null">area_target = #{areaTarget},</if>
            <if test="deviceTarget != null">device_target = #{deviceTarget},</if>
            <if test="flowTarget != null">flow_target = #{flowTarget},</if>
            <if test="osTarget != null">os_target = #{osTarget},</if>
            <if test="ispTarget != null">isp_target = #{ispTarget},</if>
            <if test="servingHour != null">serving_hour = #{servingHour},</if>
            <if test="platform != null">platform = #{platform},</if>
            <if test="weight != null">weight = #{weight},</if>
            daily_budget = #{dailyBudget},
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>

    <update id="deleteAdvertOrientation" parameterType="Long">
        update tb_advert_orientation
        set is_deleted = 1
        where id = #{id}
    </update>

    <update id="batchUpdateAreaTarget">
        update tb_advert_orientation
        <trim prefix="SET" suffixOverrides=",">
            area_target = #{areaTarget},
            gmt_modified = now()
        </trim>
        <where>
            id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </update>
    <update id="batchUpdateAdvertOrientation">
        update tb_advert_orientation
        <trim prefix="SET" suffixOverrides=",">
            <if test="areaTarget!=null">area_target=#{areaTarget},</if>
            <if test="deviceTarget!=null">device_target=#{deviceTarget},</if>
            <if test="ispTarget!=null">isp_target=#{ispTarget},</if>
            <if test="osTarget!=null">os_target=#{osTarget},</if>
            <if test="flowTarget!=null">flow_target=#{flowTarget},</if>
            <if test="servingHour!=null">serving_hour=#{servingHour},</if>
            gmt_modified = now()
        </trim>
        <where>
            id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </update>

    <select id="selectOrientCountByAdvertIds" resultType="com.ruoyi.system.bo.advert.AdvertOrientCountBo">
        select advert_id as advertId, count(1) as count
        from tb_advert_orientation
        <where>
            is_deleted = 0
            <if test="advertIds != null and advertIds.size() != 0">
                and advert_id in
                <foreach collection="advertIds" item="advertId" open="(" separator="," close=")">
                    #{advertId}
                </foreach>
            </if>
        </where>
        group by advert_id
    </select>

    <select id="selectAdvertIdsByOrientIds" resultType="Long">
        select distinct advert_id
        from tb_advert_orientation
        <where>
            <if test="ids != null and ids.size() != 0">
                and id in
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectOrientBannedOrientIds" resultType="Long">
        select orient_id from tb_advert_oriente_app
        <where>
            <if test="advertIds != null and advertIds.size() != 0">
                and advert_id in
                <foreach collection="advertIds" item="advertId" open="(" separator="," close=")">
                    #{advertId}
                </foreach>
            </if>
        </where>
        union all
        select orient_id from tb_advert_banned_app
        <where>
            <if test="advertIds != null and advertIds.size() != 0">
                and advert_id in
                <foreach collection="advertIds" item="advertId" open="(" separator="," close=")">
                    #{advertId}
                </foreach>
            </if>
        </where>
        union all
        select orient_id from tb_advert_orient_traffic
        <where>
            <if test="advertIds != null and advertIds.size() != 0">
                and advert_id in
                <foreach collection="advertIds" item="advertId" open="(" separator="," close=")">
                    #{advertId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectLandpageByAdvertIds" resultType="com.ruoyi.system.bo.advert.AdvertOrientLandpageBo">
        select `advert_id` as advertId, o.id as orientId, (CASE o.`landpage_type` WHEN 2 THEN o.`landpage_url` ELSE a.`landpage_url` END) as landpageUrl
        from `tb_advert_orientation` o
        LEFT JOIN `tb_advert` a on o.`advert_id` = a.id
        <where>
            <if test="advertIds != null and advertIds.size() != 0">
                and advert_id in
                <foreach collection="advertIds" item="advertId" open="(" separator="," close=")">
                    #{advertId}
                </foreach>
            </if>
        </where>

        union all

        select `advert_id` as advertId, 0 as orientId, (CASE o.`landpage_type` WHEN 2 THEN o.`landpage_url` ELSE a.`landpage_url` END) as landpageUrl
        from `tb_advert_orientation` o
        LEFT JOIN `tb_advert` a on o.`advert_id` = a.id
        <where>
            `is_default` = 1
            <if test="advertIds != null and advertIds.size() != 0">
                and advert_id in
                <foreach collection="advertIds" item="advertId" open="(" separator="," close=")">
                    #{advertId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectByLandpage" resultType="com.ruoyi.system.bo.advert.AdvertOrientLandpageBo">
        select o.advert_id                                                     as advertId,
               a.advert_name                                                   as advertName,
               o.id                                                            as orientId,
               o.orient_name                                                   as orientName,
               (IF(o.`landpage_type` = 2, o.`landpage_url`, a.`landpage_url`)) as landpageUrl
        FROM `tb_advert_orientation` o
            LEFT JOIN tb_advert a on o.`advert_id` = a.`id`
        where o.`landpage_url` like concat('%', #{landpageUrl}, '%')
           or (o.`landpage_type` = 2 and a.landpage_url like concat('%', #{landpageUrl}, '%'))
        order by advertId desc, orientId desc
    </select>
</mapper>
