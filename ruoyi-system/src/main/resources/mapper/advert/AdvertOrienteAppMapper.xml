<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.advert.AdvertOrienteAppMapper">

    <resultMap type="com.ruoyi.system.domain.advert.AdvertOrienteApp" id="AdvertOrienteAppResult">
        <result property="id"    column="id"    />
        <result property="advertId"    column="advert_id"    />
        <result property="orientId"    column="orient_Id"    />
        <result property="appId"    column="app_id"    />
        <result property="orienteSlotIds"    column="oriente_slot_ids"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectAdvertAppRelationVo">
        select id, advert_id, orient_Id, app_id,oriente_slot_ids, gmt_create, gmt_modified from tb_advert_oriente_app
    </sql>

    <select id="selectListByOrientId" parameterType="Long" resultType="com.ruoyi.system.bo.advert.AdvertOrientAppBo">
        select o.advert_id as advertId, o.orient_Id as orientId, o.app_id as appId, o.oriente_slot_ids as orienteSlotIds, a.app_name as appName
        from tb_advert_oriente_app o
        left join tb_app a on o.app_id = a.id
        where o.orient_Id = #{orientId}
    </select>

    <select id="selectByOrientId" parameterType="Long" resultMap="AdvertOrienteAppResult">
        <include refid="selectAdvertAppRelationVo"/>
        where orient_id = #{orientId}
    </select>

    <select id="selectByAdvertIds" parameterType="Long" resultMap="AdvertOrienteAppResult">
        <include refid="selectAdvertAppRelationVo"/>
        <where>
            advert_id in
            <foreach collection="advertIds" item="advertId" open="(" separator="," close=")">
                #{advertId}
            </foreach>
        </where>
    </select>

    <select id="selectByOrientIds" parameterType="Long" resultMap="AdvertOrienteAppResult">
        <include refid="selectAdvertAppRelationVo"/>
        <where>
            orient_id in
            <foreach collection="orientIds" item="orientId" open="(" separator="," close=")">
                #{orientId}
            </foreach>
        </where>
    </select>

    <select id="selectByAdvertId" parameterType="Long" resultMap="AdvertOrienteAppResult">
        <include refid="selectAdvertAppRelationVo"/>
        <where>
            advert_id = #{advertId}
        </where>
    </select>

    <select id="selectListByAdvertId" parameterType="Long" resultType="com.ruoyi.system.bo.advert.AdvertOrientAppBo">
        select o.advert_id as advertId, o.orient_Id as orientId, o.app_id as appId, o.oriente_slot_ids as orienteSlotIds, a.app_name as appName
        from tb_advert_oriente_app o
        left join tb_app a on o.app_id = a.id
        where o.advert_id = #{advertId}
    </select>

    <delete id="deleteByOrientId" parameterType="Long">
        delete from tb_advert_oriente_app where orient_id = #{orientId}
    </delete>

    <insert id="batchInsert">
        insert into tb_advert_oriente_app(advert_id, orient_id, app_id,oriente_slot_ids) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.advertId},#{item.orientId},#{item.appId},#{item.orienteSlotIds})
        </foreach>
    </insert>

</mapper>
