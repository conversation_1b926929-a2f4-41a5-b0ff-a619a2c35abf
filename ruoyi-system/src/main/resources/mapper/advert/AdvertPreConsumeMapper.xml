<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.advert.AdvertPreConsumeMapper">

    <resultMap type="com.ruoyi.system.entity.advert.AdvertPreConsumeEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="curDate" column="cur_date"/>
            <result property="orientId" column="orient_id"/>
            <result property="milliConsume" column="milli_consume"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            cur_date,
            orient_id,
            milli_consume,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.advert.AdvertPreConsumeEntity">
        INSERT IGNORE INTO tb_advert_pre_consume
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="curDate != null">
                    cur_date,
                </if>
                <if test="orientId != null">
                    orient_id,
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="curDate != null">
                    #{curDate},
                </if>
                <if test="orientId != null">
                    #{orientId},
                </if>
        </trim>
    </insert>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_advert_pre_consume
        WHERE id = #{id}
    </select>

    <select id="selectBy" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_advert_pre_consume
        WHERE cur_date = #{date} and orient_id = #{orientId}
    </select>

    <update id="preConsume">
        UPDATE tb_advert_pre_consume
        SET milli_consume = milli_consume + #{milliConsumeAdd}
        WHERE id = #{id} and milli_consume >= -#{milliConsumeAdd}
    </update>
</mapper>
