<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.advert.AdvertMauRepeatFilterMapper">

    <resultMap type="com.ruoyi.system.entity.advert.AdvertMauRepeatFilterEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="appId" column="app_id"/>
            <result property="slotId" column="slot_id"/>
            <result property="advertId" column="advert_id"/>
            <result property="orientId" column="orient_id"/>
            <result property="consumerId" column="consumer_id"/>
            <result property="openId" column="open_id"/>
            <result property="lastClickTime" column="last_click_time"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            advert_id,
            app_id,
            id,
            last_click_time,
            open_id,
            orient_id,
            slot_id,
            consumer_id,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.advert.AdvertMauRepeatFilterEntity">
        INSERT INTO tb_advert_mau_repeat_filter
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="advertId != null">
                    advert_id,
                </if>
                <if test="appId != null">
                    app_id,
                </if>
                <if test="consumerId != null">
                    consumer_id,
                </if>
                <if test="lastClickTime != null">
                    last_click_time,
                </if>
                <if test="openId != null">
                    open_id,
                </if>
                <if test="orientId != null">
                    orient_id,
                </if>
                <if test="slotId != null">
                    slot_id
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="advertId != null">
                    #{advertId},
                </if>
                <if test="appId != null">
                    #{appId},
                </if>
                <if test="consumerId != null">
                    #{consumerId},
                </if>
                <if test="lastClickTime != null">
                    #{lastClickTime},
                </if>
                <if test="openId != null">
                    #{openId},
                </if>
                <if test="orientId != null">
                    #{orientId},
                </if>
                <if test="slotId != null">
                    #{slotId}
                </if>
        </trim>
        ON DUPLICATE KEY UPDATE
        last_click_time = #{lastClickTime},
        advert_id = #{advertId}
    </insert>

    <update id="updateById" parameterType="com.ruoyi.system.entity.advert.AdvertMauRepeatFilterEntity">
        UPDATE tb_advert_mau_repeat_filter
        <set>
                <if test="advertId != null">
                    advert_id = #{advertId},
                </if>
                <if test="appId != null">
                    app_id = #{appId},
                </if>
                <if test="consumerId != null">
                    consumer_id = #{consumerId},
                </if>
                <if test="lastClickTime != null">
                    last_click_time = #{lastClickTime},
                </if>
                <if test="openId != null">
                    open_id = #{openId},
                </if>
                <if test="orientId != null">
                    orient_id = #{orientId},
                </if>
                <if test="slotId != null">
                    slot_id = #{slotId},
                </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_advert_mau_repeat_filter
        WHERE id = #{id}
    </select>

    <select id="selectByOpenId" parameterType="String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_advert_mau_repeat_filter
        WHERE open_id = #{openId}
    </select>

    <select id="selectList" parameterType="com.ruoyi.system.entity.advert.AdvertMauRepeatFilterEntity" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_advert_mau_repeat_filter
        <where>
            <if test="lastClickTime != null "> and last_click_time &gt;= #{lastClickTime}</if>
            <if test="id != null "> and id &gt;= #{id}</if>
        </where>
        order by id asc
        limit 1000
    </select>
</mapper>
