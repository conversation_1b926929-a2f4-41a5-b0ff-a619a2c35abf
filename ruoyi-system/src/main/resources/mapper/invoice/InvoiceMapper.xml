<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.invoice.InvoiceMapper">

    <resultMap type="com.ruoyi.system.entity.invoice.InvoiceEntity" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="accountId" column="account_id"/>
        <result property="invoiceNumber" column="invoice_number"/>
        <result property="invoiceAmount" column="invoice_amount"/>
        <result property="operatorId" column="operator_id"/>
        <result property="remarkText" column="remark_text"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
            account_id,
            invoice_number,
            invoice_amount,
            operator_id,
            remark_text,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.invoice.InvoiceEntity">
        INSERT INTO tb_invoice
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountId != null">
                account_id,
            </if>
            <if test="invoiceNumber != null">
                invoice_number,
            </if>
            <if test="invoiceAmount != null">
                invoice_amount,
            </if>
            <if test="operatorId != null">
                operator_id,
            </if>
            <if test="remarkText != null">
                remark_text
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountId != null">
                #{accountId},
            </if>
            <if test="invoiceNumber != null">
                #{invoiceNumber},
            </if>
            <if test="invoiceAmount != null">
                #{invoiceAmount},
            </if>
            <if test="operatorId != null">
                #{operatorId},
            </if>
            <if test="remarkText != null">
                #{remarkText}
            </if>
        </trim>
    </insert>

    <delete id="deleteById">
        DELETE
        FROM tb_invoice
        WHERE id = #{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.invoice.InvoiceEntity">
        UPDATE tb_invoice
        <set>
            <if test="invoiceNumber != null">
                invoice_number = #{invoiceNumber},
            </if>
            <if test="invoiceAmount != null">
                invoice_amount = #{invoiceAmount},
            </if>
            <if test="operatorId != null">
                operator_id = #{operatorId},
            </if>
            <if test="remarkText != null">
                remark_text = #{remarkText},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_invoice
        WHERE id = #{id}
    </select>
    <select id="sumInvoice" resultType="com.ruoyi.system.bo.invoice.InvoiceSumBO">
        SELECT account_id as accountId ,sum(invoice_amount) as invoiceAmountSum
        FROM tb_invoice
        WHERE account_id in
        <foreach collection="accountIds" open="(" close=")" separator="," item="accountId">
            #{accountId}
        </foreach>
        group by account_id
    </select>
    <select id="getInvoiceInfoListByAccountId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_invoice
        WHERE account_id = #{accountId}
        order by gmt_modified desc
    </select>
    <select id="selectListByGmtModified" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_invoice
        WHERE gmt_modified &gt;= #{startDate} and gmt_modified &lt; #{endDate}
    </select>

    <select id="selectListByAccountIds" resultType="com.ruoyi.system.bo.invoice.InvoiceSumListBO">
        select t.accountId, t.prepayAmount, t.invoiceAmountSum, t.gmtModified, tt.remark_text as remarkText from (
        SELECT
        max(id) as id,
        invoice.account_id as accountId,
        account.prepay_amount as prepayAmount,
        sum( invoice_amount ) AS invoiceAmountSum,
        max(gmt_modified) as gmtModified
        FROM
        tb_invoice invoice
        LEFT JOIN ( SELECT account_id, prepay_amount FROM tb_account_revenue WHERE pay_type = 1 ) AS account ON
        invoice.account_id = account.account_id
        <where>
            <if test="accountIds != null and accountIds.size > 0">
                invoice.account_id in
                <foreach collection="accountIds" item="accountId" separator="," close=")" open="(">
                    #{accountId}
                </foreach>
            </if>
        </where>
        GROUP BY
        invoice.account_id
        <choose>
            <when test="status != null">
                having
                <choose>
                    <when test="status == 1">
                        account.prepay_amount - invoiceAmountSum > 0
                    </when>
                    <when test="status == 2">
                        account.prepay_amount - invoiceAmountSum = 0
                    </when>
                </choose>
            </when>
        </choose>
        ) t
        left join tb_invoice tt on tt.id = t.id
        ORDER BY t.gmtModified desc
    </select>
</mapper>