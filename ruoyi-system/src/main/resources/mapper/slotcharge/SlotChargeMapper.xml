<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.slotcharge.SlotChargeMapper">

    <resultMap type="com.ruoyi.system.entity.slotcharge.SlotChargeEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="curDate" column="cur_date"/>
            <result property="slotId" column="slot_id"/>
            <result property="chargeType" column="charge_type"/>
            <result property="chargePrice" column="charge_price"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            cur_date,
            slot_id,
            charge_type,
            charge_price,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.slotcharge.SlotChargeEntity">
        INSERT INTO tb_slot_charge
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="curDate != null">
                    cur_date,
                </if>
                <if test="slotId != null">
                    slot_id,
                </if>
                <if test="chargeType != null">
                    charge_type,
                </if>
                <if test="chargePrice != null">
                    charge_price
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="curDate != null">
                    #{curDate},
                </if>
                <if test="slotId != null">
                    #{slotId},
                </if>
                <if test="chargeType != null">
                    #{chargeType},
                </if>
                <if test="chargePrice != null">
                    #{chargePrice}
                </if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.ruoyi.system.entity.slotcharge.SlotChargeEntity">
        UPDATE tb_slot_charge
        <set>
                    <if test="curDate != null">
                        cur_date = #{curDate},
                    </if>
                    <if test="slotId != null">
                        slot_id = #{slotId},
                    </if>
                    <if test="chargeType != null">
                        charge_type = #{chargeType},
                    </if>
                    <if test="chargePrice != null">
                        charge_price = #{chargePrice},
                    </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_slot_charge
        WHERE id = #{id}
    </select>

    <select id="selectListBySlotIdsAndDate" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_slot_charge
        where slot_id in
        <foreach collection="slotIds" separator="," item="slotId" close=")" open="(">
            #{slotId}
        </foreach>
        and cur_date = #{date}
    </select>
    <select id="selectBySlotIdAndDate" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_slot_charge
        where slot_id = #{slotId}
        and cur_date = #{date}
    </select>
    <select id="selectListBySlotIdsAndDateList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_slot_charge
        where slot_id in
        <foreach collection="slotIds" open="(" close=")" item="slotId" separator=",">
            #{slotId}
        </foreach>
        and cur_date in
        <foreach collection="dates" open="(" close=")" item="date" separator=",">
            #{date}
        </foreach>
    </select>
    <select id="selectListBySlotIdsAndDateRange" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_slot_charge
        where slot_id in
        <foreach collection="slotIds" open="(" close=")" item="slotId" separator=",">
            #{slotId}
        </foreach>
        <if test="startDate != null and endDate != null">
            and cur_date &gt;= #{startDate} and cur_date &lt;= #{endDate}
        </if>
    </select>

    <select id="selectListByDate" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_slot_charge
        where id > #{id}
        and cur_date = #{date}
        order by id asc
        limit #{pageSize}
    </select>

    <insert id="batchInsertOrUpdate" useGeneratedKeys="true" keyProperty="id">
        insert into tb_slot_charge(slot_id,charge_type,charge_price,cur_date)
        values
        <foreach collection="entities" item="entity" separator=",">
            ( #{entity.slotId},#{entity.chargeType},#{entity.chargePrice}, #{entity.curDate})
        </foreach>
        ON DUPLICATE KEY UPDATE
        charge_type = values(charge_type),
        charge_price = values(charge_price)
    </insert>

</mapper>
