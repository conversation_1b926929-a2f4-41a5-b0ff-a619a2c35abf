<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.slotcharge.SlotChargeOperLogMapper">

    <resultMap type="com.ruoyi.system.entity.slotcharge.SlotChargeOperLogEntity" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="slotId" column="slot_id"/>
        <result property="chargeType" column="charge_type"/>
        <result property="chargePrice" column="charge_price"/>
        <result property="operName" column="oper_name"/>
        <result property="curDate" column="cur_date"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        slot_id,
        charge_type,
        charge_price,
        oper_name,
        cur_date,
        gmt_create,
        gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.slotcharge.SlotChargeOperLogEntity">
        INSERT INTO tb_slot_charge_oper_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="slotId != null">
                slot_id,
            </if>
            <if test="chargeType != null">
                charge_type,
            </if>
            <if test="chargePrice != null">
                charge_price,
            </if>
            <if test="operName != null">
                oper_name,
            </if>
            <if test="curDate != null">
                cur_date
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="slotId != null">
                #{slotId},
            </if>
            <if test="chargeType != null">
                #{chargeType},
            </if>
            <if test="chargePrice != null">
                #{chargePrice},
            </if>
            <if test="operName != null">
                #{operName},
            </if>
            <if test="curDate != null">
                #{curDate}
            </if>
        </trim>
    </insert>


    <select id="selectListBySlotId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_slot_charge_oper_log
        WHERE slot_id = #{slotId}
        order by gmt_create desc
    </select>

</mapper>