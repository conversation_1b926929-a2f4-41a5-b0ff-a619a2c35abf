<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.portal.PortalPublishMapper">

    <resultMap type="com.ruoyi.system.entity.portal.PortalPublishEntity" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="accountId" column="account_id"/>
        <result property="type" column="type"/>
        <result property="content" column="content"/>
        <result property="title" column="title"/>
        <result property="price" column="price"/>
        <result property="img" column="img"/>
        <result property="contacts" column="contacts"/>
        <result property="phone" column="phone"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
            account_id,
            title,
            type,
            content,
            img,
            price,
            contacts,
            phone,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.portal.PortalPublishEntity">
        INSERT INTO tb_portal_publish
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountId != null">
                account_id,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="content != null">
                content,
            </if>
            <if test="title != null">
                title,
            </if>
            <if test="price != null">
                price,
            </if>
            <if test="img != null">
                img,
            </if>
            <if test="contacts != null">
                contacts,
            </if>
            <if test="phone != null">
                phone
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountId != null">
                #{accountId},
            </if>
            <if test="type != null">
                #{type},
            </if>
            <if test="content != null">
                #{content},
            </if>
            <if test="title != null">
                #{title},
            </if>
            <if test="price != null">
                #{price},
            </if>
            <if test="img != null">
                #{img},
            </if>
            <if test="contacts != null">
                #{contacts},
            </if>
            <if test="phone != null">
                #{phone}
            </if>
        </trim>
    </insert>

    <select id="listByType" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_portal_publish
        where type = #{type}
        order by id desc
    </select>


</mapper>