<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.account.AccountPrepayStatementRecordMapper">

    <resultMap type="com.ruoyi.system.entity.account.finance.AccountPrepayStatementRecordEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="accountId" column="account_id"/>
            <result property="prepayRecordId" column="prepay_record_id"/>
            <result property="statementId" column="statement_id"/>
            <result property="amount" column="amount"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            account_id,
            prepay_record_id,
            statement_id,
            amount,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.account.finance.AccountPrepayStatementRecordEntity">
        INSERT INTO tb_account_prepay_statement_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="accountId != null">
                    account_id,
                </if>
                <if test="prepayRecordId != null">
                    prepay_record_id,
                </if>
                <if test="statementId != null">
                    statement_id,
                </if>
                <if test="amount != null">
                    amount
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="accountId != null">
                    #{accountId},
                </if>
                <if test="prepayRecordId != null">
                    #{prepayRecordId},
                </if>
                <if test="statementId != null">
                    #{statementId},
                </if>
                <if test="amount != null">
                    #{amount}
                </if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.ruoyi.system.entity.account.finance.AccountPrepayStatementRecordEntity">
        UPDATE tb_account_prepay_statement_record
        <set>
                    <if test="accountId != null">
                        account_id = #{accountId},
                    </if>
                    <if test="prepayRecordId != null">
                        prepay_record_id = #{prepayRecordId},
                    </if>
                    <if test="statementId != null">
                        statement_id = #{statementId},
                    </if>
                    <if test="amount != null">
                        amount = #{amount},
                    </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_account_prepay_statement_record
        WHERE id = #{id}
    </select>

    <select id="sumAmountByAccountId" resultType="Integer">
        SELECT ifnull(sum(amount), 0)
        FROM tb_account_prepay_statement_record
        WHERE account_id = #{accountId}
    </select>

    <select id="sumAmountByAccountIds" resultType="com.ruoyi.system.bo.publisher.PrepayStatementAmountBo">
        SELECT account_id as accountId, sum(amount) as prepayStatementAmount
        FROM tb_account_prepay_statement_record
        WHERE account_id in
        <foreach collection="accountIds" open="(" separator="," close=")" item="accountId">
            #{accountId}
        </foreach>
        GROUP BY account_id
    </select>

    <select id="sumAmountGroupByPrepayRecordId" resultType="com.ruoyi.system.bo.publisher.PrepayRecordStatementAmountBo">
        SELECT prepay_record_id as prepayRecordId, sum(amount) as prepayStatmentAmount
        FROM tb_account_prepay_statement_record
        WHERE account_id = #{accountId}
        GROUP BY prepay_record_id
    </select>
</mapper>
