<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.account.AccountPrepayApplyRecordMapper">

    <resultMap type="com.ruoyi.system.entity.account.finance.AccountPrepayApplyRecordEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="accountId" column="account_id"/>
            <result property="applyPrepayAmount" column="apply_prepay_amount"/>
            <result property="prepayAmount" column="prepay_amount"/>
            <result property="bankName" column="bank_name"/>
            <result property="bankAccount" column="bank_account"/>
            <result property="bankAccountName" column="bank_account_name"/>
            <result property="invoiceList" column="invoice_list"/>
            <result property="remark" column="remark"/>
            <result property="auditStatus" column="audit_status"/>
            <result property="complexAuditStatus" column="complex_audit_status"/>
            <result property="auditReason" column="audit_reason"/>
            <result property="auditTime" column="audit_time"/>
            <result property="applicantId" column="applicant_id"/>
            <result property="auditorId" column="auditor_id"/>
            <result property="leaderAuditorId" column="leader_auditor_id"/>
            <result property="leaderAuditTime" column="leader_audit_time"/>
            <result property="leaderAuditReason" column="leader_audit_reason"/>
            <result property="ceoAuditorId" column="ceo_auditor_id"/>
            <result property="ceoAuditTime" column="ceo_audit_time"/>
            <result property="ceoAuditReason" column="ceo_audit_reason"/>
            <result property="financeAuditorId" column="finance_auditor_id"/>
            <result property="financeAuditTime" column="finance_audit_time"/>
            <result property="financeAuditReason" column="finance_audit_reason"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
            <result property="prepaySubjectList" column="prepay_subject_list"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            account_id,
            apply_prepay_amount,
            prepay_amount,
            bank_name,
            bank_account,
            bank_account_name,
            invoice_list,
            remark,
            audit_status,
            complex_audit_status,
            audit_reason,
            audit_time,
            applicant_id,
            auditor_id,
            leader_auditor_id,
            leader_audit_time,
            leader_audit_reason,
            ceo_auditor_id,
            ceo_audit_time,
            ceo_audit_reason,
            finance_auditor_id,
            finance_audit_time,
            finance_audit_reason,
            gmt_create,
            gmt_modified,
            prepay_subject_list
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.account.finance.AccountPrepayApplyRecordEntity">
        INSERT INTO tb_account_prepay_apply_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="accountId != null">
                    account_id,
                </if>
                <if test="applyPrepayAmount != null">
                    apply_prepay_amount,
                </if>
                <if test="prepayAmount != null">
                    prepay_amount,
                </if>
                <if test="bankName != null">
                    bank_name,
                </if>
                <if test="bankAccount != null">
                    bank_account,
                </if>
                <if test="bankAccountName != null">
                    bank_account_name,
                </if>
                <if test="invoiceList != null">
                    invoice_list,
                </if>
                <if test="remark != null">
                    remark,
                </if>
                <if test="auditStatus != null">
                    audit_status,
                </if>
                <if test="applicantId != null">
                    applicant_id,
                </if>
                <if test="complexAuditStatus != null">
                    complex_audit_status,
                </if>
                <if test="prepaySubjectList != null">
                    prepay_subject_list,
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="accountId != null">
                    #{accountId},
                </if>
                <if test="applyPrepayAmount != null">
                    #{applyPrepayAmount},
                </if>
                <if test="prepayAmount != null">
                    #{prepayAmount},
                </if>
                <if test="bankName != null">
                    #{bankName},
                </if>
                <if test="bankAccount != null">
                    #{bankAccount},
                </if>
                <if test="bankAccountName != null">
                    #{bankAccountName},
                </if>
                <if test="invoiceList != null">
                    #{invoiceList},
                </if>
                <if test="remark != null">
                    #{remark},
                </if>
                <if test="auditStatus != null">
                    #{auditStatus},
                </if>
                <if test="applicantId != null">
                    #{applicantId},
                </if>
                <if test="complexAuditStatus != null">
                    #{complexAuditStatus},
                </if>
                <if test="prepaySubjectList != null">
                    #{prepaySubjectList},
                </if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.ruoyi.system.entity.account.finance.AccountPrepayApplyRecordEntity">
        UPDATE tb_account_prepay_apply_record
        <set>
                    <if test="accountId != null">
                        account_id = #{accountId},
                    </if>
                    <if test="applyPrepayAmount != null">
                        apply_prepay_amount = #{applyPrepayAmount},
                    </if>
                    <if test="prepayAmount != null">
                        prepay_amount = #{prepayAmount},
                    </if>
                    <if test="bankName != null">
                        bank_name = #{bankName},
                    </if>
                    <if test="bankAccount != null">
                        bank_account = #{bankAccount},
                    </if>
                    <if test="bankAccountName != null">
                        bank_account_name = #{bankAccountName},
                    </if>
                    <if test="invoiceList != null">
                        invoice_list = #{invoiceList},
                    </if>
                    <if test="remark != null">
                        remark = #{remark},
                    </if>
                    <if test="auditStatus != null">
                        audit_status = #{auditStatus},
                    </if>
                    <if test="auditReason != null">
                        audit_reason = #{auditReason},
                    </if>
                    <if test="auditTime != null">
                        audit_time = #{auditTime},
                    </if>
                    <if test="auditorId != null">
                        auditor_id = #{auditorId},
                    </if>
                    <if test="complexAuditStatus != null">
                        complex_audit_status = #{complexAuditStatus},
                    </if>
                    <if test="leaderAuditorId != null">
                        leader_auditor_id = #{leaderAuditorId},
                    </if>
                    <if test="leaderAuditTime != null">
                        leader_audit_time = #{leaderAuditTime},
                    </if>
                    <if test="leaderAuditReason != null">
                        leader_audit_reason = #{leaderAuditReason},
                    </if>
                    <if test="ceoAuditorId != null">
                        ceo_auditor_id = #{ceoAuditorId},
                    </if>
                    <if test="ceoAuditTime != null">
                        ceo_audit_time = #{ceoAuditTime},
                    </if>
                    <if test="ceoAuditReason != null">
                        ceo_audit_reason = #{ceoAuditReason},
                    </if>
                    <if test="financeAuditorId != null">
                        finance_auditor_id = #{financeAuditorId},
                    </if>
                    <if test="financeAuditTime != null">
                        finance_audit_time = #{financeAuditTime},
                    </if>
                    <if test="financeAuditReason != null">
                        finance_audit_reason = #{financeAuditReason},
                    </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_account_prepay_apply_record
        WHERE id = #{id}
    </select>

    <select id="selectList" resultMap="BaseResultMap" parameterType="com.ruoyi.system.req.publisher.prepay.PrepayRecordListParam">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_account_prepay_apply_record
        <where>
            <if test="startDate != null "> and gmt_create &gt;= #{startDate}</if>
            <if test="endDate != null "> and gmt_create &lt;= #{endDate}</if>
            <if test="auditStatus != null"> and audit_status = #{auditStatus}</if>
            <if test="accountIds != null and accountIds.size() > 0">
                and account_id in
                <foreach collection="accountIds" item="accountId" open="(" separator="," close=")">
                    #{accountId}
                </foreach>
            </if>
        </where>
        order by gmt_create desc
    </select>

    <select id="selectListByAccountIdAndAuditStatus" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_account_prepay_apply_record
        <where>
            <if test="accountId != null"> and account_id = #{accountId}</if>
            <if test="auditStatus != null"> and audit_status = #{auditStatus}</if>
        </where>
        ORDER BY ID
    </select>

    <select id="countByAccountIdAndAuditStatus" resultType="Integer">
        SELECT count(1)
        FROM tb_account_prepay_apply_record
        <where>
            <if test="accountId != null"> and account_id = #{accountId}</if>
            <if test="auditStatus != null"> and audit_status = #{auditStatus}</if>
        </where>
    </select>
</mapper>
