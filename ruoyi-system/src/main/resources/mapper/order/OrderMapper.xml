<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.order.OrderMapper">

    <resultMap type="com.ruoyi.system.domain.order.Order" id="OrderResult">
        <result property="id"    column="id"    />
        <result property="orderId"    column="order_id"    />
        <result property="consumerId"    column="consumer_id"    />
        <result property="appId"    column="app_id"    />
        <result property="slotId"    column="slot_id"    />
        <result property="activityId"    column="activity_id"    />
        <result property="advertId"    column="advert_id"    />
        <result property="orientId"    column="orient_id"    />
        <result property="materialId"    column="material_id"    />
        <result property="adSnapshot"    column="ad_snapshot"    />
        <result property="adStat"    column="ad_stat"    />
        <result property="gmtCreate"    column="gmt_create"    />
    </resultMap>

    <select id="selectByConsumerIdAndDate" parameterType="com.ruoyi.system.domain.order.Order" resultMap="OrderResult">
        select id, order_id, consumer_id, app_id, slot_id, activity_id,
        advert_id, orient_id, material_id, ad_snapshot, ad_stat, gmt_create
        from tb_order_${tbSuffix}
        where consumer_id = #{consumerId}
        <if test="gmtCreate != null "> and gmt_create &gt;= #{gmtCreate} </if>
        order by gmt_create desc
    </select>

    <select id="selectByOrderId" parameterType="com.ruoyi.system.domain.order.Order" resultMap="OrderResult">
        select id, order_id, consumer_id, app_id, slot_id, activity_id,
        advert_id, orient_id, material_id, ad_snapshot, ad_stat, gmt_create
        from tb_order_${tbSuffix}
        where order_id = #{orderId}
    </select>

    <insert id="insertOrder" parameterType="com.ruoyi.system.domain.order.Order"
            useGeneratedKeys="true" keyProperty="id">
        insert into tb_order_${tbSuffix}
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">order_id,</if>
            <if test="consumerId != null">consumer_id,</if>
            <if test="appId != null and appId != ''">app_id,</if>
            <if test="slotId != null">slot_id,</if>
            <if test="activityId != null">activity_id,</if>
            <if test="advertId != null">advert_id,</if>
            <if test="orderId != null">orient_id,</if>
            <if test="materialId != null">material_id,</if>
            <if test="adSnapshot != null">ad_snapshot,</if>
            <if test="adStat != null">ad_stat,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">#{orderId},</if>
            <if test="consumerId != null">#{consumerId},</if>
            <if test="appId != null and appId != ''">#{appId},</if>
            <if test="slotId != null">#{slotId},</if>
            <if test="activityId != null">#{activityId},</if>
            <if test="advertId != null">#{advertId},</if>
            <if test="orderId != null">#{orientId},</if>
            <if test="materialId != null">#{materialId},</if>
            <if test="adSnapshot != null">#{adSnapshot},</if>
            <if test="adStat != null">#{adStat},</if>
         </trim>
    </insert>

    <update id="updateOrder" parameterType="com.ruoyi.system.domain.order.Order">
        update tb_order_${tbSuffix}
        <trim prefix="SET" suffixOverrides=",">
            <if test="adSnapshot != null">ad_snapshot = #{adSnapshot},</if>
            <if test="adStat != null">ad_stat = #{adStat},</if>
            gmt_modified = now()
        </trim>
        where order_id = #{orderId}
    </update>

    <select id="selectFirstOrderIdByConsumerIdAndDate" parameterType="com.ruoyi.system.domain.order.Order" resultType="String">
        select order_id
        from tb_order_${tbSuffix}
        where consumer_id = #{consumerId} and gmt_create &gt;= #{gmtCreate}
        order by gmt_create asc
        limit 1
    </select>
</mapper>
