<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.blindbox.BlindBoxLandpageRecordMapper">
    <resultMap type="com.ruoyi.system.entity.blindbox.BlindBoxLandpageRecordEntity" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="orderId" column="order_id"/>
        <result property="advertId" column="advert_id"/>
        <result property="activityId" column="activity_id"/>
        <result property="consumerId" column="consumer_id"/>
        <result property="landpageKey" column="landpage_key"/>
        <result property="referer" column="referer"/>
        <result property="ip" column="ip"/>
        <result property="appId" column="app_id"/>
        <result property="slotId" column="slot_id"/>
        <result property="phone" column="phone"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
            order_id,
            advert_id,
            activity_id,
            consumer_id,
            landpage_key,
            referer,
            app_id,
            slot_id,
            phone,
            ip,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.blindbox.BlindBoxLandpageRecordEntity">
        INSERT INTO tb_blind_box_landpage_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">
                order_id,
            </if>
            <if test="advertId != null">
                advert_id,
            </if>
            <if test="activityId != null">
                activity_id,
            </if>
            <if test="consumerId != null">
                consumer_id,
            </if>
            <if test="landpageKey != null">
                landpage_key,
            </if>
            <if test="referer != null">
                referer,
            </if>
            <if test="appId != null">
                app_id,
            </if>
            <if test="slotId != null">
                slot_id,
            </if>
            <if test="ip != null">
                ip,
            </if>
            <if test="phone != null">
                phone
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">
                #{orderId},
            </if>
            <if test="advertId != null">
                #{advertId},
            </if>
            <if test="activityId != null">
                #{activityId},
            </if>
            <if test="consumerId != null">
                #{consumerId},
            </if>
            <if test="landpageKey != null">
                #{landpageKey},
            </if>
            <if test="referer != null">
                #{referer},
            </if>
            <if test="appId != null">
                #{appId},
            </if>
            <if test="slotId != null">
                #{slotId},
            </if>
            <if test="ip != null">
                #{ip},
            </if>
            <if test="phone != null">
                #{phone}
            </if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.ruoyi.system.entity.blindbox.BlindBoxLandpageRecordEntity">
        UPDATE tb_blind_box_landpage_record
        <set>
            <if test="orderId != null">
                order_id = #{orderId},
            </if>
            <if test="advertId != null">
                advert_id = #{advertId},
            </if>
            <if test="activityId != null">
                activity_id = #{activityId},
            </if>
            <if test="consumerId != null">
                consumer_id = #{consumerId},
            </if>
            <if test="landpageKey != null">
                landpage_key = #{landpageKey},
            </if>
            <if test="referer != null">
                referer = #{referer},
            </if>
            <if test="appId != null">
                app_id = #{appId},
            </if>
            <if test="slotId != null">
                slot_id = #{slotId},
            </if>
            <if test="ip != null">
                ip = #{ip},
            </if>
            <if test="phone != null">
                phone = #{phone},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_blind_box_landpage_record
        WHERE id = #{id}
    </select>
</mapper>
