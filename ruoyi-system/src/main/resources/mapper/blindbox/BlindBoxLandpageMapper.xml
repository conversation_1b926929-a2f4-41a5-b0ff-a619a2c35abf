<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.blindbox.BlindBoxLandpageMapper">
    <resultMap type="com.ruoyi.system.entity.blindbox.BlindBoxLandpageEntity" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="landpageKey" column="landpage_key"/>
        <result property="targetLandpage" column="target_landpage"/>
        <result property="footer" column="footer"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
            landpage_key,
            target_landpage,
            footer,
            gmt_create,
            gmt_modified
    </sql>

    <select id="selectList"  parameterType="com.ruoyi.system.entity.blindbox.BlindBoxLandpageEntity" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_blind_box_landpage
        <where>
            <if test="landpageKey != null">and landpage_key = #{landpageKey}</if>
            <if test="targetLandpage != null  and targetLandpage != ''"> and target_landpage like concat('%', #{targetLandpage}, '%')</if>
        </where>
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.blindbox.BlindBoxLandpageEntity">
        INSERT INTO tb_blind_box_landpage
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="landpageKey != null">
                landpage_key,
            </if>
            <if test="targetLandpage != null">
                target_landpage,
            </if>
            <if test="footer != null">
                footer
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="landpageKey != null">
                #{landpageKey},
            </if>
            <if test="targetLandpage != null">
                #{targetLandpage},
            </if>
            <if test="footer != null">
                #{footer}
            </if>
        </trim>
    </insert>

    <delete id="deleteById">
        DELETE
        FROM tb_blind_box_landpage
        WHERE id = #{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.blindbox.BlindBoxLandpageEntity">
        UPDATE tb_blind_box_landpage
        <set>
            <if test="landpageKey != null">
                landpage_key = #{landpageKey},
            </if>
            <if test="targetLandpage != null">
                target_landpage = #{targetLandpage},
            </if>
            <if test="footer != null">
                footer = #{footer},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_blind_box_landpage
        WHERE id = #{id}
    </select>

    <select id="selectByLandpageKey" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_blind_box_landpage
        WHERE landpage_key = #{landpageKey}
    </select>
</mapper>
