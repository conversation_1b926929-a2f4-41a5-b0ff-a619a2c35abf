<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.landpage.QwtfLandpageFormRecordMapper">

    <resultMap type="com.ruoyi.system.entity.landpage.QwtfLandpageFormRecordEntity" id="BaseResultMap">
            <result property="advertId" column="advert_id"/>
            <result property="appId" column="app_id"/>
            <result property="consumerId" column="consumer_id"/>
            <result property="friendStatus" column="friend_status"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
            <result property="id" column="id"/>
            <result property="ip" column="ip"/>
            <result property="landpageUrl" column="landpage_url"/>
            <result property="orderId" column="order_id"/>
            <result property="phone" column="phone"/>
            <result property="slotId" column="slot_id"/>
            <result property="smsTimes" column="sms_times"/>
            <result property="unionId" column="union_id"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            advert_id,
            app_id,
            consumer_id,
            friend_status,
            ip,
            landpage_url,
            order_id,
            phone,
            slot_id,
            sms_times,
            union_id,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.landpage.QwtfLandpageFormRecordEntity">
        INSERT INTO tb_landpage_form_record_qwtf
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="advertId != null">
                    advert_id,
                </if>
                <if test="appId != null">
                    app_id,
                </if>
                <if test="consumerId != null">
                    consumer_id,
                </if>
                <if test="friendStatus != null">
                    friend_status,
                </if>
                <if test="ip != null">
                    ip,
                </if>
                <if test="landpageUrl != null">
                    landpage_url,
                </if>
                <if test="orderId != null">
                    order_id,
                </if>
                <if test="phone != null">
                    phone,
                </if>
                <if test="slotId != null">
                    slot_id,
                </if>
                <if test="smsTimes != null">
                    sms_times,
                </if>
                <if test="unionId != null">
                    union_id
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="advertId != null">
                    #{advertId},
                </if>
                <if test="appId != null">
                    #{appId},
                </if>
                <if test="consumerId != null">
                    #{consumerId},
                </if>
                <if test="friendStatus != null">
                    #{friendStatus},
                </if>
                <if test="ip != null">
                    #{ip},
                </if>
                <if test="landpageUrl != null">
                    #{landpageUrl},
                </if>
                <if test="orderId != null">
                    #{orderId},
                </if>
                <if test="phone != null">
                    #{phone},
                </if>
                <if test="slotId != null">
                    #{slotId},
                </if>
                <if test="smsTimes != null">
                    #{smsTimes},
                </if>
                <if test="unionId != null">
                    #{unionId}
                </if>
        </trim>
    </insert>

    <select id="selectList" parameterType="com.ruoyi.system.bo.landpage.QwtfLandpageFormRecordSelectBo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_landpage_form_record_qwtf
        <where>
            <if test="friendStatus != null"> and friend_status = #{friendStatus}</if>
            <if test="phone != null and phone != ''"> and phone like concat('%', #{phone}, '%')</if>
            <if test="landpageUrl != null and landpageUrl != ''"> and landpage_url like concat('%', #{landpageUrl}, '%')</if>
            <if test="startDate != null "> and gmt_create &gt;= #{startDate}</if>
            <if test="endDate != null "> and gmt_create &lt;= #{endDate}</if>
            <if test="slotIds != null and slotIds.size() > 0">
                and slot_id in
                <foreach collection="slotIds" item="slotId" open="(" separator="," close=")">
                    #{slotId}
                </foreach>
            </if>
            <if test="appIds != null and appIds.size() > 0">
                and app_id in
                <foreach collection="appIds" item="appId" open="(" separator="," close=")">
                    #{appId}
                </foreach>
            </if>
            <if test="advertIds != null and advertIds.size() > 0">
                and advert_id in
                <foreach collection="advertIds" item="advertId" open="(" separator="," close=")">
                    #{advertId}
                </foreach>
            </if>
        </where>
        order by id desc
    </select>

    <update id="updateById" parameterType="com.ruoyi.system.entity.landpage.QwtfLandpageFormRecordEntity">
        UPDATE tb_landpage_form_record_qwtf
        <set>
            <if test="phone != null">
                phone = #{phone},
            </if>
            <if test="friendStatus != null">
                friend_status = #{friendStatus},
            </if>
            <if test="smsTimes != null">
                sms_times = #{smsTimes},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <update id="batchUpdateFriendStatus">
        update tb_landpage_form_record_qwtf
        <trim prefix="SET" suffixOverrides=",">
            friend_status = 3
        </trim>
        <where>
            union_id in
            <foreach collection="unionIds" item="unionId" open="(" separator="," close=")">
                #{unionId}
            </foreach>
        </where>
    </update>

    <update id="batchUpdateFriendStatusByOrderIds">
        update tb_landpage_form_record_qwtf
        <trim prefix="SET" suffixOverrides=",">
            friend_status = 3
        </trim>
        <where>
            order_id in
            <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
                #{orderId}
            </foreach>
        </where>
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_landpage_form_record_qwtf
        WHERE id = #{id}
    </select>

    <select id="selectByUnionId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_landpage_form_record_qwtf
        WHERE union_id = #{unionId}
        ORDER BY id DESC
        LIMIT 1
    </select>

    <select id="selectNotFriendByUnionIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_landpage_form_record_qwtf
        WHERE friend_status = 1
        <if test="unionIds != null and unionIds.size() != 0">
            and `union_id` in
            <foreach collection="unionIds" item="unionId" open="(" separator="," close=")">
                #{unionId}
            </foreach>
        </if>
        ORDER BY id desc
    </select>

    <select id="selectNotFriendByOrderIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_landpage_form_record_qwtf
        WHERE friend_status = 1
        <if test="orderIds != null and orderIds.size() != 0">
            and `order_id` in
            <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
                #{orderId}
            </foreach>
        </if>
        ORDER BY id desc
    </select>

    <select id="selectTotalAdvertIds" resultType="Long">
        select distinct advert_id
        from tb_landpage_form_record_qwtf
    </select>

    <select id="selectTotalAppIds" resultType="Long">
        select distinct app_id
        from tb_landpage_form_record_qwtf
    </select>

    <select id="selectTotalSlotIds" resultType="Long">
        select distinct slot_id
        from tb_landpage_form_record_qwtf
    </select>
</mapper>
