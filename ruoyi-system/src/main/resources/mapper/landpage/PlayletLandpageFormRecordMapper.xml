<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.landpage.PlayletLandpageFormRecordMapper">

    <resultMap type="com.ruoyi.system.entity.landpage.PlayletLandpageFormRecordEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="advertId" column="advert_id"/>
            <result property="orderId" column="order_id"/>
            <result property="consumerId" column="consumer_id"/>
            <result property="appId" column="app_id"/>
            <result property="slotId" column="slot_id"/>
            <result property="landpageUrl" column="landpage_url"/>
            <result property="tradeNo" column="trade_no"/>
            <result property="tradeAmount" column="trade_amount"/>
            <result property="tradeTime" column="trade_time"/>
            <result property="tradeStatus" column="trade_status"/>
            <result property="transactionId" column="transaction_id"/>
            <result property="openid" column="openid"/>
            <result property="ip" column="ip"/>
            <result property="payPlatform" column="pay_platform"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            advert_id,
            order_id,
            consumer_id,
            app_id,
            slot_id,
            landpage_url,
            trade_no,
            trade_amount,
            trade_time,
            trade_status,
            transaction_id,
            openid,
            ip,
            pay_platform,
            gmt_create,
            gmt_modified
    </sql>

    <select id="selectList" parameterType="com.ruoyi.system.bo.landpage.PlayletLandpageFormRecordSelectBo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_landpage_form_record_playlet
        <where>
            <if test="tradeStatus != null"> and trade_status = #{tradeStatus}</if>
            <if test="transactionId != null"> and transaction_id = #{transactionId}</if>
            <if test="landpageUrl != null and landpageUrl != ''"> and landpage_url like concat('%', #{landpageUrl}, '%')</if>
            <if test="startDate != null "> and gmt_create &gt;= #{startDate}</if>
            <if test="endDate != null "> and gmt_create &lt;= #{endDate}</if>
            <if test="payPlatform != null"> and pay_platform = #{payPlatform}</if>
            <if test="slotIds != null and slotIds.size() > 0">
                and slot_id in
                <foreach collection="slotIds" item="slotId" open="(" separator="," close=")">
                    #{slotId}
                </foreach>
            </if>
            <if test="appIds != null and appIds.size() > 0">
                and app_id in
                <foreach collection="appIds" item="appId" open="(" separator="," close=")">
                    #{appId}
                </foreach>
            </if>
            <if test="advertIds != null and advertIds.size() > 0">
                and advert_id in
                <foreach collection="advertIds" item="advertId" open="(" separator="," close=")">
                    #{advertId}
                </foreach>
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectStatistic" parameterType="com.ruoyi.system.bo.landpage.PlayletLandpageFormRecordSelectBo" resultMap="BaseResultMap">
        SELECT sum(ifnull(trade_amount, 0)) as trade_amount
        FROM tb_landpage_form_record_playlet
        <where>
            <if test="tradeStatus != null"> and trade_status = #{tradeStatus}</if>
            <if test="transactionId != null"> and transaction_id = #{transactionId}</if>
            <if test="landpageUrl != null and landpageUrl != ''"> and landpage_url like concat('%', #{landpageUrl}, '%')</if>
            <if test="startDate != null "> and gmt_create &gt;= #{startDate}</if>
            <if test="endDate != null "> and gmt_create &lt;= #{endDate}</if>
            <if test="payPlatform != null"> and pay_platform = #{payPlatform}</if>
            <if test="slotIds != null and slotIds.size() > 0">
                and slot_id in
                <foreach collection="slotIds" item="slotId" open="(" separator="," close=")">
                    #{slotId}
                </foreach>
            </if>
            <if test="appIds != null and appIds.size() > 0">
                and app_id in
                <foreach collection="appIds" item="appId" open="(" separator="," close=")">
                    #{appId}
                </foreach>
            </if>
            <if test="advertIds != null and advertIds.size() > 0">
                and advert_id in
                <foreach collection="advertIds" item="advertId" open="(" separator="," close=")">
                    #{advertId}
                </foreach>
            </if>
        </where>
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.landpage.PlayletLandpageFormRecordEntity">
        INSERT INTO tb_landpage_form_record_playlet
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="advertId != null">
                    advert_id,
                </if>
                <if test="orderId != null">
                    order_id,
                </if>
                <if test="consumerId != null">
                    consumer_id,
                </if>
                <if test="appId != null">
                    app_id,
                </if>
                <if test="slotId != null">
                    slot_id,
                </if>
                <if test="landpageUrl != null">
                    landpage_url,
                </if>
                <if test="tradeNo != null">
                    trade_no,
                </if>
                <if test="tradeAmount != null">
                    trade_amount,
                </if>
                <if test="tradeTime != null">
                    trade_time,
                </if>
                <if test="tradeStatus != null">
                    trade_status,
                </if>
                <if test="transactionId != null">
                    transaction_id,
                </if>
                <if test="openid != null">
                    openid,
                </if>
                <if test="ip != null">
                    ip,
                </if>
                <if test="payPlatform != null">
                    pay_platform,
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="advertId != null">
                    #{advertId},
                </if>
                <if test="orderId != null">
                    #{orderId},
                </if>
                <if test="consumerId != null">
                    #{consumerId},
                </if>
                <if test="appId != null">
                    #{appId},
                </if>
                <if test="slotId != null">
                    #{slotId},
                </if>
                <if test="landpageUrl != null">
                    #{landpageUrl},
                </if>
                <if test="tradeNo != null">
                    #{tradeNo},
                </if>
                <if test="tradeAmount != null">
                    #{tradeAmount},
                </if>
                <if test="tradeTime != null">
                    #{tradeTime},
                </if>
                <if test="tradeStatus != null">
                    #{tradeStatus},
                </if>
                <if test="transactionId != null">
                    #{transactionId},
                </if>
                <if test="openid != null">
                    #{openid},
                </if>
                <if test="ip != null">
                    #{ip},
                </if>
                <if test="payPlatform != null">
                    #{payPlatform},
                </if>
        </trim>
    </insert>

    <delete id="deleteById">
        DELETE FROM tb_landpage_form_record_playlet WHERE id=#{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.landpage.PlayletLandpageFormRecordEntity">
        UPDATE tb_landpage_form_record_playlet
        <set>
                    <if test="advertId != null">
                        advert_id = #{advertId},
                    </if>
                    <if test="orderId != null">
                        order_id = #{orderId},
                    </if>
                    <if test="consumerId != null">
                        consumer_id = #{consumerId},
                    </if>
                    <if test="appId != null">
                        app_id = #{appId},
                    </if>
                    <if test="slotId != null">
                        slot_id = #{slotId},
                    </if>
                    <if test="landpageUrl != null">
                        landpage_url = #{landpageUrl},
                    </if>
                    <if test="tradeNo != null">
                        trade_no = #{tradeNo},
                    </if>
                    <if test="tradeAmount != null">
                        trade_amount = #{tradeAmount},
                    </if>
                    <if test="tradeTime != null">
                        trade_time = #{tradeTime},
                    </if>
                    <if test="tradeStatus != null">
                        trade_status = #{tradeStatus},
                    </if>
                    <if test="transactionId != null">
                        transaction_id = #{transactionId},
                    </if>
                    <if test="openid != null">
                        openid = #{openid},
                    </if>
                    <if test="ip != null">
                        ip = #{ip},
                    </if>
                    <if test="payPlatform != null">
                        pay_platform = #{payPlatform},
                    </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_landpage_form_record_playlet
        WHERE id = #{id}
    </select>

    <select id="selectByTradeNo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_landpage_form_record_playlet
        WHERE trade_no = #{tradeNo}
    </select>

    <select id="selectTotalAdvertIds" resultType="Long">
        select distinct advert_id
        from tb_landpage_form_record_playlet
    </select>

    <select id="selectTotalAppIds" resultType="Long">
        select distinct app_id
        from tb_landpage_form_record_playlet
    </select>

    <select id="selectTotalSlotIds" resultType="Long">
        select distinct slot_id
        from tb_landpage_form_record_playlet
    </select>

</mapper>
