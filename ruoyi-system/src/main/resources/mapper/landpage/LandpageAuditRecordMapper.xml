<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.manager.LandpageAuditRecordMapper">
    
    <resultMap type="com.ruoyi.system.entity.manager.LandpageAuditRecord" id="LandpageAuditRecordResult">
        <result property="id"    column="id"    />
        <result property="originUrl"    column="origin_url"    />
        <result property="originUrlMd5"    column="origin_url_md5"    />
        <result property="iqiyiUrl"    column="iqiyi_url"    />
        <result property="auditApplyTime"    column="audit_apply_time"    />
        <result property="auditFinishTime"    column="audit_finish_time"    />
        <result property="auditStatus"    column="audit_status"    />
        <result property="auditReason"    column="audit_reason"    />
        <result property="extInfo"    column="ext_info"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectLandpageAuditRecordVo">
        select id, origin_url, origin_url_md5, iqiyi_url, audit_apply_time, audit_finish_time, audit_status, audit_reason, ext_info, gmt_create, gmt_modified from tb_landpage_audit_record
    </sql>

    <select id="selectLandpageAuditRecordList" parameterType="com.ruoyi.system.entity.manager.LandpageAuditRecord" resultMap="LandpageAuditRecordResult">
        <include refid="selectLandpageAuditRecordVo"/>
        <where>  
            <if test="originUrl != null and originUrl != ''"> and origin_url like concat('%', #{originUrl}, '%')</if>
            <if test="auditStatus != null "> and audit_status = #{auditStatus}</if>
            <if test="startDate != null "> and audit_apply_time &gt;= #{startDate}</if>
            <if test="endDate != null "> and audit_apply_time &lt;= #{endDate}</if>
        </where>
        order by audit_apply_time desc
    </select>
    
    <select id="selectLandpageAuditRecordById" parameterType="Long" resultMap="LandpageAuditRecordResult">
        <include refid="selectLandpageAuditRecordVo"/>
        where id = #{id}
    </select>

    <select id="selectByOriginUrl" parameterType="String" resultMap="LandpageAuditRecordResult">
        <include refid="selectLandpageAuditRecordVo"/>
        where origin_url_md5 = #{originUrlMd5}
    </select>
        
    <insert id="insertLandpageAuditRecord" parameterType="com.ruoyi.system.entity.manager.LandpageAuditRecord" useGeneratedKeys="true" keyProperty="id">
        insert into tb_landpage_audit_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="originUrl != null and originUrl != ''">origin_url,</if>
            <if test="originUrlMd5 != null and originUrlMd5 != ''">origin_url_md5,</if>
            <if test="iqiyiUrl != null">iqiyi_url,</if>
            <if test="auditApplyTime != null">audit_apply_time,</if>
            <if test="auditFinishTime != null">audit_finish_time,</if>
            <if test="auditStatus != null">audit_status,</if>
            <if test="auditReason != null">audit_reason,</if>
            <if test="extInfo != null and extInfo != ''">ext_info,</if>
            <if test="gmtCreate != null">gmt_create,</if>
            <if test="gmtModified != null">gmt_modified,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="originUrl != null and originUrl != ''">#{originUrl},</if>
            <if test="originUrlMd5 != null and originUrlMd5 != ''">#{originUrlMd5},</if>
            <if test="iqiyiUrl != null">#{iqiyiUrl},</if>
            <if test="auditApplyTime != null">#{auditApplyTime},</if>
            <if test="auditFinishTime != null">#{auditFinishTime},</if>
            <if test="auditStatus != null">#{auditStatus},</if>
            <if test="auditReason != null">#{auditReason},</if>
            <if test="extInfo != null and extInfo != ''">#{extInfo},</if>
            <if test="gmtCreate != null">#{gmtCreate},</if>
            <if test="gmtModified != null">#{gmtModified},</if>
         </trim>
    </insert>

    <update id="updateLandpageAuditRecord" parameterType="com.ruoyi.system.entity.manager.LandpageAuditRecord">
        update tb_landpage_audit_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="originUrl != null and originUrl != ''">origin_url = #{originUrl},</if>
            <if test="originUrlMd5 != null and originUrlMd5 != ''">origin_url_md5 = #{originUrlMd5},</if>
            <if test="iqiyiUrl != null">iqiyi_url = #{iqiyiUrl},</if>
            <if test="auditApplyTime != null">audit_apply_time = #{auditApplyTime},</if>
            <if test="auditFinishTime != null">audit_finish_time = #{auditFinishTime},</if>
            <if test="auditStatus != null">audit_status = #{auditStatus},</if>
            <if test="auditReason != null">audit_reason = #{auditReason},</if>
            <if test="extInfo != null and extInfo != ''">ext_info = #{extInfo},</if>
        </trim>
        where id = #{id}
    </update>

    <select id="checkOriginUrlExist"  resultType="int">
        select count(1)
        from tb_landpage_audit_record
        where origin_url_md5 = #{originUrlMd5}
    </select>
</mapper>