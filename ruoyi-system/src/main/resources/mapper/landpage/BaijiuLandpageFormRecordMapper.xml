<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.landpage.BaijiuLandpageFormRecordMapper">

    <resultMap type="com.ruoyi.system.entity.datashow.BaijiuLandpageFormRecord" id="BaijiuLandpageFormRecordResult">
        <result property="id"    column="id"    />
        <result property="advertId"    column="advert_id"    />
        <result property="orderId"    column="order_id"    />
        <result property="appId"    column="app_id"    />
        <result property="slotId"    column="slot_id"    />
        <result property="consumerId"    column="consumer_id"    />
        <result property="landpageUrl"    column="landpage_url"    />
        <result property="referer"    column="referer"    />
        <result property="phone"    column="phone"    />
        <result property="name"    column="name"    />
        <result property="areaNum"    column="area_num"    />
        <result property="province"    column="province"    />
        <result property="city"    column="city"    />
        <result property="district"    column="district"    />
        <result property="address"    column="address"    />
        <result property="ip"    column="ip"    />
        <result property="amount"    column="amount"    />
        <result property="tradeNo"    column="trade_no"    />
        <result property="payTime"    column="pay_time"    />
        <result property="tradeStatus"    column="trade_status"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <resultMap type="com.ruoyi.system.entity.landpage.LandpageFormCount" id="LandpageFormCountResult">
        <result property="assignDate"    column="assign_date"    />
        <result property="slotId"    column="slot_id"    />
        <result property="advertId"    column="advert_id"    />
        <result property="formCount"    column="form_count"    />
    </resultMap>

    <sql id="selectLandpageFormRecordVo">
        select id, advert_id, app_id, slot_id, order_id, consumer_id, referer, landpage_url, phone, name, area_num, province, city, district, address, ip, amount,pay_time,trade_status,trade_no,gmt_create, gmt_modified
        from tb_landpage_form_record_baijiu
    </sql>

    <select id="selectList" parameterType="com.ruoyi.system.entity.datashow.BaijiuLandpageFormRecord" resultMap="BaijiuLandpageFormRecordResult">
        <include refid="selectLandpageFormRecordVo"/>
        <where>
            <if test="advertId != null"> and advert_id = #{advertId}</if>
            <if test="appId != null"> and app_id = #{appId}</if>
            <if test="slotId != null"> and slot_id = #{slotId}</if>
            <if test="orderId != null  and orderId != ''"> and order_id = #{orderId}</if>
            <if test="consumerId != null"> and consumer_id = #{consumerId}</if>
            <if test="landpageUrl != null and landpageUrl != ''"> and landpage_url like concat('%', #{landpageUrl}, '%')</if>
            <if test="referer != null and referer != ''"> and referer like concat('%', #{referer}, '%')</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="areaNum != null  and areaNum != ''"> and area_num = #{areaNum}</if>
            <if test="province != null  and province != ''"> and province = #{province}</if>
            <if test="city != null  and city != ''"> and city = #{city}</if>
            <if test="district != null  and district != ''"> and district = #{district}</if>
            <if test="startDate != null "> and gmt_create &gt;= #{startDate}</if>
            <if test="endDate != null "> and gmt_create &lt;= #{endDate}</if>
            <if test="slotIds != null and slotIds.size() > 0">
                and slot_id in
                <foreach collection="slotIds" item="slotId" open="(" separator="," close=")">
                    #{slotId}
                </foreach>
            </if>
            <if test="appIds != null and appIds.size() > 0">
                and app_id in
                <foreach collection="appIds" item="appId" open="(" separator="," close=")">
                    #{appId}
                </foreach>
            </if>
            <if test="advertIds != null and advertIds.size() > 0">
                and advert_id in
                <foreach collection="advertIds" item="advertId" open="(" separator="," close=")">
                    #{advertId}
                </foreach>
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectListByAdvertIdAndDate" resultMap="BaijiuLandpageFormRecordResult">
        <include refid="selectLandpageFormRecordVo"/>
        <where>
         advert_id in
        <foreach collection="advertIds" close=")" separator="," open="(" item="advertId">
            #{advertId}
        </foreach>
        <if test="startDate != null "> and gmt_create &gt;= #{startDate}</if>
        <if test="endDate != null "> and gmt_create &lt;= #{endDate}</if>
        </where>
        order by id desc
    </select>

    <select id="selectLandpageFormRecordById" parameterType="Long" resultMap="BaijiuLandpageFormRecordResult">
        <include refid="selectLandpageFormRecordVo"/>
        where id = #{id}
    </select>

    <select id="existByOrderId" resultType="Integer">
        select 1
        where order_id = #{orderId}
        limit 1
    </select>
    <select id="selectByTradeNo" resultMap="BaijiuLandpageFormRecordResult">
        <include refid="selectLandpageFormRecordVo"/>
        where trade_no = #{tradeNo}
        order by id desc
        limit 1
    </select>

    <insert id="insertLandpageFormRecord" parameterType="com.ruoyi.system.entity.datashow.BaijiuLandpageFormRecord"
            useGeneratedKeys="true" keyProperty="id">
        insert into tb_landpage_form_record_baijiu
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="advertId != null">advert_id,</if>
            <if test="orderId != null and orderId != ''">order_id,</if>
            <if test="consumerId != null">consumer_id,</if>
            <if test="referer != null">referer,</if>
            <if test="landpageUrl != null">landpage_url,</if>
            <if test="phone != null and phone != ''">phone,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="areaNum != null and areaNum != ''">area_num,</if>
            <if test="province != null and province != ''">province,</if>
            <if test="city != null and city != ''">city,</if>
            <if test="district != null and district != ''">district,</if>
            <if test="address != null and address != ''">address,</if>
            <if test="appId != null">app_id,</if>
            <if test="slotId != null">slot_id,</if>
            <if test="ip != null">ip,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="advertId != null">#{advertId},</if>
            <if test="orderId != null and orderId != ''">#{orderId},</if>
            <if test="consumerId != null">#{consumerId},</if>
            <if test="referer != null">#{referer},</if>
            <if test="landpageUrl != null">#{landpageUrl},</if>
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="areaNum != null and areaNum != ''">#{areaNum},</if>
            <if test="province != null and province != ''">#{province},</if>
            <if test="city != null and city != ''">#{city},</if>
            <if test="district != null and district != ''">#{district},</if>
            <if test="address != null and address != ''">#{address},</if>
            <if test="appId != null">#{appId},</if>
            <if test="slotId != null">#{slotId},</if>
            <if test="ip != null">#{ip},</if>
         </trim>
    </insert>

    <update id="updateLandpageFormRecord" parameterType="com.ruoyi.system.entity.datashow.BaijiuLandpageFormRecord">
        update tb_landpage_form_record_baijiu
        <trim prefix="SET" suffixOverrides=",">
            <if test="advertId != null">advert_id = #{advertId},</if>
            <if test="orderId != null and orderId != ''">order_id = #{orderId},</if>
            <if test="consumerId != null">consumer_id = #{consumerId},</if>
            <if test="referer != null">referer = #{referer},</if>
            <if test="landpageUrl != null">landpage_url = #{landpageUrl},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="areaNum != null and areaNum != ''">area_num = #{areaNum},</if>
            <if test="province != null and province != ''">province = #{province},</if>
            <if test="city != null and city != ''">city = #{city},</if>
            <if test="district != null and district != ''">district = #{district},</if>
            <if test="address != null and address != ''">address = #{address},</if>
            <if test="appId != null">app_id = #{appId},</if>
            <if test="slotId != null">slot_id = #{slotId},</if>
            <if test="ip != null">ip = #{ip},</if>
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>

    <select id="countByDateAndSlotId" parameterType="com.ruoyi.system.entity.datashow.BaijiuLandpageFormRecord" resultMap="LandpageFormCountResult">
        select DATE_FORMAT(gmt_create,'%Y-%m-%d') as assign_date,slot_id as slot_id, count(1) as form_count
        from tb_landpage_form_record_baijiu
        <where>
            <if test="startDate != null "> and gmt_create &gt;= #{startDate}</if>
            <if test="endDate != null "> and gmt_create &lt;= #{endDate}</if>
            <if test="slotIds != null and slotIds.size() > 0">
                and slot_id in
                <foreach collection="slotIds" item="slotId" open="(" separator="," close=")">
                    #{slotId}
                </foreach>
            </if>
        </where>
        group by assign_date, slot_id
    </select>

    <select id="sumByDateAndSlotId" parameterType="com.ruoyi.system.entity.datashow.BaijiuLandpageFormRecord" resultType="Integer">
        select count(1)
        from tb_landpage_form_record_baijiu
        <where>
            <if test="startDate != null "> and gmt_create &gt;= #{startDate}</if>
            <if test="endDate != null "> and gmt_create &lt;= #{endDate}</if>
            <if test="slotIds != null and slotIds.size() > 0">
                and slot_id in
                <foreach collection="slotIds" item="slotId" open="(" separator="," close=")">
                    #{slotId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="countByDate" parameterType="com.ruoyi.system.entity.datashow.BaijiuLandpageFormRecord" resultMap="LandpageFormCountResult">
        select slot_id , advert_id , count(1) as form_count
        from tb_landpage_form_record_baijiu
        <where>
            <if test="startDate != null "> and gmt_create &gt;= #{startDate}</if>
            <if test="endDate != null "> and gmt_create &lt;= #{endDate}</if>
            <if test="slotIds != null and slotIds.size() > 0">
                and slot_id in
                <foreach collection="slotIds" item="slotId" open="(" separator="," close=")">
                    #{slotId}
                </foreach>
            </if>
        </where>
        group by slot_id,advert_id
    </select>

    <update id="updateTradeNo">
        update tb_landpage_form_record_baijiu
        set trade_no = #{tradeNo}
        where id = #{id}
    </update>

    <update id="updateTradeStatus" parameterType="com.ruoyi.system.entity.datashow.BaijiuLandpageFormRecord">
        update tb_landpage_form_record_baijiu
        set amount = #{amount} ,pay_time = #{payTime},trade_status = #{tradeStatus}
        where trade_no = #{tradeNo}
    </update>

    <select id="selectTotalAdvertIds" resultType="Long">
        select distinct advert_id
        from tb_landpage_form_record_baijiu
    </select>

    <select id="selectTotalAppIds" resultType="Long">
        select distinct app_id
        from tb_landpage_form_record_baijiu
    </select>

    <select id="selectTotalSlotIds" resultType="Long">
        select distinct slot_id
        from tb_landpage_form_record_baijiu
    </select>
</mapper>
