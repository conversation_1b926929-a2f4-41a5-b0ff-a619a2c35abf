<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.landpage.LandpageFormSendRuleMapper">

    <resultMap type="com.ruoyi.system.entity.datashow.LandpageFormSendRuleEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="advertiserId" column="advertiser_id"/>
            <result property="area" column="area"/>
            <result property="ageMin" column="age_min"/>
            <result property="ageMax" column="age_max"/>
            <result property="dailyLimit" column="daily_limit"/>
            <result property="isDeleted" column="is_deleted"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            advertiser_id,
            area,
            age_min,
            age_max,
            daily_limit,
            is_deleted,
            gmt_create,
            gmt_modified
    </sql>

    <select id="selectList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tb_landpage_form_send_rule
        <where>
            is_deleted = 0
        </where>
        order by gmt_create desc
    </select>

    <select id="selectByAdvertiserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tb_landpage_form_send_rule
        <where>
            advertiser_id = #{advertiserId}
            and is_deleted = 0
        </where>
        order by gmt_create desc
        LIMIT 1
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.datashow.LandpageFormSendRuleEntity">
        INSERT INTO tb_landpage_form_send_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="advertiserId != null">
                advertiser_id,
            </if>
            <if test="area != null">
                area,
            </if>
            <if test="ageMin != null">
                age_min,
            </if>
            <if test="ageMax != null">
                age_max,
            </if>
            <if test="dailyLimit != null">
                daily_limit,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="advertiserId != null">
                #{advertiserId},
            </if>
            <if test="area != null">
                #{area},
            </if>
            <if test="ageMin != null">
                #{ageMin},
            </if>
            <if test="ageMax != null">
                #{ageMax},
            </if>
            <if test="dailyLimit != null">
                #{dailyLimit},
            </if>
        </trim>
    </insert>

    <delete id="deleteByAdvertiserId">
        UPDATE tb_landpage_form_send_rule SET is_deleted = 1 WHERE advertiser_id=#{advertiserId} and is_deleted = 0
    </delete>
</mapper>
