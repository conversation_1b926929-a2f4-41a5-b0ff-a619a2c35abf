<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.landpage.SlotLandpageFormDataMapper">

    <resultMap type="com.ruoyi.system.entity.landpage.SlotLandpageFormDataEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="curDate" column="cur_date"/>
            <result property="slotId" column="slot_id"/>
            <result property="formCount" column="form_count"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
            <result property="formConsume" column="form_consume"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            cur_date,
            slot_id,
            form_count,
            form_consume,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.landpage.SlotLandpageFormDataEntity">
        INSERT IGNORE INTO tb_slot_landpage_form_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="curDate != null">
                    cur_date,
                </if>
                <if test="slotId != null">
                    slot_id,
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="curDate != null">
                    #{curDate},
                </if>
                <if test="slotId != null">
                    #{slotId},
                </if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.ruoyi.system.entity.landpage.SlotLandpageFormDataEntity">
        UPDATE tb_slot_landpage_form_data
        <set>
            <if test="formCountAdd != null">
                form_count = form_count + #{formCountAdd},
            </if>
            <if test="formConsumeAdd != null">
                form_consume = form_consume + #{formConsumeAdd}
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_slot_landpage_form_data
        WHERE id = #{id}
    </select>

    <select id="selectBy" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_slot_landpage_form_data
        WHERE cur_date = #{curDate} and slot_id = #{slotId}
    </select>

    <select id="selectListByDateAndSlotIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_slot_landpage_form_data
        <where>
            <if test="startDate != null "> and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null "> and cur_date &lt;= #{endDate}</if>
            <if test="slotIds != null and slotIds.size() > 0">
                and slot_id in
                <foreach collection="slotIds" item="slotId" open="(" separator="," close=")">
                    #{slotId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="sumFormDataByDateAndSlotIds" resultType="Integer">
        SELECT ifnull(sum(form_count), 0)
        FROM tb_slot_landpage_form_data
        <where>
            <if test="startDate != null "> and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null "> and cur_date &lt;= #{endDate}</if>
            <if test="slotIds != null and slotIds.size() > 0">
                and slot_id in
                <foreach collection="slotIds" item="slotId" open="(" separator="," close=")">
                    #{slotId}
                </foreach>
            </if>
        </where>
    </select>
</mapper>
