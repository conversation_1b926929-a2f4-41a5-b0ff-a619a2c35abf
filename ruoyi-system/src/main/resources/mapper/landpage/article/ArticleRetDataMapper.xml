<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.landpage.article.ArticleRetDataMapper">

    <resultMap type="com.ruoyi.system.entity.landpage.article.ArticleRetDataEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="curDate" column="cur_date"/>
            <result property="linkId" column="link_id"/>
            <result property="retUrlMd5" column="ret_url_md5"/>
            <result property="retUrl" column="ret_url"/>
            <result property="requestPv" column="request_pv"/>
            <result property="requestUv" column="request_uv"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            cur_date,
            link_id,
            ret_url_md5,
            ret_url,
            request_pv,
            request_uv
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.landpage.article.ArticleRetDataEntity">
        INSERT INTO tb_article_ret_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="curDate != null">
                    cur_date,
                </if>
                <if test="linkId != null">
                    link_id,
                </if>
                <if test="retUrlMd5 != null">
                    ret_url_md5,
                </if>
                <if test="retUrl != null">
                    ret_url,
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="curDate != null">
                    #{curDate},
                </if>
                <if test="linkId != null">
                    #{linkId},
                </if>
                <if test="retUrlMd5 != null">
                    #{retUrlMd5},
                </if>
                <if test="retUrl != null">
                    #{retUrl},
                </if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.ruoyi.system.entity.landpage.article.ArticleRetDataEntity">
        UPDATE tb_article_ret_data
        <set>
                    <if test="curDate != null">
                        cur_date = #{curDate},
                    </if>
                    <if test="linkId != null">
                        link_id = #{linkId},
                    </if>
                    <if test="retUrlMd5 != null">
                        ret_url_md5 = #{retUrlMd5},
                    </if>
                    <if test="retUrl != null">
                        ret_url = #{retUrl},
                    </if>
                    <if test="requestPv != null">
                        request_pv = #{requestPv},
                    </if>
                    <if test="requestUv != null">
                        request_uv = #{requestUv},
                    </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_article_ret_data
        WHERE id = #{id}
    </select>

    <select id="selectBy" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_article_ret_data
        <where>
            <if test="curDate != null "> and cur_date = #{curDate}</if>
            <if test="linkId != null "> and link_id = #{linkId}</if>
            <if test="retUrlMd5 != null "> and ret_url_md5 = #{retUrlMd5}</if>
        </where>
    </select>

    <update id="update" parameterType="com.ruoyi.system.bo.landpage.article.ArticleRetDataUpdateParamBo">
        UPDATE tb_article_ret_data
        <set>
            <if test="requestPvAdd != null">request_pv = request_pv + #{requestPvAdd},</if>
            <if test="requestUvAdd != null">request_uv = request_uv + #{requestUvAdd},</if>
        </set>
        WHERE id=#{id}
    </update>
</mapper>
