<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.landpage.article.ArticleRefreshRecordMapper">

    <resultMap type="com.ruoyi.system.entity.landpage.article.ArticleRefreshRecordEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="advertiserId" column="advertiser_id"/>
            <result property="linkId" column="link_id"/>
            <result property="articleId" column="article_id"/>
            <result property="actualRequestPv" column="actual_request_pv"/>
            <result property="url" column="url"/>
            <result property="curDate" column="cur_date"/>
            <result property="operatorId" column="operator_id"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            cur_date,
            advertiser_id,
            link_id,
            article_id,
            actual_request_pv,
            url,
            operator_id,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.landpage.article.ArticleRefreshRecordEntity">
        INSERT INTO tb_article_refresh_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="advertiserId != null">
                    advertiser_id,
                </if>
                <if test="operatorId != null">
                    operator_id,
                </if>
                <if test="articleId != null">
                    article_id,
                </if>
                <if test="url != null">
                    url,
                </if>
                <if test="curDate != null">
                    cur_date,
                </if>
                <if test="linkId != null">
                    link_id,
                </if>
                <if test="actualRequestPv != null">
                    actual_request_pv,
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="advertiserId != null">
                    #{advertiserId},
                </if>
                <if test="operatorId != null">
                    #{operatorId},
                </if>
                <if test="articleId != null">
                    #{articleId},
                </if>
                <if test="url != null">
                    #{url},
                </if>
                <if test="curDate != null">
                    #{curDate},
                </if>
                <if test="linkId != null">
                    #{linkId},
                </if>
                <if test="actualRequestPv != null">
                    #{actualRequestPv},
                </if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.ruoyi.system.entity.landpage.article.ArticleRefreshRecordEntity">
        UPDATE tb_article_refresh_record
        <set>
            <if test="advertiserId != null">
                advertiser_id = #{advertiserId},
            </if>
            <if test="operatorId != null">
                operator_id = #{operatorId},
            </if>
            <if test="articleId != null">
                article_id = #{articleId},
            </if>
            <if test="url != null">
                url = #{url},
            </if>
            <if test="curDate != null">
                cur_date = #{curDate},
            </if>
            <if test="actualRequestPv != null">
                actual_request_pv = #{actualRequestPv},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_article_refresh_record
        WHERE id = #{id}
    </select>

    <select id="countBy" parameterType="com.ruoyi.system.bo.landpage.article.ArticleListParamBo" resultType="Integer">
        SELECT count(*)
        FROM tb_article_refresh_record r
        left join tb_article a on r.article_id = a.id
        <where>
            and r.actual_request_pv > 0
            and r.operator_id > 0
            <if test="searchKey != null and searchKey != ''"> and (a.`name` like concat('%', #{searchKey}, '%') or a.`url` like concat('%', #{searchKey}, '%'))</if>
            <if test="linkId != null "> and r.link_id = #{linkId}</if>
            <if test="name != null and name != ''"> and a.`name` like concat('%', #{name}, '%') </if>
            <if test="url != null and url != ''"> and a.`url` like concat('%', #{url}, '%')</if>
            <if test="startDate != null "> and r.cur_date &gt;= #{startDate}</if>
            <if test="endDate != null "> and r.cur_date &lt;= #{endDate}</if>
            <if test="ids != null and ids.size > 0">
                and a.id in
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>
</mapper>
