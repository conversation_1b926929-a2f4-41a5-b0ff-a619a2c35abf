<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.landpage.article.ArticleAggrLinkMapper">

    <resultMap type="com.ruoyi.system.entity.landpage.article.ArticleAggrLinkEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="key" column="key"/>
            <result property="sySlot" column="sy_slot"/>
            <result property="name" column="name"/>
            <result property="url" column="url"/>
            <result property="retConfig" column="ret_config"/>
            <result property="status" column="status"/>
            <result property="operatorId" column="operator_id"/>
            <result property="advertiserId" column="advertiser_id"/>
            <result property="operatorName" column="operator_name"/>
            <result property="creatorId" column="creator_id"/>
            <result property="creatorName" column="creator_name"/>
            <result property="operatorTime" column="operator_time"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            `key`,
            `name`,
            url,
            ret_config,
            sy_slot,
            `status`,
            operator_id,
            advertiser_id,
            operator_name,
            operator_time,
            creator_id,
            creator_name,
            gmt_create,
            gmt_modified
    </sql>

    <select id="selectList" parameterType="com.ruoyi.system.bo.landpage.article.ArticleAggrLinkListParamBo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_article_aggr_link
        <where>
            `status` != 1
            <if test="searchKey != null and searchKey != ''"> and (`name` like concat('%', #{searchKey}, '%') or `url` like concat('%', #{searchKey}, '%'))</if>
            <if test="fcSearchKey != null and fcSearchKey != ''"> and (`name` like concat('%', #{fcSearchKey}, '%') or `id` like concat('%', #{fcSearchKey}, '%'))</if>
            <if test="keys != null and keys.size() > 0">
                and `key` in
                <foreach collection="keys" item="key" open="(" separator="," close=")">
                    #{key}
                </foreach>
            </if>
            <if test="name != null and name != ''">
                and  `name` like concat('%', #{name}, '%')
            </if>
            <if test="advertiserId != null">
                and  advertiser_id = #{advertiserId}
            </if>
            <if test="startDate != null">
                and gmt_create &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                and gmt_create &lt;= #{endDate}
            </if>
            <if test="linkIds != null and linkIds.size > 0">
                and id in
                <foreach collection="linkIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="notInLinkIds != null and notInLinkIds.size() > 0">
                and id not in
                <foreach collection="notInLinkIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        order by id desc
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.landpage.article.ArticleAggrLinkEntity">
        INSERT INTO tb_article_aggr_link
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="key != null">
                    `key`,
                </if>
                <if test="name != null">
                    `name`,
                </if>
                <if test="url != null">
                    url,
                </if>
                <if test="status != null">
                    `status`,
                </if>
                <if test="operatorId != null">
                    operator_id,
                </if>
                <if test="operatorName != null">
                    operator_name,
                </if>
                <if test="creatorId != null">
                    creator_id,
                </if>
                <if test="creatorName != null">
                    creator_name,
                </if>
                <if test="advertiserId != null">
                    advertiser_id,
                </if>
                <if test="operatorTime != null">
                    operator_time,
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="key != null">
                    #{key},
                </if>
                <if test="name != null">
                    #{name},
                </if>
                <if test="url != null">
                    #{url},
                </if>
                <if test="status != null">
                    #{status},
                </if>
                <if test="operatorId != null">
                    #{operatorId},
                </if>
                <if test="operatorName != null">
                    #{operatorName},
                </if>
                <if test="creatorId != null">
                    #{creatorId},
                </if>
                <if test="creatorName != null">
                    #{creatorName},
                </if>
                <if test="advertiserId != null">
                    #{advertiserId},
                </if>
                <if test="operatorTime != null">
                    #{operatorTime},
                </if>
        </trim>
    </insert>

    <update id="deleteById">
        UPDATE tb_article_aggr_link SET status = 1 WHERE id=#{id}
    </update>

    <update id="updateById" parameterType="com.ruoyi.system.entity.landpage.article.ArticleAggrLinkEntity">
        UPDATE tb_article_aggr_link
        <set>
            <if test="key != null">
                `key` = #{key},
            </if>
            <if test="name != null">
                `name` = #{name},
            </if>
            <if test="url != null">
                url = #{url},
            </if>
            <if test="retConfig != null">
                ret_config = #{retConfig},
            </if>
            <if test="sySlot != null">
                sy_slot = #{sySlot},
            </if>
            <if test="status != null">
                `status` = #{status},
            </if>
            <if test="operatorId != null">
                operator_id = #{operatorId},
            </if>
            <if test="operatorName != null">
                operator_name = #{operatorName},
            </if>
            <if test="operatorTime != null">
                operator_time = #{operatorTime},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_article_aggr_link
        WHERE id = #{id}
    </select>

    <select id="selectByKey" resultMap="BaseResultMap">
        SELECT id, `key`, `name`, url, `status`, ret_config, sy_slot
        FROM tb_article_aggr_link
        WHERE `key` = #{key}
    </select>

    <select id="selectTotalKey" resultType="String">
        SELECT `key`
        FROM tb_article_aggr_link
    </select>

    <select id="existByName" resultType="Integer">
        select 1
        FROM tb_article_aggr_link
        <where>
            `name` = #{name}
            and `status` != 1
            <if test="advertiserId != null">
                and advertiser_id = #{advertiserId}
            </if>
            <if test="id != null">
                and id != #{id}
            </if>
        </where>
        limit 1
    </select>
    <select id="selectTodayLinkByAdvertiserId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_article_aggr_link
        <where>
            `status` != 1
            and advertiser_id = #{advertiserId}
            and gmt_create &gt;= #{startDate} and gmt_create &lt;= #{endDate}
        </where>
        limit 1
    </select>
</mapper>
