<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.landpage.article.ArticleCompetitorUrlMapper">

    <resultMap type="com.ruoyi.system.entity.landpage.article.ArticleCompetitorUrlEntity" id="BaseResultMap">
            <result property="createTime" column="create_time"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
            <result property="id" column="id"/>
            <result property="nickname" column="nickname"/>
            <result property="provinceName" column="province_name"/>
            <result property="read" column="read"/>
            <result property="source" column="source"/>
            <result property="title" column="title"/>
            <result property="url" column="url"/>
    </resultMap>

    <sql id="Base_Column_List">
            create_time,
            gmt_create,
            gmt_modified,
            id,
            nickname,
            province_name,
            `read`,
            `source`,
            title,
            url
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.landpage.article.ArticleCompetitorUrlEntity">
        INSERT INTO tb_article_competitor_url
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="createTime != null">
                    create_time,
                </if>
                <if test="nickname != null">
                    nickname,
                </if>
                <if test="provinceName != null">
                    province_name,
                </if>
                <if test="read != null">
                    `read`,
                </if>
                <if test="source != null">
                    `source`,
                </if>
                <if test="title != null">
                    title,
                </if>
                <if test="url != null">
                    url
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="createTime != null">
                    #{createTime},
                </if>
                <if test="nickname != null">
                    #{nickname},
                </if>
                <if test="provinceName != null">
                    #{provinceName},
                </if>
                <if test="read != null">
                    #{read},
                </if>
                <if test="source != null">
                    #{source},
                </if>
                <if test="title != null">
                    #{title},
                </if>
                <if test="url != null">
                    #{url}
                </if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.ruoyi.system.entity.landpage.article.ArticleCompetitorUrlEntity">
        UPDATE tb_article_competitor_url
        <set>
                    <if test="createTime != null">
                        create_time = #{createTime},
                    </if>
                    <if test="gmtModified != null">
                        gmt_modified = #{gmtModified},
                    </if>
                    <if test="nickname != null">
                        nickname = #{nickname},
                    </if>
                    <if test="provinceName != null">
                        province_name = #{provinceName},
                    </if>
                    <if test="read != null">
                        `read` = #{read},
                    </if>
                    <if test="source != null">
                        `source` = #{source},
                    </if>
                    <if test="title != null">
                        title = #{title},
                    </if>
                    <if test="url != null">
                        url = #{url},
                    </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_article_competitor_url
        WHERE id = #{id}
    </select>

</mapper>
