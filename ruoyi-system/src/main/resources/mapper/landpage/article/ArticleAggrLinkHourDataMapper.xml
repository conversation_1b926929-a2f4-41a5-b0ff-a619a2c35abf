<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.landpage.article.ArticleAggrLinkHourDataMapper">

    <resultMap type="com.ruoyi.system.entity.landpage.article.ArticleAggrLinkHourDataEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="curDate" column="cur_date"/>
            <result property="curHour" column="cur_hour"/>
            <result property="linkId" column="link_id"/>
            <result property="requestPv" column="request_pv"/>
            <result property="requestUv" column="request_uv"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            cur_date,
            cur_hour,
            link_id,
            request_pv,
            request_uv,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.landpage.article.ArticleAggrLinkHourDataEntity">
        INSERT INTO tb_article_aggr_link_hour_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="curDate != null">
                    cur_date,
                </if>
                <if test="curHour != null">
                    cur_hour,
                </if>
                <if test="linkId != null">
                    link_id,
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="curDate != null">
                    #{curDate},
                </if>
                <if test="curHour != null">
                    #{curHour},
                </if>
                <if test="linkId != null">
                    #{linkId},
                </if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.ruoyi.system.entity.landpage.article.ArticleAggrLinkHourDataEntity">
        UPDATE tb_article_aggr_link_hour_data
        <set>
            <if test="curDate != null">
                cur_date = #{curDate},
            </if>
            <if test="curHour != null">
                cur_hour = #{curHour},
            </if>
            <if test="linkId != null">
                link_id = #{linkId},
            </if>
            <if test="requestPv != null">
                request_pv = #{requestPv},
            </if>
            <if test="requestUv != null">
                request_uv = #{requestUv},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <update id="update" parameterType="com.ruoyi.system.bo.landpage.article.ArticleAggrLinkDataUpdateParamBo">
        UPDATE tb_article_aggr_link_hour_data
        <set>
            <if test="requestPvAdd != null">request_pv = request_pv + #{requestPvAdd},</if>
            <if test="requestUvAdd != null">request_uv = request_uv + #{requestUvAdd},</if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_article_aggr_link_hour_data
        WHERE id = #{id}
    </select>

    <select id="selectBy" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_article_aggr_link_hour_data
        <where>
            <if test="curDate != null "> and cur_date = #{curDate}</if>
            <if test="curHour != null "> and cur_hour = #{curHour}</if>
            <if test="linkId != null "> and link_id = #{linkId}</if>
        </where>
    </select>

    <select id="selectSumBy" parameterType="com.ruoyi.system.bo.landpage.article.ArticleAggrLinkDataParamBo" resultType="com.ruoyi.system.bo.landpage.article.ArticleAggrLinkDataBo">
        SELECT ifnull(sum(request_pv), 0) as requestPv, ifnull(sum(request_uv), 0) as requestUv
        FROM tb_article_aggr_link_hour_data
        <where>
            <if test="startDate != null "> and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null "> and cur_date &lt;= #{endDate}</if>
            <if test="linkIds != null and linkIds.size() > 0">
                and link_id in
                <foreach collection="linkIds" item="linkId" open="(" separator="," close=")">
                    #{linkId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectDayDataBy" parameterType="com.ruoyi.system.bo.landpage.article.ArticleAggrLinkDataParamBo" resultType="com.ruoyi.system.bo.landpage.article.ArticleAggrLinkDataBo">
        SELECT cur_date as curDate, link_id as linkId, ifnull(sum(request_pv), 0) as requestPv, ifnull(sum(request_uv), 0) as requestUv
        FROM tb_article_aggr_link_hour_data
        <where>
            <if test="startDate != null "> and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null "> and cur_date &lt;= #{endDate}</if>
            <if test="linkIds != null and linkIds.size() > 0">
                and link_id in
                <foreach collection="linkIds" item="linkId" open="(" separator="," close=")">
                    #{linkId}
                </foreach>
            </if>
        </where>
        GROUP BY cur_date, link_id
        ORDER BY cur_date desc, requestPv desc
    </select>
</mapper>
