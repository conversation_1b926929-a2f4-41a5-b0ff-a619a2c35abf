<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.landpage.article.ArticleEditHistoryMapper">

    <resultMap type="com.ruoyi.system.entity.landpage.article.ArticleEditHistoryEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="articleId" column="article_id"/>
            <result property="name" column="name"/>
            <result property="targetRequestPv" column="target_request_pv"/>
            <result property="weight" column="weight"/>
            <result property="operatorId" column="operator_id"/>
            <result property="operatorName" column="operator_name"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            article_id,
            `name`,
            target_request_pv,
            weight,
            operator_id,
            operator_name,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.landpage.article.ArticleEditHistoryEntity">
        INSERT INTO tb_article_edit_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="articleId != null">
                    article_id,
                </if>
                <if test="name != null">
                    `name`,
                </if>
                <if test="targetRequestPv != null">
                    target_request_pv,
                </if>
                <if test="weight != null">
                    weight,
                </if>
                <if test="operatorId != null">
                    operator_id,
                </if>
                <if test="operatorName != null">
                    operator_name,
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="articleId != null">
                    #{articleId},
                </if>
                <if test="name != null">
                    #{name},
                </if>
                <if test="targetRequestPv != null">
                    #{targetRequestPv},
                </if>
                <if test="weight != null">
                    #{weight},
                </if>
                <if test="operatorId != null">
                    #{operatorId},
                </if>
                <if test="operatorName != null">
                    #{operatorName},
                </if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.ruoyi.system.entity.landpage.article.ArticleEditHistoryEntity">
        UPDATE tb_article_edit_history
        <set>
            <if test="articleId != null">
                article_id = #{articleId},
            </if>
            <if test="name != null">
                `name` = #{name},
            </if>
            <if test="targetRequestPv != null">
                target_request_pv = #{targetRequestPv},
            </if>
            <if test="weight != null">
                weight = #{weight},
            </if>
            <if test="operatorId != null">
                operator_id = #{operatorId},
            </if>
            <if test="operatorName != null">
                operator_name = #{operatorName},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_article_edit_history
        WHERE id = #{id}
    </select>

    <select id="existByArticleId" resultType="Integer">
        select 1
        FROM tb_article_edit_history
        where `article_id` = #{articleId}
        limit 1
    </select>
</mapper>
