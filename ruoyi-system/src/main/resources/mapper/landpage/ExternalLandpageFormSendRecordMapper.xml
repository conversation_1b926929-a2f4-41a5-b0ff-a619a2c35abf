<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.landpage.ExternalLandpageFormSendRecordMapper">

    <resultMap type="com.ruoyi.system.entity.landpage.ExternalLandpageFormSendRecordEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="curDate" column="cur_date"/>
            <result property="recordId" column="record_id"/>
            <result property="externalNo" column="external_no"/>
            <result property="advertiserId" column="advertiser_id"/>
            <result property="url" column="url"/>
            <result property="resp" column="resp"/>
            <result property="isSuccess" column="is_success"/>
            <result property="operAccountId" column="oper_account_id"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            cur_date,
            record_id,
            external_no,
            advertiser_id,
            url,
            resp,
            is_success,
            oper_account_id,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.landpage.ExternalLandpageFormSendRecordEntity">
        INSERT INTO tb_external_landpage_form_send_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="curDate != null">
                    cur_date,
                </if>
                <if test="recordId != null">
                    record_id,
                </if>
                <if test="externalNo != null">
                    external_no,
                </if>
                <if test="advertiserId != null">
                    advertiser_id,
                </if>
                <if test="url != null">
                    url,
                </if>
                <if test="resp != null">
                    resp,
                </if>
                <if test="isSuccess != null">
                    is_success,
                </if>
                <if test="operAccountId != null">
                    oper_account_id
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="curDate != null">
                    #{curDate},
                </if>
                <if test="recordId != null">
                    #{recordId},
                </if>
                <if test="externalNo != null">
                    #{externalNo},
                </if>
                <if test="advertiserId != null">
                    #{advertiserId},
                </if>
                <if test="url != null">
                    #{url},
                </if>
                <if test="resp != null">
                    #{resp},
                </if>
                <if test="isSuccess != null">
                    #{isSuccess},
                </if>
                <if test="operAccountId != null">
                    #{operAccountId}
                </if>
        </trim>
    </insert>

    <delete id="deleteById">
        DELETE FROM tb_external_landpage_form_send_record WHERE id=#{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.landpage.ExternalLandpageFormSendRecordEntity">
        UPDATE tb_external_landpage_form_send_record
        <set>
                    <if test="curDate != null">
                        cur_date = #{curDate},
                    </if>
                    <if test="recordId != null">
                        record_id = #{recordId},
                    </if>
                    <if test="externalNo != null">
                        external_no = #{externalNo},
                    </if>
                    <if test="advertiserId != null">
                        advertiser_id = #{advertiserId},
                    </if>
                    <if test="url != null">
                        url = #{url},
                    </if>
                    <if test="resp != null">
                        resp = #{resp},
                    </if>
                    <if test="isSuccess != null">
                        is_success = #{isSuccess},
                    </if>
                    <if test="operAccountId != null">
                        oper_account_id = #{operAccountId},
                    </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_external_landpage_form_send_record
        WHERE id = #{id}
    </select>

    <select id="selectExternalNo" resultType="String">
        select external_no
        from tb_external_landpage_form_send_record
        where external_no in
        <foreach collection="list" open="(" separator="," close=")" item="externalNo">
            #{externalNo}
        </foreach>
    </select>

    <select id="selectAdvertiserIds" resultType="Long">
        select distinct advertiser_id
        from tb_landpage_form_send_record
    </select>
</mapper>
