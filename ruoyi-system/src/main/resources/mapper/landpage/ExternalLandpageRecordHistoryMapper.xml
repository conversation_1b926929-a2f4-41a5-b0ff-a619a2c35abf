<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.landpage.ExternalLandpageRecordHistoryMapper">

    <resultMap type="com.ruoyi.system.entity.landpage.ExternalLandpageRecordHistoryEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="operAccountId" column="oper_account_id"/>
            <result property="originRecordId" column="origin_record_id"/>
            <result property="externalNo" column="external_no"/>
            <result property="name" column="name"/>
            <result property="phone" column="phone"/>
            <result property="province" column="province"/>
            <result property="city" column="city"/>
            <result property="district" column="district"/>
            <result property="address" column="address"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            oper_account_id,
            origin_record_id,
            external_no,
            name,
            phone,
            province,
            city,
            district,
            address,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.landpage.ExternalLandpageRecordHistoryEntity">
        INSERT INTO tb_external_landpage_record_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="operAccountId != null">
                    oper_account_id,
                </if>
                <if test="originRecordId != null">
                    origin_record_id,
                </if>
                <if test="externalNo != null">
                    external_no,
                </if>
                <if test="name != null">
                    name,
                </if>
                <if test="phone != null">
                    phone,
                </if>
                <if test="province != null">
                    province,
                </if>
                <if test="city != null">
                    city,
                </if>
                <if test="district != null">
                    district,
                </if>
                <if test="address != null">
                    address
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="operAccountId != null">
                    #{operAccountId},
                </if>
                <if test="originRecordId != null">
                    #{originRecordId},
                </if>
                <if test="externalNo != null">
                    #{externalNo},
                </if>
                <if test="name != null">
                    #{name},
                </if>
                <if test="phone != null">
                    #{phone},
                </if>
                <if test="province != null">
                    #{province},
                </if>
                <if test="city != null">
                    #{city},
                </if>
                <if test="district != null">
                    #{district},
                </if>
                <if test="address != null">
                    #{address}
                </if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.ruoyi.system.entity.landpage.ExternalLandpageRecordHistoryEntity">
        UPDATE tb_external_landpage_record_history
        <set>
                    <if test="operAccountId != null">
                        oper_account_id = #{operAccountId},
                    </if>
                    <if test="originRecordId != null">
                        origin_record_id = #{originRecordId},
                    </if>
                    <if test="externalNo != null">
                        external_no = #{externalNo},
                    </if>
                    <if test="name != null">
                        name = #{name},
                    </if>
                    <if test="phone != null">
                        phone = #{phone},
                    </if>
                    <if test="province != null">
                        province = #{province},
                    </if>
                    <if test="city != null">
                        city = #{city},
                    </if>
                    <if test="district != null">
                        district = #{district},
                    </if>
                    <if test="address != null">
                        address = #{address},
                    </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_external_landpage_record_history
        WHERE id = #{id}
    </select>

    <select id="selectListByOriginRecordId" resultType="com.ruoyi.system.vo.datashow.CrmExternalLandpageRecordHistoryVO">
        SELECT h.oper_account_id as operAccountId, a.company_name as operAccountName,
            h.origin_record_id as originRecordId, h.external_no as externalNo, h.gmt_create as gmtCreate,
            h.name, h.phone, h.province, h.city, h.district, h.address
        FROM tb_external_landpage_record_history h
        left join tb_account a on h.oper_account_id = a.id
        WHERE h.origin_record_id in
        <foreach collection="list" open="(" separator="," close=")" item="originRecordId">
            #{originRecordId}
        </foreach>
        order by h.id desc
    </select>
</mapper>
