<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.landpage.LandpageLiuziAlipayMapper">

    <resultMap type="com.ruoyi.system.entity.landpage.LandpageLiuziAlipayEntity" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="landpageId" column="landpage_id"/>
        <result property="landpageName" column="landpage_name"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
            landpage_id,
            landpage_name,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.landpage.LandpageLiuziAlipayEntity">
        INSERT INTO tb_landpage_liuzi_alipay
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="landpageId != null">
                landpage_id,
            </if>
            <if test="landpageName != null">
                landpage_name
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="landpageId != null">
                #{landpageId},
            </if>
            <if test="landpageName != null">
                #{landpageName}
            </if>
        </trim>
    </insert>

    <delete id="deleteById">
        DELETE
        FROM tb_landpage_liuzi_alipay
        WHERE id = #{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.landpage.LandpageLiuziAlipayEntity">
        UPDATE tb_landpage_liuzi_alipay
        <set>
            <if test="landpageId != null">
                landpage_id = #{landpageId},
            </if>
            <if test="landpageName != null">
                landpage_name = #{landpageName},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_landpage_liuzi_alipay
        WHERE id = #{id}
    </select>

    <select id="selectListByLandpageIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_landpage_liuzi_alipay
        where landpage_id in
        <foreach collection="landpageIds" item="pageId" separator="," close=")" open="(">
            #{pageId}
        </foreach>
    </select>

    <insert id="batchInsertOrUpdate" useGeneratedKeys="true" keyProperty="id">
        insert into tb_landpage_liuzi_alipay(landpage_id,landpage_name)
        values
        <foreach collection="entities" item="entity" separator=",">
            ( #{entity.landpageId},#{entity.landpageName})
        </foreach>
        ON DUPLICATE KEY UPDATE
        gmt_modified = now()
    </insert>

</mapper>