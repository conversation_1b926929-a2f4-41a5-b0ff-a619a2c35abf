<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.landpage.ExternalLandpageRecordMapper">

    <resultMap type="com.ruoyi.system.entity.landpage.ExternalLandpageRecord" id="ExternalLandpageRecordResult">
        <result property="id"    column="id"    />
        <result property="submitDate"    column="submit_date"    />
        <result property="advertiserId"    column="advertiser_id"    />
        <result property="agentId"    column="agent_id"    />
        <result property="externalNo"    column="external_no"    />
        <result property="name"    column="name"    />
        <result property="phone"    column="phone"    />
        <result property="idCard"    column="id_card"    />
        <result property="idCardMd5"    column="id_card_md5"    />
        <result property="province"    column="province"    />
        <result property="city"    column="city"    />
        <result property="district"    column="district"    />
        <result property="address"    column="address"    />
        <result property="importType"    column="import_type"    />
        <result property="isExported"    column="is_exported"    />
        <result property="extInfo"    column="ext_info"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <resultMap type="com.ruoyi.system.bo.landpage.ExternalLandpageRecordBo" id="ExternalLandpageRecordBoResult">
        <result property="id"    column="id"    />
        <result property="submitDate"    column="submit_date"    />
        <result property="advertiserId"    column="advertiser_id"    />
        <result property="agentId"    column="agent_id"    />
        <result property="externalNo"    column="external_no"    />
        <result property="name"    column="name"    />
        <result property="phone"    column="phone"    />
        <result property="idCard"    column="id_card"    />
        <result property="province"    column="province"    />
        <result property="city"    column="city"    />
        <result property="district"    column="district"    />
        <result property="address"    column="address"    />
        <result property="importType"    column="import_type"    />
        <result property="isExported"    column="is_exported"    />
        <result property="extInfo"    column="ext_info"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="isSuccess"    column="is_success"    />
        <result property="resp"    column="resp"    />
        <result property="sendTime"    column="send_time"    />
        <result property="sendAdvertiserId"    column="send_advertiser_id"    />
    </resultMap>

    <sql id="selectExternalLandpageRecordVo">
        select id, submit_date, advertiser_id, agent_id, external_no, `name`, phone, id_card, id_card_md5, province, city, district, address, import_type, is_exported, ext_info, gmt_create, gmt_modified from tb_external_landpage_record
    </sql>

    <select id="selectList" parameterType="com.ruoyi.system.bo.landpage.ExternalLandpageRecordSelectBo" resultMap="ExternalLandpageRecordBoResult">
        select r.id, r.submit_date, r.advertiser_id, r.agent_id, r.external_no, r.`name`, r.phone, r.id_card, r.id_card_md5,
            r.province, r.city, r.district, r.address, r.import_type, r.is_exported, r.ext_info, r.gmt_create,
            s.is_success, s.resp, s.gmt_create as send_time, s.advertiser_id as send_advertiser_id
        from tb_external_landpage_record r
        left join tb_external_landpage_form_send_record s on r.id = s.record_id
        <where>
            <if test="submitDateStart != null "> and r.submit_date &gt;= #{submitDateStart}</if>
            <if test="submitDateEnd != null "> and r.submit_date &lt;= #{submitDateEnd}</if>
            <if test="sendDateStart != null "> and s.gmt_create &gt;= #{sendDateStart}</if>
            <if test="sendDateEnd != null "> and s.gmt_create &lt;= #{sendDateEnd}</if>
            <if test="isExported != null "> and r.is_exported = #{isExported}</if>
            <if test="sendStatus != null and sendStatus == 0"> and s.is_success is null</if>
            <if test="sendStatus != null and sendStatus == 1"> and s.is_success = 1</if>
            <if test="sendStatus != null and sendStatus == 2"> and s.is_success = 0</if>
            <if test="sendStatus != null and sendStatus == 3"> and s.is_success is not null</if>
            <if test="userSearch != null and userSearch != ''"> and (r.`name` like concat('%', #{userSearch}, '%') or r.`phone` like concat('%', #{userSearch}, '%'))</if>
            <if test="advertiserIds != null and advertiserIds.size() > 0 ">
                and r.advertiser_id in
                <foreach collection="advertiserIds" item="advertiserId" open="(" separator="," close=")">
                    #{advertiserId}
                </foreach>
            </if>
            <if test="agentIds != null and agentIds.size() > 0 ">
                and r.agent_id in
                <foreach collection="agentIds" item="agentId" open="(" separator="," close=")">
                    #{agentId}
                </foreach>
            </if>
            <if test="sendAdvertiserIds != null and sendAdvertiserIds.size() > 0 ">
                and s.advertiser_id in
                <foreach collection="sendAdvertiserIds" item="sendAdvertiserId" open="(" separator="," close=")">
                    #{sendAdvertiserId}
                </foreach>
            </if>
        </where>
        order by r.id desc
    </select>

    <select id="selectExternalLandpageRecordById" parameterType="Long" resultMap="ExternalLandpageRecordResult">
        <include refid="selectExternalLandpageRecordVo"/>
        where id = #{id}
    </select>

    <select id="selectAdvertiserIds" resultType="Long">
        select distinct advertiser_id
        from tb_external_landpage_record
    </select>

    <select id="selectAgentIds" resultType="Long">
        select distinct agent_id
        from tb_external_landpage_record where agent_id > 0
    </select>

    <insert id="insertExternalLandpageRecord" parameterType="com.ruoyi.system.entity.landpage.ExternalLandpageRecord" useGeneratedKeys="true" keyProperty="id">
        insert into tb_external_landpage_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="advertiserId != null">advertiser_id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="phone != null and phone != ''">phone,</if>
            <if test="idCard != null and idCard != ''">id_card,</if>
            <if test="idCardMd5 != null and idCardMd5 != ''">id_card_md5,</if>
            <if test="province != null and province != ''">province,</if>
            <if test="city != null and city != ''">city,</if>
            <if test="district != null and district != ''">district,</if>
            <if test="address != null and address != ''">address,</if>
            <if test="extInfo != null and extInfo != ''">ext_info,</if>
            <if test="gmtCreate != null">gmt_create,</if>
            <if test="gmtModified != null">gmt_modified,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="advertiserId != null">#{advertiserId},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="idCard != null and idCard != ''">#{idCard},</if>
            <if test="idCardMd5 != null and idCardMd5 != ''">#{idCardMd5},</if>
            <if test="province != null and province != ''">#{province},</if>
            <if test="city != null and city != ''">#{city},</if>
            <if test="district != null and district != ''">#{district},</if>
            <if test="address != null and address != ''">#{address},</if>
            <if test="extInfo != null and extInfo != ''">#{extInfo},</if>
            <if test="gmtCreate != null">#{gmtCreate},</if>
            <if test="gmtModified != null">#{gmtModified},</if>
         </trim>
    </insert>

    <update id="updateExternalLandpageRecord" parameterType="com.ruoyi.system.entity.landpage.ExternalLandpageRecord">
        update tb_external_landpage_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">`name` = #{name},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="province != null and province != ''">province = #{province},</if>
            <if test="city != null and city != ''">city = #{city},</if>
            <if test="district != null and district != ''">district = #{district},</if>
            <if test="address != null and address != ''">address = #{address},</if>
            <if test="extInfo != null and extInfo != ''">ext_info = #{extInfo},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="batchUpdateExportStatus" >
        update tb_external_landpage_record
        set is_exported = 1
        where id in
        <foreach collection="ids" open="(" separator="," close=")" item="id">
           #{id}
        </foreach>
    </update>

    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.landpage.ExternalLandpageRecord">
        INSERT INTO tb_external_landpage_record(`submit_date`,`advertiser_id`,`agent_id`,`external_no`,`name`,`phone`,
            `id_card`,`id_card_md5`,`province`,`city`,`district`,`address`,`ext_info`, import_type)
        values
        <foreach collection="entities" separator="," item="entity">
            (#{entity.submitDate},#{entity.advertiserId},#{entity.agentId},#{entity.externalNo},#{entity.name},#{entity.phone},
            #{entity.idCard},#{entity.idCardMd5},#{entity.province},#{entity.city},#{entity.district},#{entity.address},#{entity.extInfo},#{entity.importType})
        </foreach>
    </insert>

    <select id="selectExternalNo" resultType="String">
        select external_no
        from tb_external_landpage_record
        where external_no in
        <foreach collection="list" open="(" separator="," close=")" item="externalNo">
            #{externalNo}
        </foreach>
    </select>

    <select id="selectListByExternalNo" resultMap="ExternalLandpageRecordResult">
        <include refid="selectExternalLandpageRecordVo"/>
        where external_no in
        <foreach collection="list" open="(" separator="," close=")" item="externalNo">
            #{externalNo}
        </foreach>
    </select>
</mapper>
