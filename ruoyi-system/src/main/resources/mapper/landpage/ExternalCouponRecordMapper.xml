<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.landpage.ExternalCouponRecordMapper">

    <resultMap type="com.ruoyi.system.entity.landpage.ExternalCouponRecordEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="curDate" column="cur_date"/>
            <result property="advertId" column="advert_id"/>
            <result property="advertiserId" column="advertiser_id"/>
            <result property="appId" column="app_id"/>
            <result property="consumerId" column="consumer_id"/>
            <result property="couponType" column="coupon_type"/>
            <result property="couponInfo" column="coupons_info"/>
            <result property="couponParam" column="coupon_param"/>
            <result property="couponResp" column="coupon_resp"/>
            <result property="couponRequestId" column="coupon_request_id"/>
            <result property="couponStatus" column="coupon_status"/>
            <result property="orderId" column="order_id"/>
            <result property="slotId" column="slot_id"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            advert_id,
            advertiser_id,
            app_id,
            consumer_id,
            coupon_type,
            coupon_info,
            coupon_request_id,
            coupon_status,
            coupon_param,
            coupon_resp,
            cur_date,
            order_id,
            slot_id,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.landpage.ExternalCouponRecordEntity">
        INSERT INTO tb_external_coupon_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="advertId != null">
                    advert_id,
                </if>
                <if test="advertiserId != null">
                    advertiser_id,
                </if>
                <if test="appId != null">
                    app_id,
                </if>
                <if test="consumerId != null">
                    consumer_id,
                </if>
                <if test="couponInfo != null">
                    coupon_info,
                </if>
                <if test="couponParam != null">
                    coupon_param,
                </if>
                <if test="couponResp != null">
                    coupon_resp,
                </if>
                <if test="couponRequestId != null">
                    coupon_request_id,
                </if>
                <if test="couponStatus != null">
                    coupon_status,
                </if>
                <if test="curDate != null">
                    cur_date,
                </if>
                <if test="orderId != null">
                    order_id,
                </if>
                <if test="slotId != null">
                    slot_id,
                </if>
                <if test="couponType != null">
                    coupon_type,
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="advertId != null">
                    #{advertId},
                </if>
                <if test="advertiserId != null">
                    #{advertiserId},
                </if>
                <if test="appId != null">
                    #{appId},
                </if>
                <if test="consumerId != null">
                    #{consumerId},
                </if>
                <if test="couponInfo != null">
                    #{couponInfo},
                </if>
                <if test="couponParam != null">
                    #{couponParam},
                </if>
                <if test="couponResp != null">
                    #{couponResp},
                </if>
                <if test="couponRequestId != null">
                    #{couponRequestId},
                </if>
                <if test="couponStatus != null">
                    #{couponStatus},
                </if>
                <if test="curDate != null">
                    #{curDate},
                </if>
                <if test="orderId != null">
                    #{orderId},
                </if>
                <if test="slotId != null">
                    #{slotId},
                </if>
                <if test="couponType != null">
                    #{couponType},
                </if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.ruoyi.system.entity.landpage.ExternalCouponRecordEntity">
        UPDATE tb_external_coupon_record
        <set>
            <if test="advertId != null">
                advert_id = #{advertId},
            </if>
            <if test="advertiserId != null">
                advertiser_id = #{advertiserId},
            </if>
            <if test="appId != null">
                app_id = #{appId},
            </if>
            <if test="consumerId != null">
                consumer_id = #{consumerId},
            </if>
            <if test="couponInfo != null">
                coupon_info = #{couponInfo},
            </if>
            <if test="couponParam != null">
                coupon_param = #{couponParam},
            </if>
            <if test="couponResp != null">
                coupon_resp = #{couponResp},
            </if>
            <if test="couponRequestId != null">
                coupon_request_id = #{couponRequestId},
            </if>
            <if test="couponStatus != null">
                coupon_status = #{couponStatus},
            </if>
            <if test="couponType != null">
                coupon_type = #{couponType},
            </if>
            <if test="curDate != null">
                cur_date = #{curDate},
            </if>
            <if test="orderId != null">
                order_id = #{orderId},
            </if>
            <if test="slotId != null">
                slot_id = #{slotId},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectByOrderId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_external_coupon_record
        WHERE order_id = #{orderId}
        ORDER BY id desc
        LIMIT 1
    </select>
</mapper>
