<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.landpage.ExternalLandpageDayDataMapper">

    <resultMap type="com.ruoyi.system.entity.landpage.ExternalLandpageDayData" id="ExternalLandpageDayDataResult">
        <result property="id"    column="id"    />
        <result property="curDate"    column="cur_date"    />
        <result property="advertiserId"    column="advertiser_id"    />
        <result property="landpageId"    column="landpage_id"    />
        <result property="lpExposurePv"    column="lp_exposure_pv"    />
        <result property="lpExposureUv"    column="lp_exposure_uv"    />
        <result property="lpClickPv"    column="lp_click_pv"    />
        <result property="lpClickUv"    column="lp_click_uv"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectExternalLandpageDayDataVo">
        select id, cur_date, advertiser_id, landpage_id, lp_exposure_pv, lp_exposure_uv, lp_click_pv, lp_click_uv, gmt_create, gmt_modified from tb_external_landpage_day_data
    </sql>

    <select id="selectBy" resultMap="ExternalLandpageDayDataResult">
        <include refid="selectExternalLandpageDayDataVo"/>
        where cur_date = #{curDate} and landpage_id = #{landpageId} and advertiser_id = #{advertiserId}
    </select>

    <select id="selectExternalLandpageDayDataList" parameterType="com.ruoyi.system.entity.landpage.ExternalLandpageDayData" resultMap="ExternalLandpageDayDataResult">
        <include refid="selectExternalLandpageDayDataVo"/>
        <where>
            <if test="curDate != null "> and cur_date = #{curDate}</if>
            <if test="advertiserId != null "> and advertiser_id = #{advertiserId}</if>
            <if test="landpageId != null  and landpageId != ''"> and landpage_id = #{landpageId}</if>
            <if test="lpExposurePv != null "> and lp_exposure_pv = #{lpExposurePv}</if>
            <if test="lpExposureUv != null "> and lp_exposure_uv = #{lpExposureUv}</if>
            <if test="lpClickPv != null "> and lp_click_pv = #{lpClickPv}</if>
            <if test="lpClickUv != null "> and lp_click_uv = #{lpClickUv}</if>
            <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
            <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
        </where>
    </select>

    <select id="selectExternalLandpageDayDataById" parameterType="Long" resultMap="ExternalLandpageDayDataResult">
        <include refid="selectExternalLandpageDayDataVo"/>
        where id = #{id}
    </select>

    <insert id="insertExternalLandpageDayData" parameterType="com.ruoyi.system.entity.landpage.ExternalLandpageDayData" useGeneratedKeys="true" keyProperty="id">
        insert into tb_external_landpage_day_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="curDate != null">cur_date,</if>
            <if test="advertiserId != null">advertiser_id,</if>
            <if test="landpageId != null and landpageId != ''">landpage_id,</if>
            <if test="lpExposurePv != null">lp_exposure_pv,</if>
            <if test="lpExposureUv != null">lp_exposure_uv,</if>
            <if test="lpClickPv != null">lp_click_pv,</if>
            <if test="lpClickUv != null">lp_click_uv,</if>
            <if test="gmtCreate != null">gmt_create,</if>
            <if test="gmtModified != null">gmt_modified,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="curDate != null">#{curDate},</if>
            <if test="advertiserId != null">#{advertiserId},</if>
            <if test="landpageId != null and landpageId != ''">#{landpageId},</if>
            <if test="lpExposurePv != null">#{lpExposurePv},</if>
            <if test="lpExposureUv != null">#{lpExposureUv},</if>
            <if test="lpClickPv != null">#{lpClickPv},</if>
            <if test="lpClickUv != null">#{lpClickUv},</if>
            <if test="gmtCreate != null">#{gmtCreate},</if>
            <if test="gmtModified != null">#{gmtModified},</if>
         </trim>
    </insert>

    <update id="updateExternalLandpageDayData" parameterType="com.ruoyi.system.entity.landpage.ExternalLandpageDayData">
        update tb_external_landpage_day_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="curDate != null">cur_date = #{curDate},</if>
            <if test="advertiserId != null">advertiser_id = #{advertiserId},</if>
            <if test="landpageId != null and landpageId != ''">landpage_id = #{landpageId},</if>
            <if test="lpExposurePv != null">lp_exposure_pv = #{lpExposurePv},</if>
            <if test="lpExposureUv != null">lp_exposure_uv = #{lpExposureUv},</if>
            <if test="lpClickPv != null">lp_click_pv = #{lpClickPv},</if>
            <if test="lpClickUv != null">lp_click_uv = #{lpClickUv},</if>
            <if test="lpExposurePvAdd != null">lp_exposure_pv = lp_exposure_pv + #{lpExposurePvAdd},</if>
            <if test="lpExposureUvAdd != null">lp_exposure_uv = lp_exposure_uv + #{lpExposureUvAdd},</if>
            <if test="lpClickPvAdd != null">lp_click_pv = lp_click_pv + #{lpClickPvAdd},</if>
            <if test="lpClickUvAdd != null">lp_click_uv = lp_click_uv + #{lpClickUvAdd},</if>
        </trim>
        where id = #{id}
    </update>
</mapper>
