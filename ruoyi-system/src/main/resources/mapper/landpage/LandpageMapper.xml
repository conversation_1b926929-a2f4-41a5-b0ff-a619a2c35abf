<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.landpage.LandpageMapper">

    <resultMap type="com.ruoyi.system.entity.landpage.Landpage" id="LandpageResult">
        <result property="id"    column="id"    />
        <result property="key"    column="key"    />
        <result property="name"    column="name"    />
        <result property="url"    column="url"    />
        <result property="tag"    column="tag"    />
        <result property="skinType"    column="skin_type"    />
        <result property="pageConfig"    column="page_config"    />
        <result property="remark"    column="remark"    />
        <result property="isInvalid"    column="is_invalid"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectLandpageVo">
        select id, `key`, name, url, tag, page_config, skin_type, remark, is_invalid, gmt_create, gmt_modified from tb_landpage
    </sql>

    <select id="selectById" parameterType="Long" resultMap="LandpageResult">
        <include refid="selectLandpageVo"/>
        where id = #{id}
    </select>

    <select id="selectByKey" parameterType="String" resultMap="LandpageResult">
        <include refid="selectLandpageVo"/>
        where `key` = #{key}
    </select>

    <select id="selectByKeys" resultMap="LandpageResult">
        select id, `key`, name, url from tb_landpage
        <where>
            <if test="keys != null and keys.size() != 0">
                and `key` in
                <foreach collection="keys" item="key" open="(" separator="," close=")">
                    #{key}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectList" parameterType="com.ruoyi.system.entity.landpage.Landpage" resultMap="LandpageResult">
        <include refid="selectLandpageVo"/>
        <where>
            <if test="id != null "> and id = #{id}</if>
            <if test="tag != null "> and tag = #{tag}</if>
            <if test="isInvalid != null "> and is_invalid = #{isInvalid}</if>
            <if test="name != null  and name != ''"> and (`name` like concat('%', #{name}, '%') or `url` like concat('%', #{name}, '%'))</if>
            <if test="pageConfig != null and pageConfig != ''"> and page_config like concat('%', #{pageConfig}, '%')</if>
            <if test="skinType != null "> and skin_type = #{skinType}</if>
        </where>
        order by id desc
    </select>

    <select id="selectSimpleList" parameterType="com.ruoyi.system.entity.landpage.Landpage" resultMap="LandpageResult">
        select id, `key`, name, url, tag from tb_landpage
        <where>
            <if test="id != null "> and id = #{id}</if>
            <if test="tag != null "> and tag = #{tag}</if>
            <if test="name != null  and name != ''"> and (`name` like concat('%', #{name}, '%') or `url` like concat('%', #{name}, '%'))</if>
            <if test="pageConfig != null and pageConfig != ''"> and page_config like concat('%', #{pageConfig}, '%')</if>
        </where>
        order by id desc
    </select>

    <insert id="insertLandpage" parameterType="com.ruoyi.system.entity.landpage.Landpage"
            useGeneratedKeys="true" keyProperty="id">
        insert into tb_landpage
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="key != null">`key`,</if>
            <if test="name != null">`name`,</if>
            <if test="url != null">url,</if>
            <if test="tag != null">tag,</if>
            <if test="pageConfig != null">page_config,</if>
            <if test="skinType != null">skin_type,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="key != null">#{key},</if>
            <if test="name != null">#{name},</if>
            <if test="url != null">#{url},</if>
            <if test="tag != null">#{tag},</if>
            <if test="pageConfig != null">#{pageConfig},</if>
            <if test="skinType != null">#{skinType},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateLandpage" parameterType="com.ruoyi.system.entity.landpage.Landpage">
        update tb_landpage
        <trim prefix="SET" suffixOverrides=",">
            <if test="key != null">`key` = #{key},</if>
            <if test="name != null">`name` = #{name},</if>
            <if test="url != null">url = #{url},</if>
            <if test="tag != null">tag = #{tag},</if>
            <if test="pageConfig != null">page_config = #{pageConfig},</if>
            <if test="remark != null">remark = #{remark},</if>
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>

    <select id="existByKey" resultType="Integer">
        select 1
        from tb_landpage
        where `key` = #{key}
    </select>

    <update id="invalidateLandpage">
        update tb_landpage set is_invalid = 1
        where id = #{id}
        limit 1
    </update>

    <select id="getLandpageTags" resultType="String">
        select distinct tag
        from tb_landpage
    </select>
</mapper>
