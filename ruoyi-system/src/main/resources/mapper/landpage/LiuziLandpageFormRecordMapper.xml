<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.landpage.LiuziLandpageFormRecordMapper">

    <resultMap type="com.ruoyi.system.entity.datashow.LiuziLandpageFormRecord" id="LiuziLandpageFormRecordResult">
        <result property="id" column="id"/>
        <result property="advertId" column="advert_id"/>
        <result property="orderId" column="order_id"/>
        <result property="appId" column="app_id"/>
        <result property="slotId" column="slot_id"/>
        <result property="consumerId" column="consumer_id"/>
        <result property="landpageUrl" column="landpage_url"/>
        <result property="referer" column="referer"/>
        <result property="phone" column="phone"/>
        <result property="name" column="name"/>
        <result property="ip" column="ip"/>
        <result property="payAmount" column="pay_amount"/>
        <result property="payTime" column="pay_time"/>
        <result property="outTradeNo" column="out_trade_no"/>
        <result property="landpageType" column="landpage_type"/>
        <result property="friendStatus" column="friend_status"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <resultMap type="com.ruoyi.system.entity.landpage.LandpageFormCount" id="LandpageFormCountResult">
        <result property="assignDate" column="assign_date"/>
        <result property="slotId" column="slot_id"/>
        <result property="advertId" column="advert_id"/>
        <result property="formCount" column="form_count"/>
    </resultMap>

    <sql id="selectLandpageFormRecordVo">
        select id,
               advert_id,
               app_id,
               slot_id,
               order_id,
               consumer_id,
               referer,
               landpage_url,
               phone,
               name,
               ip,
               pay_amount,
               pay_time,
               out_trade_no,
               landpage_type,
               friend_status,
               gmt_create,
               gmt_modified
        from tb_landpage_form_record_liuzi
    </sql>

    <select id="selectList" parameterType="com.ruoyi.system.entity.datashow.LiuziLandpageFormRecord"
            resultMap="LiuziLandpageFormRecordResult">
        <include refid="selectLandpageFormRecordVo"/>
        <where>
            <if test="advertId != null">and advert_id = #{advertId}</if>
            <if test="appId != null">and app_id = #{appId}</if>
            <if test="slotId != null">and slot_id = #{slotId}</if>
            <if test="orderId != null  and orderId != ''">and order_id = #{orderId}</if>
            <if test="consumerId != null">and consumer_id = #{consumerId}</if>
            <if test="landpageUrl != null and landpageUrl != ''">and landpage_url like concat('%', #{landpageUrl},
                '%')
            </if>
            <if test="referer != null and referer != ''">and referer like concat('%', #{referer}, '%')</if>
            <if test="phone != null  and phone != ''">and phone = #{phone}</if>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="startDate != null ">and gmt_create &gt;= #{startDate}</if>
            <if test="endDate != null ">and gmt_create &lt;= #{endDate}</if>
            <if test="friendStatus != null ">and friend_status = #{friendStatus}</if>
            <if test="slotIds != null and slotIds.size() > 0">
                and slot_id in
                <foreach collection="slotIds" item="slotId" open="(" separator="," close=")">
                    #{slotId}
                </foreach>
            </if>
            <if test="appIds != null and appIds.size() > 0">
                and app_id in
                <foreach collection="appIds" item="appId" open="(" separator="," close=")">
                    #{appId}
                </foreach>
            </if>
            <if test="advertIds != null and advertIds.size() > 0">
                and advert_id in
                <foreach collection="advertIds" item="advertId" open="(" separator="," close=")">
                    #{advertId}
                </foreach>
            </if>
            <if test="infoSearch != null and infoSearch != ''">
                and (phone like concat('%',#{infoSearch},'%') or name like concat('%', #{infoSearch}, '%'))
            </if>
            <if test="landpageType != null">
                and landpage_type = #{landpageType}
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectLandpageFormRecordById" parameterType="Long" resultMap="LiuziLandpageFormRecordResult">
        <include refid="selectLandpageFormRecordVo"/>
        where id = #{id}
    </select>

    <select id="existByOrderId" resultType="Integer">
        select 1 where order_id = #{orderId} limit 1
    </select>

    <insert id="insertLandpageFormRecord" parameterType="com.ruoyi.system.entity.datashow.LiuziLandpageFormRecord"
            useGeneratedKeys="true" keyProperty="id">
        insert into tb_landpage_form_record_liuzi
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="advertId != null">advert_id,</if>
            <if test="orderId != null and orderId != ''">order_id,</if>
            <if test="consumerId != null">consumer_id,</if>
            <if test="referer != null">referer,</if>
            <if test="landpageUrl != null">landpage_url,</if>
            <if test="phone != null and phone != ''">phone,</if>
            <if test="name != null">name,</if>
            <if test="appId != null">app_id,</if>
            <if test="slotId != null">slot_id,</if>
            <if test="ip != null">ip,</if>
            <if test="landpageType != null">landpage_type,</if>
            <if test="outTradeNo != null">out_trade_no,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="advertId != null">#{advertId},</if>
            <if test="orderId != null and orderId != ''">#{orderId},</if>
            <if test="consumerId != null">#{consumerId},</if>
            <if test="referer != null">#{referer},</if>
            <if test="landpageUrl != null">#{landpageUrl},</if>
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="name != null">#{name},</if>
            <if test="appId != null">#{appId},</if>
            <if test="slotId != null">#{slotId},</if>
            <if test="ip != null">#{ip},</if>
            <if test="landpageType != null">#{landpageType},</if>
            <if test="outTradeNo != null">#{outTradeNo},</if>
        </trim>
    </insert>
    <insert id="batchInsert">
        insert into
        tb_landpage_form_record_liuzi(advert_id,order_id,consumer_id,landpage_url,phone,name,app_id,slot_id,origin,gmt_create,gmt_modified)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.advertId},#{item.orderId},#{item.consumerId},#{item.landpageUrl},#{item.phone},#{item.name},#{item.appId},#{item.slotId},#{item.origin},#{item.gmtCreate},#{item.gmtModified})
        </foreach>
    </insert>

    <update id="updateLandpageFormRecord" parameterType="com.ruoyi.system.entity.datashow.LiuziLandpageFormRecord">
        update tb_landpage_form_record_liuzi
        <trim prefix="SET" suffixOverrides=",">
            <if test="advertId != null">advert_id = #{advertId},</if>
            <if test="orderId != null and orderId != ''">order_id = #{orderId},</if>
            <if test="consumerId != null">consumer_id = #{consumerId},</if>
            <if test="referer != null">referer = #{referer},</if>
            <if test="landpageUrl != null">landpage_url = #{landpageUrl},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="appId != null">app_id = #{appId},</if>
            <if test="slotId != null">slot_id = #{slotId},</if>
            <if test="ip != null">ip = #{ip},</if>
            <if test="payAmount != null">pay_amount = #{payAmount},</if>
            <if test="payTime != null">pay_time = #{payTime},</if>
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>

    <select id="countByDateAndSlotId" parameterType="com.ruoyi.system.entity.datashow.LiuziLandpageFormRecord"
            resultMap="LandpageFormCountResult">
        select DATE_FORMAT(gmt_create,'%Y-%m-%d') as assign_date,slot_id as slot_id, count(1) as form_count
        from tb_landpage_form_record_liuzi
        <where>
            <if test="startDate != null ">and gmt_create &gt;= #{startDate}</if>
            <if test="endDate != null ">and gmt_create &lt;= #{endDate}</if>
            <if test="slotIds != null and slotIds.size() > 0">
                and slot_id in
                <foreach collection="slotIds" item="slotId" open="(" separator="," close=")">
                    #{slotId}
                </foreach>
            </if>
        </where>
        group by assign_date, slot_id
    </select>

    <select id="countByDate" parameterType="com.ruoyi.system.entity.datashow.LiuziLandpageFormRecord"
            resultMap="LandpageFormCountResult">
        select slot_id , advert_id , count(1) as form_count
        from tb_landpage_form_record_liuzi
        <where>
            <if test="startDate != null ">and gmt_create &gt;= #{startDate}</if>
            <if test="endDate != null ">and gmt_create &lt;= #{endDate}</if>
            <if test="slotIds != null and slotIds.size() > 0">
                and slot_id in
                <foreach collection="slotIds" item="slotId" open="(" separator="," close=")">
                    #{slotId}
                </foreach>
            </if>
        </where>
        group by slot_id,advert_id
    </select>

    <select id="selectOrderIdsByOrderIds" resultType="java.lang.String">
        select order_id
        from tb_landpage_form_record_liuzi
        where order_id in
        <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </select>

    <select id="selectUnSendSmsList" resultMap="LiuziLandpageFormRecordResult">
        select liuzi.id, liuzi.advert_id, liuzi.phone, liuzi.landpage_type, landpage_url
        from tb_landpage_form_record_liuzi liuzi
                 left join tb_liuzi_sms_send_record record
                           on liuzi.id = record.liuzi_record_id
        where record.id is null
    </select>

    <select id="selectByOutTradeNo" parameterType="String" resultMap="LiuziLandpageFormRecordResult">
        <include refid="selectLandpageFormRecordVo"/>
        where out_trade_no = #{outTradeNo}
        limit 1
    </select>

    <update id="updateFriendStatus">
        update tb_landpage_form_record_liuzi
        set friend_status = #{status}
        where phone = #{phone}
          and friend_status != 3
    </update>

    <select id="selectTotalAdvertIds" resultType="Long">
        select distinct advert_id
        from tb_landpage_form_record_liuzi
    </select>

    <select id="selectTotalAppIds" resultType="Long">
        select distinct app_id
        from tb_landpage_form_record_liuzi
    </select>

    <select id="selectTotalSlotIds" resultType="Long">
        select distinct slot_id
        from tb_landpage_form_record_liuzi
    </select>
    <select id="selectListByPhone" resultMap="LiuziLandpageFormRecordResult">
        <include refid="selectLandpageFormRecordVo"/>
        where phone in
        <foreach collection="phones" separator="," open="(" item="phone" close=")">
            #{phone}
        </foreach>
        and origin = 1
    </select>
</mapper>
