<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.landpage.LandpageFormRecordMapper">

    <resultMap type="com.ruoyi.system.entity.datashow.LandpageFormRecord" id="LandpageFormRecordResult">
        <result property="id"    column="id"    />
        <result property="advertId"    column="advert_id"    />
        <result property="orderId"    column="order_id"    />
        <result property="appId"    column="app_id"    />
        <result property="slotId"    column="slot_id"    />
        <result property="consumerId"    column="consumer_id"    />
        <result property="landpageUrl"    column="landpage_url"    />
        <result property="referer"    column="referer"    />
        <result property="phone"    column="phone"    />
        <result property="name"    column="name"    />
        <result property="idCard"    column="id_card"    />
        <result property="idCardMd5"    column="id_card_md5"    />
        <result property="areaNum"    column="area_num"    />
        <result property="province"    column="province"    />
        <result property="city"    column="city"    />
        <result property="district"    column="district"    />
        <result property="address"    column="address"    />
        <result property="ip"    column="ip"    />
        <result property="idCardAudit"    column="id_card_audit"    />
        <result property="auditApiType"    column="audit_api_type"    />
        <result property="isRepeated"    column="is_repeated"    />
        <result property="landpageTag"    column="landpage_tag"    />
        <result property="birth"    column="birth"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectLandpageFormRecordVo">
        select id, advert_id, app_id, slot_id, order_id, consumer_id, landpage_url, phone, name, id_card, id_card_md5,
               area_num, province, city, district, address, id_card_audit, audit_api_type, ip, is_repeated, landpage_tag,
               birth, gmt_create, gmt_modified
        from tb_landpage_form_record
    </sql>

    <select id="selectList" parameterType="com.ruoyi.system.entity.datashow.LandpageFormRecord" resultMap="LandpageFormRecordResult">
        <include refid="selectLandpageFormRecordVo"/>
        <where>
            <if test="advertId != null"> and advert_id = #{advertId}</if>
            <if test="appId != null"> and app_id = #{appId}</if>
            <if test="slotId != null"> and slot_id = #{slotId}</if>
            <if test="orderId != null  and orderId != ''"> and order_id = #{orderId}</if>
            <if test="consumerId != null"> and consumer_id = #{consumerId}</if>
            <if test="landpageUrl != null and landpageUrl != ''"> and landpage_url like concat('%', #{landpageUrl}, '%')</if>
            <if test="referer != null and referer != ''"> and referer like concat('%', #{referer}, '%')</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="idCard != null  and idCard != ''"> and id_card = #{idCard}</if>
            <if test="idCardMd5 != null  and idCardMd5 != ''"> and id_card_md5 = #{idCardMd5}</if>
            <if test="areaNum != null  and areaNum != ''"> and area_num = #{areaNum}</if>
            <if test="province != null  and province != ''"> and province = #{province}</if>
            <if test="city != null  and city != ''"> and city = #{city}</if>
            <if test="district != null  and district != ''"> and district = #{district}</if>
            <if test="idCardAudit != null "> and id_card_audit = #{idCardAudit}</if>
            <if test="auditApiType != null "> and audit_api_type = #{auditApiType}</if>
            <if test="startDate != null "> and gmt_create &gt;= #{startDate}</if>
            <if test="endDate != null "> and gmt_create &lt;= #{endDate}</if>
        </where>
        order by id desc
    </select>

    <select id="selectLandpageFormRecordById" parameterType="Long" resultMap="LandpageFormRecordResult">
        <include refid="selectLandpageFormRecordVo"/>
        where id = #{id}
    </select>

    <select id="existByOrderId" resultType="Integer">
        select 1
        where order_id = #{orderId}
        limit 1
    </select>

    <insert id="insertLandpageFormRecord" parameterType="com.ruoyi.system.entity.datashow.LandpageFormRecord"
            useGeneratedKeys="true" keyProperty="id">
        insert into tb_landpage_form_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="advertId != null">advert_id,</if>
            <if test="orderId != null and orderId != ''">order_id,</if>
            <if test="consumerId != null">consumer_id,</if>
            <if test="referer != null">referer,</if>
            <if test="landpageUrl != null">landpage_url,</if>
            <if test="phone != null and phone != ''">phone,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="idCard != null and idCard != ''">id_card,</if>
            <if test="idCardMd5 != null and idCardMd5 != ''">id_card_md5,</if>
            <if test="areaNum != null and areaNum != ''">area_num,</if>
            <if test="province != null and province != ''">province,</if>
            <if test="city != null and city != ''">city,</if>
            <if test="district != null and district != ''">district,</if>
            <if test="address != null and address != ''">address,</if>
            <if test="idCardAudit != null">id_card_audit,</if>
            <if test="auditApiType != null">audit_api_type,</if>
            <if test="appId != null">app_id,</if>
            <if test="slotId != null">slot_id,</if>
            <if test="ip != null">ip,</if>
            <if test="isRepeated != null">is_Repeated,</if>
            <if test="landpageTag != null">landpage_tag,</if>
            <if test="birth != null">birth,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="advertId != null">#{advertId},</if>
            <if test="orderId != null and orderId != ''">#{orderId},</if>
            <if test="consumerId != null">#{consumerId},</if>
            <if test="referer != null">#{referer},</if>
            <if test="landpageUrl != null">#{landpageUrl},</if>
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="idCard != null and idCard != ''">#{idCard},</if>
            <if test="idCardMd5 != null and idCardMd5 != ''">#{idCardMd5},</if>
            <if test="areaNum != null and areaNum != ''">#{areaNum},</if>
            <if test="province != null and province != ''">#{province},</if>
            <if test="city != null and city != ''">#{city},</if>
            <if test="district != null and district != ''">#{district},</if>
            <if test="address != null and address != ''">#{address},</if>
            <if test="idCardAudit != null">#{idCardAudit},</if>
            <if test="auditApiType != null">#{auditApiType},</if>
            <if test="appId != null">#{appId},</if>
            <if test="slotId != null">#{slotId},</if>
            <if test="ip != null">#{ip},</if>
            <if test="isRepeated != null">#{isRepeated},</if>
            <if test="landpageTag != null">#{landpageTag},</if>
            <if test="birth != null">#{birth},</if>
         </trim>
    </insert>

    <update id="updateLandpageFormRecord" parameterType="com.ruoyi.system.entity.datashow.LandpageFormRecord">
        update tb_landpage_form_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="advertId != null">advert_id = #{advertId},</if>
            <if test="orderId != null and orderId != ''">order_id = #{orderId},</if>
            <if test="consumerId != null">consumer_id = #{consumerId},</if>
            <if test="referer != null">referer = #{referer},</if>
            <if test="landpageUrl != null">landpage_url = #{landpageUrl},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="idCard != null and idCard != ''">id_card = #{idCard},</if>
            <if test="idCardMd5 != null and idCardMd5 != ''">id_card_md5 = #{idCardMd5},</if>
            <if test="areaNum != null and areaNum != ''">area_num = #{areaNum},</if>
            <if test="province != null and province != ''">province = #{province},</if>
            <if test="city != null and city != ''">city = #{city},</if>
            <if test="district != null and district != ''">district = #{district},</if>
            <if test="address != null and address != ''">address = #{address},</if>
            <if test="idCardAudit != null">id_card_audit = #{idCardAudit},</if>
            <if test="auditApiType != null">audit_api_type = #{auditApiType},</if>
            <if test="appId != null">app_id = #{appId},</if>
            <if test="slotId != null">slot_id = #{slotId},</if>
            <if test="ip != null">ip = #{ip},</if>
            <if test="isRepeated != null">is_repeated = #{isRepeated},</if>
            <if test="landpageTag != null">landpage_tag = #{landpageTag},</if>
            <if test="birth != null">birth = #{birth},</if>
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>

    <select id="selectByIdCardMd5" parameterType="String" resultMap="LandpageFormRecordResult">
        <include refid="selectLandpageFormRecordVo"/>
        <where>
            id_card_md5 = #{idCardMd5}
            <if test="landpageTag != null and landpageTag != ''">and landpage_tag = #{landpageTag}</if>
        </where>
    </select>

    <select id="isFormRepeated" resultType="Integer">
        select 1
        from tb_landpage_form_record
        <where>
            id_card_md5 = #{idCardMd5} and landpage_tag = #{landpageTag}
        </where>
        limit 1
    </select>

    <select id="isFormRepeatedInHalfMonth" resultType="Integer">
        select 1
        from tb_landpage_form_record
        <where>
            id_card_md5 = #{idCardMd5} and landpage_tag = #{landpageTag} and `gmt_create` &gt;= date_sub(curdate(), interval 15 day)
        </where>
        limit 1
    </select>

    <select id="selectTotalAdvertIds" resultType="Long">
        select distinct advert_id
        from tb_landpage_form_record
    </select>

    <select id="selectTotalAppIds" resultType="Long">
        select distinct app_id
        from tb_landpage_form_record
    </select>

    <select id="selectTotalSlotIds" resultType="Long">
        select distinct slot_id
        from tb_landpage_form_record
    </select>
</mapper>
