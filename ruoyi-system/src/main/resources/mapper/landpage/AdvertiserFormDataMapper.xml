<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.landpage.AdvertiserFormDataMapper">

    <resultMap type="com.ruoyi.system.entity.landpage.AdvertiserFormDataEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="curDate" column="cur_date"/>
            <result property="advertiserId" column="advertiser_id"/>
            <result property="formCount" column="form_count"/>
            <result property="successFormCount" column="success_form_count"/>
            <result property="consume" column="consume"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            cur_date,
            advertiser_id,
            form_count,
            success_form_count,
            consume,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.landpage.AdvertiserFormDataEntity">
        INSERT IGNORE INTO tb_advertiser_form_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="curDate != null">
                    cur_date,
                </if>
                <if test="advertiserId != null">
                    advertiser_id,
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="curDate != null">
                    #{curDate},
                </if>
                <if test="advertiserId != null">
                    #{advertiserId},
                </if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.ruoyi.system.entity.landpage.AdvertiserFormDataEntity">
        UPDATE tb_advertiser_form_data
        <set>
            <if test="formCountAdd != null">
                form_count = form_count + #{formCountAdd},
            </if>
            <if test="successFormCountAdd != null">
                success_form_count = success_form_count + #{successFormCountAdd},
            </if>
            <if test="consumeAdd != null">
                consume = consume + #{consumeAdd},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_advertiser_form_data
        WHERE id = #{id}
    </select>

    <select id="selectBy" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_advertiser_form_data
        WHERE cur_date = #{curDate} and advertiser_id = #{advertiserId}
    </select>

    <select id="selectListByDateAndAdvertiserIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_advertiser_form_data
        <where>
            <if test="startDate != null "> and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null "> and cur_date &lt;= #{endDate}</if>
            <if test="advertiserIds != null and advertiserIds.size() > 0">
                and advertiser_id in
                <foreach collection="advertiserIds" item="advertiserId" open="(" separator="," close=")">
                    #{advertiserId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="sumFormDataByDateAndAdvertiserIds" resultMap="BaseResultMap">
        SELECT ifnull(sum(form_count), 0) as form_count, ifnull(sum(success_form_count), 0) as success_form_count, ifnull(sum(consume), 0) as consume
        FROM tb_advertiser_form_data
        <where>
            <if test="startDate != null "> and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null "> and cur_date &lt;= #{endDate}</if>
            <if test="advertiserIds != null and advertiserIds.size() > 0">
                and advertiser_id in
                <foreach collection="advertiserIds" item="advertiserId" open="(" separator="," close=")">
                    #{advertiserId}
                </foreach>
            </if>
        </where>
    </select>
</mapper>
