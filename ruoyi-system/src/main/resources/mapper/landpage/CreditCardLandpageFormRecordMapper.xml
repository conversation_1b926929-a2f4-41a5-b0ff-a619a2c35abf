<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.landpage.CreditCardLandpageFormRecordMapper">

    <resultMap type="com.ruoyi.system.entity.landpage.CreditCardLandpageFormRecordEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="orderId" column="order_id"/>
            <result property="appId" column="app_id"/>
            <result property="slotId" column="slot_id"/>
            <result property="advertiserId" column="advertiser_id"/>
            <result property="advertId" column="advert_id"/>
            <result property="consumerId" column="consumer_id"/>
            <result property="landpageUrl" column="landpage_url"/>
            <result property="referer" column="referer"/>
            <result property="name" column="name"/>
            <result property="phone" column="phone"/>
            <result property="address" column="address"/>
            <result property="idCard" column="id_card"/>
            <result property="idCardMd5" column="id_card_md5"/>
            <result property="birth" column="birth"/>
            <result property="creditLimit" column="credit_limit"/>
            <result property="ip" column="ip"/>
            <result property="auditApiType" column="audit_api_type"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            order_id,
            app_id,
            slot_id,
            advertiser_id,
            advert_id,
            consumer_id,
            landpage_url,
            referer,
            name,
            phone,
            address,
            id_card,
            id_card_md5,
            birth,
            credit_limit,
            ip,
            audit_api_type,
            gmt_create,
            gmt_modified
    </sql>

    <select id="selectList" parameterType="com.ruoyi.system.req.datashow.CreditCardLandpageFromRecordReq" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_landpage_form_record_credit_card
        <where>
            <if test="landpageUrl != null and landpageUrl != ''"> and landpage_url like concat('%', #{landpageUrl}, '%')</if>
            <if test="startDate != null "> and gmt_create &gt;= #{startDate}</if>
            <if test="endDate != null "> and gmt_create &lt;= #{endDate}</if>
            <if test="slotIds != null and slotIds.size() > 0">
                and slot_id in
                <foreach collection="slotIds" item="slotId" open="(" separator="," close=")">
                    #{slotId}
                </foreach>
            </if>
            <if test="appIds != null and appIds.size() > 0">
                and app_id in
                <foreach collection="appIds" item="appId" open="(" separator="," close=")">
                    #{appId}
                </foreach>
            </if>
            <if test="advertIds != null and advertIds.size() > 0">
                and advert_id in
                <foreach collection="advertIds" item="advertId" open="(" separator="," close=")">
                    #{advertId}
                </foreach>
            </if>
            <if test="advertiserIds != null and advertiserIds.size() > 0">
                and advertiser_id in
                <foreach collection="advertiserIds" item="advertiserId" open="(" separator="," close=")">
                    #{advertiserId}
                </foreach>
            </if>
        </where>
        order by id desc
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.landpage.CreditCardLandpageFormRecordEntity">
        INSERT INTO tb_landpage_form_record_credit_card
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="orderId != null">
                    order_id,
                </if>
                <if test="appId != null">
                    app_id,
                </if>
                <if test="slotId != null">
                    slot_id,
                </if>
                <if test="advertiserId != null">
                    advertiser_id,
                </if>
                <if test="advertId != null">
                    advert_id,
                </if>
                <if test="consumerId != null">
                    consumer_id,
                </if>
                <if test="landpageUrl != null">
                    landpage_url,
                </if>
                <if test="referer != null">
                    referer,
                </if>
                <if test="name != null">
                    name,
                </if>
                <if test="phone != null">
                    phone,
                </if>
                <if test="address != null">
                    address,
                </if>
                <if test="idCard != null">
                    id_card,
                </if>
                <if test="idCardMd5 != null">
                    id_card_md5,
                </if>
                <if test="birth != null">
                    birth,
                </if>
                <if test="creditLimit != null">
                    credit_limit,
                </if>
                <if test="ip != null">
                    ip,
                </if>
                <if test="auditApiType != null">
                    audit_api_type
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="orderId != null">
                    #{orderId},
                </if>
                <if test="appId != null">
                    #{appId},
                </if>
                <if test="slotId != null">
                    #{slotId},
                </if>
                <if test="advertiserId != null">
                    #{advertiserId},
                </if>
                <if test="advertId != null">
                    #{advertId},
                </if>
                <if test="consumerId != null">
                    #{consumerId},
                </if>
                <if test="landpageUrl != null">
                    #{landpageUrl},
                </if>
                <if test="referer != null">
                    #{referer},
                </if>
                <if test="name != null">
                    #{name},
                </if>
                <if test="phone != null">
                    #{phone},
                </if>
                <if test="address != null">
                    #{address},
                </if>
                <if test="idCard != null">
                    #{idCard},
                </if>
                <if test="idCardMd5 != null">
                    #{idCardMd5},
                </if>
                <if test="birth != null">
                    #{birth},
                </if>
                <if test="creditLimit != null">
                    #{creditLimit},
                </if>
                <if test="ip != null">
                    #{ip},
                </if>
                <if test="auditApiType != null">
                    #{auditApiType}
                </if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.ruoyi.system.entity.landpage.CreditCardLandpageFormRecordEntity">
        UPDATE tb_landpage_form_record_credit_card
        <set>
                    <if test="orderId != null">
                        order_id = #{orderId},
                    </if>
                    <if test="appId != null">
                        app_id = #{appId},
                    </if>
                    <if test="slotId != null">
                        slot_id = #{slotId},
                    </if>
                    <if test="advertiserId != null">
                        advertiser_id = #{advertiserId},
                    </if>
                    <if test="advertId != null">
                        advert_id = #{advertId},
                    </if>
                    <if test="consumerId != null">
                        consumer_id = #{consumerId},
                    </if>
                    <if test="landpageUrl != null">
                        landpage_url = #{landpageUrl},
                    </if>
                    <if test="referer != null">
                        referer = #{referer},
                    </if>
                    <if test="name != null">
                        name = #{name},
                    </if>
                    <if test="phone != null">
                        phone = #{phone},
                    </if>
                    <if test="address != null">
                        address = #{address},
                    </if>
                    <if test="idCard != null">
                        id_card = #{idCard},
                    </if>
                    <if test="idCardMd5 != null">
                        id_card_md5 = #{idCardMd5},
                    </if>
                    <if test="birth != null">
                        birth = #{birth},
                    </if>
                    <if test="creditLimit != null">
                        credit_limit = #{creditLimit},
                    </if>
                    <if test="ip != null">
                        ip = #{ip},
                    </if>
                    <if test="auditApiType != null">
                        audit_api_type = #{auditApiType},
                    </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_landpage_form_record_credit_card
        WHERE id = #{id}
    </select>

    <select id="selectTotalAdvertIds" resultType="Long">
        select distinct advert_id
        from tb_landpage_form_record_credit_card
    </select>

    <select id="selectTotalAdvertiserIds" resultType="Long">
        select distinct advertiser_id
        from tb_landpage_form_record_credit_card
    </select>

    <select id="selectTotalAppIds" resultType="Long">
        select distinct app_id
        from tb_landpage_form_record_credit_card
    </select>

    <select id="selectTotalSlotIds" resultType="Long">
        select distinct slot_id
        from tb_landpage_form_record_credit_card
    </select>
</mapper>
