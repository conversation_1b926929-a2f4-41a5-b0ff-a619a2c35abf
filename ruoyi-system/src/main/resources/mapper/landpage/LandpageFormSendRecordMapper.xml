<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.landpage.LandpageFormSendRecordMapper">

    <resultMap type="com.ruoyi.system.entity.datashow.LandpageFormSendRecordEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="advertiserId" column="advertiser_id"/>
            <result property="recordId" column="record_id"/>
            <result property="orderId" column="order_id"/>
            <result property="channel" column="channel"/>
            <result property="url" column="url"/>
            <result property="resp" column="resp"/>
            <result property="isSuccess" column="is_success"/>
            <result property="formPrice" column="form_price"/>
            <result property="remark" column="remark"/>
            <result property="operatorId" column="operator_id"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            advertiser_id,
            record_id,
            order_id,
            channel,
            resp,
            is_success,
            form_price,
            remark,
            operator_id,
            gmt_create
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.datashow.LandpageFormSendRecordEntity">
        INSERT INTO tb_landpage_form_send_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="advertiserId != null">
                advertiser_id,
            </if>
            <if test="recordId != null">
                record_id,
            </if>
            <if test="orderId != null">
                order_id,
            </if>
            <if test="channel != null">
                channel,
            </if>
            <if test="url != null">
                url,
            </if>
            <if test="resp != null">
                resp,
            </if>
            <if test="isSuccess != null">
                is_success,
            </if>
            <if test="formPrice != null">
                form_price,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="operatorId != null">
                operator_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="advertiserId != null">
                #{advertiserId},
            </if>
            <if test="recordId != null">
                #{recordId},
            </if>
            <if test="orderId != null">
                #{orderId},
            </if>
            <if test="channel != null">
                #{channel},
            </if>
            <if test="url != null">
                #{url},
            </if>
            <if test="resp != null">
                #{resp},
            </if>
            <if test="isSuccess != null">
                #{isSuccess},
            </if>
            <if test="formPrice != null">
                #{formPrice},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="operatorId != null">
                #{operatorId},
            </if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.ruoyi.system.entity.datashow.LandpageFormSendRecordEntity">
        UPDATE tb_landpage_form_send_record
        <set>
            <if test="advertiserId != null">
                advertiser_id = #{advertiserId},
            </if>
            <if test="recordId != null">
                record_id = #{recordId},
            </if>
            <if test="orderId != null">
                order_id = #{orderId},
            </if>
            <if test="channel != null">
                channel = #{channel},
            </if>
            <if test="url != null">
                url = #{url},
            </if>
            <if test="resp != null">
                resp = #{resp},
            </if>
            <if test="isSuccess != null">
                is_success = #{isSuccess},
            </if>
            <if test="formPrice != null">
                form_price = #{formPrice},
            </if>
            gmt_modified = now()
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_landpage_form_send_record
        WHERE id = #{id}
    </select>

    <select id="selectByRecordId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_landpage_form_send_record
        WHERE record_id = #{recordId}
        LIMIT 1
    </select>

    <select id="existByAdvertiserIdAndRecordId" resultType="Integer">
        SELECT 1
        FROM tb_landpage_form_send_record
        WHERE advertiser_id = #{advertiserId} and record_id = #{recordId}
        LIMIT 1
    </select>

    <select id="existByRecordId" resultType="Integer">
        SELECT 1
        FROM tb_landpage_form_send_record
        WHERE record_id = #{recordId}
        LIMIT 1
    </select>

    <select id="selectMapByRecordIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_landpage_form_send_record
        where record_id in
        <foreach item="recordId" collection="list" open="(" separator="," close=")">
            #{recordId}
        </foreach>
        order by gmt_create asc
    </select>

    <select id="selectTotalTargetAdvertiserIds" resultType="Long">
        select distinct advertiser_id
        from tb_landpage_form_send_record
    </select>
</mapper>
