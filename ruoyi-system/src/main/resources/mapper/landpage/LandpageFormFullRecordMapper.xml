<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.landpage.LandpageFormFullRecordMapper">

    <resultMap type="com.ruoyi.system.entity.landpage.LandpageFormFullRecord" id="LandpageFormFullRecordResult">
        <result property="id"    column="id"    />
        <result property="advertId"    column="advert_id"    />
        <result property="orderId"    column="order_id"    />
        <result property="appId"    column="app_id"    />
        <result property="slotId"    column="slot_id"    />
        <result property="consumerId"    column="consumer_id"    />
        <result property="landpageUrl"    column="landpage_url"    />
        <result property="referer"    column="referer"    />
        <result property="phone"    column="phone"    />
        <result property="name"    column="name"    />
        <result property="idCard"    column="id_card"    />
        <result property="idCardMd5"    column="id_card_md5"    />
        <result property="areaNum"    column="area_num"    />
        <result property="province"    column="province"    />
        <result property="city"    column="city"    />
        <result property="district"    column="district"    />
        <result property="address"    column="address"    />
        <result property="ip"    column="ip"    />
        <result property="idCardAudit"    column="id_card_audit"    />
        <result property="auditApiType"    column="audit_api_type"    />
        <result property="isRepeated"    column="is_repeated"    />
        <result property="isSuccess"    column="is_success"    />
        <result property="landpageTag"    column="landpage_tag"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
        <result property="targetAdvertiserId"    column="advertiser_id"    />
        <result property="channel"    column="channel"    />
        <result property="resp"    column="resp"    />
        <result property="callbackTime"    column="callback_time"    />
        <result property="birth"    column="birth"    />
        <result property="formPrice"    column="form_price"    />
        <result property="remark"    column="remark"    />
        <result property="operatorId"    column="operator_id"    />
    </resultMap>

    <resultMap type="com.ruoyi.system.entity.landpage.LandpageFormCount" id="LandpageFormCountResult">
        <result property="assignDate"    column="assign_date"    />
        <result property="slotId"    column="slot_id"    />
        <result property="advertId"    column="advert_id"    />
        <result property="advertiserId"   column="advertiser_id"    />
        <result property="formCount"    column="form_count"    />
    </resultMap>

    <select id="selectList" parameterType="com.ruoyi.system.entity.landpage.LandpageFormFullRecord" resultMap="LandpageFormFullRecordResult">
        select r.id, r.advert_id, r.app_id, r.slot_id, r.order_id, r.consumer_id, r.landpage_url, r.phone, r.name, r.id_card,
        r.id_card_md5, r.area_num, r.province, r.city, r.district, r.address, r.id_card_audit, r.audit_api_type, r.ip,
        r.is_repeated, r.landpage_tag, r.gmt_create, r.gmt_modified,
        s.advertiser_id, s.channel, s.resp, s.gmt_create as callback_time, s.is_success, r.birth, s.form_price, s.remark, s.operator_id
        from tb_landpage_form_record r left join tb_landpage_form_send_record s on r.id = s.record_id
        <where>
            <if test="id != null"> and r.id = #{id}</if>
            <if test="advertId != null"> and r.advert_id = #{advertId}</if>
            <if test="appId != null"> and r.app_id = #{appId}</if>
            <if test="slotId != null"> and r.slot_id = #{slotId}</if>
            <if test="orderId != null  and orderId != ''"> and r.order_id = #{orderId}</if>
            <if test="landpageUrl != null and landpageUrl != ''"> and r.landpage_url like concat('%', #{landpageUrl}, '%')</if>
            <if test="landpageTag != null and landpageTag != ''"> and r.landpage_tag = #{landpageTag}</if>
            <if test="phone != null  and phone != ''"> and r.phone = #{phone}</if>
            <if test="name != null  and name != ''"> and r.name like concat('%', #{name}, '%')</if>
            <if test="idCard != null  and idCard != ''"> and r.id_card = #{idCard}</if>
            <if test="idCardMd5 != null  and idCardMd5 != ''"> and r.id_card_md5 = #{idCardMd5}</if>
            <if test="areaNum != null  and areaNum != ''"> and r.area_num = #{areaNum}</if>
            <if test="province != null  and province != ''"> and r.province = #{province}</if>
            <if test="city != null  and city != ''"> and r.city = #{city}</if>
            <if test="district != null  and district != ''"> and r.district = #{district}</if>
            <if test="idCardAudit != null "> and r.id_card_audit = #{idCardAudit}</if>
            <if test="auditApiType != null "> and r.audit_api_type = #{auditApiType}</if>
            <if test="startDate != null "> and r.gmt_create &gt;= #{startDate}</if>
            <if test="endDate != null "> and r.gmt_create &lt;= #{endDate}</if>
            <if test="cbStartDate != null "> and s.gmt_create &gt;= #{cbStartDate}</if>
            <if test="cbEndDate != null "> and s.gmt_create &lt;= #{cbEndDate}</if>
            <if test="assignStatus != null and assignStatus == 1"> and s.gmt_create is null and r.is_repeated = 0</if>
            <if test="assignStatus != null and assignStatus == 2"> and s.gmt_create is null and r.is_repeated = 1</if>
            <if test="assignStatus != null and assignStatus == 3"> and s.gmt_create is not null and s.is_success = 1</if>
            <if test="assignStatus != null and assignStatus == 4"> and s.gmt_create is not null and s.is_success = 0</if>
            <if test="targetAdvertiserId != null "> and s.advertiser_id = #{targetAdvertiserId}</if>
            <if test="startBirth != null "> and r.birth &gt;= #{startBirth}</if>
            <if test="endBirth != null "> and r.birth &lt;= #{endBirth}</if>
            <if test="advertIds != null and advertIds.size() > 0">
                and r.advert_id in
                <foreach collection="advertIds" item="advertId" open="(" separator="," close=")">
                    #{advertId}
                </foreach>
            </if>
            <if test="appIds != null and appIds.size() > 0">
                and r.app_id in
                <foreach collection="appIds" item="appId" open="(" separator="," close=")">
                    #{appId}
                </foreach>
            </if>
            <if test="slotIds != null and slotIds.size() > 0">
                and r.slot_id in
                <foreach collection="slotIds" item="slotId" open="(" separator="," close=")">
                    #{slotId}
                </foreach>
            </if>
            <if test="advertiserIds != null and advertiserIds.size() > 0">
                and s.advertiser_id in
                <foreach collection="advertiserIds" item="advertiserId" open="(" separator="," close=")">
                    #{advertiserId}
                </foreach>
            </if>
            <if test="areaList != null and areaList.size() > 0">
                and (r.province in
                <foreach collection="areaList" item="area" open="(" separator="," close=")">
                    #{area}
                </foreach>
                 or r.city in
                <foreach collection="areaList" item="area" open="(" separator="," close=")">
                    #{area}
                </foreach>
                )
            </if>
        </where>
        order by r.id desc
    </select>

    <select id="selectSumPriceGroupBySlot" parameterType="com.ruoyi.system.entity.landpage.LandpageFormFullRecord" resultType="com.ruoyi.system.bo.landpage.LandPageSumDataBo">
        select r.slot_id as slotId, sum(s.form_price) as formPriceSum
        from tb_landpage_form_record r left join tb_landpage_form_send_record s on r.id = s.record_id
        <where>
            <if test="cbStartDate != null "> and s.gmt_create &gt;= #{cbStartDate}</if>
            <if test="cbEndDate != null "> and s.gmt_create &lt;= #{cbEndDate}</if>
            <if test="slotIds != null and slotIds.size() > 0">
                and r.slot_id in
                <foreach collection="slotIds" item="slotId" open="(" separator="," close=")">
                    #{slotId}
                </foreach>
            </if>
        </where>
        group by slot_id
    </select>

    <select id="countByDateAndSlotId" parameterType="com.ruoyi.system.entity.landpage.LandpageFormFullRecord" resultMap="LandpageFormCountResult">
        select DATE_FORMAT(gmt_create,'%Y-%m-%d') as assign_date,slot_id as slot_id, count(1) as form_count
        from tb_landpage_form_record
        <where>
            <if test="startDate != null "> and gmt_create &gt;= #{startDate}</if>
            <if test="endDate != null "> and gmt_create &lt;= #{endDate}</if>
            <if test="slotIds != null and slotIds.size() > 0">
                and slot_id in
                <foreach collection="slotIds" item="slotId" open="(" separator="," close=")">
                    #{slotId}
                </foreach>
            </if>
        </where>
        group by assign_date, slot_id
    </select>

    <select id="sumByDateAndSlotId" parameterType="com.ruoyi.system.entity.landpage.LandpageFormFullRecord" resultType="Integer">
        select count(1)
        from tb_landpage_form_record
        <where>
            <if test="startDate != null "> and gmt_create &gt;= #{startDate}</if>
            <if test="endDate != null "> and gmt_create &lt;= #{endDate}</if>
            <if test="slotIds != null and slotIds.size() > 0">
                and slot_id in
                <foreach collection="slotIds" item="slotId" open="(" separator="," close=")">
                    #{slotId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="countByDate" parameterType="com.ruoyi.system.entity.landpage.LandpageFormFullRecord" resultMap="LandpageFormCountResult">
        select slot_id , advert_id , count(1) as form_count
        from tb_landpage_form_record
        <where>
            <if test="startDate != null "> and gmt_create &gt;= #{startDate}</if>
            <if test="endDate != null "> and gmt_create &lt;= #{endDate}</if>
            <if test="slotIds != null and slotIds.size() > 0">
                and slot_id in
                <foreach collection="slotIds" item="slotId" open="(" separator="," close=")">
                    #{slotId}
                </foreach>
            </if>
        </where>
        group by slot_id,advert_id
    </select>

    <select id="countByDateAndAdvertiserId" resultMap="LandpageFormCountResult">
        select DATE_FORMAT(gmt_create,'%Y-%m-%d') as assign_date, advertiser_id as advertiser_id, count(1) as form_count
        from tb_landpage_form_send_record
        <where>
            <if test="startDate != null "> and gmt_create &gt;= #{startDate}</if>
            <if test="endDate != null "> and gmt_create &lt;= #{endDate}</if>
            <if test="advertiserIds != null and advertiserIds.size() > 0">
                and advertiser_id in
                <foreach collection="advertiserIds" item="advertiserId" open="(" separator="," close=")">
                    #{advertiserId}
                </foreach>
            </if>
            <if test="isSuccess != null"> and is_success = #{isSuccess}</if>
        </where>
        group by assign_date, advertiser_id
    </select>
    <select id="selectByOrderIds" resultMap="LandpageFormFullRecordResult">
        select order_id , gmt_create
        from tb_landpage_form_record
        <where>
            <if test="orderIds != null and orderIds.size > 0">
                and order_id in
                <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
                    #{orderId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectFormCountGroupByArea" resultType="com.ruoyi.system.entity.landpage.LandpageFormAreaCount">
        select form_t.`province`, form_t.`city`, form_t.formCount, form_t.consume, register, pay
        from (
            select `province`, `city`, COUNT(1) as formCount, SUM(`form_price`) as consume
            from `tb_landpage_form_send_record` s
            left join `tb_landpage_form_record` r on s.record_id = r.id
            <where>
                <if test="startDate != null "> and s.`gmt_create` &gt;= #{startDate}</if>
                <if test="endDate != null "> and s.`gmt_create` &lt;= #{endDate}</if>
                <if test="advertiserIds != null and advertiserIds.size() > 0">
                    and s.advertiser_id in
                    <foreach collection="advertiserIds" item="advertiserId" open="(" separator="," close=")">
                        #{advertiserId}
                    </foreach>
                </if>
                <if test="areaList != null">
                    and (province in
                    <foreach collection="areaList" open="(" separator="," close=")" item="area">
                        #{area}
                    </foreach>
                    or city in
                    <foreach collection="areaList" open="(" separator="," close=")" item="area">
                        #{area}
                    </foreach>
                    )
                </if>
            </where>
            GROUP BY `province`, `city`
        ) form_t
        left join (
            select `province`, `city`,
                count(distinct case when c.`status` in (2,4) then s.record_id end ) as register,
                count(distinct case when c.`status` = 2 then s.record_id end ) as pay
            from `tb_landpage_form_send_record` s
            left join `tb_landpage_form_record` r on s.record_id = r.id
            left join `tb_advertiser_callback_record` c on s.order_id = c.`order_no`
            <where>
                c.`status` in (2, 4)
                <if test="startDate != null "> and s.`gmt_create` &gt;= #{startDate}</if>
                <if test="endDate != null "> and s.`gmt_create` &lt;= #{endDate}</if>
                <if test="advertiserIds != null and advertiserIds.size() > 0">
                    and s.advertiser_id in
                    <foreach collection="advertiserIds" item="advertiserId" open="(" separator="," close=")">
                        #{advertiserId}
                    </foreach>
                </if>
            </where>
            GROUP BY `province`, `city`
        ) conv_t on form_t.province = conv_t.province and form_t.city = conv_t.city
        ORDER BY form_t.formCount desc, form_t.consume desc, register desc
    </select>

    <select id="selectFormCountSummary" resultType="com.ruoyi.system.entity.landpage.LandpageFormAreaCount">
        select formCount, consume, register, pay
        from(
            select count(distinct case when c.`status` = 2 then s.record_id end ) as pay,
            count(distinct case when c.`status` in (2,4) then s.record_id end ) as register
            from `tb_landpage_form_send_record` s
            left join `tb_advertiser_callback_record` c on s.order_id= c.`order_no`
            left join `tb_landpage_form_record` r on s.record_id = r.id
            <where>
                c.`status` in (2, 4)
                <if test="startDate != null "> and s.`gmt_create` &gt;= #{startDate}</if>
                <if test="endDate != null "> and s.`gmt_create` &lt;= #{endDate}</if>
                <if test="advertiserIds != null and advertiserIds.size() > 0">
                    and s.advertiser_id in
                    <foreach collection="advertiserIds" item="advertiserId" open="(" separator="," close=")">
                        #{advertiserId}
                    </foreach>
                </if>
                <if test="areaList != null">
                    and (province in
                    <foreach collection="areaList" open="(" separator="," close=")" item="area">
                        #{area}
                    </foreach>
                    or city in
                    <foreach collection="areaList" open="(" separator="," close=")" item="area">
                        #{area}
                    </foreach>
                    )
                </if>
            </where>
        ) t, (
            select COUNT(1) as formCount, SUM(`form_price`) as consume
            from `tb_landpage_form_send_record` s
            left join `tb_landpage_form_record` r on s.record_id = r.id
            <where>
                <if test="startDate != null "> and s.`gmt_create` &gt;= #{startDate}</if>
                <if test="endDate != null "> and s.`gmt_create` &lt;= #{endDate}</if>
                <if test="advertiserIds != null and advertiserIds.size() > 0">
                    and s.advertiser_id in
                    <foreach collection="advertiserIds" item="advertiserId" open="(" separator="," close=")">
                        #{advertiserId}
                    </foreach>
                </if>
                <if test="areaList != null">
                    and (province in
                    <foreach collection="areaList" open="(" separator="," close=")" item="area">
                        #{area}
                    </foreach>
                    or city in
                    <foreach collection="areaList" open="(" separator="," close=")" item="area">
                        #{area}
                    </foreach>
                    )
                </if>
            </where>
        ) form
        LIMIT 1
    </select>
</mapper>
