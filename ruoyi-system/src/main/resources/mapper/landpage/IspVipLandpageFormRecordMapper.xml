<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.landpage.IspVipLandpageFormRecordMapper">

    <resultMap type="com.ruoyi.system.entity.landpage.IspVipLandpageFormRecordEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="orderId" column="order_id"/>
            <result property="advertId" column="advert_id"/>
            <result property="consumerId" column="consumer_id"/>
            <result property="appId" column="app_id"/>
            <result property="slotId" column="slot_id"/>
            <result property="productType" column="product_type"/>
            <result property="bizOrderNo" column="biz_order_no"/>
            <result property="phone" column="phone"/>
            <result property="status" column="status"/>
            <result property="msg" column="msg"/>
            <result property="ext" column="ext"/>
            <result property="ip" column="ip"/>
            <result property="landpageUrl" column="landpage_url"/>
            <result property="referer" column="referer"/>
            <result property="userAgent" column="user_agent"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            order_id,
            advert_id,
            consumer_id,
            app_id,
            slot_id,
            product_type,
            biz_order_no,
            phone,
            status,
            msg,
            ext,
            ip,
            landpage_url,
            referer,
            user_agent,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.landpage.IspVipLandpageFormRecordEntity">
        INSERT IGNORE INTO tb_landpage_form_record_isp_vip
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="orderId != null">
                    order_id,
                </if>
                <if test="advertId != null">
                    advert_id,
                </if>
                <if test="consumerId != null">
                    consumer_id,
                </if>
                <if test="appId != null">
                    app_id,
                </if>
                <if test="slotId != null">
                    slot_id,
                </if>
                <if test="bizOrderNo != null">
                    biz_order_no,
                </if>
                <if test="phone != null">
                    phone,
                </if>
                <if test="status != null">
                    status,
                </if>
                <if test="msg != null">
                    msg,
                </if>
                <if test="ext != null">
                    ext,
                </if>
                <if test="ip != null">
                    ip,
                </if>
                <if test="landpageUrl != null">
                    landpage_url,
                </if>
                <if test="referer != null">
                    referer,
                </if>
                <if test="userAgent != null">
                    user_agent,
                </if>
                <if test="productType != null">
                    product_type,
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="orderId != null">
                    #{orderId},
                </if>
                <if test="advertId != null">
                    #{advertId},
                </if>
                <if test="consumerId != null">
                    #{consumerId},
                </if>
                <if test="appId != null">
                    #{appId},
                </if>
                <if test="slotId != null">
                    #{slotId},
                </if>
                <if test="bizOrderNo != null">
                    #{bizOrderNo},
                </if>
                <if test="phone != null">
                    #{phone},
                </if>
                <if test="status != null">
                    #{status},
                </if>
                <if test="msg != null">
                    #{msg},
                </if>
                <if test="ext != null">
                    #{ext},
                </if>
                <if test="ip != null">
                    #{ip},
                </if>
                <if test="landpageUrl != null">
                    #{landpageUrl},
                </if>
                <if test="referer != null">
                    #{referer},
                </if>
                <if test="userAgent != null">
                    #{userAgent},
                </if>
                <if test="productType != null">
                    #{productType},
                </if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.ruoyi.system.entity.landpage.IspVipLandpageFormRecordEntity">
        UPDATE tb_landpage_form_record_isp_vip
        <set>
            <if test="bizOrderNo != null">
                biz_order_no = #{bizOrderNo},
            </if>
            <if test="phone != null">
                phone = #{phone},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="msg != null">
                msg = #{msg},
            </if>
            <if test="ext != null">
                ext = #{ext},
            </if>
            <if test="ip != null">
                ip = #{ip},
            </if>
            <if test="referer != null">
                referer = #{referer},
            </if>
            <if test="userAgent != null">
                user_agent = #{userAgent},
            </if>
            <if test="productType != null">
                product_type = #{productType},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_landpage_form_record_isp_vip
        WHERE id = #{id}
    </select>

    <select id="selectBy" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_landpage_form_record_isp_vip
        WHERE order_id = #{orderId} and phone = #{phone}
        order by id desc
        limit 1
    </select>

    <select id="selectByBizOrderNoAndOrderId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_landpage_form_record_isp_vip
        WHERE biz_order_no = #{bizOrderNo} and order_id = #{orderId}
        LIMIT 1
    </select>

    <select id="selectByOrderId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_landpage_form_record_isp_vip
        WHERE order_id = #{orderId}
        ORDER BY id desc
        LIMIT 1
    </select>
</mapper>
