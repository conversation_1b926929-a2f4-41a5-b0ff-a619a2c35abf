<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.landpage.LandpageSkinMapper">

    <resultMap type="com.ruoyi.system.entity.landpage.LandpageSkinEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="skinName" column="skin_name"/>
            <result property="thumbnailImage" column="thumbnail_image"/>
            <result property="skinConfig" column="skin_config"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            skin_name,
            thumbnail_image,
            skin_config,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.landpage.LandpageSkinEntity">
        INSERT INTO tb_landpage_skin
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="skinName != null">
                    skin_name,
                </if>
                <if test="thumbnailImage != null">
                    thumbnail_image,
                </if>
                <if test="skinConfig != null">
                    skin_config
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="skinName != null">
                    #{skinName},
                </if>
                <if test="thumbnailImage != null">
                    #{thumbnailImage},
                </if>
                <if test="skinConfig != null">
                    #{skinConfig}
                </if>
        </trim>
    </insert>

    <delete id="deleteById">
        DELETE FROM tb_landpage_skin WHERE id=#{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.landpage.LandpageSkinEntity">
        UPDATE tb_landpage_skin
        <set>
                    <if test="skinName != null">
                        skin_name = #{skinName},
                    </if>
                    <if test="thumbnailImage != null">
                        thumbnail_image = #{thumbnailImage},
                    </if>
                    <if test="skinConfig != null">
                        skin_config = #{skinConfig},
                    </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_landpage_skin
        WHERE id = #{id}
    </select>

    <select id="selectList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_landpage_skin
        <where>
            <if test="skinName != null and skinName != ''"> and skin_name like concat('%', #{skinName}, '%')</if>
        </where>
    </select>
</mapper>
