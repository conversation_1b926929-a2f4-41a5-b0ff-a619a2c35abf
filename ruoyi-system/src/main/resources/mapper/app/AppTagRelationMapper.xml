<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.app.AppTagRelationMapper">

    <resultMap type="com.ruoyi.system.entity.apptag.AppTagRelationEntity" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="appId" column="app_id"/>
        <result property="tagId" column="tag_id"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        app_id,
        tag_id,
        is_deleted,
        gmt_create,
        gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.apptag.AppTagRelationEntity">
        INSERT INTO tb_app_tag_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appId != null">
                app_id,
            </if>
            <if test="tagId != null">
                tag_id,
            </if>
            <if test="isDeleted != null">
                is_deleted
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appId != null">
                #{appId},
            </if>
            <if test="tagId != null">
                #{tagId},
            </if>
            <if test="isDeleted != null">
                #{isDeleted}
            </if>
        </trim>
    </insert>

    <delete id="deleteById">
        DELETE
        FROM tb_app_tag_relation
        WHERE id = #{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.apptag.AppTagRelationEntity">
        UPDATE tb_app_tag_relation
        <set>
            <if test="appId != null">
                app_id = #{appId},
            </if>
            <if test="tagId != null">
                tag_id = #{tagId},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_app_tag_relation
        WHERE id = #{id}
    </select>

    <select id="selectTagIdsByTagIds" resultType="java.lang.Long">
        SELECT distinct tag_id
        FROM tb_app_tag_relation
        WHERE is_deleted = 0 and tag_id in
        <foreach collection="tagIds" item="tagId" open="(" separator="," close=")">
            #{tagId}
        </foreach>
    </select>
    <select id="countByTagId" resultType="java.lang.Integer">
        SELECT count(*)
        FROM tb_app_tag_relation
        WHERE is_deleted = 0 and tag_id = #{tagId}
    </select>
    <select id="selectTagIdsByAppId" resultType="java.lang.Long">
        SELECT distinct tag_id
        FROM tb_app_tag_relation
        WHERE is_deleted = 0 and app_id = #{appId}
    </select>
    <update id="deleteByAppId">
        update tb_app_tag_relation
        set is_deleted = 1 where app_id = #{appId}
    </update>

    <insert id="batchInsert">
        insert into tb_app_tag_relation(`app_id`,`tag_id`)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.appId}, #{entity.tagId})
        </foreach>
    </insert>

    <select id="selectAppIdsByTagIds" resultType="Long">
        SELECT distinct app_id
        FROM tb_app_tag_relation
        WHERE tag_id in
        <foreach collection="tagIds" separator="," item="tagId" close=")" open="(">
            #{tagId}
        </foreach>
    </select>
</mapper>
