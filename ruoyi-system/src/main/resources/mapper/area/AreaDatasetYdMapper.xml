<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.area.AreaDatasetYdMapper">

    <resultMap type="com.ruoyi.system.entity.area.AreaDataset" id="AreaDatasetYdResult">
        <result property="id"    column="id"    />
        <result property="areaNum"    column="area_num"    />
        <result property="areaType"    column="area_type"    />
        <result property="areaName"    column="area_name"    />
        <result property="parentNum"    column="parent_num"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectAreaDatasetVo">
        select id, area_num, area_type, area_name, parent_num, gmt_create, gmt_modified from tb_area_dataset_yd
    </sql>

    <select id="selectAreaDatasetList" parameterType="com.ruoyi.system.entity.area.AreaDataset" resultMap="AreaDatasetYdResult">
        <include refid="selectAreaDatasetVo"/>
        <where>
            <if test="areaNum != null  and areaNum != ''"> and area_num = #{areaNum}</if>
            <if test="areaType != null "> and area_type = #{areaType}</if>
            <if test="areaName != null  and areaName != ''"> and area_name like concat('%', #{areaName}, '%')</if>
            <if test="parentNum != null  and parentNum != ''"> and parent_num = #{parentNum}</if>
            <if test="areaTypes != null and areaTypes.size() > 0 ">
                and area_type in
                <foreach collection="areaTypes" item="areaType" open="(" separator="," close=")">
                    #{areaType}
                </foreach>
            </if>
            <if test="parentNums != null and parentNums.size() > 0 ">
                and parent_num in
                <foreach collection="parentNums" item="parentNum" open="(" separator="," close=")">
                    #{parentNum}
                </foreach>
            </if>
        </where>
        order by area_num asc
    </select>

    <select id="selectByAreaNumAndType" resultMap="AreaDatasetYdResult">
        <include refid="selectAreaDatasetVo"/>
        where area_num = #{areaNum} and area_type = #{areaType}
        limit 1
    </select>

    <select id="selectByAreaName" parameterType="String" resultMap="AreaDatasetYdResult">
        <include refid="selectAreaDatasetVo"/>
        where area_name = #{areaName}
        limit 1
    </select>
</mapper>
