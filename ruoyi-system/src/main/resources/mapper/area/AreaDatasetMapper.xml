<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.area.AreaDatasetMapper">

    <resultMap type="com.ruoyi.system.entity.area.AreaDataset" id="AreaDatasetResult">
        <result property="id"    column="id"    />
        <result property="areaNum"    column="area_num"    />
        <result property="areaType"    column="area_type"    />
        <result property="areaName"    column="area_name"    />
        <result property="parentNum"    column="parent_num"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectAreaDatasetVo">
        select id, area_num, area_type, area_name, parent_num, gmt_create, gmt_modified from tb_area_dataset
    </sql>

    <select id="selectAreaDatasetList" parameterType="com.ruoyi.system.entity.area.AreaDataset" resultMap="AreaDatasetResult">
        <include refid="selectAreaDatasetVo"/>
        <where>
            <if test="areaNum != null  and areaNum != ''"> and area_num = #{areaNum}</if>
            <if test="areaType != null "> and area_type = #{areaType}</if>
            <if test="areaName != null  and areaName != ''"> and area_name like concat('%', #{areaName}, '%')</if>
            <if test="parentNum != null  and parentNum != ''"> and parent_num = #{parentNum}</if>
            <if test="areaTypes != null and areaTypes.size() > 0 ">
                and area_type in
                <foreach collection="areaTypes" item="areaType" open="(" separator="," close=")">
                    #{areaType}
                </foreach>
            </if>
            <if test="parentNums != null and parentNums.size() > 0 ">
                and parent_num in
                <foreach collection="parentNums" item="parentNum" open="(" separator="," close=")">
                    #{parentNum}
                </foreach>
            </if>
            <if test="areaNames != null and areaNames.size() > 0 ">
                and area_name in
                <foreach collection="areaNames" item="areaName" open="(" separator="," close=")">
                    #{areaName}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectByAreaNumAndType" resultMap="AreaDatasetResult">
        <include refid="selectAreaDatasetVo"/>
        where area_num = #{areaNum} and area_type = #{areaType}
        limit 1
    </select>

    <select id="selectByAreaNum" parameterType="String" resultMap="AreaDatasetResult">
        <include refid="selectAreaDatasetVo"/>
        where area_num = #{areaNum}
    </select>

    <select id="selectByAreaName" parameterType="String" resultMap="AreaDatasetResult">
        <include refid="selectAreaDatasetVo"/>
        where area_name = #{areaName}
        limit 1
    </select>

    <select id="selectByAreaNameLike" parameterType="String" resultMap="AreaDatasetResult">
        <include refid="selectAreaDatasetVo"/>
        where area_name like concat(#{areaName}, '%')
        ORDER BY `area_type`
        limit 1
    </select>
</mapper>
