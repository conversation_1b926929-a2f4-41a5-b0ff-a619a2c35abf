<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.common.InnerLogMapper">

    <insert id="insert" parameterType="com.ruoyi.system.entity.common.InnerLogEntity"
            useGeneratedKeys="true" keyProperty="id">
        insert ignore into tb_inner_log_${tbSuffix}
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="type != null">`type`,</if>
            <if test="typeDesc != null">type_desc,</if>
            <if test="appId != null">app_id,</if>
            <if test="slotId != null">slot_id,</if>
            <if test="activityId != null">activity_id,</if>
            <if test="deviceId != null">device_id,</if>
            <if test="consumerId != null">consumer_id,</if>
            <if test="orderId != null">order_id,</if>
            <if test="advertId != null">advert_id,</if>
            <if test="orientId != null">orient_id,</if>
            <if test="materialId != null">material_id,</if>
            <if test="pluginId != null">plugin_id,</if>
            <if test="areaNum != null">area_num,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="isp != null">isp,</if>
            <if test="ip != null">ip,</if>
            <if test="srid != null">`srid`,</if>
            <if test="osType != null">`os_type`,</if>
            <if test="osVersion != null">`os_version`,</if>
            <if test="mobileBrand != null">`mobile_brand`,</if>
            <if test="mobileModel != null">`mobile_model`,</if>
            <if test="referer != null">referer,</if>
            <if test="userAgent != null">user_agent,</if>
            <if test="gmtCreate != null">gmt_create,</if>
            <if test="msgKey != null">`msg_key`,</if>
            <if test="unitPrice != null">`unit_price`,</if>
            <if test="convType != null">`conv_type`,</if>
            <if test="action != null">`action`,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="type != null">#{type},</if>
            <if test="typeDesc != null">#{typeDesc},</if>
            <if test="appId != null">#{appId},</if>
            <if test="slotId != null">#{slotId},</if>
            <if test="activityId != null">#{activityId},</if>
            <if test="deviceId != null">#{deviceId},</if>
            <if test="consumerId != null">#{consumerId},</if>
            <if test="orderId != null">#{orderId},</if>
            <if test="advertId != null">#{advertId},</if>
            <if test="orientId != null">#{orientId},</if>
            <if test="materialId != null">#{materialId},</if>
            <if test="pluginId != null">#{pluginId},</if>
            <if test="areaNum != null">#{areaNum},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="isp != null">#{isp},</if>
            <if test="ip != null">#{ip},</if>
            <if test="srid != null">#{srid},</if>
            <if test="osType != null">#{osType},</if>
            <if test="osVersion != null">#{osVersion},</if>
            <if test="mobileBrand != null">#{mobileBrand},</if>
            <if test="mobileModel != null">#{mobileModel},</if>
            <if test="referer != null">#{referer},</if>
            <if test="userAgent != null">#{userAgent},</if>
            <if test="gmtCreate != null">#{gmtCreate},</if>
            <if test="msgKey != null">#{msgKey},</if>
            <if test="unitPrice != null">#{unitPrice},</if>
            <if test="convType != null">#{convType},</if>
            <if test="action != null">#{action},</if>
         </trim>
    </insert>

    <insert id="batchInsert">
        insert ignore into tb_inner_log_${tbSuffix} (
            `type`, type_desc, app_id, slot_id, activity_id, device_id, consumer_id,
            order_id, advert_id, orient_id, material_id, plugin_id, area_num, province,
            city, isp, ip, `srid`, `os_type`, `os_version`, `mobile_brand`,
            `mobile_model`, referer, user_agent, gmt_create, msg_key, unit_price, conv_type,
            action
        ) values
        <foreach collection="records" item="record" separator=",">
            (#{record.type}, #{record.typeDesc}, #{record.appId}, #{record.slotId}, #{record.activityId}, #{record.deviceId}, #{record.consumerId},
             #{record.orderId}, #{record.advertId}, #{record.orientId}, #{record.materialId}, #{record.pluginId}, #{record.areaNum}, #{record.province},
             #{record.city}, #{record.isp}, #{record.ip}, #{record.srid}, #{record.osType}, #{record.osVersion}, #{record.mobileBrand},
             #{record.mobileModel}, #{record.referer}, #{record.userAgent}, #{record.gmtCreate}, #{record.msgKey}, #{record.unitPrice}, #{record.convType},
             #{record.action})
        </foreach>
    </insert>

    <update id="createTable">
        CREATE TABLE IF NOT EXISTS tb_inner_log_${date} LIKE `tb_inner_log`
    </update>

    <update id="dropTable">
        DROP TABLE IF EXISTS tb_inner_log_${date}
    </update>
</mapper>
