<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.common.IndustryQualificationRequireMapper">

    <resultMap type="com.ruoyi.system.entity.common.IndustryQualificationRequireEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="industryId" column="industry_id"/>
            <result property="qualificationName" column="qualification_name"/>
            <result property="isMust" column="is_must"/>
            <result property="enableStatus" column="enable_status"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            industry_id,
            qualification_name,
            is_must,
            enable_status,
            gmt_create,
            gmt_modified
    </sql>

    <select id="selectList" parameterType="com.ruoyi.system.entity.common.IndustryQualificationRequireEntity" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_industry_qualification_require
        <where>
            <if test="industryId != null"> and industry_id = #{industryId}</if>
            <if test="qualificationName != null and qualificationName != ''"> and qualification_name like concat('%', #{qualificationName}, '%')</if>
            <if test="isMust != null"> and is_must = #{isMust}</if>
            <if test="enableStatus != null"> and enable_status = #{enableStatus}</if>
        </where>
        ORDER BY id desc
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.common.IndustryQualificationRequireEntity">
        INSERT INTO tb_industry_qualification_require
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="industryId != null">
                    industry_id,
                </if>
                <if test="qualificationName != null">
                    qualification_name,
                </if>
                <if test="isMust != null">
                    is_must,
                </if>
                <if test="enableStatus != null">
                    enable_status
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="industryId != null">
                    #{industryId},
                </if>
                <if test="qualificationName != null">
                    #{qualificationName},
                </if>
                <if test="isMust != null">
                    #{isMust},
                </if>
                <if test="enableStatus != null">
                    #{enableStatus}
                </if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.ruoyi.system.entity.common.IndustryQualificationRequireEntity">
        UPDATE tb_industry_qualification_require
        <set>
                    <if test="industryId != null">
                        industry_id = #{industryId},
                    </if>
                    <if test="qualificationName != null">
                        qualification_name = #{qualificationName},
                    </if>
                    <if test="isMust != null">
                        is_must = #{isMust},
                    </if>
                    <if test="enableStatus != null">
                        enable_status = #{enableStatus},
                    </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_industry_qualification_require
        WHERE id = #{id}
    </select>

    <select id="selectByIndustryIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_industry_qualification_require
        WHERE  industry_id in
        <foreach collection="industryIds" separator="," item="industryId" close=")" open="(">
            #{industryId}
        </foreach>
    </select>

    <select id="selectMustRequireIdByIndustryId" parameterType="Long" resultType="Long">
        SELECT id
        FROM tb_industry_qualification_require
        WHERE industry_id = #{industryId} and is_must = 1 and enable_status = 1
    </select>
</mapper>
