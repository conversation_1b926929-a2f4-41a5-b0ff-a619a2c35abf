<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.common.AdvertOrderLogMapper">

    <resultMap type="com.ruoyi.system.entity.common.AdvertOrderLogEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="srid" column="srid"/>
            <result property="orderId" column="order_id"/>
            <result property="curDate" column="cur_date"/>
            <result property="curHour" column="cur_hour"/>
            <result property="advertId" column="advert_id"/>
            <result property="slotId" column="slot_id"/>
            <result property="appId" column="app_id"/>
            <result property="activityId" column="activity_id"/>
            <result property="orientId" column="orient_id"/>
            <result property="materialId" column="material_id"/>
            <result property="unitPrice" column="unit_price"/>
            <result property="deviceId" column="device_id"/>
            <result property="consumerId" column="consumer_id"/>
            <result property="province" column="province"/>
            <result property="city" column="city"/>
            <result property="isp" column="isp"/>
            <result property="ip" column="ip"/>
            <result property="osType" column="os_type"/>
            <result property="osVersion" column="os_version"/>
            <result property="mobileBrand" column="mobile_brand"/>
            <result property="mobileModel" column="mobile_model"/>
            <result property="adLaunch" column="ad_launch"/>
            <result property="launchSeq" column="launch_seq"/>
            <result property="adExposure" column="ad_exposure"/>
            <result property="adClick" column="ad_click"/>
            <result property="cpcCost" column="cpc_cost"/>
            <result property="lpExposure" column="lp_exposure"/>
            <result property="lpClick" column="lp_click"/>
            <result property="assessType" column="assess_type"/>
            <result property="theoryCost" column="theory_cost"/>
            <result property="take" column="take"/>
            <result property="pay" column="pay"/>
            <result property="refund" column="refund"/>
            <result property="register" column="register"/>
            <result property="pCtr" column="p_ctr"/>
            <result property="pCvr" column="p_cvr"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            srid,
            order_id,
            cur_date,
            cur_hour,
            advert_id,
            slot_id,
            app_id,
            activity_id,
            orient_id,
            material_id,
            unit_price,
            device_id,
            consumer_id,
            province,
            city,
            isp,
            ip,
            os_type,
            os_version,
            mobile_brand,
            mobile_model,
            ad_launch,
            launch_seq,
            ad_exposure,
            ad_click,
            cpc_cost,
            lp_exposure,
            lp_click,
            assess_type,
            theory_cost,
            take,
            pay,
            refund,
            register,
            p_ctr,
            p_cvr,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.common.AdvertOrderLogEntity">
        INSERT IGNORE INTO tb_advert_order_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="srid != null">
                    srid,
                </if>
                <if test="orderId != null">
                    order_id,
                </if>
                <if test="curDate != null">
                    cur_date,
                </if>
                <if test="curHour != null">
                    cur_hour,
                </if>
                <if test="advertId != null">
                    advert_id,
                </if>
                <if test="slotId != null">
                    slot_id,
                </if>
                <if test="appId != null">
                    app_id,
                </if>
                <if test="activityId != null">
                    activity_id,
                </if>
                <if test="orientId != null">
                    orient_id,
                </if>
                <if test="materialId != null">
                    material_id,
                </if>
                <if test="unitPrice != null">
                    unit_price,
                </if>
                <if test="deviceId != null">
                    device_id,
                </if>
                <if test="consumerId != null">
                    consumer_id,
                </if>
                <if test="province != null">
                    province,
                </if>
                <if test="city != null">
                    city,
                </if>
                <if test="isp != null">
                    isp,
                </if>
                <if test="ip != null">
                    ip,
                </if>
                <if test="osType != null">
                    os_type,
                </if>
                <if test="osVersion != null">
                    os_version,
                </if>
                <if test="mobileBrand != null">
                    mobile_brand,
                </if>
                <if test="mobileModel != null">
                    mobile_model,
                </if>
                <if test="adLaunch != null">
                    ad_launch,
                </if>
                <if test="launchSeq != null">
                    launch_seq,
                </if>
                <if test="adExposure != null">
                    ad_exposure,
                </if>
                <if test="adClick != null">
                    ad_click,
                </if>
                <if test="cpcCost != null">
                    cpc_cost,
                </if>
                <if test="lpExposure != null">
                    lp_exposure,
                </if>
                <if test="lpClick != null">
                    lp_click,
                </if>
                <if test="assessType != null">
                    assess_type,
                </if>
                <if test="theoryCost != null">
                    theory_cost,
                </if>
                <if test="take != null">
                    take,
                </if>
                <if test="pay != null">
                    pay,
                </if>
                <if test="refund != null">
                    refund,
                </if>
                <if test="register != null">
                    register,
                </if>
                <if test="pCtr != null">
                    p_ctr,
                </if>
                <if test="pCvr != null">
                    p_cvr
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="srid != null">
                    #{srid},
                </if>
                <if test="orderId != null">
                    #{orderId},
                </if>
                <if test="curDate != null">
                    #{curDate},
                </if>
                <if test="curHour != null">
                    #{curHour},
                </if>
                <if test="advertId != null">
                    #{advertId},
                </if>
                <if test="slotId != null">
                    #{slotId},
                </if>
                <if test="appId != null">
                    #{appId},
                </if>
                <if test="activityId != null">
                    #{activityId},
                </if>
                <if test="orientId != null">
                    #{orientId},
                </if>
                <if test="materialId != null">
                    #{materialId},
                </if>
                <if test="unitPrice != null">
                    #{unitPrice},
                </if>
                <if test="deviceId != null">
                    #{deviceId},
                </if>
                <if test="consumerId != null">
                    #{consumerId},
                </if>
                <if test="province != null">
                    #{province},
                </if>
                <if test="city != null">
                    #{city},
                </if>
                <if test="isp != null">
                    #{isp},
                </if>
                <if test="ip != null">
                    #{ip},
                </if>
                <if test="osType != null">
                    #{osType},
                </if>
                <if test="osVersion != null">
                    #{osVersion},
                </if>
                <if test="mobileBrand != null">
                    #{mobileBrand},
                </if>
                <if test="mobileModel != null">
                    #{mobileModel},
                </if>
                <if test="adLaunch != null">
                    #{adLaunch},
                </if>
                <if test="launchSeq != null">
                    #{launchSeq},
                </if>
                <if test="adExposure != null">
                    #{adExposure},
                </if>
                <if test="adClick != null">
                    #{adClick},
                </if>
                <if test="cpcCost != null">
                    #{cpcCost},
                </if>
                <if test="lpExposure != null">
                    #{lpExposure},
                </if>
                <if test="lpClick != null">
                    #{lpClick},
                </if>
                <if test="assessType != null">
                    #{assessType},
                </if>
                <if test="theoryCost != null">
                    #{theoryCost},
                </if>
                <if test="take != null">
                    #{take},
                </if>
                <if test="pay != null">
                    #{pay},
                </if>
                <if test="refund != null">
                    #{refund},
                </if>
                <if test="register != null">
                    #{register},
                </if>
                <if test="pCtr != null">
                    #{pCtr},
                </if>
                <if test="pCvr != null">
                    #{pCvr}
                </if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.ruoyi.system.entity.common.AdvertOrderLogEntity">
        UPDATE tb_advert_order_log
        <set>
            <if test="srid != null">
                srid = #{srid},
            </if>
            <if test="orderId != null">
                order_id = #{orderId},
            </if>
            <if test="curDate != null">
                cur_date = #{curDate},
            </if>
            <if test="curHour != null">
                cur_hour = #{curHour},
            </if>
            <if test="advertId != null">
                advert_id = #{advertId},
            </if>
            <if test="slotId != null">
                slot_id = #{slotId},
            </if>
            <if test="appId != null">
                app_id = #{appId},
            </if>
            <if test="activityId != null">
                activity_id = #{activityId},
            </if>
            <if test="orientId != null">
                orient_id = #{orientId},
            </if>
            <if test="materialId != null">
                material_id = #{materialId},
            </if>
            <if test="unitPrice != null">
                unit_price = #{unitPrice},
            </if>
            <if test="deviceId != null">
                device_id = #{deviceId},
            </if>
            <if test="consumerId != null">
                consumer_id = #{consumerId},
            </if>
            <if test="province != null">
                province = #{province},
            </if>
            <if test="city != null">
                city = #{city},
            </if>
            <if test="isp != null">
                isp = #{isp},
            </if>
            <if test="ip != null">
                ip = #{ip},
            </if>
            <if test="osType != null">
                os_type = #{osType},
            </if>
            <if test="osVersion != null">
                os_version = #{osVersion},
            </if>
            <if test="mobileBrand != null">
                mobile_brand = #{mobileBrand},
            </if>
            <if test="mobileModel != null">
                mobile_model = #{mobileModel},
            </if>
            <if test="adLaunch != null">
                ad_launch = #{adLaunch},
            </if>
            <if test="launchSeq != null">
                launch_seq = #{launchSeq},
            </if>
            <if test="adExposure != null">
                ad_exposure = #{adExposure},
            </if>
            <if test="adClick != null">
                ad_click = #{adClick},
            </if>
            <if test="cpcCost != null">
                cpc_cost = #{cpcCost},
            </if>
            <if test="lpExposure != null">
                lp_exposure = #{lpExposure},
            </if>
            <if test="lpClick != null">
                lp_click = #{lpClick},
            </if>
            <if test="assessType != null">
                assess_type = #{assessType},
            </if>
            <if test="theoryCost != null">
                theory_cost = #{theoryCost},
            </if>
            <if test="take != null">
                take = #{take},
            </if>
            <if test="pay != null">
                pay = #{pay},
            </if>
            <if test="refund != null">
                refund = #{refund},
            </if>
            <if test="register != null">
                register = #{register},
            </if>
            <if test="pCtr != null">
                p_ctr = #{pCtr},
            </if>
            <if test="pCvr != null">
                p_cvr = #{pCvr},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_advert_order_log
        WHERE id = #{id}
    </select>

    <select id="selectIdByOrderId" resultType="Long">
        SELECT id
        FROM tb_advert_order_log
        WHERE order_id = #{orderId}
    </select>

    <select id="selectCtrCvrBySlotIdAndAdvertIdAndMobileBrand" resultType="com.ruoyi.system.bo.data.OrderDataBo">
        SELECT count(*) as adLaunchSum, min(s.id) as minId,
            ifnull(sum(ad_exposure), 0) as adExposure, ifnull(sum(ad_click),0) as adClick, ifnull(sum(ad_click) / sum(ad_exposure), 0) as ctr,
            ifnull(sum(lp_click), 0) as lpClick, ifnull(sum(lp_click) / sum(ad_click), 0) as cvr
        FROM tb_advert_order_log m
        RIGHT JOIN (
            SELECT id
            FROM tb_advert_order_log
            <where>
                <if test="slotId != null"> and slot_id = #{slotId}</if>
                <if test="advertId != null"> and advert_id = #{advertId}</if>
                <if test="launchSeq != null and launchSeq == 1"> and launch_seq = 1</if>
                <if test="launchSeq != null and launchSeq == 2"> and launch_seq = 2</if>
                <if test="launchSeq != null and launchSeq == 3"> and launch_seq in (3,4,5,6,7,8)</if>
                <if test="launchSeq == null"> and launch_seq in (1,2,3,4,5,6,7,8)</if>
                <if test="mobileBrand != null and mobileBrand != ''"> and mobile_brand = #{mobileBrand}</if>
                <if test="province != null and province != ''"> and province = #{province}</if>
                <if test="slotId != null and advertId != null and province == null and mobileBrand == null"> and province in ('北京','天津','河北','山西','内蒙古','辽宁','吉林','黑龙江','上海','江苏','浙江','安徽','福建','江西','山东','河南','湖北','湖南','广东','广西','海南','重庆','四川','贵州','云南','西藏','陕西','甘肃','青海','宁夏','新疆','北京市','天津市','河北省','山西省','内蒙古自治区','辽宁省','吉林省','黑龙江省','上海市','江苏省','浙江省','安徽省','福建省','江西省','山东省','河南省','湖北省','湖南省','广东省','广西壮族自治区','海南省','重庆市','四川省','贵州省','云南省','西藏自治区','陕西省','甘肃省','青海省','宁夏回族自治区','新疆维吾尔自治区')</if>
                <if test="minId != null"> and id &gt;= #{minId}</if>
                and activity_id > 0
                and `os_type` != "Windows"
                and `os_type` != ""
                and gmt_create &lt;= DATE_SUB(NOW(), INTERVAL 5 MINUTE)
            </where>
            order by id desc
            limit #{times}
        ) s on m.id = s.id
    </select>

    <select id="selectCvrBySlotIdAndAdvertId" resultType="com.ruoyi.system.bo.data.OrderDataBo">
        SELECT count(*) as adLaunchSum, min(s.id) as minId,
            ifnull(sum(ad_click),0) as adClick, ifnull(sum(lp_click), 0) as lpClick, ifnull(sum(lp_click) / sum(ad_click), 0) as cvr
        FROM tb_advert_order_log m
        RIGHT JOIN (
            SELECT id
            FROM tb_advert_order_log
            <where>
                <if test="slotId != null"> and slot_id = #{slotId}</if>
                <if test="advertId != null"> and advert_id = #{advertId}</if>
                <if test="minId != null"> and id &gt;= #{minId}</if>
                and activity_id = 0
                and `os_type` != "Windows"
                and `os_type` != ""
                and launch_seq in (1,2,3)
                and gmt_create &lt;= DATE_SUB(NOW(), INTERVAL 5 MINUTE)
            </where>
            order by id desc
            limit #{times}
        ) s on m.id = s.id
    </select>

    <select id="selectCtrCvrByAdvertId" resultType="com.ruoyi.system.bo.data.OrderDataBo">
        SELECT count(*) as adLaunchSum,
            ifnull(sum(ad_exposure), 0) as adExposure, ifnull(sum(ad_click),0) as adClick, ifnull(sum(if(ad_exposure > 0, ad_click, 0)) / sum(ad_exposure), 0) as ctr,
            ifnull(sum(lp_click), 0) as lpClick, ifnull(sum(lp_click) / sum(ad_click), 0) as cvr
        FROM tb_advert_order_log m
        RIGHT JOIN (
            SELECT id
            FROM tb_advert_order_log
            <where>
                <if test="advertId != null"> and advert_id = #{advertId}</if>
                and launch_seq = 1
                and `os_type` != "Windows"
                and `os_type` != ""
                and gmt_create &lt;= DATE_SUB(NOW(), INTERVAL 5 MINUTE)
            </where>
            order by id desc
            limit #{times}
        ) s on m.id = s.id
    </select>
</mapper>
