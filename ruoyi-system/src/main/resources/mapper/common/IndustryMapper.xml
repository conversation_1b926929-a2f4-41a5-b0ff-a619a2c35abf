<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.common.IndustryMapper">

    <resultMap type="com.ruoyi.system.entity.common.IndustryEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="industryName" column="industry_name"/>
            <result property="enableStatus" column="enable_status"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            industry_name,
            enable_status,
            gmt_create,
            gmt_modified
    </sql>

    <select id="selectList" parameterType="com.ruoyi.system.entity.common.IndustryEntity" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_industry
        <where>
            <if test="industryName != null and industryName != ''"> and industry_name like concat('%', #{industryName}, '%')</if>
            <if test="enableStatus != null"> and enable_status = #{enableStatus}</if>
        </where>
        ORDER BY id desc
    </select>

    <select id="selectByIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_industry
        <where>
            <if test="ids != null and ids.size() > 0">
                and id in
                <foreach collection="ids" separator="," item="id" close=")" open="(">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.common.IndustryEntity">
        INSERT INTO tb_industry
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="industryName != null">
                    industry_name,
                </if>
                <if test="enableStatus != null">
                    enable_status
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="industryName != null">
                    #{industryName},
                </if>
                <if test="enableStatus != null">
                    #{enableStatus}
                </if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.ruoyi.system.entity.common.IndustryEntity">
        UPDATE tb_industry
        <set>
                    <if test="industryName != null">
                        industry_name = #{industryName},
                    </if>
                    <if test="enableStatus != null">
                        enable_status = #{enableStatus},
                    </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_industry
        WHERE id = #{id}
    </select>

    <select id="existByIndustryName" resultType="Integer">
        SELECT 1
        FROM tb_industry
        WHERE industry_name = #{industryName}
    </select>
</mapper>
