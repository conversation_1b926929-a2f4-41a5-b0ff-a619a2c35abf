<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.common.MobileMapper">

    <resultMap type="com.ruoyi.system.entity.common.MobileEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="model" column="model"/>
            <result property="brand" column="brand"/>
            <result property="operatorId" column="operator_id"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            model,
            brand,
            operator_id,
            gmt_create,
            gmt_modified
    </sql>

    <select id="selectList" parameterType="com.ruoyi.system.req.traffic.MobileListReq" resultType="com.ruoyi.system.bo.common.MobileDataBo">
        SELECT m.id, m.model, m.brand, d.ad_click_pv as adClickPv, d.hap_launch_pv as hapLaunchPv, d.lp_exposure_pv as lpExposurePv
        FROM tb_mobile m
        LEFT JOIN tb_mobile_hap_data d on m.model = d.model
        <where>
            <if test="model != null and model != ''"> and m.model like concat('%', #{model}, '%')</if>
            <if test="brandType != null and brandType == 1"> and m.brand = '华为'</if>
            <if test="brandType != null and brandType == 2"> and m.brand = '小米'</if>
            <if test="brandType != null and brandType == 3"> and m.brand = 'VIVO'</if>
            <if test="brandType != null and brandType == 4"> and m.brand = 'OPPO'</if>
            <if test="brandType != null and brandType == 5"> and m.brand not in ('华为', '小米', 'VIVO', 'OPPO')</if>
            <if test="brandType != null and brandType == 6"> and m.brand = ''</if>
        </where>
        order by d.ad_click_pv desc, d.hap_launch_pv desc, d.lp_exposure_pv desc, m.id desc
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.ruoyi.system.entity.common.MobileEntity">
        INSERT IGNORE INTO tb_mobile
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="model != null">
                    model,
                </if>
                <if test="brand != null">
                    brand,
                </if>
                <if test="operatorId != null">
                    operator_id
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="model != null">
                    #{model},
                </if>
                <if test="brand != null">
                    #{brand},
                </if>
                <if test="operatorId != null">
                    #{operatorId}
                </if>
        </trim>
    </insert>

    <delete id="deleteById">
        DELETE FROM tb_mobile WHERE id=#{id}
    </delete>

    <update id="updateByModel" parameterType="com.ruoyi.system.entity.common.MobileEntity">
        UPDATE tb_mobile
        <set>
            <if test="brand != null">
                brand = #{brand},
            </if>
            <if test="operatorId != null">
                operator_id = #{operatorId},
            </if>
        </set>
        WHERE model = #{model}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_mobile
        WHERE id = #{id}
    </select>

    <select id="selectBrandByModel" parameterType="String" resultType="String">
        SELECT brand
        FROM tb_mobile
        WHERE model = #{model}
    </select>

    <select id="selectTotalModel" resultType="String">
        SELECT model
        FROM tb_mobile
    </select>
</mapper>
