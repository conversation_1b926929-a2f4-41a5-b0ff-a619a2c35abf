<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.common.DocumentMapper">

    <resultMap type="com.ruoyi.system.entity.common.DocumentEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="documentName" column="document_name"/>
            <result property="documentUrl" column="document_url"/>
            <result property="documentType" column="document_type"/>
            <result property="companyName" column="company_name"/>
            <result property="remark" column="remark"/>
            <result property="operateTime" column="operate_time"/>
            <result property="operatorId" column="operator_id"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            document_name,
            document_url,
            document_type,
            company_name,
            remark,
            operate_time,
            operator_id,
            gmt_create,
            gmt_modified
    </sql>

    <select id="selectList" parameterType="com.ruoyi.system.entity.common.DocumentEntity" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_document
        <where>
            <if test="companyName != null and companyName != ''"> and company_name like concat('%', #{companyName}, '%')</if>
            <if test="documentType != null"> and document_type = #{documentType}</if>
        </where>
        ORDER BY operate_time desc
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.common.DocumentEntity">
        INSERT INTO tb_document
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="documentName != null">
                    document_name,
                </if>
                <if test="documentUrl != null">
                    document_url,
                </if>
                <if test="documentType != null">
                    document_type,
                </if>
                <if test="companyName != null">
                    company_name,
                </if>
                <if test="remark != null">
                    remark,
                </if>
                <if test="operateTime != null">
                    operate_time,
                </if>
                <if test="operatorId != null">
                    operator_id
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="documentName != null">
                    #{documentName},
                </if>
                <if test="documentUrl != null">
                    #{documentUrl},
                </if>
                <if test="documentType != null">
                    #{documentType},
                </if>
                <if test="companyName != null">
                    #{companyName},
                </if>
                <if test="remark != null">
                    #{remark},
                </if>
                <if test="operateTime != null">
                    #{operateTime},
                </if>
                <if test="operatorId != null">
                    #{operatorId}
                </if>
        </trim>
    </insert>

    <delete id="deleteById">
        DELETE FROM tb_document WHERE id=#{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.common.DocumentEntity">
        UPDATE tb_document
        <set>
                    <if test="documentName != null">
                        document_name = #{documentName},
                    </if>
                    <if test="documentUrl != null">
                        document_url = #{documentUrl},
                    </if>
                    <if test="documentType != null">
                        document_type = #{documentType},
                    </if>
                    <if test="companyName != null">
                        company_name = #{companyName},
                    </if>
                    <if test="remark != null">
                        remark = #{remark},
                    </if>
                    <if test="operateTime != null">
                        operate_time = #{operateTime},
                    </if>
                    <if test="operatorId != null">
                        operator_id = #{operatorId},
                    </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_document
        WHERE id = #{id}
    </select>

    <select id="existDuplication" resultType="Integer">
        SELECT 1
        FROM tb_document
        <where>
            company_name = #{companyName}
            <if test="documentName != ''"> and document_name = #{documentName}</if>
            <if test="documentName == ''"> and document_url = #{documentUrl}</if>
            <if test="id != null"> and id != #{id}</if>
        </where>
        LIMIT 1
    </select>
</mapper>
