<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.open.ZxsCallbackRecordMapper">

    <resultMap type="com.ruoyi.system.entity.open.ZxsCallbackRecord" id="BaseResultMap">
        <result property="id"    column="id"    />
        <result property="orderNo"    column="order_no"    />
        <result property="status"    column="status"    />
        <result property="phone"    column="phone"    />
        <result property="price"    column="price"    />
        <result property="productId"    column="product_id"    />
        <result property="others"    column="others"    />
        <result property="sign"    column="sign"    />
    </resultMap>

    <insert id="insert" parameterType="com.ruoyi.system.entity.open.ZxsCallbackRecord" useGeneratedKeys="true" keyProperty="id">
        insert into tb_zxs_callback_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderNo != null">order_no,</if>
            <if test="status != null">`status`,</if>
            <if test="phone != null">phone,</if>
            <if test="price != null">price,</if>
            <if test="productId != null">product_id,</if>
            <if test="others != null">`others`,</if>
            <if test="sign != null">sign,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderNo != null">#{orderNo},</if>
            <if test="status != null">#{status},</if>
            <if test="phone != null">#{phone},</if>
            <if test="price != null">#{price},</if>
            <if test="productId != null">#{productId},</if>
            <if test="others != null">#{others},</if>
            <if test="sign != null">#{sign},</if>
         </trim>
    </insert>
</mapper>
