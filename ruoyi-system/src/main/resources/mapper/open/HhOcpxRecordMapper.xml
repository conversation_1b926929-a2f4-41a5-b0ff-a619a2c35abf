<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.open.HhOcpxRecordMapper">

    <resultMap type="com.ruoyi.system.entity.open.HhOcpxRecordEntity" id="BaseResultMap">
            <result property="adAgent" column="ad_agent"/>
            <result property="aid" column="aid"/>
            <result property="brand" column="brand"/>
            <result property="callbackUrl" column="callback_url"/>
            <result property="campaignId" column="campaign_id"/>
            <result property="chainCode" column="chain_code"/>
            <result property="curDate" column="cur_date"/>
            <result property="eventList" column="event_list"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
            <result property="id" column="id"/>
            <result property="idfa" column="idfa"/>
            <result property="idfaMd5" column="idfa_md5"/>
            <result property="imeiMd5" column="imei_md5"/>
            <result property="ip" column="ip"/>
            <result property="model" column="model"/>
            <result property="oaid" column="oaid"/>
            <result property="oaidMd5" column="oaid_md5"/>
            <result property="os" column="os"/>
            <result property="requestId" column="request_id"/>
            <result property="tms" column="tms"/>
            <result property="type" column="type"/>
            <result property="version" column="version"/>
    </resultMap>

    <sql id="Base_Column_List">
            ad_agent,
            aid,
            brand,
            callback_url,
            campaign_id,
            chain_code,
            cur_date,
            event_list,
            gmt_create,
            gmt_modified,
            id,
            idfa,
            idfa_md5,
            imei_md5,
            ip,
            model,
            oaid,
            oaid_md5,
            os,
            request_id,
            tms,
            type,
            version
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.open.HhOcpxRecordEntity">
        INSERT INTO tb_hh_ocpx_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="adAgent != null">
                    ad_agent,
                </if>
                <if test="aid != null">
                    aid,
                </if>
                <if test="brand != null">
                    brand,
                </if>
                <if test="callbackUrl != null">
                    callback_url,
                </if>
                <if test="campaignId != null">
                    campaign_id,
                </if>
                <if test="chainCode != null">
                    chain_code,
                </if>
                <if test="curDate != null">
                    cur_date,
                </if>
                <if test="eventList != null">
                    event_list,
                </if>
                <if test="gmtModified != null">
                    gmt_modified,
                </if>
                <if test="idfa != null">
                    idfa,
                </if>
                <if test="idfaMd5 != null">
                    idfa_md5,
                </if>
                <if test="imeiMd5 != null">
                    imei_md5,
                </if>
                <if test="ip != null">
                    ip,
                </if>
                <if test="model != null">
                    model,
                </if>
                <if test="oaid != null">
                    oaid,
                </if>
                <if test="oaidMd5 != null">
                    oaid_md5,
                </if>
                <if test="os != null">
                    os,
                </if>
                <if test="requestId != null">
                    request_id,
                </if>
                <if test="tms != null">
                    tms,
                </if>
                <if test="type != null">
                    type,
                </if>
                <if test="version != null">
                    version
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="adAgent != null">
                    #{adAgent},
                </if>
                <if test="aid != null">
                    #{aid},
                </if>
                <if test="brand != null">
                    #{brand},
                </if>
                <if test="callbackUrl != null">
                    #{callbackUrl},
                </if>
                <if test="campaignId != null">
                    #{campaignId},
                </if>
                <if test="chainCode != null">
                    #{chainCode},
                </if>
                <if test="curDate != null">
                    #{curDate},
                </if>
                <if test="eventList != null">
                    #{eventList},
                </if>
                <if test="gmtModified != null">
                    #{gmtModified},
                </if>
                <if test="idfa != null">
                    #{idfa},
                </if>
                <if test="idfaMd5 != null">
                    #{idfaMd5},
                </if>
                <if test="imeiMd5 != null">
                    #{imeiMd5},
                </if>
                <if test="ip != null">
                    #{ip},
                </if>
                <if test="model != null">
                    #{model},
                </if>
                <if test="oaid != null">
                    #{oaid},
                </if>
                <if test="oaidMd5 != null">
                    #{oaidMd5},
                </if>
                <if test="os != null">
                    #{os},
                </if>
                <if test="requestId != null">
                    #{requestId},
                </if>
                <if test="tms != null">
                    #{tms},
                </if>
                <if test="type != null">
                    #{type},
                </if>
                <if test="version != null">
                    #{version}
                </if>
        </trim>
    </insert>

    <delete id="deleteById">
        DELETE FROM tb_hh_ocpx_record WHERE id=#{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.open.HhOcpxRecordEntity">
        UPDATE tb_hh_ocpx_record
        <set>
                    <if test="adAgent != null">
                        ad_agent = #{adAgent},
                    </if>
                    <if test="aid != null">
                        aid = #{aid},
                    </if>
                    <if test="brand != null">
                        brand = #{brand},
                    </if>
                    <if test="callbackUrl != null">
                        callback_url = #{callbackUrl},
                    </if>
                    <if test="campaignId != null">
                        campaign_id = #{campaignId},
                    </if>
                    <if test="chainCode != null">
                        chain_code = #{chainCode},
                    </if>
                    <if test="curDate != null">
                        cur_date = #{curDate},
                    </if>
                    <if test="eventList != null">
                        event_list = #{eventList},
                    </if>
                    <if test="gmtModified != null">
                        gmt_modified = #{gmtModified},
                    </if>
                    <if test="idfa != null">
                        idfa = #{idfa},
                    </if>
                    <if test="idfaMd5 != null">
                        idfa_md5 = #{idfaMd5},
                    </if>
                    <if test="imeiMd5 != null">
                        imei_md5 = #{imeiMd5},
                    </if>
                    <if test="ip != null">
                        ip = #{ip},
                    </if>
                    <if test="model != null">
                        model = #{model},
                    </if>
                    <if test="oaid != null">
                        oaid = #{oaid},
                    </if>
                    <if test="oaidMd5 != null">
                        oaid_md5 = #{oaidMd5},
                    </if>
                    <if test="os != null">
                        os = #{os},
                    </if>
                    <if test="requestId != null">
                        request_id = #{requestId},
                    </if>
                    <if test="tms != null">
                        tms = #{tms},
                    </if>
                    <if test="type != null">
                        type = #{type},
                    </if>
                    <if test="version != null">
                        version = #{version},
                    </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_hh_ocpx_record
        WHERE id = #{id}
    </select>

</mapper>
