<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.open.AdvertiserCallbackRecordMapper">

    <resultMap type="com.ruoyi.system.entity.open.AdvertiserCallbackRecord" id="BaseResultMap">
        <result property="id"    column="id"    />
        <result property="curDate"    column="cur_date"    />
        <result property="orderNo"    column="order_no"    />
        <result property="consumerId"    column="consumer_id"    />
        <result property="slotId"    column="slot_id"    />
        <result property="activityId"    column="activity_id"    />
        <result property="advertId"    column="advert_id"    />
        <result property="orientId"    column="orient_id"    />
        <result property="advertiserId"    column="advertiser_id"    />
        <result property="submitTime"    column="submit_time"    />
        <result property="status"    column="status"    />
        <result property="ext"    column="ext"    />
    </resultMap>

    <select id="selectBy" parameterType="com.ruoyi.system.entity.open.AdvertiserCallbackRecord" resultMap="BaseResultMap">
        SELECT id, cur_date, order_no, consumer_id, slot_id, activity_id, advert_id, orient_id, advertiser_id, submit_time, status, ext
        FROM tb_advertiser_callback_record
        <where>
            <if test="orderNo != null "> and order_no = #{orderNo}</if>
            <if test="status != null">and status = #{status}</if>
            <if test="advertiserId != null">and advertiser_id = #{advertiserId}</if>
        </where>
        limit 1
    </select>

    <insert id="insert" parameterType="com.ruoyi.system.entity.open.AdvertiserCallbackRecord" useGeneratedKeys="true" keyProperty="id">
        insert into tb_advertiser_callback_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="curDate != null">cur_date,</if>
            <if test="orderNo != null">order_no,</if>
            <if test="consumerId != null">consumer_id,</if>
            <if test="slotId != null">slot_id,</if>
            <if test="activityId != null">activity_id,</if>
            <if test="advertId != null">advert_id,</if>
            <if test="orientId != null">orient_id,</if>
            <if test="advertiserId != null">advertiser_id,</if>
            <if test="submitTime != null">`submit_time`,</if>
            <if test="status != null">`status`,</if>
            <if test="ext != null">ext,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="curDate != null">#{curDate},</if>
            <if test="orderNo != null">#{orderNo},</if>
            <if test="consumerId != null">#{consumerId},</if>
            <if test="slotId != null">#{slotId},</if>
            <if test="activityId != null">#{activityId},</if>
            <if test="advertId != null">#{advertId},</if>
            <if test="orientId != null">#{orientId},</if>
            <if test="advertiserId != null">#{advertiserId},</if>
            <if test="submitTime != null">#{submitTime},</if>
            <if test="status != null">#{status},</if>
            <if test="ext != null">#{ext},</if>
         </trim>
    </insert>

    <select id="selectStatusByOrderNo" resultMap="BaseResultMap">
        SELECT order_no, status
        FROM tb_advertiser_callback_record
        <where>
            <if test="orderNoList != null and orderNoList.size()>0">
                and order_no in
                <foreach collection="orderNoList" item="orderNo" open="(" separator="," close=")">
                    #{orderNo}
                </foreach>
            </if>
        </where>
        order by `status`
    </select>
</mapper>
