<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.quickapp.QuickappContentMapper">

    <resultMap type="com.ruoyi.system.entity.quickapp.QuickappContentEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="title" column="title"/>
            <result property="secondary" column="secondary"/>
            <result property="content" column="content"/>
            <result property="image" column="image"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            title,
            secondary,
            content,
            image,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.quickapp.QuickappContentEntity">
        INSERT INTO tb_quickapp_content
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="title != null">
                    title,
                </if>
                <if test="secondary != null">
                    secondary,
                </if>
                <if test="content != null">
                    content,
                </if>
                <if test="image != null">
                    image
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="title != null">
                    #{title},
                </if>
                <if test="secondary != null">
                    #{secondary},
                </if>
                <if test="content != null">
                    #{content},
                </if>
                <if test="image != null">
                    #{image}
                </if>
        </trim>
    </insert>

    <delete id="deleteById">
        DELETE FROM tb_quickapp_content WHERE id=#{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.quickapp.QuickappContentEntity">
        UPDATE tb_quickapp_content
        <set>
                    <if test="title != null">
                        title = #{title},
                    </if>
                    <if test="secondary != null">
                        secondary = #{secondary},
                    </if>
                    <if test="content != null">
                        content = #{content},
                    </if>
                    <if test="image != null">
                        image = #{image},
                    </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_quickapp_content
        WHERE id = #{id}
    </select>

    <select id="selectByTitle" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_quickapp_content
        WHERE title like concat('%', #{title}, '%')
        limit 1
    </select>

    <select id="selectList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_quickapp_content
        limit 100
    </select>

</mapper>