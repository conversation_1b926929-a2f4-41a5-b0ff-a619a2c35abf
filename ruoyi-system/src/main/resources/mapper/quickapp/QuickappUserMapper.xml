<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.quickapp.QuickappUserMapper">

    <resultMap type="com.ruoyi.system.entity.quickapp.QuickappUserEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="userName" column="user_name"/>
            <result property="userAvatar" column="user_avatar"/>
            <result property="email" column="email"/>
            <result property="passwd" column="passwd"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            user_name,
            user_avatar,
            email,
            passwd,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.quickapp.QuickappUserEntity">
        INSERT INTO tb_quickapp_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="userName != null">
                    user_name,
                </if>
                <if test="userAvatar != null">
                    user_avatar,
                </if>
                <if test="email != null">
                    email,
                </if>
                <if test="passwd != null">
                    passwd
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="userName != null">
                    #{userName},
                </if>
                <if test="userAvatar != null">
                    #{userAvatar},
                </if>
                <if test="email != null">
                    #{email},
                </if>
                <if test="passwd != null">
                    #{passwd}
                </if>
        </trim>
    </insert>

    <delete id="deleteById">
        DELETE FROM tb_quickapp_user WHERE id=#{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.quickapp.QuickappUserEntity">
        UPDATE tb_quickapp_user
        <set>
                    <if test="userName != null">
                        user_name = #{userName},
                    </if>
                    <if test="userAvatar != null">
                        user_avatar = #{userAvatar},
                    </if>
                    <if test="email != null">
                        email = #{email},
                    </if>
                    <if test="passwd != null">
                        passwd = #{passwd},
                    </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_quickapp_user
        WHERE id = #{id}
    </select>
    <select id="selectByEmail" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_quickapp_user
        WHERE email = #{email}
        limit 1
    </select>
    <select id="selectByUserIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_quickapp_user
        WHERE id in
        <foreach collection="userIds" item="id" close=")" separator="," open="(">
            #{id}
        </foreach>
    </select>

</mapper>