<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.quickapp.QuickappGoodsMapper">

    <resultMap type="com.ruoyi.system.entity.quickapp.QuickappGoodsEntity" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="price" column="price"/>
        <result property="thumbnail" column="thumbnail"/>
        <result property="detailImg" column="detail_img"/>
        <result property="headImg" column="head_img"/>
        <result property="phone" column="phone"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
            title,
            price,
            thumbnail,
            detail_img,
            head_img,
            phone,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.quickapp.QuickappGoodsEntity">
        INSERT INTO tb_quickapp_goods
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null">
                title,
            </if>
            <if test="price != null">
                price,
            </if>
            <if test="thumbnail != null">
                thumbnail,
            </if>
            <if test="detailImg != null">
                detail_img,
            </if>
            <if test="headImg != null">
                head_img,
            </if>
            <if test="phone != null">
                phone
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null">
                #{title},
            </if>
            <if test="price != null">
                #{price},
            </if>
            <if test="thumbnail != null">
                #{thumbnail},
            </if>
            <if test="detailImg != null">
                #{detailImg},
            </if>
            <if test="headImg != null">
                #{headImg},
            </if>
            <if test="phone != null">
                #{phone}
            </if>
        </trim>
    </insert>

    <delete id="deleteById">
        DELETE
        FROM tb_quickapp_goods
        WHERE id = #{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.quickapp.QuickappGoodsEntity">
        UPDATE tb_quickapp_goods
        <set>
            <if test="title != null">
                title = #{title},
            </if>
            <if test="price != null">
                price = #{price},
            </if>
            <if test="thumbnail != null">
                thumbnail = #{thumbnail},
            </if>
            <if test="detailImg != null">
                detail_img = #{detailImg},
            </if>
            <if test="headImg != null">
                head_img = #{headImg},
            </if>
            <if test="phone != null">
                phone = #{phone},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_quickapp_goods
        WHERE id = #{id}
    </select>

    <select id="selectList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_quickapp_goods
    </select>

</mapper>