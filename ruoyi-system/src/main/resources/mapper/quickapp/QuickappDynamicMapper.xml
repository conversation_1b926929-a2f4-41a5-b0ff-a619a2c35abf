<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.quickapp.QuickappDynamicMapper">

    <resultMap type="com.ruoyi.system.entity.quickapp.QuickappDynamicEntity" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="content" column="content"/>
        <result property="images" column="images"/>
        <result property="userId" column="user_id"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
            content,
            images,
            user_id,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.quickapp.QuickappDynamicEntity">
        INSERT INTO tb_quickapp_dynamic
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="content != null">
                content,
            </if>
            <if test="images != null">
                images,
            </if>
            <if test="userId != null">
                user_id
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="content != null">
                #{content},
            </if>
            <if test="images != null">
                #{images},
            </if>
            <if test="userId != null">
                #{userId}
            </if>
        </trim>
    </insert>

    <delete id="deleteById">
        DELETE
        FROM tb_quickapp_dynamic
        WHERE id = #{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.quickapp.QuickappDynamicEntity">
        UPDATE tb_quickapp_dynamic
        <set>
            <if test="content != null">
                content = #{content},
            </if>
            <if test="images != null">
                images = #{images},
            </if>
            <if test="userId != null">
                user_id = #{userId},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_quickapp_dynamic
        WHERE id = #{id}
    </select>

    <select id="selectList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_quickapp_dynamic
        order by gmt_create desc
    </select>

</mapper>