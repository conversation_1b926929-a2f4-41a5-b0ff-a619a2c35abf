<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.permission.SspPermissionMapper">

    <resultMap type="com.ruoyi.system.entity.permission.PermissionEntity" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="permissionName" column="permission_name"/>
        <result property="permissionKey" column="permission_key"/>
        <result property="parentId" column="parent_id"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        permission_name,
        permission_key,
        parent_id,
        gmt_create,
        gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.permission.PermissionEntity">
        INSERT INTO tb_permission
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="permissionName != null">
                permission_name,
            </if>
            <if test="permissionKey != null">
                permission_key
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="permissionName != null">
                #{permissionName},
            </if>
            <if test="permissionKey != null">
                #{permissionKey}
            </if>
        </trim>
    </insert>

    <delete id="deleteById">
        DELETE
        FROM tb_permission
        WHERE id = #{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.permission.PermissionEntity">
        UPDATE tb_permission
        <set>
            <if test="permissionName != null">
                permission_name = #{permissionName},
            </if>
            <if test="permissionKey != null">
                permission_key = #{permissionKey},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_permission
        WHERE id = #{id}
    </select>

    <select id="selectByIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_permission
        WHERE id in
        <foreach collection="ids" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </select>

    <select id="selectAllPermission" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_permission
    </select>
</mapper>
