<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.permission.PostPermissionRelationMapper">

    <resultMap type="com.ruoyi.system.entity.permission.PostPermissionRelationEntity" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="oaPostId" column="oa_post_id"/>
        <result property="permissionIds" column="permission_ids"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
            oa_post_id,
            permission_ids,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.permission.PostPermissionRelationEntity">
        INSERT INTO tb_post_permission_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="oaPostId != null">
                oa_post_id,
            </if>
            <if test="permissionIds != null">
                permission_ids
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="oaPostId != null">
                #{oaPostId},
            </if>
            <if test="permissionIds != null">
                #{permissionIds}
            </if>
        </trim>
    </insert>

    <delete id="deleteById">
        DELETE
        FROM tb_post_permission_relation
        WHERE id = #{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.permission.PostPermissionRelationEntity">
        UPDATE tb_post_permission_relation
        <set>
            <if test="oaPostId != null">
                oa_post_id = #{oaPostId},
            </if>
            <if test="permissionIds != null">
                permission_ids = #{permissionIds},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <update id="updatePermissionByPostId" parameterType="com.ruoyi.system.entity.permission.PostPermissionRelationEntity">
        UPDATE tb_post_permission_relation
        set permission_ids = #{permissionIds}
        WHERE oa_post_id = #{oaPostId}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_post_permission_relation
        WHERE id = #{id}
    </select>

    <select id="selectByPostId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_post_permission_relation
        WHERE oa_post_id = #{postId} limit 1
    </select>
</mapper>
