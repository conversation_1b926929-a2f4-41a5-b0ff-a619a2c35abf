<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.pay.WxPayOrderMapper">

    <resultMap type="com.ruoyi.system.entity.pay.WxPayOrderEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="outTradeNo" column="out_trade_no"/>
            <result property="landpageKey" column="landpage_key"/>
            <result property="nhOrderId" column="nh_order_id"/>
            <result property="nhAppId" column="nh_app_id"/>
            <result property="nhSlotId" column="nh_slot_id"/>
            <result property="nhConsumerId" column="nh_consumer_id"/>
            <result property="phone" column="phone"/>
            <result property="name" column="name"/>
            <result property="appId" column="app_id"/>
            <result property="mchId" column="mch_id"/>
            <result property="tradeType" column="trade_type"/>
            <result property="totalFee" column="total_fee"/>
            <result property="ip" column="ip"/>
            <result property="referer" column="referer"/>
            <result property="userAgent" column="user_agent"/>
            <result property="status" column="status"/>
            <result property="transactionId" column="transaction_id"/>
            <result property="openid" column="openid"/>
            <result property="bankType" column="bank_type"/>
            <result property="timeEnd" column="time_end"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            out_trade_no,
            landpage_key,
            nh_order_id,
            nh_app_id,
            nh_slot_id,
            nh_consumer_id,
            phone,
            `name`,
            app_id,
            mch_id,
            trade_type,
            total_fee,
            ip,
            referer,
            user_agent,
            `status`,
            transaction_id,
            openid,
            bank_type,
            time_end,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.pay.WxPayOrderEntity">
        INSERT INTO tb_wx_pay_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="outTradeNo != null">
                    out_trade_no,
                </if>
                <if test="landpageKey != null">
                    landpage_key,
                </if>
                <if test="nhOrderId != null">
                    nh_order_id,
                </if>
                <if test="nhAppId != null">
                    nh_app_id,
                </if>
                <if test="nhSlotId != null">
                    nh_slot_id,
                </if>
                <if test="nhConsumerId != null">
                    nh_consumer_id,
                </if>
                <if test="phone != null">
                    phone,
                </if>
                <if test="name != null">
                    `name`,
                </if>
                <if test="appId != null">
                    app_id,
                </if>
                <if test="mchId != null">
                    mch_id,
                </if>
                <if test="tradeType != null">
                    trade_type,
                </if>
                <if test="totalFee != null">
                    total_fee,
                </if>
                <if test="ip != null">
                    ip,
                </if>
                <if test="referer != null">
                    referer,
                </if>
                <if test="userAgent != null">
                    user_agent,
                </if>
                <if test="status != null">
                    `status`,
                </if>
                <if test="transactionId != null">
                    transaction_id,
                </if>
                <if test="openid != null">
                    openid,
                </if>
                <if test="bankType != null">
                    bank_type,
                </if>
                <if test="timeEnd != null">
                    time_end
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="outTradeNo != null">
                    #{outTradeNo},
                </if>
                <if test="landpageKey != null">
                    #{landpageKey},
                </if>
                <if test="nhOrderId != null">
                    #{nhOrderId},
                </if>
                <if test="nhAppId != null">
                    #{nhAppId},
                </if>
                <if test="nhSlotId != null">
                    #{nhSlotId},
                </if>
                <if test="nhConsumerId != null">
                    #{nhConsumerId},
                </if>
                <if test="phone != null">
                    #{phone},
                </if>
                <if test="name != null">
                    #{name},
                </if>
                <if test="appId != null">
                    #{appId},
                </if>
                <if test="mchId != null">
                    #{mchId},
                </if>
                <if test="tradeType != null">
                    #{tradeType},
                </if>
                <if test="totalFee != null">
                    #{totalFee},
                </if>
                <if test="ip != null">
                    #{ip},
                </if>
                <if test="referer != null">
                    #{referer},
                </if>
                <if test="userAgent != null">
                    #{userAgent},
                </if>
                <if test="status != null">
                    #{status},
                </if>
                <if test="transactionId != null">
                    #{transactionId},
                </if>
                <if test="openid != null">
                    #{openid},
                </if>
                <if test="bankType != null">
                    #{bankType},
                </if>
                <if test="timeEnd != null">
                    #{timeEnd}
                </if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.ruoyi.system.entity.pay.WxPayOrderEntity">
        UPDATE tb_wx_pay_order
        <set>
                    <if test="landpageKey != null">
                        landpage_key = #{landpageKey},
                    </if>
                    <if test="nhOrderId != null">
                        nh_order_id = #{nhOrderId},
                    </if>
                    <if test="nhAppId != null">
                        nh_app_id = #{nhAppId},
                    </if>
                    <if test="nhSlotId != null">
                        nh_slot_id = #{nhSlotId},
                    </if>
                    <if test="nhConsumerId != null">
                        nh_consumer_id = #{nhConsumerId},
                    </if>
                    <if test="phone != null">
                        phone = #{phone},
                    </if>
                    <if test="name != null">
                        `name` = #{name},
                    </if>
                    <if test="appId != null">
                        app_id = #{appId},
                    </if>
                    <if test="mchId != null">
                        mch_id = #{mchId},
                    </if>
                    <if test="tradeType != null">
                        trade_type = #{tradeType},
                    </if>
                    <if test="totalFee != null">
                        total_fee = #{totalFee},
                    </if>
                    <if test="ip != null">
                        ip = #{ip},
                    </if>
                    <if test="referer != null">
                        referer = #{referer},
                    </if>
                    <if test="userAgent != null">
                        user_agent = #{userAgent},
                    </if>
                    <if test="status != null">
                        `status` = #{status},
                    </if>
                    <if test="transactionId != null">
                        transaction_id = #{transactionId},
                    </if>
                    <if test="openid != null">
                        openid = #{openid},
                    </if>
                    <if test="bankType != null">
                        bank_type = #{bankType},
                    </if>
                    <if test="timeEnd != null">
                        time_end = #{timeEnd},
                    </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectByOutTradeNo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_wx_pay_order
        WHERE out_trade_no = #{outTradeNo}
    </select>

</mapper>
