<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.slotdata.SlotMonthDataMapper">

    <resultMap type="com.ruoyi.system.entity.slotdata.SlotMonthDataEntity" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="monthDate" column="month_date"/>
        <result property="accountId" column="account_id"/>
        <result property="appId" column="app_id"/>
        <result property="slotId" column="slot_id"/>
        <result property="slotRequestPv" column="slot_request_pv"/>
        <result property="slotRequestUv" column="slot_request_uv"/>
        <result property="appSlotClickPv"    column="app_slot_click_pv"    />
        <result property="appSlotClickUv"    column="app_slot_click_uv"    />
        <result property="appSlotExposurePv"    column="app_slot_exposure_pv"    />
        <result property="appSlotExposureUv"    column="app_slot_exposure_uv"    />
        <result property="slotRequestUvCalculate" column="slot_request_uv_calculate"/>
        <result property="joinPv" column="join_pv"/>
        <result property="joinUv" column="join_uv"/>
        <result property="totalConsume" column="total_consume"/>
        <result property="nhConsume" column="nh_consume"/>
        <result property="outerConsume" column="outer_consume"/>
        <result property="appRevenue" column="app_revenue"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        month_date,
        account_id,
        app_id,
        slot_id,
        slot_request_pv,
        slot_request_uv,
        app_slot_click_pv,
        app_slot_click_uv,
        app_slot_exposure_pv,
        app_slot_exposure_uv,
        slot_request_uv_calculate,
        join_pv,
        join_uv,
        total_consume,
        nh_consume,
        outer_consume,
        app_revenue,
        gmt_create,
        gmt_modified
    </sql>

    <insert id="batchInsertOrUpdate">
        INSERT INTO tb_slot_month_data
        (month_date,account_id,app_id,slot_id,slot_request_pv,slot_request_uv,app_slot_click_pv,app_slot_click_uv,app_slot_exposure_pv,
        app_slot_exposure_uv,slot_request_uv_calculate,join_pv, join_uv,total_consume,nh_consume, outer_consume,app_revenue)
        values
        <foreach collection="dataEntities" item="entity" separator=",">
            ( #{entity.monthDate},#{entity.accountId},#{entity.appId},#{entity.slotId}, #{entity.slotRequestPv},#{entity.slotRequestUv},#{entity.appSlotClickPv},#{entity.appSlotClickUv}
            ,#{entity.appSlotExposurePv},#{entity.appSlotExposureUv},#{entity.slotRequestUvCalculate},#{entity.joinPv}, #{entity.joinUv},#{entity.totalConsume},#{entity.nhConsume},#{entity.outerConsume},#{entity.appRevenue})
        </foreach>
        ON DUPLICATE KEY UPDATE
        slot_request_pv = values(slot_request_pv),
        slot_request_uv = values(slot_request_uv) ,
        app_slot_click_pv = values(app_slot_click_pv) ,
        app_slot_click_uv = values(app_slot_click_uv) ,
        app_slot_exposure_pv = values(app_slot_exposure_pv) ,
        app_slot_exposure_uv = values(app_slot_exposure_uv) ,
        slot_request_uv_calculate = values(slot_request_uv_calculate),
        join_pv = values(join_pv),
        join_uv = values(join_uv),
        total_consume = values(total_consume),
        nh_consume = values(nh_consume),
        outer_consume = values(outer_consume),
        app_revenue = values(app_revenue)
    </insert>


    <select id="selectByAppIdAndDate" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_slot_month_data
        where app_id = #{appId} and month_date = #{monthDate}
        order by app_revenue desc
    </select>

    <select id="countBySlotIdAndMonth" resultType="int">
        select count(*)
        from tb_slot_month_data
        where slot_id = #{slotId} and month_date = #{month}
    </select>

    <select id="selectBySlotIdsAndMonths" resultMap="BaseResultMap">
        select month_date, slot_id
        from tb_slot_month_data
        <where>
            <if test="slotIds != null and slotIds.size() > 0">
                and slot_id in
                <foreach collection="slotIds" item="slotId" open="(" separator="," close=")">
                    #{slotId}
                </foreach>
            </if>
            <if test="months != null and months.size() > 0">
                and month_date in
                <foreach collection="months" item="month" open="(" separator="," close=")">
                    #{month}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectBySlotIdsAndMonth" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_slot_month_data
        where slot_id in
        <foreach collection="slotIds" item="slotId" separator="," close=")" open="(">
            #{slotId}
        </foreach>
        and month_date = #{month}
    </select>

    <select id="selectBySlotIdAndMonth" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_slot_month_data
        where slot_id = #{slotId} and month_date = #{month}
    </select>

    <insert id="insert" parameterType="com.ruoyi.system.entity.slotdata.SlotMonthDataEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_slot_month_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="monthDate != null">month_date,</if>
            <if test="accountId != null">account_id,</if>
            <if test="appId != null">app_id,</if>
            <if test="slotId != null">slot_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="monthDate != null">#{monthDate},</if>
            <if test="accountId != null">#{accountId},</if>
            <if test="appId != null">#{appId},</if>
            <if test="slotId != null">#{slotId},</if>
        </trim>
    </insert>

    <update id="update" parameterType="com.ruoyi.system.entity.slotdata.SlotMonthDataEntity">
        update tb_slot_month_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="slotRequestPv != null">slot_request_pv = #{slotRequestPv},</if>
            <if test="slotRequestUv != null">slot_request_uv = #{slotRequestUv},</if>
            <if test="appRevenue != null">app_revenue = #{appRevenue},</if>
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>
</mapper>
