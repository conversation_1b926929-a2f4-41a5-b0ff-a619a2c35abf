<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.redpacket.PhoneRedPacketRecordMapper">
    <resultMap type="com.ruoyi.system.entity.redpacket.PhoneRedPacketRecordEntity" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="phone" column="phone"/>
        <result property="name" column="name"/>
        <result property="amount" column="amount"/>
        <result property="alipayAccount" column="alipay_account"/>
        <result property="alipayName" column="alipay_name"/>
        <result property="ip" column="ip"/>
        <result property="userAgent" column="user_agent"/>
        <result property="transferStatus" column="transfer_status"/>
        <result property="operatorId" column="operator_id"/>
        <result property="operatorName" column="operator_name"/>
        <result property="operatorTime" column="operator_time"/>
        <result property="remark" column="remark"/>
        <result property="orderId" column="order_id"/>
        <result property="extInfo" column="ext_info"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
            phone,
            name,
            amount,
            alipay_account,
            alipay_name,
            ip,
            user_agent,
            transfer_status,
            operator_id,
            operator_name,
            operator_time,
            remark,
            order_id,
            ext_info,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.redpacket.PhoneRedPacketRecordEntity">
        INSERT INTO tb_phone_red_packet_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="phone != null">
                phone,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="alipayAccount != null">
                alipay_account,
            </if>
            <if test="alipayName != null">
                alipay_name,
            </if>
            <if test="ip != null">
                ip,
            </if>
            <if test="userAgent != null">
                user_agent,
            </if>
            <if test="transferStatus != null">
                transfer_status,
            </if>
            <if test="operatorId != null">
                operator_id,
            </if>
            <if test="operatorName != null">
                operator_name,
            </if>
            <if test="operatorTime != null">
                operator_time,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="orderId != null">
                order_id
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="phone != null">
                #{phone},
            </if>
            <if test="name != null">
                #{name},
            </if>
            <if test="amount != null">
                #{amount},
            </if>
            <if test="alipayAccount != null">
                #{alipayAccount},
            </if>
            <if test="alipayName != null">
                #{alipayName},
            </if>
            <if test="ip != null">
                #{ip},
            </if>
            <if test="userAgent != null">
                #{userAgent},
            </if>
            <if test="transferStatus != null">
                #{transferStatus},
            </if>
            <if test="operatorId != null">
                #{operatorId},
            </if>
            <if test="operatorName != null">
                #{operatorName},
            </if>
            <if test="operatorTime != null">
                #{operatorTime},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="orderId != null">
                #{orderId}
            </if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.ruoyi.system.entity.redpacket.PhoneRedPacketRecordEntity">
        UPDATE tb_phone_red_packet_record
        <set>
            <if test="phone != null">
                phone = #{phone},
            </if>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="amount != null">
                amount = #{amount},
            </if>
            <if test="alipayAccount != null">
                alipay_account = #{alipayAccount},
            </if>
            <if test="alipayName != null">
                alipay_name = #{alipayName},
            </if>
            <if test="ip != null">
                ip = #{ip},
            </if>
            <if test="userAgent != null">
                user_agent = #{userAgent},
            </if>
            <if test="transferStatus != null">
                transfer_status = #{transferStatus},
            </if>
            <if test="operatorId != null">
                operator_id = #{operatorId},
            </if>
            <if test="operatorName != null">
                operator_name = #{operatorName},
            </if>
            <if test="operatorTime != null">
                operator_time = #{operatorTime},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="extInfo != null">
                ext_info = #{extInfo}
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectByPhone" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_phone_red_packet_record
        WHERE phone = #{phone}
    </select>

    <select id="selectByReq" resultMap="BaseResultMap" parameterType="com.ruoyi.system.req.cashback.CashBackListReq">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_phone_red_packet_record
        <where>
            <if test="alipaySearch != null">
                (alipay_account like concat('%', #{alipaySearch}, '%') or alipay_name like  concat('%', #{alipaySearch}, '%'))
            </if>
            <if test="formSearch != null">
                and (phone like concat('%', #{formSearch}, '%') or name like  concat('%', #{formSearch}, '%'))
            </if>
            <if test="submitStartTime != null and submitEndTime != null">
                and gmt_create &gt;= #{submitStartTime} and gmt_create &lt;= #{submitEndTime}
            </if>
            <if test="operatorStartTime != null and operatorEndTime != null">
                and operator_time &gt;= #{operatorStartTime} and operator_time &lt;= #{operatorEndTime}
            </if>
            <if test="transferStatus != null">
                and transfer_status = #{transferStatus}
            </if>
        </where>
        order by gmt_create desc
    </select>
</mapper>
