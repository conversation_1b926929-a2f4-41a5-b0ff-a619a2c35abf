<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.datashow.AdvertSlotConvDayDataMapper">

    <resultMap type="com.ruoyi.system.entity.datashow.AdvertSlotConvDayDataEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="curDate" column="cur_date"/>
            <result property="advertId" column="advert_id"/>
            <result property="slotId" column="slot_id"/>
            <result property="convType" column="conv_type"/>
            <result property="convPv" column="conv_pv"/>
            <result property="convUv" column="conv_uv"/>
            <result property="convPrice" column="conv_price"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            cur_date,
            advert_id,
            slot_id,
            conv_type,
            conv_pv,
            conv_uv,
            conv_price,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.datashow.AdvertSlotConvDayDataEntity">
        INSERT IGNORE INTO tb_advert_slot_conv_day_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="curDate != null">
                    cur_date,
                </if>
                <if test="advertId != null">
                    advert_id,
                </if>
                <if test="slotId != null">
                    slot_id,
                </if>
                <if test="convType != null">
                    conv_type,
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="curDate != null">
                    #{curDate},
                </if>
                <if test="advertId != null">
                    #{advertId},
                </if>
                <if test="slotId != null">
                    #{slotId},
                </if>
                <if test="convType != null">
                    #{convType},
                </if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.ruoyi.system.entity.datashow.AdvertSlotConvDayDataEntity">
        UPDATE tb_advert_slot_conv_day_data
        <set>
            <if test="convPvAdd != null">conv_pv = conv_pv + #{convPvAdd},</if>
            <if test="convUvAdd != null">conv_uv = conv_uv + #{convUvAdd},</if>
            <if test="convPriceAdd != null">conv_price = conv_price + #{convPriceAdd},</if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectBy" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_advert_slot_conv_day_data
        <where>
            <if test="curDate != null "> and cur_date = #{curDate}</if>
            <if test="advertId != null"> and advert_id = #{advertId}</if>
            <if test="slotId != null"> and slot_id = #{slotId}</if>
            <if test="convType != null"> and conv_type = #{convType}</if>
        </where>
    </select>

    <select id="countByDateAndSlotIds" resultType="com.ruoyi.system.bo.landpage.ConvDataBo">
        select `cur_date` as curDate, `slot_id` as slotId,
        max(IF(tt.conv_type = 2, conv_pv, 0)) as 'pay' ,
        max(IF(tt.conv_type = 2, conv_price, 0)) as 'payPrice',
        max(IF(tt.conv_type = 3, conv_pv, 0)) as 'refund',
        max(IF(tt.conv_type = 4, conv_pv, 0)) as 'register',
        max(IF(tt.conv_type = 6, conv_pv, 0)) as 'complain',
        max(IF(tt.conv_type = 12, conv_pv, 0)) as 'appActive'
        from (
        select `cur_date` , `slot_id`, `conv_type`, sum(conv_pv) as conv_pv, sum(`conv_price`) as conv_price
        from `tb_advert_slot_conv_day_data`
        <where>
            <if test="startDate != null "> and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null "> and cur_date &lt;= #{endDate}</if>
            <if test="slotIds != null and slotIds.size() > 0">
                and slot_id in
                <foreach collection="slotIds" item="slotId" open="(" separator="," close=")">
                    #{slotId}
                </foreach>
            </if>
        </where>
        group by `cur_date`, `slot_id`, `conv_type`
        ) tt
        GROUP BY `cur_date`, `slot_id`
    </select>

    <select id="countByDateAndAdvertIds" resultType="com.ruoyi.system.bo.landpage.ConvDataBo">
        select `cur_date` as curDate, `advert_id` as advertId,
        max(IF(tt.conv_type = 2, conv_pv, 0)) as 'pay' ,
        max(IF(tt.conv_type = 2, conv_price, 0)) as 'payPrice',
        max(IF(tt.conv_type = 3, conv_pv, 0)) as 'refund',
        max(IF(tt.conv_type = 4, conv_pv, 0)) as 'register',
        max(IF(tt.conv_type = 6, conv_pv, 0)) as 'complain',
        max(IF(tt.conv_type = 12, conv_pv, 0)) as 'appActive'
        from (
        select `cur_date` , `advert_id`, `conv_type`, sum(conv_pv) as conv_pv, sum(`conv_price`) as conv_price
        from `tb_advert_slot_conv_day_data`
        <where>
            <if test="startDate != null "> and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null "> and cur_date &lt;= #{endDate}</if>
            <if test="advertIds != null and advertIds.size() > 0">
                and advert_id in
                <foreach collection="advertIds" item="advertId" open="(" separator="," close=")">
                    #{advertId}
                </foreach>
            </if>
        </where>
        group by `cur_date`, `advert_id`, `conv_type`
        ) tt
        GROUP BY `cur_date`, `advert_id`
    </select>

    <select id="countByDateAndAdvertIds2" resultType="com.ruoyi.system.bo.landpage.ConvDataBo">
        select `cur_date` as curDate,
        max(IF(tt.conv_type = 2, conv_pv, 0)) as 'pay' ,
        max(IF(tt.conv_type = 2, conv_price, 0)) as 'payPrice',
        max(IF(tt.conv_type = 3, conv_pv, 0)) as 'refund',
        max(IF(tt.conv_type = 4, conv_pv, 0)) as 'register',
        max(IF(tt.conv_type = 6, conv_pv, 0)) as 'complain',
        max(IF(tt.conv_type = 12, conv_pv, 0)) as 'appActive'
        from (
        select `cur_date`, `conv_type`, sum(conv_pv) as conv_pv, sum(`conv_price`) as conv_price
        from `tb_advert_slot_conv_day_data`
        <where>
            <if test="startDate != null "> and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null "> and cur_date &lt;= #{endDate}</if>
            <if test="advertIds != null and advertIds.size() > 0">
                and advert_id in
                <foreach collection="advertIds" item="advertId" open="(" separator="," close=")">
                    #{advertId}
                </foreach>
            </if>
        </where>
        group by `cur_date`, `conv_type`
        ) tt
        GROUP BY `cur_date`
    </select>

    <select id="countByDateAndAdvertIdAndAppIds" resultType="com.ruoyi.system.bo.landpage.ConvDataBo">
        select `cur_date` as curDate, `advert_id` as advertId, `app_id` as appId,
        max(IF(tt.conv_type = 2, conv_pv, 0)) as 'pay' ,
        max(IF(tt.conv_type = 2, conv_price, 0)) as 'payPrice',
        max(IF(tt.conv_type = 3, conv_pv, 0)) as 'refund',
        max(IF(tt.conv_type = 4, conv_pv, 0)) as 'register',
        max(IF(tt.conv_type = 6, conv_pv, 0)) as 'complain',
        max(IF(tt.conv_type = 12, conv_pv, 0)) as 'appActive'
        from (
        select `cur_date` , `advert_id`, `app_id`, `conv_type`, sum(conv_pv) as conv_pv, sum(`conv_price`) as conv_price
        from tb_advert_slot_conv_day_data d
        left join tb_slot s on d.slot_id = s.id
        <where>
            <if test="startDate != null "> and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null "> and cur_date &lt;= #{endDate}</if>
            <if test="advertId != null"> and advert_id = #{advertId}</if>
            <if test="appIds != null and appIds.size() > 0">
                and app_id in
                <foreach collection="appIds" item="appId" open="(" separator="," close=")">
                    #{appId}
                </foreach>
            </if>
        </where>
        group by `cur_date`, `advert_id`, `app_id`, `conv_type`
        ) tt
        GROUP BY `cur_date`, `advert_id`, `app_id`
    </select>

    <select id="countByDateAndAdvertIdAndSlotIds" resultType="com.ruoyi.system.bo.landpage.ConvDataBo">
        select `cur_date` as curDate, `advert_id` as advertId, `slot_id` as slotId,
        max(IF(tt.conv_type = 2, conv_pv, 0)) as 'pay' ,
        max(IF(tt.conv_type = 2, conv_price, 0)) as 'payPrice',
        max(IF(tt.conv_type = 3, conv_pv, 0)) as 'refund',
        max(IF(tt.conv_type = 4, conv_pv, 0)) as 'register',
        max(IF(tt.conv_type = 6, conv_pv, 0)) as 'complain',
        max(IF(tt.conv_type = 12, conv_pv, 0)) as 'appActive'
        from (
        select `cur_date` , `advert_id`, `slot_id`, `conv_type`, sum(conv_pv) as conv_pv, sum(`conv_price`) as conv_price
        from `tb_advert_slot_conv_day_data`
        <where>
            <if test="startDate != null "> and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null "> and cur_date &lt;= #{endDate}</if>
            <if test="advertId != null"> and advert_id = #{advertId}</if>
            <if test="slotIds != null and slotIds.size() > 0">
                and slot_id in
                <foreach collection="slotIds" item="slotId" open="(" separator="," close=")">
                    #{slotId}
                </foreach>
            </if>
            <if test="advertIds != null and advertIds.size() > 0">
                and advert_id in
                <foreach collection="advertIds" item="advertId" open="(" separator="," close=")">
                    #{advertId}
                </foreach>
            </if>
        </where>
        group by `cur_date`, `advert_id`, `slot_id`, `conv_type`
        ) tt
        GROUP BY `cur_date`, `advert_id`, `slot_id`
    </select>

    <select id="selectStatisticAdvertConvData" parameterType="com.ruoyi.system.bo.landpage.AdvertConvDataParamBo" resultType="com.ruoyi.system.bo.landpage.ConvDataBo">
        select
        max(IF(tt.conv_type = 2, conv_pv, 0)) as 'pay' ,
        max(IF(tt.conv_type = 2, conv_price, 0)) as 'payPrice',
        max(IF(tt.conv_type = 3, conv_pv, 0)) as 'refund',
        max(IF(tt.conv_type = 4, conv_pv, 0)) as 'register',
        max(IF(tt.conv_type = 6, conv_pv, 0)) as 'complain',
        max(IF(tt.conv_type = 12, conv_pv, 0)) as 'appActive'
        from (
        select `conv_type`, sum(conv_pv) as conv_pv, sum(`conv_price`) as conv_price
        from `tb_advert_slot_conv_day_data`
        <where>
            <if test="curDate != null "> and cur_date = #{curDate}</if>
            <if test="advertId != null"> and advert_id = #{advertId}</if>
            <if test="startDate != null"> and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null"> and cur_date &lt;= #{endDate}</if>
            <if test="advertIds != null and advertIds.size() > 0">
                and advert_id in
                <foreach collection="advertIds" item="advertId" open="(" separator="," close=")">
                    #{advertId}
                </foreach>
            </if>
            <if test="slotIds != null and slotIds.size() > 0">
                and slot_id in
                <foreach collection="slotIds" item="slotId" open="(" separator="," close=")">
                    #{slotId}
                </foreach>
            </if>
        </where>
        group by `conv_type`
        ) tt
    </select>
</mapper>
