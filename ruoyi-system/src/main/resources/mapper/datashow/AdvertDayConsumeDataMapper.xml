<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.datashow.AdvertDayConsumeDataMapper">

    <resultMap type="com.ruoyi.system.entity.datashow.AdvertDayConsumeData" id="AdvertDayConsumeDataResult">
        <result property="id"    column="id"    />
        <result property="curDate"    column="cur_date"    />
        <result property="advertiserId"    column="advertiser_id"    />
        <result property="advertId"    column="advert_id"    />
        <result property="consume"    column="consume"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectAdvertDayConsumeDataVo">
        select id, cur_date, advertiser_id, advert_id, consume, gmt_create, gmt_modified from tb_advert_day_consume_data
    </sql>

    <select id="selectAdvertDayConsumeDataList" parameterType="com.ruoyi.system.entity.datashow.AdvertDayConsumeData" resultMap="AdvertDayConsumeDataResult">
        <include refid="selectAdvertDayConsumeDataVo"/>
        <where>
            <if test="curDate != null "> and cur_date = #{curDate}</if>
            <if test="advertiserId != null  and advertiserId != ''"> and advertiser_id = #{advertiserId}</if>
            <if test="advertId != null  and advertId != ''"> and advert_id = #{advertId}</if>
            <if test="consume != null  and consume != ''"> and consume = #{consume}</if>
            <if test="startDate != null "> and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null "> and cur_date &lt;= #{endDate}</if>
            <if test="advertIds != null and advertIds.size() > 0">
                and advert_id in
                <foreach collection="advertIds" item="advertId" open="(" separator="," close=")">
                    #{advertId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectAdvertDayConsumeDataById" parameterType="Long" resultMap="AdvertDayConsumeDataResult">
        <include refid="selectAdvertDayConsumeDataVo"/>
        where id = #{id}
    </select>

    <select id="selectByDateAndAdvertId" resultMap="AdvertDayConsumeDataResult">
        <include refid="selectAdvertDayConsumeDataVo"/>
        where cur_date = #{curDate} and advert_id = #{advertId}
    </select>

    <update id="addConsumeData" parameterType="com.ruoyi.system.entity.datashow.AdvertDayConsumeData">
        update tb_advert_day_consume_data
        <trim prefix="SET" suffixOverrides=",">
            consume = consume + #{consumeAdd},
            gmt_modified = now()
        </trim>
        where id = #{id}
        <if test="dailyBudget != null"> and consume + #{consumeAdd} &lt;= #{dailyBudget}</if>
    </update>

    <insert id="insertAdvertDayConsumeData" parameterType="com.ruoyi.system.entity.datashow.AdvertDayConsumeData" useGeneratedKeys="true" keyProperty="id">
        insert ignore into tb_advert_day_consume_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="curDate != null">cur_date,</if>
            <if test="advertiserId != null">advertiser_id,</if>
            <if test="advertId != null">advert_id,</if>
            <if test="consume != null">consume,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="curDate != null">#{curDate},</if>
            <if test="advertiserId != null">#{advertiserId},</if>
            <if test="advertId != null">#{advertId},</if>
            <if test="consume != null">#{consume},</if>
        </trim>
    </insert>

    <update id="updateAdvertDayConsumeData" parameterType="com.ruoyi.system.entity.datashow.AdvertDayConsumeData">
        update tb_advert_day_consume_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="curDate != null">cur_date = #{curDate},</if>
            <if test="advertiserId != null">advertiser_id = #{advertiserId},</if>
            <if test="advertId != null">advert_id = #{advertId},</if>
            <if test="consume != null">consume = #{consume},</if>
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>
</mapper>
