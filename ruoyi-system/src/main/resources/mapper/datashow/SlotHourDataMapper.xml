<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.datashow.SlotHourDataMapper">

    <resultMap type="com.ruoyi.system.entity.datashow.SlotHourData" id="SlotHourDataResult">
        <result property="id"    column="id"    />
        <result property="curDate"    column="cur_date"    />
        <result property="curHour"    column="cur_hour"    />
        <result property="accountId"    column="account_id"    />
        <result property="appId"    column="app_id"    />
        <result property="slotId"    column="slot_id"    />
        <result property="slotRequestPv"    column="slot_request_pv"    />
        <result property="slotRequestUv"    column="slot_request_uv"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectSlotHourDataVo">
        select id, cur_date, cur_hour, account_id, app_id, slot_id, slot_request_pv, slot_request_uv,
               gmt_create, gmt_modified from tb_slot_hour_data
    </sql>

    <select id="selectSlotHourDataList" parameterType="com.ruoyi.system.entity.datashow.SlotHourData" resultMap="SlotHourDataResult">
        <include refid="selectSlotHourDataVo"/>
        <where>
            <if test="curDate != null"> and cur_date = #{curDate}</if>
            <if test="curHour != null"> and cur_hour = #{curHour}</if>
            <if test="accountId != null"> and account_id = #{accountId}</if>
            <if test="appId != null"> and app_id = #{appId}</if>
            <if test="slotId != null"> and slot_id = #{slotId}</if>
        </where>
    </select>

    <select id="selectBySlotIdAndDateHour" resultMap="SlotHourDataResult">
        <include refid="selectSlotHourDataVo"/>
        where slot_id = #{slotId} and cur_date = #{curDate} and cur_hour =  #{curHour}
    </select>

    <insert id="insertSlotHourData" parameterType="com.ruoyi.system.entity.datashow.SlotHourData"
            useGeneratedKeys="true" keyProperty="id">
        insert ignore into tb_slot_hour_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="curDate != null">cur_date,</if>
            <if test="curHour != null">cur_hour,</if>
            <if test="accountId != null">account_id,</if>
            <if test="appId != null">app_id,</if>
            <if test="slotId != null">slot_id,</if>
            <if test="slotRequestPv != null">slot_request_pv,</if>
            <if test="slotRequestUv != null">slot_request_uv,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="curDate != null">#{curDate},</if>
            <if test="curHour != null">#{curHour},</if>
            <if test="accountId != null">#{accountId},</if>
            <if test="appId != null">#{appId},</if>
            <if test="slotId != null ">#{slotId},</if>
            <if test="slotRequestPv != null">#{slotRequestPv},</if>
            <if test="slotRequestUv != null">#{slotRequestUv},</if>
         </trim>
    </insert>

    <update id="updateSlotHourData" parameterType="com.ruoyi.system.entity.datashow.SlotHourData">
        update tb_slot_hour_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="curDate != null">cur_date = #{curDate},</if>
            <if test="curHour != null">cur_hour = #{curHour},</if>
            <if test="accountId != null">account_id = #{accountId},</if>
            <if test="appId != null">app_id = #{appId},</if>
            <if test="slotId != null">slot_id = #{slotId},</if>
            <if test="slotRequestPv != null">slot_request_pv = #{slotRequestPv},</if>
            <if test="slotRequestUv != null">slot_request_uv = #{slotRequestUv},</if>
            <if test="slotRequestPvAdd != null">slot_request_pv = slot_request_pv + #{slotRequestPvAdd},</if>
            <if test="slotRequestUvAdd != null">slot_request_uv = slot_request_uv + #{slotRequestUvAdd},</if>
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>

</mapper>
