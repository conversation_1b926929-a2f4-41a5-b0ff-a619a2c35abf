<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.datashow.SlotActivityDataMapper">

    <resultMap type="com.ruoyi.system.entity.datashow.SlotActivityData" id="SlotActivityDataResult">
        <result property="id"    column="id"    />
        <result property="curDate"    column="cur_date"    />
        <result property="accountId"    column="account_id"    />
        <result property="appId"    column="app_id"    />
        <result property="slotId"    column="slot_id"    />
        <result property="activityId"    column="activity_id"    />
        <result property="activityRequestPv"    column="activity_request_pv"    />
        <result property="activityRequestUv"    column="activity_request_uv"    />
        <result property="joinPv"    column="join_pv"    />
        <result property="joinUv"    column="join_uv"    />
        <result property="adRequest" column="ad_request"    />
        <result property="adLaunch" column="ad_launch"    />
        <result property="adExposurePv"    column="ad_exposure_pv"    />
        <result property="adExposureUv"    column="ad_exposure_uv"    />
        <result property="adClickPv"    column="ad_click_pv"    />
        <result property="adClickUv"    column="ad_click_uv"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectSlotActivityDataVo">
        select id, cur_date, account_id, app_id, slot_id, activity_id,
               activity_request_pv, activity_request_uv, join_pv, join_uv,
               ad_request, ad_launch, ad_exposure_pv, ad_exposure_uv, ad_click_pv, ad_click_uv,
               gmt_create, gmt_modified from tb_slot_activity_data
    </sql>

    <select id="selectBy" resultMap="SlotActivityDataResult">
        <include refid="selectSlotActivityDataVo"/>
        where cur_date = #{curDate} and slot_id = #{slotId} and activity_id = #{activityId}
    </select>

    <select id="selectSlotActivityDataList" parameterType="com.ruoyi.system.entity.datashow.SlotActivityData" resultMap="SlotActivityDataResult">
        <include refid="selectSlotActivityDataVo"/>
        <where>
            <if test="curDate != null "> and cur_date = #{curDate}</if>
            <if test="accountId != null"> and account_id = #{accountId}</if>
            <if test="appId != null "> and app_id = #{appId}</if>
            <if test="slotId != null"> and slot_id = #{slotId}</if>
            <if test="activityId != null"> and activity_id = #{activityId}</if>
            <if test="activityRequestPv != null "> and activity_request_pv = #{activityRequestPv}</if>
            <if test="activityRequestUv != null "> and activity_request_uv = #{activityRequestUv}</if>
            <if test="joinPv != null "> and join_pv = #{joinPv}</if>
            <if test="joinUv != null "> and join_uv = #{joinUv}</if>
            <if test="adLaunch != null"> and ad_launch = #{adLaunch}</if>
            <if test="adExposurePv != null "> and ad_exposure_pv = #{adExposurePv}</if>
            <if test="adExposureUv != null "> and ad_exposure_uv = #{adExposureUv}</if>
            <if test="adClickPv != null "> and ad_click_pv = #{adClickPv}</if>
            <if test="adClickUv != null "> and ad_click_uv = #{adClickUv}</if>
            <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
            <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
        </where>
    </select>

    <select id="selectSlotActivityDataById" parameterType="String" resultMap="SlotActivityDataResult">
        <include refid="selectSlotActivityDataVo"/>
        where id = #{id}
    </select>

    <insert id="insertSlotActivityData" parameterType="com.ruoyi.system.entity.datashow.SlotActivityData" useGeneratedKeys="true" keyProperty="id">
        insert ignore into tb_slot_activity_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="curDate != null">cur_date,</if>
            <if test="accountId != null">account_id,</if>
            <if test="appId != null">app_id,</if>
            <if test="slotId != null">slot_id,</if>
            <if test="activityId != null">activity_id,</if>
            <if test="activityRequestPv != null">activity_request_pv,</if>
            <if test="activityRequestUv != null">activity_request_uv,</if>
            <if test="joinPv != null">join_pv,</if>
            <if test="joinUv != null">join_uv,</if>
            <if test="adRequest != null">ad_request,</if>
            <if test="adLaunch != null">ad_launch,</if>
            <if test="adExposurePv != null">ad_exposure_pv,</if>
            <if test="adExposureUv != null">ad_exposure_uv,</if>
            <if test="adClickPv != null">ad_click_pv,</if>
            <if test="adClickUv != null">ad_click_uv,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="curDate != null">#{curDate},</if>
            <if test="accountId != null">#{accountId},</if>
            <if test="appId != null">#{appId},</if>
            <if test="slotId != null">#{slotId},</if>
            <if test="activityId != null">#{activityId},</if>
            <if test="activityRequestPv != null">#{activityRequestPv},</if>
            <if test="activityRequestPv != null">#{activityRequestPv},</if>
            <if test="joinPv != null">#{joinPv},</if>
            <if test="joinUv != null">#{joinUv},</if>
            <if test="adRequest != null">#{adRequest},</if>
            <if test="adLaunch != null">#{adLaunch},</if>
            <if test="adExposurePv != null">#{adExposurePv},</if>
            <if test="adExposureUv != null">#{adExposureUv},</if>
            <if test="adClickPv != null">#{adClickPv},</if>
            <if test="adClickUv != null">#{adClickUv},</if>
         </trim>
    </insert>

    <update id="updateSlotActivityData" parameterType="com.ruoyi.system.entity.datashow.SlotActivityData">
        update tb_slot_activity_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="curDate != null">cur_date = #{curDate},</if>
            <if test="accountId != null">account_id = #{accountId},</if>
            <if test="appId != null">app_id = #{appId},</if>
            <if test="slotId != null">slot_id = #{slotId},</if>
            <if test="activityId != null">activity_id = #{activityId},</if>
            <if test="activityRequestPv != null">activity_request_pv = #{activityRequestPv},</if>
            <if test="activityRequestUv != null">activity_request_uv = #{activityRequestUv},</if>
            <if test="joinPv != null">join_pv = #{joinPv},</if>
            <if test="joinUv != null">join_uv = #{joinUv},</if>
            <if test="adRequest != null">ad_request = #{adRequest},</if>
            <if test="adLaunch != null">ad_launch = #{adLaunch},</if>
            <if test="adExposurePv != null">ad_exposure_pv = #{adExposurePv},</if>
            <if test="adExposureUv != null">ad_exposure_uv = #{adExposureUv},</if>
            <if test="adClickPv != null">ad_click_pv = #{adClickPv},</if>
            <if test="adClickUv != null">ad_click_uv = #{adClickUv},</if>
            <if test="activityRequestPvAdd != null">activity_request_pv = activity_request_pv + #{activityRequestPvAdd},</if>
            <if test="activityRequestUvAdd != null">activity_request_uv = activity_request_uv + #{activityRequestUvAdd},</if>
            <if test="joinPvAdd != null">join_pv = join_pv + #{joinPvAdd},</if>
            <if test="joinUvAdd != null">join_uv = join_uv + #{joinUvAdd},</if>
            <if test="adRequestAdd != null">ad_request = ad_request + #{adRequestAdd},</if>
            <if test="adLaunchAdd != null">ad_launch = ad_launch + #{adLaunchAdd},</if>
            <if test="adExposurePvAdd != null">ad_exposure_pv = ad_exposure_pv + #{adExposurePvAdd},</if>
            <if test="adExposureUvAdd != null">ad_exposure_uv = ad_exposure_uv + #{adExposureUvAdd},</if>
            <if test="adClickPvAdd != null">ad_click_pv = ad_click_pv + #{adClickPvAdd},</if>
            <if test="adClickUvAdd != null">ad_click_uv = ad_click_uv + #{adClickUvAdd},</if>
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>

    <select id="selectSumSlotActivityDataBySlotIdsAndDate" resultMap="SlotActivityDataResult">
        select cur_date, slot_id,sum(join_pv)  as join_pv, sum(join_uv) as join_uv ,
        sum(ad_request) as ad_request, sum(ad_launch) as ad_launch, sum(ad_exposure_pv) as ad_exposure_pv,
        sum(ad_exposure_uv) as ad_exposure_uv, sum(ad_click_pv) as ad_click_pv, sum(ad_click_uv) as ad_click_uv,
        sum(activity_request_uv) as activity_request_uv, sum(activity_request_pv) as activity_request_pv
        from tb_slot_activity_data
        <where>
            <if test="slotIds != null  and slotIds.size > 0">
                slot_id in
                <foreach collection="slotIds" separator="," item="slotId" open="(" close=")">
                    #{slotId}
                </foreach>
            </if>
            <if test="startDate != null">
                and cur_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                and cur_date &lt;= #{endDate}
            </if>
        </where>
        group by cur_date,slot_id
    </select>

    <select id="sumSlotActivityDataBySlotIdsAndDate" resultMap="SlotActivityDataResult">
        select sum(join_pv)  as join_pv, sum(join_uv) as join_uv ,
            sum(ad_request) as ad_request, sum(ad_launch) as ad_launch,
            sum(ad_exposure_pv) as ad_exposure_pv, sum(ad_exposure_uv) as ad_exposure_uv,
            sum(ad_click_pv) as ad_click_pv, sum(ad_click_uv) as ad_click_uv,
            sum(activity_request_uv) as activity_request_uv, sum(activity_request_pv) as activity_request_pv
        from tb_slot_activity_data
        <where>
            <if test="slotIds != null  and slotIds.size > 0">
                slot_id in
                <foreach collection="slotIds" separator="," item="slotId" open="(" close=")">
                    #{slotId}
                </foreach>
            </if>
            <if test="startDate != null">
                and cur_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                and cur_date &lt;= #{endDate}
            </if>
        </where>
    </select>

    <select id="selectSumSlotActivityDataGroupBySlot" resultType="com.ruoyi.system.bo.slotactivitydata.SlotActivityDataSumBo">
        select slot_id as slotId,sum(join_pv) as joinPv, sum(join_uv) as joinUv
        from tb_slot_activity_data
        where slot_id in
        <foreach collection="slotIds" separator="," item="slotId" open="(" close=")">
            #{slotId}
        </foreach>
        and cur_date &gt;= #{startDate}
        and cur_date &lt;= #{endDate}
        group by slot_id
    </select>

    <select id="selectSlotActivityDataSum" resultType="com.ruoyi.system.bo.slotactivitydata.SlotActivityDataSumBo">
        select sum(join_pv) as joinPv, sum(join_uv) as joinUv
        from tb_slot_activity_data
        <where>
            <if test="slotIds != null and slotIds.size > 0">
                slot_id in
                <foreach collection="slotIds" separator="," item="slotId" open="(" close=")">
                    #{slotId}
                </foreach>
            </if>
            <if test="startDate != null">
                and cur_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                and cur_date &lt;= #{endDate}
            </if>
        </where>
    </select>

    <select id="selectSlotActivityData" resultMap="SlotActivityDataResult">
        <include refid="selectSlotActivityDataVo"/>
        where slot_id in
        <foreach collection="slotIds" separator="," item="slotId" open="(" close=")">
            #{slotId}
        </foreach>
        and cur_date &gt;= #{startDate}
        and cur_date &lt;= #{endDate}
    </select>
    <select id="selectDataByAppIdAndDate" resultMap="SlotActivityDataResult">
        select slot_id,sum(activity_request_uv) as activity_request_uv, sum(activity_request_pv) as activity_request_pv
        from tb_slot_activity_data
        where app_id =#{appId}
        and cur_date = #{curDate}
        group by slot_id
    </select>
</mapper>
