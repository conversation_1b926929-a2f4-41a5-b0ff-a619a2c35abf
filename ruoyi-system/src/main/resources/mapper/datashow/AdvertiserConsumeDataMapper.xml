<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.datashow.AdvertiserConsumeDataMapper">

    <resultMap type="com.ruoyi.system.entity.datashow.AdvertiserConsumeData" id="AdvertiserConsumeDataResult">
        <result property="id"    column="id"    />
        <result property="curDate"    column="cur_date"    />
        <result property="advertiserId"    column="advertiser_id"    />
        <result property="consume"    column="consume"    />
        <result property="budget"    column="budget"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectAdvertiserConsumeDataVo">
        select id, cur_date, advertiser_id, consume, budget, gmt_create, gmt_modified from tb_advertiser_consume_data
    </sql>

    <select id="selectAdvertiserConsumeDataList" parameterType="com.ruoyi.system.entity.datashow.AdvertiserConsumeData" resultMap="AdvertiserConsumeDataResult">
        <include refid="selectAdvertiserConsumeDataVo"/>
        <where>
            <if test="advertiserId != null"> and advertiser_id = #{advertiserId}</if>
            <if test="advertiserIds != null and advertiserIds.size() > 0 ">
                and advertiser_id in
                <foreach collection="advertiserIds" item="advertiserId" open="(" separator="," close=")">
                    #{advertiserId}
                </foreach>
            </if>
            <if test="startDate != null "> and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null "> and cur_date &lt;= #{endDate}</if>
        </where>
        order by cur_date desc
    </select>

    <select id="selectByDateAndAdvertiserId" resultMap="AdvertiserConsumeDataResult">
        <include refid="selectAdvertiserConsumeDataVo"/>
        where cur_date = #{curDate} and advertiser_id = #{advertiserId}
    </select>

    <insert id="insertAdvertiserConsumeData" parameterType="com.ruoyi.system.entity.datashow.AdvertiserConsumeData"
            useGeneratedKeys="true" keyProperty="id">
        insert ignore into tb_advertiser_consume_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="curDate != null">cur_date,</if>
            <if test="advertiserId != null">advertiser_id,</if>
            <if test="consume != null">consume,</if>
            <if test="budget != null">budget,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="curDate != null">#{curDate},</if>
            <if test="advertiserId != null">#{advertiserId},</if>
            <if test="consume != null">#{consume},</if>
            <if test="budget != null">budget,</if>
         </trim>
    </insert>

    <update id="updateAdvertiserConsumeData" parameterType="com.ruoyi.system.entity.datashow.AdvertiserConsumeData">
        update tb_advertiser_consume_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="consume != null">consume = #{consume},</if>
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>

    <update id="updateBudget">
        update tb_advertiser_consume_data
        set budget = #{budget}
        where id = #{id}
    </update>

    <update id="addConsumeData" parameterType="com.ruoyi.system.entity.datashow.AdvertiserConsumeData">
        update tb_advertiser_consume_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="consumeAdd != null">consume = consume + #{consumeAdd},</if>
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>

    <select id="selectListByAdvertiserIdAndDate" resultMap="AdvertiserConsumeDataResult">
        <include refid="selectAdvertiserConsumeDataVo"/>
        where cur_date = #{curDate}
        <if test="advertiserIds != null and advertiserIds.size() > 0 ">
            and advertiser_id in
            <foreach collection="advertiserIds" item="advertiserId" open="(" separator="," close=")">
                #{advertiserId}
            </foreach>
        </if>
    </select>

</mapper>
