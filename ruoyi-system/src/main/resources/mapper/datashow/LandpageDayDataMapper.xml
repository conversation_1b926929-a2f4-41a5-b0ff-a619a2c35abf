<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.datashow.LandpageDayDataMapper">

    <resultMap type="com.ruoyi.system.entity.datashow.LandpageDayData" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="curDate" column="cur_date"/>
            <result property="landpageKey" column="landpage_key"/>
            <result property="adLaunchPv" column="ad_launch_pv"/>
            <result property="adLaunchUv" column="ad_launch_uv"/>
            <result property="adExposurePv" column="ad_exposure_pv"/>
            <result property="adExposureUv" column="ad_exposure_uv"/>
            <result property="adClickPv" column="ad_click_pv"/>
            <result property="adClickUv" column="ad_click_uv"/>
            <result property="lpExposurePv" column="lp_exposure_pv"/>
            <result property="lpExposureUv" column="lp_exposure_uv"/>
            <result property="lpClickPv" column="lp_click_pv"/>
            <result property="lpClickUv" column="lp_click_uv"/>
            <result property="lpJoinPv" column="lp_join_pv"/>
            <result property="lpJoinUv" column="lp_join_uv"/>
            <result property="popupExposurePv" column="popup_exposure_pv"/>
            <result property="popupExposureUv" column="popup_exposure_uv"/>
            <result property="popupClickPv" column="popup_click_pv"/>
            <result property="popupClickUv" column="popup_click_uv"/>
            <result property="register" column="register"/>
            <result property="pay" column="pay"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            cur_date,
            landpage_key,
            ad_launch_pv,
            ad_launch_uv,
            ad_exposure_pv,
            ad_exposure_uv,
            ad_click_pv,
            ad_click_uv,
            lp_exposure_pv,
            lp_exposure_uv,
            lp_click_pv,
            lp_click_uv,
            lp_join_pv,
            lp_join_uv,
            popup_exposure_pv,
            popup_exposure_uv,
            popup_click_pv,
            popup_click_uv,
            register,
            pay,
            gmt_create,
            gmt_modified
    </sql>

    <select id="selectList" parameterType="com.ruoyi.system.req.landpage.LandpageDataReq" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_landpage_day_data
        <where>
            lp_exposure_pv > 0
            <if test="startDate != null "> and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null "> and cur_date &lt;= #{endDate}</if>
            <if test="landpageKeyList != null and landpageKeyList.size()>0 ">
                and landpage_key in
                <foreach collection="landpageKeyList" item="landpageKey" open="(" separator="," close=")">
                    #{landpageKey}
                </foreach>
            </if>
        </where>
        order by cur_date desc, lp_exposure_pv desc
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.datashow.LandpageDayData">
        INSERT IGNORE INTO tb_landpage_day_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="curDate != null">
                cur_date,
            </if>
            <if test="landpageKey != null">
                landpage_key,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="curDate != null">
                #{curDate},
            </if>
            <if test="landpageKey != null">
                #{landpageKey},
            </if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.ruoyi.system.entity.datashow.LandpageDayData">
        UPDATE tb_landpage_day_data
        <set>
            <if test="adLaunchPvAdd != null">ad_launch_pv = ad_launch_pv + #{adLaunchPvAdd},</if>
            <if test="adLaunchUvAdd != null">ad_launch_uv = ad_launch_uv + #{adLaunchUvAdd},</if>
            <if test="adExposurePvAdd != null">ad_exposure_pv = ad_exposure_pv + #{adExposurePvAdd},</if>
            <if test="adExposureUvAdd != null">ad_exposure_uv = ad_exposure_uv + #{adExposureUvAdd},</if>
            <if test="adClickPvAdd != null">ad_click_pv = ad_click_pv + #{adClickPvAdd},</if>
            <if test="adClickUvAdd != null">ad_click_uv = ad_click_uv + #{adClickUvAdd},</if>
            <if test="lpExposurePvAdd != null">lp_exposure_pv = lp_exposure_pv + #{lpExposurePvAdd},</if>
            <if test="lpExposureUvAdd != null">lp_exposure_uv = lp_exposure_uv + #{lpExposureUvAdd},</if>
            <if test="lpClickPvAdd != null">lp_click_pv = lp_click_pv + #{lpClickPvAdd},</if>
            <if test="lpClickUvAdd != null">lp_click_uv = lp_click_uv + #{lpClickUvAdd},</if>
            <if test="lpJoinPvAdd != null">lp_join_pv = lp_join_pv + #{lpJoinPvAdd},</if>
            <if test="lpJoinUvAdd != null">lp_join_uv = lp_join_uv + #{lpJoinUvAdd},</if>
            <if test="popupExposurePvAdd != null">popup_exposure_pv = popup_exposure_pv + #{popupExposurePvAdd},</if>
            <if test="popupExposureUvAdd != null">popup_exposure_uv = popup_exposure_uv + #{popupExposureUvAdd},</if>
            <if test="popupClickPvAdd != null">popup_click_pv = popup_click_pv + #{popupClickPvAdd},</if>
            <if test="popupClickUvAdd != null">popup_click_uv = popup_click_uv + #{popupClickUvAdd},</if>
            <if test="registerAdd != null">register = register + #{registerAdd},</if>
            <if test="payAdd != null">pay = pay + #{payAdd},</if>
            gmt_modified = now()
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_landpage_day_data
        WHERE id = #{id}
    </select>

    <select id="selectBy" parameterType="com.ruoyi.system.entity.datashow.LandpageDayData" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_landpage_day_data
        <where>
            <if test="curDate != null "> and cur_date = #{curDate}</if>
            <if test="landpageKey != null"> and landpage_key = #{landpageKey}</if>
        </where>
        LIMIT 1
    </select>
</mapper>
