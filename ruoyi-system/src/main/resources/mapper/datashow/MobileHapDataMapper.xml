<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.datashow.MobileHapDataMapper">

    <resultMap type="com.ruoyi.system.entity.datashow.MobileHapDataEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="model" column="model"/>
            <result property="adLaunchPv" column="ad_launch_pv"/>
            <result property="adLaunchUv" column="ad_launch_uv"/>
            <result property="adExposurePv" column="ad_exposure_pv"/>
            <result property="adExposureUv" column="ad_exposure_uv"/>
            <result property="adClickPv" column="ad_click_pv"/>
            <result property="adClickUv" column="ad_click_uv"/>
            <result property="hapLaunchPv" column="hap_launch_pv"/>
            <result property="hapLaunchUv" column="hap_launch_uv"/>
            <result property="lpExposurePv" column="lp_exposure_pv"/>
            <result property="lpExposureUv" column="lp_exposure_uv"/>
            <result property="lpClickPv" column="lp_click_pv"/>
            <result property="lpClickUv" column="lp_click_uv"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            model,
            ad_launch_pv,
            ad_launch_uv,
            ad_exposure_pv,
            ad_exposure_uv,
            ad_click_pv,
            ad_click_uv,
            hap_launch_pv,
            hap_launch_uv,
            lp_exposure_pv,
            lp_exposure_uv,
            lp_click_pv,
            lp_click_uv,
            gmt_create,
            gmt_modified
    </sql>

    <select id="selectBy" parameterType="com.ruoyi.system.entity.datashow.MobileHapDataEntity" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_mobile_hap_data
        <where>
            <if test="model != null "> and model = #{model}</if>
        </where>
        LIMIT 1
    </select>

    <select id="selectList" parameterType="com.ruoyi.system.entity.datashow.MobileHapDataEntity" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_mobile_hap_data
        <where>
            <if test="model != null "> and model = #{model}</if>
        </where>
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.datashow.MobileHapDataEntity">
        INSERT IGNORE INTO tb_mobile_hap_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="model != null">
                    model,
                </if>
                <if test="adLaunchPv != null">
                    ad_launch_pv,
                </if>
                <if test="adLaunchUv != null">
                    ad_launch_uv,
                </if>
                <if test="adExposurePv != null">
                    ad_exposure_pv,
                </if>
                <if test="adExposureUv != null">
                    ad_exposure_uv,
                </if>
                <if test="adClickPv != null">
                    ad_click_pv,
                </if>
                <if test="adClickUv != null">
                    ad_click_uv,
                </if>
                <if test="hapLaunchPv != null">
                    hap_launch_pv,
                </if>
                <if test="hapLaunchUv != null">
                    hap_launch_uv,
                </if>
                <if test="lpExposurePv != null">
                    lp_exposure_pv,
                </if>
                <if test="lpExposureUv != null">
                    lp_exposure_uv,
                </if>
                <if test="lpClickPv != null">
                    lp_click_pv,
                </if>
                <if test="lpClickUv != null">
                    lp_click_uv
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="model != null">
                    #{model},
                </if>
                <if test="adLaunchPv != null">
                    #{adLaunchPv},
                </if>
                <if test="adLaunchUv != null">
                    #{adLaunchUv},
                </if>
                <if test="adExposurePv != null">
                    #{adExposurePv},
                </if>
                <if test="adExposureUv != null">
                    #{adExposureUv},
                </if>
                <if test="adClickPv != null">
                    #{adClickPv},
                </if>
                <if test="adClickUv != null">
                    #{adClickUv},
                </if>
                <if test="hapLaunchPv != null">
                    #{hapLaunchPv},
                </if>
                <if test="hapLaunchUv != null">
                    #{hapLaunchUv},
                </if>
                <if test="lpExposurePv != null">
                    #{lpExposurePv},
                </if>
                <if test="lpExposureUv != null">
                    #{lpExposureUv},
                </if>
                <if test="lpClickPv != null">
                    #{lpClickPv},
                </if>
                <if test="lpClickUv != null">
                    #{lpClickUv}
                </if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.ruoyi.system.entity.datashow.MobileHapDataEntity">
        UPDATE tb_mobile_hap_data
        <set>
            <if test="adLaunchPvAdd != null">ad_launch_pv = ad_launch_pv + #{adLaunchPvAdd},</if>
            <if test="adLaunchUvAdd != null">ad_launch_uv = ad_launch_uv + #{adLaunchUvAdd},</if>
            <if test="adExposurePvAdd != null">ad_exposure_pv = ad_exposure_pv + #{adExposurePvAdd},</if>
            <if test="adExposureUvAdd != null">ad_exposure_uv = ad_exposure_uv + #{adExposureUvAdd},</if>
            <if test="adClickPvAdd != null">ad_click_pv = ad_click_pv + #{adClickPvAdd},</if>
            <if test="adClickUvAdd != null">ad_click_uv = ad_click_uv + #{adClickUvAdd},</if>
            <if test="hapLaunchPvAdd != null">hap_launch_pv = hap_launch_pv + #{hapLaunchPvAdd},</if>
            <if test="hapLaunchUvAdd != null">hap_launch_uv = hap_launch_uv + #{hapLaunchUvAdd},</if>
            <if test="lpExposurePvAdd != null">lp_exposure_pv = lp_exposure_pv + #{lpExposurePvAdd},</if>
            <if test="lpExposureUvAdd != null">lp_exposure_uv = lp_exposure_uv + #{lpExposureUvAdd},</if>
            <if test="lpClickPvAdd != null">lp_click_pv = lp_click_pv + #{lpClickPvAdd},</if>
            <if test="lpClickUvAdd != null">lp_click_uv = lp_click_uv + #{lpClickUvAdd},</if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_mobile_hap_data
        WHERE id = #{id}
    </select>

</mapper>
