<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.datashow.AdvertChargeHourDataMapper">

    <resultMap type="com.ruoyi.system.entity.datashow.AdvertChargeHourDataEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="curDate" column="cur_date"/>
            <result property="curHour" column="cur_hour"/>
            <result property="advertId" column="advert_id"/>
            <result property="chargeType" column="charge_type"/>
            <result property="adLaunchPv" column="ad_launch_pv"/>
            <result property="adLaunchUv" column="ad_launch_uv"/>
            <result property="exposurePv" column="exposure_pv"/>
            <result property="exposureUv" column="exposure_uv"/>
            <result property="clickPv" column="click_pv"/>
            <result property="clickUv" column="click_uv"/>
            <result property="billingClickPv" column="billing_click_pv"/>
            <result property="billingClickUv" column="billing_click_uv"/>
            <result property="consume" column="consume"/>
            <result property="lpExposurePv" column="lp_exposure_pv"/>
            <result property="lpExposureUv" column="lp_exposure_uv"/>
            <result property="lpClickPv" column="lp_click_pv"/>
            <result property="lpClickUv" column="lp_click_uv"/>
            <result property="register" column="register"/>
            <result property="pay" column="pay"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            cur_date,
            cur_hour,
            advert_id,
            charge_type,
            ad_launch_pv,
            ad_launch_uv,
            exposure_pv,
            exposure_uv,
            click_pv,
            click_uv,
            billing_click_pv,
            billing_click_uv,
            consume,
            lp_exposure_pv,
            lp_exposure_uv,
            lp_click_pv,
            lp_click_uv,
            register,
            pay,
            gmt_create,
            gmt_modified
    </sql>

    <select id="selectBy" parameterType="com.ruoyi.system.entity.datashow.AdvertChargeHourDataEntity" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        from tb_advert_charge_hour_data
        <where>
            <if test="curDate != null "> and cur_date = #{curDate}</if>
            <if test="curHour != null "> and cur_hour = #{curHour}</if>
            <if test="advertId != null"> and advert_id = #{advertId}</if>
            <if test="chargeType != null"> and charge_type = #{chargeType}</if>
        </where>
        limit 1
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.datashow.AdvertChargeHourDataEntity">
        INSERT IGNORE INTO tb_advert_charge_hour_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="curDate != null">cur_date,</if>
                <if test="curHour != null">cur_hour,</if>
                <if test="advertId != null">advert_id,</if>
                <if test="chargeType != null">charge_type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="curDate != null">#{curDate},</if>
                <if test="curHour != null">#{curHour},</if>
                <if test="advertId != null">#{advertId},</if>
                <if test="chargeType != null">#{chargeType},</if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.ruoyi.system.entity.datashow.AdvertChargeHourDataEntity">
        UPDATE tb_advert_charge_hour_data
        <set>
            <if test="adLaunchPvAdd != null">ad_launch_pv = ad_launch_pv + #{adLaunchPvAdd},</if>
            <if test="adLaunchUvAdd != null">ad_launch_uv = ad_launch_uv + #{adLaunchUvAdd},</if>
            <if test="exposurePvAdd != null">exposure_pv = exposure_pv + #{exposurePvAdd},</if>
            <if test="exposureUvAdd != null">exposure_uv = exposure_uv + #{exposureUvAdd},</if>
            <if test="clickPvAdd != null">click_pv = click_pv + #{clickPvAdd},</if>
            <if test="clickUvAdd != null">click_uv = click_uv + #{clickUvAdd},</if>
            <if test="billingClickPvAdd != null">billing_click_pv = billing_click_pv + #{billingClickPvAdd},</if>
            <if test="billingClickUvAdd != null">billing_click_uv = billing_click_uv + #{billingClickUvAdd},</if>
            <if test="consumeAdd != null">consume = consume + #{consumeAdd},</if>
            <if test="lpExposurePvAdd != null">lp_exposure_pv = lp_exposure_pv + #{lpExposurePvAdd},</if>
            <if test="lpExposureUvAdd != null">lp_exposure_uv = lp_exposure_uv + #{lpExposureUvAdd},</if>
            <if test="lpClickPvAdd != null">lp_click_pv = lp_click_pv + #{lpClickPvAdd},</if>
            <if test="lpClickUvAdd != null">lp_click_uv = lp_click_uv + #{lpClickUvAdd},</if>
            <if test="registerAdd != null">register = register + #{registerAdd},</if>
            <if test="payAdd != null">pay = pay + #{payAdd},</if>
            gmt_modified = now()
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_advert_charge_hour_data
        WHERE id = #{id}
    </select>

</mapper>
