<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.datashow.OrientHourConsumeDataMapper">

    <resultMap type="com.ruoyi.system.entity.datashow.OrientHourConsumeData" id="OrientHourConsumeDataResult">
        <result property="id"    column="id"    />
        <result property="curDate"    column="cur_date"    />
        <result property="curHour"    column="cur_hour"    />
        <result property="advertId"    column="advert_id"    />
        <result property="orientId"    column="orient_id"    />
        <result property="consume"    column="consume"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectOrientHourConsumeDataVo">
        select id, cur_date, cur_hour, advert_id, orient_id, consume, gmt_create, gmt_modified
        from tb_orient_hour_consume_data
    </sql>

    <select id="selectByDateHourAndOrientId" resultMap="OrientHourConsumeDataResult">
        <include refid="selectOrientHourConsumeDataVo"/>
        where cur_date = #{curDate} and cur_hour = #{curHour} and orient_id = #{orientId}
    </select>

    <update id="addConsumeData" parameterType="com.ruoyi.system.entity.datashow.OrientHourConsumeData">
        update tb_orient_hour_consume_data
        <trim prefix="SET" suffixOverrides=",">
            consume = consume + #{consumeAdd},
            gmt_modified = now()
        </trim>
        <where>
            id = #{id}
        </where>
    </update>

    <select id="selectOrientHourConsumeDataList" parameterType="com.ruoyi.system.entity.datashow.OrientHourConsumeData" resultMap="OrientHourConsumeDataResult">
        <include refid="selectOrientHourConsumeDataVo"/>
        <where>
            <if test="curDate != null "> and cur_date = #{curDate}</if>
            <if test="curHour != null "> and cur_hour = #{curHour}</if>
            <if test="advertId != null"> and advert_id = #{advertId}</if>
            <if test="orientId != null"> and orient_id = #{orientId}</if>
            <if test="consume != null"> and consume = #{consume}</if>
        </where>
    </select>

    <select id="selectOrientHourConsumeDataById" parameterType="Long" resultMap="OrientHourConsumeDataResult">
        <include refid="selectOrientHourConsumeDataVo"/>
        where id = #{id}
    </select>

    <insert id="insertOrientHourConsumeData" parameterType="com.ruoyi.system.entity.datashow.OrientHourConsumeData" useGeneratedKeys="true" keyProperty="id">
        insert ignore into tb_orient_hour_consume_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="curDate != null">cur_date,</if>
            <if test="curHour != null">cur_hour,</if>
            <if test="advertId != null">advert_id,</if>
            <if test="orientId != null">orient_id,</if>
            <if test="consume != null">consume,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="curDate != null">#{curDate},</if>
            <if test="curHour != null">#{curHour},</if>
            <if test="advertId != null">#{advertId},</if>
            <if test="orientId != null">#{orientId},</if>
            <if test="consume != null">#{consume},</if>
         </trim>
    </insert>

    <update id="updateOrientHourConsumeData" parameterType="com.ruoyi.system.entity.datashow.OrientHourConsumeData">
        update tb_orient_hour_consume_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="curDate != null">cur_date = #{curDate},</if>
            <if test="curHour != null">cur_hour = #{curHour},</if>
            <if test="advertId != null">advert_id = #{advertId},</if>
            <if test="orientId != null">orient_id = #{orientId},</if>
            <if test="consume != null">consume = #{consume},</if>
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>
</mapper>
