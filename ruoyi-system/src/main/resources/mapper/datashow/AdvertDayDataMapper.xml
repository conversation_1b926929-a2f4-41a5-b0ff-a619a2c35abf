<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.datashow.AdvertDayDataMapper">

    <resultMap type="com.ruoyi.system.entity.datashow.AdvertDayData" id="AdvertDayDataResult">
        <result property="id"    column="id"    />
        <result property="curDate"    column="cur_date"    />
        <result property="advertId"    column="advert_id"    />
        <result property="adLaunchPv"    column="ad_launch_pv"    />
        <result property="adLaunchUv"    column="ad_launch_uv"    />
        <result property="exposurePv"    column="exposure_pv"    />
        <result property="exposureUv"    column="exposure_uv"    />
        <result property="clickPv"    column="click_pv"    />
        <result property="clickUv"    column="click_uv"    />
        <result property="billingClickPv"    column="billing_click_pv"    />
        <result property="billingClickUv"    column="billing_click_uv"    />
        <result property="lpExposurePv"    column="lp_exposure_pv"    />
        <result property="lpExposureUv"    column="lp_exposure_uv"    />
        <result property="lpClickPv"    column="lp_click_pv"    />
        <result property="lpClickUv"    column="lp_click_uv"    />
        <result property="takePv"    column="take_pv"    />
        <result property="takeUv"    column="take_uv"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <resultMap type="com.ruoyi.system.bo.advert.AdvertDayDataBo" id="AdvertDayDataBoResult">
        <result property="id" column="id"/>
        <result property="curDate" column="cur_date"/>
        <result property="advertId" column="advert_id"/>
        <result property="adLaunchPv" column="ad_launch_pv"/>
        <result property="adLaunchUv" column="ad_launch_uv"/>
        <result property="exposurePv" column="exposure_pv"/>
        <result property="exposureUv" column="exposure_uv"/>
        <result property="clickPv" column="click_pv"/>
        <result property="clickUv" column="click_uv"/>
        <result property="billingClickPv" column="billing_click_pv"/>
        <result property="billingClickUv" column="billing_click_uv"/>
        <result property="lpExposurePv" column="lp_exposure_pv"/>
        <result property="lpExposureUv" column="lp_exposure_uv"/>
        <result property="lpClickPv" column="lp_click_pv"/>
        <result property="lpClickUv" column="lp_click_uv"/>
        <result property="takePv" column="take_pv"/>
        <result property="takeUv" column="take_uv"/>
        <result property="consume" column="consume"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="selectAdvertDayDataVo">
        select id, cur_date, advert_id, ad_launch_pv, ad_launch_uv, exposure_pv, exposure_uv,
               click_pv, click_uv, billing_click_pv, billing_click_uv, lp_exposure_pv, lp_exposure_uv,
               lp_click_pv, lp_click_uv, take_pv, take_uv, gmt_create, gmt_modified from tb_advert_day_data
    </sql>

    <select id="selectAdvertDayDataList" parameterType="com.ruoyi.system.entity.datashow.AdvertDayData" resultMap="AdvertDayDataResult">
        <include refid="selectAdvertDayDataVo"/>
        <where>
            <if test="curDate != null "> and cur_date = #{curDate}</if>
            <if test="advertId != null"> and advert_id = #{advertId}</if>
            <if test="startDate != null "> and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null "> and cur_date &lt;= #{endDate}</if>
            <if test="advertIds != null and advertIds.size() > 0">
                and advert_id in
                <foreach collection="advertIds" item="advertId" open="(" separator="," close=")">
                    #{advertId}
                </foreach>
            </if>
        </where>
        order by cur_date desc, ad_launch_pv desc
    </select>

    <select id="selectAdvertDayDataBoList" parameterType="com.ruoyi.system.entity.datashow.AdvertDayData"
            resultMap="AdvertDayDataBoResult">
        select d.id as id, d.cur_date , d.advert_id, d.ad_launch_pv, d.ad_launch_uv, d.exposure_pv, d.exposure_uv,
            d.click_pv, d.click_uv, d.billing_click_pv, d.billing_click_uv, d.lp_exposure_pv, d.lp_exposure_uv,
            d.lp_click_pv, d.lp_click_uv, d.take_pv, d.take_uv, d.gmt_create, d.gmt_modified, c.consume
        from tb_advert_day_data d
        left join tb_advert_day_consume_data c on d.cur_date = c.cur_date and d.advert_id = c.advert_id
        <where>
            <if test="curDate != null ">and d.cur_date = #{curDate}</if>
            <if test="advertId != null">and d.advert_id = #{advertId}</if>
            <if test="startDate != null ">and d.cur_date &gt;= #{startDate}</if>
            <if test="endDate != null ">and d.cur_date &lt;= #{endDate}</if>
            <if test="advertIds != null and advertIds.size() > 0">
                and d.advert_id in
                <foreach collection="advertIds" item="advertId" open="(" separator="," close=")">
                    #{advertId}
                </foreach>
            </if>
        </where>
        order by
        <choose>
            <when test="orderColumn != null and orderColumn == 'adLaunchPv'">
                ad_launch_pv ${orderType}
            </when>
            <when test="orderColumn != null and orderColumn == 'adLaunchUv'">
                ad_launch_uv ${orderType}
            </when>
            <when test="orderColumn != null and orderColumn == 'lpClickUv'">
                lp_click_uv ${orderType}
            </when>
            <when test="orderColumn != null and orderColumn == 'consume'">
                consume ${orderType}
            </when>
            <otherwise>
                cur_date desc, ad_launch_pv desc
            </otherwise>
        </choose>
    </select>

    <select id="selectStatisticAdvertDayData" parameterType="com.ruoyi.system.entity.datashow.AdvertDayData"
            resultType="com.ruoyi.system.entity.datashow.AdvertDayStatisticData">
        select sum(ad_launch_pv) as adLaunchPv, sum(ad_launch_uv) as adLaunchUv, sum(exposure_pv) as exposurePv, sum(exposure_uv) as exposureUv,
            sum(click_pv) as clickPv, sum(click_uv) as clickUv, sum(billing_click_pv) as billingClickPv, sum(billing_click_uv) as billingClickUv,
            sum(lp_exposure_pv) as lpExposurePv, sum(lp_exposure_uv) as lpExposureUv, sum(take_pv) as takePv, sum(take_uv) as takeUv,
            sum(lp_click_pv) as lpClickPv, sum(lp_click_uv) as lpClickUv, sum(consume) as consume
        from tb_advert_day_data d
        LEFT JOIN tb_advert_day_consume_data c on d.cur_date = c.cur_date and d.advert_id = c.advert_id
        <where>
            <if test="curDate != null "> and d.cur_date = #{curDate}</if>
            <if test="advertId != null"> and d.advert_id = #{advertId}</if>
            <if test="startDate != null "> and d.cur_date &gt;= #{startDate}</if>
            <if test="endDate != null "> and d.cur_date &lt;= #{endDate}</if>
            <if test="advertIds != null and advertIds.size() > 0">
                and d.advert_id in
                <foreach collection="advertIds" item="advertId" open="(" separator="," close=")">
                    #{advertId}
                </foreach>
            </if>
        </where>
        order by d.cur_date desc, adLaunchPv desc
    </select>

    <select id="selectAdvertCpcDayDataList" parameterType="com.ruoyi.system.req.advertiser.AdvertiserCpcDataReq" resultMap="AdvertDayDataResult">
        <include refid="selectAdvertDayDataVo"/>
        <where>
            <if test="startDate != null">cur_date &gt;= #{startDate}</if>
            <if test="endDate != null">and cur_date &lt;= #{endDate}</if>
            <if test="advertIds != null and advertIds.size > 0">
                and advert_id in
                <foreach collection="advertIds" separator="," item="advertId" close=")" open="(">
                    #{advertId}
                </foreach>
            </if>
            <if test="invisibleDateList != null and invisibleDateList.size() > 0">
                and cur_date not in
                <foreach collection="invisibleDateList" separator="," item="date" close=")" open="(">
                    #{date}
                </foreach>
            </if>
        </where>
        order by cur_date desc, ad_launch_pv desc
    </select>

    <select id="selectBy" parameterType="com.ruoyi.system.entity.datashow.AdvertDayData" resultMap="AdvertDayDataResult">
        <include refid="selectAdvertDayDataVo"/>
        <where>
            <if test="curDate != null "> and cur_date = #{curDate}</if>
            <if test="advertId != null"> and advert_id = #{advertId}</if>
        </where>
        limit 1
    </select>

    <insert id="insertAdvertDayData" parameterType="com.ruoyi.system.entity.datashow.AdvertDayData"
            useGeneratedKeys="true" keyProperty="id">
        insert ignore into tb_advert_day_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="curDate != null">cur_date,</if>
            <if test="advertId != null">advert_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="curDate != null">#{curDate},</if>
            <if test="advertId != null">#{advertId},</if>
        </trim>
    </insert>

    <update id="updateAdvertDayData" parameterType="com.ruoyi.system.entity.datashow.AdvertDayData">
        update tb_advert_day_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="adLaunchPvAdd != null">ad_launch_pv = ad_launch_pv + #{adLaunchPvAdd},</if>
            <if test="adLaunchPvAdd != null">ad_launch_uv = ad_launch_uv + #{adLaunchUvAdd},</if>
            <if test="exposurePvAdd != null">exposure_pv = exposure_pv + #{exposurePvAdd},</if>
            <if test="exposureUvAdd != null">exposure_uv = exposure_uv + #{exposureUvAdd},</if>
            <if test="clickPvAdd != null">click_pv = click_pv + #{clickPvAdd},</if>
            <if test="clickUvAdd != null">click_uv = click_uv + #{clickUvAdd},</if>
            <if test="billingClickPvAdd != null">billing_click_pv = billing_click_pv + #{billingClickPvAdd},</if>
            <if test="billingClickUvAdd != null">billing_click_uv = billing_click_uv + #{billingClickUvAdd},</if>
            <if test="lpExposurePvAdd != null">lp_exposure_pv = lp_exposure_pv + #{lpExposurePvAdd},</if>
            <if test="lpExposureUvAdd != null">lp_exposure_uv = lp_exposure_uv + #{lpExposureUvAdd},</if>
            <if test="lpClickPvAdd != null">lp_click_pv = lp_click_pv + #{lpClickPvAdd},</if>
            <if test="lpClickUvAdd != null">lp_click_uv = lp_click_uv + #{lpClickUvAdd},</if>
            <if test="takePvAdd != null">take_pv = take_pv + #{takePvAdd},</if>
            <if test="takeUvAdd != null">take_uv = take_uv + #{takeUvAdd},</if>
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>

    <select id="selectAdvertDaySumData" parameterType="com.ruoyi.system.req.advertiser.AdvertiserCpcDataReq" resultType="com.ruoyi.system.bo.advert.AdvertDaySumDataBo">
        select cur_date as curDate, sum(billing_click_pv) as billingClickPv, sum(billing_click_uv) as billingClickUv,
            sum(lp_click_pv) as lpClickPv, sum(lp_click_uv) as lpClickUv
        from tb_advert_day_data
        <where>
            <if test="startDate != null">cur_date &gt;= #{startDate}</if>
            <if test="endDate != null">and cur_date &lt;= #{endDate}</if>
            <if test="advertIds != null and advertIds.size > 0">
                and advert_id in
                <foreach collection="advertIds" separator="," item="advertId" close=")" open="(">
                    #{advertId}
                </foreach>
            </if>
            <if test="invisibleDateList != null and invisibleDateList.size() > 0">
                and cur_date not in
                <foreach collection="invisibleDateList" separator="," item="date" close=")" open="(">
                    #{date}
                </foreach>
            </if>
        </where>
        group by cur_date
        order by cur_date desc
    </select>

    <select id="selectAdvertiserDaySumData" parameterType="com.ruoyi.system.req.advertiser.AdvertiserDaySumDataReq" resultType="com.ruoyi.system.bo.advertiser.finance.AdvertiserDaySumDataBo">
        select
            d.`cur_date` as curDate, a.`advertiser_id` as advertiserId,
            sum(d.`billing_click_pv`) as billingClickPv, sum(d.`billing_click_uv`) as billingClickUv,
            sum(d.`lp_click_pv`) as lpClickPv
        from  tb_advert_day_data d
        left join `tb_advert` a on d.`advert_id` = a.`id`
        <where>
            <if test="startDate != null">cur_date &gt;= #{startDate}</if>
            <if test="endDate != null">and cur_date &lt;= #{endDate}</if>
            <if test="advertiserIds != null and advertiserIds.size > 0">
                and a.`advertiser_id`  in
                <foreach collection="advertiserIds" separator="," item="advertiserId" close=")" open="(">
                    #{advertiserId}
                </foreach>
            </if>
        </where>
        GROUP BY d.`cur_date`, a.`advertiser_id`
    </select>
</mapper>
