<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.datashow.SlotDataMapper">

    <resultMap type="com.ruoyi.system.entity.datashow.SlotData" id="SlotDataResult">
        <result property="id"    column="id"    />
        <result property="curDate"    column="cur_date"    />
        <result property="accountId"    column="account_id"    />
        <result property="appId"    column="app_id"    />
        <result property="slotId"    column="slot_id"    />
        <result property="slotRequestPv"    column="slot_request_pv"    />
        <result property="slotRequestUv"    column="slot_request_uv"    />
        <result property="appSlotClickPv"    column="app_slot_click_pv"    />
        <result property="appSlotClickUv"    column="app_slot_click_uv"    />
        <result property="appSlotExposurePv"    column="app_slot_exposure_pv"    />
        <result property="appSlotExposureUv"    column="app_slot_exposure_uv"    />
        <result property="nhConsume"    column="nh_consume"    />
        <result property="outerConsume"    column="outer_consume"    />
        <result property="totalConsume"    column="total_consume"    />
        <result property="appRevenue"    column="app_revenue"    />
        <result property="nhCost"    column="nh_cost"    />
        <result property="outerCost"    column="outer_cost"    />
        <result property="isVisible"    column="is_visible"    />
        <result property="isEditable"    column="is_editable"    />
        <result property="operateTime"    column="operate_time"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <resultMap type="com.ruoyi.system.bo.slot.CrmSlotDataBo" id="CrmSlotDataBoResult">
        <result property="id"    column="id"    />
        <result property="curDate"    column="cur_date"    />
        <result property="accountId"    column="account_id"    />
        <result property="appId"    column="app_id"    />
        <result property="appName"    column="app_name"    />
        <result property="slotId"    column="slot_id"    />
        <result property="slotName"    column="slot_name"    />
        <result property="slotRequestPv"    column="slot_request_pv"    />
        <result property="slotRequestUv"    column="slot_request_uv"    />
        <result property="appSlotClickPv"    column="app_slot_click_pv"    />
        <result property="appSlotClickUv"    column="app_slot_click_uv"    />
        <result property="appSlotExposurePv"    column="app_slot_exposure_pv"    />
        <result property="appSlotExposureUv"    column="app_slot_exposure_uv"    />
        <result property="nhConsume"    column="nh_consume"    />
        <result property="outerConsume"    column="outer_consume"    />
        <result property="totalConsume"    column="total_consume"    />
        <result property="appRevenue"    column="app_revenue"    />
        <result property="totalCost"    column="total_cost"    />
        <result property="nhCost"    column="nh_cost"    />
        <result property="outerCost"    column="outer_cost"    />
        <result property="isVisible"    column="is_visible"    />
        <result property="isEditable"    column="is_editable"    />
        <result property="operateTime"    column="operate_time"    />
    </resultMap>

    <sql id="selectSlotDataVo">
        select id, cur_date, account_id, app_id, slot_id, slot_request_pv,app_slot_click_pv,app_slot_click_uv, app_slot_exposure_pv,app_slot_exposure_uv,slot_request_uv,nh_consume,
               outer_consume,total_consume,app_revenue, nh_cost, outer_cost,
               is_visible, is_editable, operate_time, gmt_create, gmt_modified from tb_slot_data
    </sql>

    <select id="selectSlotDataList" parameterType="com.ruoyi.system.entity.datashow.SlotData" resultMap="SlotDataResult">
        <include refid="selectSlotDataVo"/>
        <where>
            <if test="curDate != null "> and cur_date = #{curDate}</if>
            <if test="accountId != null  and accountId != ''"> and account_id = #{accountId}</if>
            <if test="appIds != null and appIds.size()>0">
                and app_id in
                <foreach collection="appIds" item="appId" open="(" separator="," close=")">
                    #{appId}
                </foreach>
            </if>
            <if test="appId != null">and app_id = #{appId}</if>
            <if test="slotIds != null and slotIds.size()>0">
                and slot_id in
                <foreach collection="slotIds" item="slotId" open="(" separator="," close=")">
                    #{slotId}
                </foreach>
            </if>
            <if test="accountIds != null and accountIds.size()>0">
                and account_id in
                <foreach collection="accountIds" item="accountId" open="(" separator="," close=")">
                    #{accountId}
                </foreach>
            </if>
            <if test="startDate != null "> and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null "> and cur_date &lt;= #{endDate}</if>
            <if test="isVisible != null "> and is_visible = #{isVisible}</if>
            <if test="isEditable != null "> and is_editable = #{isEditable}</if>
            <if test="isEdited != null and isEdited == 0"> and operate_time is null</if>
            <if test="isEdited != null and isEdited == 1"> and operate_time is not null</if>
            <if test="slotId != null"> and slot_id = #{slotId}</if>
        </where>
        order by cur_date desc, slot_request_pv desc ,id desc
    </select>

    <select id="selectCrmSlotDataList" parameterType="com.ruoyi.system.entity.datashow.SlotData"
            resultMap="CrmSlotDataBoResult">
        select d.id, d.cur_date, d.account_id, d.app_id, d.slot_id, d.slot_request_pv, d.app_slot_click_pv, d.app_slot_click_uv,
            d.app_slot_exposure_pv, d.app_slot_exposure_uv, d.slot_request_uv, d.nh_consume, d.outer_consume, d.total_consume,
            d.app_revenue, d.outer_cost, d.is_visible, d.is_editable, d.operate_time,
            a.app_name, s.slot_name, ad.ad_launch, (d.nh_cost + ifnull(cpcd.consume, 0) +  ifnull(lf.consume, 0)) as nh_cost,
            (d.nh_cost + d.outer_cost + ifnull(cpcd.consume, 0) + ifnull(lf.consume, 0)) as total_cost
        from tb_slot_data d
        left join tb_app a on d.app_id = a.id
        left join tb_slot s on d.slot_id = s.id
        left join (
            select cur_date, slot_id, sum(ad_launch) as ad_launch
            from tb_slot_activity_data
            <where>
                <if test="startDate != null ">and cur_date &gt;= #{startDate}</if>
                <if test="endDate != null ">and cur_date &lt;= #{endDate}</if>
                <if test="slotIds != null and slotIds.size()>0">
                    and slot_id in
                    <foreach collection="slotIds" item="slotId" open="(" separator="," close=")">
                        #{slotId}
                    </foreach>
                </if>
            </where>
            group by cur_date, slot_id
        ) ad on ad.cur_date = d.cur_date and ad.slot_id = d.slot_id
        left join (
            select sd.slot_id as slot_id, sd.cur_date, sum(sd.consume) as consume
            from tb_advert_slot_day_data  sd
            LEFT JOIN tb_advert_cost ac on sd.`cur_date` = ac.`cur_date` and sd.`advert_id` = ac.`advert_id`
            <where>
                sd.`cur_date` = curdate()
                and ac.`billing_type` = 1
                <if test="slotIds != null and slotIds.size()>0">
                    and sd.slot_id in
                    <foreach collection="slotIds" item="slotId" open="(" separator="," close=")">
                        #{slotId}
                    </foreach>
                </if>
            </where>
            group by sd.slot_id, sd.cur_date
        ) cpcd on cpcd.slot_id = d.slot_id and cpcd.cur_date = d.cur_date
        left join (
            select slot_id, cur_date, form_consume as consume
            from tb_slot_landpage_form_data
            <where>
                `cur_date` = curdate()
                and form_consume > 0
                <if test="slotIds != null and slotIds.size()>0">
                    and slot_id in
                    <foreach collection="slotIds" item="slotId" open="(" separator="," close=")">
                        #{slotId}
                    </foreach>
                </if>
            </where>
        ) lf on lf.slot_id = d.slot_id and lf.cur_date = d.cur_date
        <where>
            <if test="accountId != null  and accountId != ''">and d.account_id = #{accountId}</if>
            <if test="appIds != null and appIds.size()>0">
                and d.app_id in
                <foreach collection="appIds" item="appId" open="(" separator="," close=")">
                    #{appId}
                </foreach>
            </if>
            <if test="appId != null">and d.app_id = #{appId}</if>
            <if test="slotIds != null and slotIds.size()>0">
                and d.slot_id in
                <foreach collection="slotIds" item="slotId" open="(" separator="," close=")">
                    #{slotId}
                </foreach>
            </if>
            <if test="accountIds != null and accountIds.size()>0">
                and d.account_id in
                <foreach collection="accountIds" item="accountId" open="(" separator="," close=")">
                    #{accountId}
                </foreach>
            </if>
            <if test="startDate != null ">and d.cur_date &gt;= #{startDate}</if>
            <if test="endDate != null ">and d.cur_date &lt;= #{endDate}</if>
            <if test="isVisible != null ">and d.is_visible = #{isVisible}</if>
            <if test="isEditable != null ">and d.is_editable = #{isEditable}</if>
            <if test="isEdited != null and isEdited == 0">and d.operate_time is null</if>
            <if test="isEdited != null and isEdited == 1">and d.operate_time is not null</if>
            <if test="slotId != null">and d.slot_id = #{slotId}</if>
        </where>
        order by
        <choose>
            <when test="orderColumn!=null and orderColumn=='appRevenue'">
                d.app_revenue ${orderType}
            </when>
            <when test="orderColumn!=null and orderColumn=='slotRequestPv'">
                d.slot_request_pv ${orderType}
            </when>
            <when test="orderColumn!=null and orderColumn=='totalCost'">
                total_cost ${orderType}
            </when>
            <when test="orderColumn!=null and orderColumn=='adLaunch'">
                ad.ad_launch ${orderType}
            </when>
            <otherwise>
                d.cur_date desc, d.slot_request_pv desc ,d.id desc
            </otherwise>
        </choose>
    </select>

    <select id="selectStatisticSlotData" parameterType="com.ruoyi.system.entity.datashow.SlotData" resultMap="SlotDataResult">
        select sum(slot_request_pv) as slot_request_pv, sum(slot_request_uv) as slot_request_uv,
        sum(app_slot_click_pv) as app_slot_click_pv,sum(app_slot_click_uv) as app_slot_click_uv,sum(app_slot_exposure_pv) as app_slot_exposure_pv,sum(app_slot_exposure_uv) as app_slot_exposure_uv,
            sum(nh_consume) as nh_consume, sum(outer_consume) as outer_consume, sum(total_consume) as total_consume,
            sum(app_revenue) as app_revenue, sum(nh_cost) as nh_cost, sum(outer_cost) as outer_cost
        from tb_slot_data
        <where>
            <if test="curDate != null "> and cur_date = #{curDate}</if>
            <if test="accountId != null  and accountId != ''"> and account_id = #{accountId}</if>
            <if test="appIds != null and appIds.size()>0">
                and app_id in
                <foreach collection="appIds" item="appId" open="(" separator="," close=")">
                    #{appId}
                </foreach>
            </if>
            <if test="appId != null">and app_id = #{appId}</if>
            <if test="slotIds != null and slotIds.size()>0">
                and slot_id in
                <foreach collection="slotIds" item="slotId" open="(" separator="," close=")">
                    #{slotId}
                </foreach>
            </if>
            <if test="accountIds != null and accountIds.size()>0">
                and account_id in
                <foreach collection="accountIds" item="accountId" open="(" separator="," close=")">
                    #{accountId}
                </foreach>
            </if>
            <if test="startDate != null "> and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null "> and cur_date &lt;= #{endDate}</if>
            <if test="isVisible != null "> and is_visible = #{isVisible}</if>
            <if test="isEditable != null "> and is_editable = #{isEditable}</if>
            <if test="isEdited != null and isEdited == 0"> and operate_time is null</if>
            <if test="isEdited != null and isEdited == 1"> and operate_time is not null</if>
            <if test="slotId != null"> and slot_id = #{slotId}</if>
        </where>
    </select>

    <select id="selectSlotIds" parameterType="com.ruoyi.system.entity.datashow.SlotData" resultType="Long">
        select distinct slot_id from tb_slot_data
        <where>
            <if test="curDate != null "> and cur_date = #{curDate}</if>
            <if test="accountId != null  and accountId != ''"> and account_id = #{accountId}</if>
            <if test="appIds != null and appIds.size()>0">
                and app_id in
                <foreach collection="appIds" item="appId" open="(" separator="," close=")">
                    #{appId}
                </foreach>
            </if>
            <if test="appId != null">and app_id = #{appId}</if>
            <if test="slotIds != null and slotIds.size()>0">
                and slot_id in
                <foreach collection="slotIds" item="slotId" open="(" separator="," close=")">
                    #{slotId}
                </foreach>
            </if>
            <if test="accountIds != null and accountIds.size()>0">
                and account_id in
                <foreach collection="accountIds" item="accountId" open="(" separator="," close=")">
                    #{accountId}
                </foreach>
            </if>
            <if test="startDate != null "> and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null "> and cur_date &lt;= #{endDate}</if>
            <if test="isVisible != null "> and is_visible = #{isVisible}</if>
            <if test="isEditable != null "> and is_editable = #{isEditable}</if>
            <if test="isEdited != null and isEdited == 0"> and operate_time is null</if>
            <if test="isEdited != null and isEdited == 1"> and operate_time is not null</if>
            <if test="slotId != null"> and slot_id = #{slotId}</if>
        </where>
    </select>

    <select id="selectSlotDataById" parameterType="Long" resultMap="SlotDataResult">
        <include refid="selectSlotDataVo"/>
        where id = #{id}
    </select>

    <select id="selectBySlotIdAndDate" resultMap="SlotDataResult">
        <include refid="selectSlotDataVo"/>
        where slot_id = #{slotId} and cur_date = #{curDate}
    </select>

    <insert id="insertSlotData" parameterType="com.ruoyi.system.entity.datashow.SlotData" useGeneratedKeys="true"
            keyProperty="id">
        insert ignore into tb_slot_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="curDate != null">cur_date,</if>
            <if test="accountId != null and accountId != ''">account_id,</if>
            <if test="appId != null and appId != ''">app_id,</if>
            <if test="slotId != null and slotId != ''">slot_id,</if>
            <if test="slotRequestPv != null">slot_request_pv,</if>
            <if test="slotRequestUv != null">slot_request_uv,</if>
            <if test="appSlotClickPv != null">app_slot_click_pv,</if>
            <if test="appSlotClickUv != null">app_slot_click_uv,</if>
            <if test="appSlotExposurePv != null">app_slot_exposure_pv,</if>
            <if test="appSlotExposureUv != null">app_slot_exposure_uv,</if>
            <if test="nhConsume != null">nh_consume,</if>
            <if test="totalConsume != null">total_consume,</if>
            <if test="appRevenue != null">app_revenue,</if>
            <if test="outerConsume != null">outer_consume,</if>
            <if test="nhCost != null">nh_cost,</if>
            <if test="isVisible != null">is_visible,</if>
            <if test="isEditable != null">is_editable,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="curDate != null">#{curDate},</if>
            <if test="accountId != null and accountId != ''">#{accountId},</if>
            <if test="appId != null and appId != ''">#{appId},</if>
            <if test="slotId != null and slotId != ''">#{slotId},</if>
            <if test="slotRequestPv != null">#{slotRequestPv},</if>
            <if test="slotRequestUv != null">#{slotRequestUv},</if>
            <if test="appSlotClickPv != null">#{appSlotClickPv},</if>
            <if test="appSlotClickUv != null">#{appSlotClickUv},</if>
            <if test="appSlotExposurePv != null">#{appSlotExposurePv},</if>
            <if test="appSlotExposureUv != null">#{appSlotExposureUv},</if>
            <if test="nhConsume != null">#{nhConsume},</if>
            <if test="totalConsume != null">#{totalConsume},</if>
            <if test="appRevenue != null">#{appRevenue},</if>
            <if test="outerConsume != null">#{outerConsume},</if>
            <if test="nhCost != null">#{nhCost},</if>
            <if test="isVisible != null">#{isVisible},</if>
            <if test="isEditable != null">#{isEditable},</if>
        </trim>
    </insert>

    <update id="updateSlotData" parameterType="com.ruoyi.system.entity.datashow.SlotData">
        update tb_slot_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="curDate != null">cur_date = #{curDate},</if>
            <if test="accountId != null">account_id = #{accountId},</if>
            <if test="appId != null">app_id = #{appId},</if>
            <if test="slotId != null">slot_id = #{slotId},</if>
            <if test="slotRequestPv != null">slot_request_pv = #{slotRequestPv},</if>
            <if test="slotRequestUv != null">slot_request_uv = #{slotRequestUv},</if>
            <if test="appSlotClickPv != null">app_slot_click_pv = #{appSlotClickPv},</if>
            <if test="appSlotClickUv != null">app_slot_click_uv = #{appSlotClickUv},</if>
            <if test="appSlotExposurePv != null">app_slot_exposure_pv = #{appSlotExposurePv},</if>
            <if test="appSlotExposureUv != null">app_slot_exposure_uv = #{appSlotExposureUv},</if>
            <if test="slotRequestPvAdd != null">slot_request_pv = slot_request_pv + #{slotRequestPvAdd},</if>
            <if test="slotRequestUvAdd != null">slot_request_uv = slot_request_uv + #{slotRequestUvAdd},</if>
            <if test="nhConsumeAdd != null">nh_consume = nh_consume + #{nhConsumeAdd},</if>
            <if test="totalConsumeAdd != null">total_consume = total_consume + #{totalConsumeAdd},</if>
            <if test="totalConsume != null">total_consume = #{totalConsume},</if>
            <if test="appRevenue != null">app_revenue = #{appRevenue},</if>
            <if test="outerConsume != null">outer_consume = #{outerConsume},</if>
            <if test="nhCost != null">nh_cost = #{nhCost},</if>
            <if test="outerCost != null">outer_cost = #{outerCost},</if>
            <if test="appRevenueAdd != null">app_revenue = app_revenue + #{appRevenueAdd},</if>
            <if test="nhCostAdd != null">nh_cost = nh_cost + #{nhCostAdd},</if>
            <if test="outerCostAdd != null">outer_cost = outer_cost + #{outerCostAdd},</if>
            <if test="isVisible != null">is_visible = #{isVisible},</if>
            <if test="isEditable != null">is_editable = #{isEditable},</if>
            <if test="operateTime != null">operate_time = #{operateTime},</if>
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>


    <update id="updateNhCost" parameterType="com.ruoyi.system.entity.datashow.SlotData">
        update tb_slot_data
        set nh_cost = #{nhCost}
        where slot_id = #{slotId} and cur_date = #{curDate}
    </update>

    <select id="selectDistinctSlotIdByDate" resultType="java.lang.Long">
        select distinct slot_id
        from tb_slot_data
        where cur_date &gt;= #{startDate} and cur_date &lt; #{endDate}
    </select>

    <select id="sumAppRevenueByAccountId" resultMap="SlotDataResult">
        select account_id, sum(app_revenue) as app_revenue
        from tb_slot_data
        where cur_date &lt; curdate() and account_id in
        <foreach collection="accountIds" open="(" separator="," close=")" item="accountId">
            #{accountId}
        </foreach>
        group by account_id
    </select>

    <select id="selectBySlotIdsAndDates" resultMap="SlotDataResult">
        <include refid="selectSlotDataVo"/>
        where slot_id in
        <foreach collection="slotIds" open="(" separator="," close=")" item="slotId">
            #{slotId}
        </foreach>
        and cur_date in
        <foreach collection="dates" open="(" separator="," close=")" item="date">
            #{date}
        </foreach>
    </select>
    <select id="selectDataByAppIdAndDate" resultMap="SlotDataResult">
        <include refid="selectSlotDataVo"/>
        where app_id = #{appId}
        and cur_date =#{curDate}
    </select>

    <insert id="batchInsertOrUpdateAppSlotRequestData">
        insert into tb_slot_data(cur_date,account_id,slot_id,app_id,app_slot_click_pv,app_slot_click_uv,app_slot_exposure_pv,app_slot_exposure_uv)
        values
        <foreach collection="datas" item="entity" separator=",">
            ( #{entity.curDate},#{entity.accountId},#{entity.slotId},#{entity.appId},#{entity.appSlotClickPv},#{entity.appSlotClickUv},#{entity.appSlotExposurePv},#{entity.appSlotExposureUv})
        </foreach>
        ON DUPLICATE KEY UPDATE
        app_slot_click_pv = values(app_slot_click_pv) ,
        app_slot_click_uv = values(app_slot_click_uv) ,
        app_slot_exposure_pv = values(app_slot_exposure_pv) ,
        app_slot_exposure_uv = values(app_slot_exposure_uv)
    </insert>

    <select id="selectListByAppIdsAndDates" resultMap="SlotDataResult">
        <include refid="selectSlotDataVo"/>
        where app_id in
        <foreach collection="appIds" open="(" separator="," close=")" item="appId">
            #{appId}
        </foreach>
        and cur_date in
        <foreach collection="curDates" item="curDate" open="(" close=")" separator=",">
            #{curDate}
        </foreach>
        and is_visible = 1
    </select>

    <update id="batchUpdateSlotRequestPvAndUv">
        <foreach collection="slotDataList" item="data" separator=";">
            update tb_slot_data
            set slot_request_pv = #{data.slotRequestPv},
            slot_request_uv = #{data.slotRequestUv}
            where id = #{data.id}
        </foreach>
    </update>
    <update id="batchUpdateSlotAppRevenue">
        <foreach collection="slotDataList" item="data" separator=";">
            update tb_slot_data
            set app_revenue = #{data.appRevenue}
            where id = #{data.id}
        </foreach>
    </update>
    <update id="initAppRevenueByDate">
        update tb_slot_data
        set app_revenue = 0
        where cur_date = #{curDate}
    </update>
</mapper>
