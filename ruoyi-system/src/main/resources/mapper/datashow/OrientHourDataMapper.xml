<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.datashow.OrientHourDataMapper">

    <resultMap type="com.ruoyi.system.entity.datashow.OrientHourDataEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="curDate" column="cur_date"/>
            <result property="curHour" column="cur_hour"/>
            <result property="advertId" column="advert_id"/>
            <result property="orientId" column="orient_id"/>
            <result property="appId" column="app_id"/>
            <result property="slotId" column="slot_id"/>
            <result property="activityId" column="activity_id"/>
            <result property="adLaunchPv" column="ad_launch_pv"/>
            <result property="adLaunchUv" column="ad_launch_uv"/>
            <result property="exposurePv" column="exposure_pv"/>
            <result property="exposureUv" column="exposure_uv"/>
            <result property="clickPv" column="click_pv"/>
            <result property="clickUv" column="click_uv"/>
            <result property="billingClickPv" column="billing_click_pv"/>
            <result property="billingClickUv" column="billing_click_uv"/>
            <result property="consume" column="consume"/>
            <result property="lpExposurePv" column="lp_exposure_pv"/>
            <result property="lpExposureUv" column="lp_exposure_uv"/>
            <result property="lpClickPv" column="lp_click_pv"/>
            <result property="lpClickUv" column="lp_click_uv"/>
            <result property="takePv" column="take_pv"/>
            <result property="takeUv" column="take_uv"/>
            <result property="form" column="form"/>
            <result property="pay" column="pay"/>
            <result property="refund" column="refund"/>
            <result property="register" column="register"/>
            <result property="delivery" column="delivery"/>
            <result property="complain" column="complain"/>
            <result property="appDownload" column="app_download"/>
            <result property="appActive" column="app_active"/>
            <result property="appRegister" column="app_register"/>
            <result property="payPrice" column="pay_price"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            cur_date,
            cur_hour,
            advert_id,
            orient_id,
            app_id,
            slot_id,
            activity_id,
            ad_launch_pv,
            ad_launch_uv,
            exposure_pv,
            exposure_uv,
            click_pv,
            click_uv,
            billing_click_pv,
            billing_click_uv,
            consume,
            lp_exposure_pv,
            lp_exposure_uv,
            lp_click_pv,
            lp_click_uv,
            take_pv,
            take_uv,
            form,
            pay,
            refund,
            register,
            delivery,
            complain,
            app_download,
            app_active,
            app_register,
            pay_price,
            gmt_create,
            gmt_modified
    </sql>

    <select id="selectBy" parameterType="com.ruoyi.system.entity.datashow.OrientHourDataEntity" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_orient_hour_data
        <where>
            <if test="curDate != null "> and cur_date = #{curDate}</if>
            <if test="curHour != null "> and cur_hour = #{curHour}</if>
            <if test="orientId != null"> and orient_id = #{orientId}</if>
            <if test="slotId != null"> and slot_id = #{slotId}</if>
            <if test="activityId != null"> and activity_id = #{activityId}</if>
        </where>
        limit 1
    </select>

    <select id="isExistBy" parameterType="com.ruoyi.system.bo.advert.OrientHourDataParam" resultType="Integer">
        SELECT 1
        FROM tb_orient_hour_data
        <where>
            <if test="orientId != null"> and orient_id = #{orientId}</if>
            <if test="startDate != null "> and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null "> and cur_date &lt;= #{endDate}</if>
        </where>
        LIMIT 1
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.datashow.OrientHourDataEntity">
        INSERT IGNORE INTO tb_orient_hour_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="curDate != null">
                    cur_date,
                </if>
                <if test="curHour != null">
                    cur_hour,
                </if>
                <if test="advertId != null">
                    advert_id,
                </if>
                <if test="orientId != null">
                    orient_id,
                </if>
                <if test="appId != null">
                    app_id,
                </if>
                <if test="slotId != null">
                    slot_id,
                </if>
                <if test="activityId != null">
                    activity_id,
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="curDate != null">
                    #{curDate},
                </if>
                <if test="curHour != null">
                    #{curHour},
                </if>
                <if test="advertId != null">
                    #{advertId},
                </if>
                <if test="orientId != null">
                    #{orientId},
                </if>
                <if test="appId != null">
                    #{appId},
                </if>
                <if test="slotId != null">
                    #{slotId},
                </if>
                <if test="activityId != null">
                    #{activityId},
                </if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.ruoyi.system.bo.advert.OrientHourDataParam">
        UPDATE tb_orient_hour_data
        <set>
            <if test="adLaunchPvAdd != null">
                ad_launch_pv = ad_launch_pv + #{adLaunchPvAdd},
            </if>
            <if test="adLaunchUvAdd != null">
                ad_launch_uv = ad_launch_uv + #{adLaunchUvAdd},
            </if>
            <if test="exposurePvAdd != null">
                exposure_pv = exposure_pv + #{exposurePvAdd},
            </if>
            <if test="exposureUvAdd != null">
                exposure_uv = exposure_uv + #{exposureUvAdd},
            </if>
            <if test="clickPvAdd != null">
                click_pv = click_pv + #{clickPvAdd},
            </if>
            <if test="clickUvAdd != null">
                click_uv = click_uv + #{clickUvAdd},
            </if>
            <if test="billingClickPvAdd != null">
                billing_click_pv = billing_click_pv + #{billingClickPvAdd},
            </if>
            <if test="billingClickUvAdd != null">
                billing_click_uv = billing_click_uv + #{billingClickUvAdd},
            </if>
            <if test="consumeAdd != null">
                consume = consume + #{consumeAdd},
            </if>
            <if test="lpExposurePvAdd != null">
                lp_exposure_pv = lp_exposure_pv + #{lpExposurePvAdd},
            </if>
            <if test="lpExposureUvAdd != null">
                lp_exposure_uv = lp_exposure_uv + #{lpExposureUvAdd},
            </if>
            <if test="lpClickPvAdd != null">
                lp_click_pv = lp_click_pv + #{lpClickPvAdd},
            </if>
            <if test="lpClickUvAdd != null">
                lp_click_uv = lp_click_uv + #{lpClickUvAdd},
            </if>
            <if test="takePvAdd != null">
                take_pv = take_pv + #{takePvAdd},
            </if>
            <if test="takeUvAdd != null">
                take_uv = take_uv + #{takeUvAdd},
            </if>
            <if test="formAdd != null">
                form = form + #{formAdd},
            </if>
            <if test="payAdd != null">
                pay = pay + #{payAdd},
            </if>
            <if test="refundAdd != null">
                refund = refund + #{refundAdd},
            </if>
            <if test="registerAdd != null">
                register = register + #{registerAdd},
            </if>
            <if test="deliveryAdd != null">
                delivery = delivery + #{deliveryAdd},
            </if>
            <if test="complainAdd != null">
                complain = complain + #{complainAdd},
            </if>
            <if test="appDownloadAdd != null">
                app_download = app_download + #{appDownloadAdd},
            </if>
            <if test="appActiveAdd != null">
                app_active = app_active + #{appActiveAdd},
            </if>
            <if test="appRegisterAdd != null">
                app_register = app_register + #{appRegisterAdd},
            </if>
            <if test="payPriceAdd != null">
                pay_price = pay_price + #{payPriceAdd},
            </if>
            gmt_modified = now()
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectOrientDayDataList" parameterType="com.ruoyi.system.bo.advert.OrientHourDataParam" resultType="com.ruoyi.system.bo.advert.OrientDayDataBo">
        select d.cur_date as curDate, d.advert_id as advertId, d.orient_id as orientId,
            sum(d.ad_launch_pv) as adLaunchPv, sum(d.ad_launch_uv) as adLaunchUv, sum(d.exposure_pv) as exposurePv, sum(d.exposure_uv) as exposureUv,
            sum(d.click_pv) as clickPv, sum(d.click_uv) as clickUv, sum(d.billing_click_pv) as billingClickPv, sum(d.billing_click_uv) as billingClickUv, sum(d.consume) as consume,
            sum(d.lp_exposure_pv) as lpExposurePv, sum(d.lp_exposure_uv) as lpExposureUv, sum(d.lp_click_pv) as lpClickPv, sum(d.lp_click_uv) as lpClickUv,
            sum(d.take_pv) as takePv, sum(d.take_uv) as takeUv,
            sum(d.register) as register, sum(d.pay) as pay, sum(d.refund) as refund, sum(d.complain) as complain,
            sum(d.app_active) as appActive, sum(d.pay_price) as extPrice
        from tb_orient_hour_data d
        <where>
            <if test="orientId != null"> and orient_id = #{orientId}</if>
            <if test="startDate != null ">and d.cur_date &gt;= #{startDate}</if>
            <if test="endDate != null ">and d.cur_date &lt;= #{endDate}</if>
            <if test="orientIds != null and orientIds.size() > 0">
                and d.orient_id in
                <foreach collection="orientIds" item="orientId" open="(" separator="," close=")">
                    #{orientId}
                </foreach>
            </if>
            <if test="advertIds != null and advertIds.size() > 0">
                and d.advert_id in
                <foreach collection="advertIds" item="advertId" open="(" separator="," close=")">
                    #{advertId}
                </foreach>
            </if>
        </where>
        group by d.cur_date, d.advert_id, d.orient_id
        order by
        <choose>
            <when test="orderColumn != null and orderColumn == 'adLaunchPv'">
                adLaunchPv ${orderType}
            </when>
            <when test="orderColumn != null and orderColumn == 'adLaunchUv'">
                adLaunchUv ${orderType}
            </when>
            <when test="orderColumn != null and orderColumn == 'lpClickUv'">
                lpClickUv ${orderType}
            </when>
            <when test="orderColumn != null and orderColumn == 'consume'">
                consume ${orderType}
            </when>
            <otherwise>
                cur_date desc, adLaunchPv desc
            </otherwise>
        </choose>
    </select>

    <select id="selectOrientHourDataList" parameterType="com.ruoyi.system.bo.advert.OrientHourDataParam" resultType="com.ruoyi.system.bo.advert.OrientDayDataBo">
        select d.advert_id as advertId, d.orient_id as orientId, d.cur_date as curDate, d.cur_hour as curHour,
            sum(d.ad_launch_pv) as adLaunchPv, sum(d.ad_launch_uv) as adLaunchUv, sum(d.exposure_pv) as exposurePv, sum(d.exposure_uv) as exposureUv,
            sum(d.click_pv) as clickPv, sum(d.click_uv) as clickUv, sum(d.billing_click_pv) as billingClickPv, sum(d.billing_click_uv) as billingClickUv, sum(d.consume) as consume,
            sum(d.lp_exposure_pv) as lpExposurePv, sum(d.lp_exposure_uv) as lpExposureUv, sum(d.lp_click_pv) as lpClickPv, sum(d.lp_click_uv) as lpClickUv,
            sum(d.take_pv) as takePv, sum(d.take_uv) as takeUv,
            sum(d.register) as register, sum(d.pay) as pay, sum(d.refund) as refund, sum(d.complain) as complain,
            sum(d.app_active) as appActive, sum(d.pay_price) as extPrice
        from tb_orient_hour_data d
        <where>
            <if test="advertId != null"> and advert_id = #{advertId}</if>
            <if test="orientId != null"> and orient_id = #{orientId}</if>
            <if test="curDate != null ">and d.cur_date = #{curDate}</if>
            <if test="startDate != null">and d.cur_date &gt;= #{startDate}</if>
            <if test="endDate != null">and d.cur_date &lt;= #{endDate}</if>
        </where>
        group by d.advert_id, d.orient_id, d.cur_date, d.cur_hour
        order by d.cur_date desc, d.cur_hour desc
    </select>

    <select id="selectOrientHourDataListGroupByDateHour" parameterType="com.ruoyi.system.bo.advert.OrientHourDataParam" resultType="com.ruoyi.system.bo.advert.OrientDayDataBo">
        select d.cur_date as curDate, d.cur_hour as curHour,
            sum(d.ad_launch_pv) as adLaunchPv, sum(d.ad_launch_uv) as adLaunchUv, sum(d.exposure_pv) as exposurePv, sum(d.exposure_uv) as exposureUv,
            sum(d.click_pv) as clickPv, sum(d.click_uv) as clickUv, sum(d.billing_click_pv) as billingClickPv, sum(d.billing_click_uv) as billingClickUv, sum(d.consume) as consume,
            sum(d.lp_exposure_pv) as lpExposurePv, sum(d.lp_exposure_uv) as lpExposureUv, sum(d.lp_click_pv) as lpClickPv, sum(d.lp_click_uv) as lpClickUv,
            sum(d.take_pv) as takePv, sum(d.take_uv) as takeUv,
            sum(d.register) as register, sum(d.pay) as pay, sum(d.refund) as refund, sum(d.complain) as complain,
            sum(d.app_active) as appActive, sum(d.pay_price) as extPrice
        from tb_orient_hour_data d
        <where>
            <if test="advertId != null"> and advert_id = #{advertId}</if>
            <if test="orientId != null"> and orient_id = #{orientId}</if>
            <if test="curDate != null ">and d.cur_date = #{curDate}</if>
            <if test="startDate != null">and d.cur_date &gt;= #{startDate}</if>
            <if test="endDate != null">and d.cur_date &lt;= #{endDate}</if>
        </where>
        group by d.cur_date, d.cur_hour
        order by d.cur_date desc, d.cur_hour desc
    </select>

    <select id="selectOrientAppDayDataList" parameterType="com.ruoyi.system.bo.advert.OrientHourDataParam" resultType="com.ruoyi.system.bo.advert.OrientDayDataBo">
        select d.cur_date as curDate, d.advert_id as advertId, d.orient_id as orientId, d.app_id as appId,
        sum(d.ad_launch_pv) as adLaunchPv, sum(d.ad_launch_uv) as adLaunchUv, sum(d.exposure_pv) as exposurePv, sum(d.exposure_uv) as exposureUv,
        sum(d.click_pv) as clickPv, sum(d.click_uv) as clickUv, sum(d.billing_click_pv) as billingClickPv, sum(d.billing_click_uv) as billingClickUv, sum(d.consume) as consume,
        sum(d.lp_exposure_pv) as lpExposurePv, sum(d.lp_exposure_uv) as lpExposureUv, sum(d.lp_click_pv) as lpClickPv, sum(d.lp_click_uv) as lpClickUv,
        sum(d.take_pv) as takePv, sum(d.take_uv) as takeUv,
        sum(d.register) as register, sum(d.pay) as pay, sum(d.refund) as refund, sum(d.complain) as complain,
        sum(d.app_active) as appActive, sum(d.pay_price) as extPrice
        from tb_orient_hour_data d
        <where>
            <if test="orientId != null"> and orient_id = #{orientId}</if>
            <if test="startDate != null ">and d.cur_date &gt;= #{startDate}</if>
            <if test="endDate != null ">and d.cur_date &lt;= #{endDate}</if>
            <if test="orientIds != null and orientIds.size() > 0">
                and d.orient_id in
                <foreach collection="orientIds" item="orientId" open="(" separator="," close=")">
                    #{orientId}
                </foreach>
            </if>
            <if test="advertIds != null and advertIds.size() > 0">
                and d.advert_id in
                <foreach collection="advertIds" item="advertId" open="(" separator="," close=")">
                    #{advertId}
                </foreach>
            </if>
            <if test="appIds != null and appIds.size() > 0">
                and d.app_id in
                <foreach collection="appIds" item="appId" open="(" separator="," close=")">
                    #{appId}
                </foreach>
            </if>
        </where>
        group by d.cur_date, d.advert_id, d.orient_id, d.app_id
        order by curDate desc, adLaunchPv desc
    </select>

    <select id="selectOrientSlotDayDataList" parameterType="com.ruoyi.system.bo.advert.OrientHourDataParam" resultType="com.ruoyi.system.bo.advert.OrientDayDataBo">
        select d.cur_date as curDate, d.advert_id as advertId, d.orient_id as orientId, d.app_id as appId, d.slot_id as slotId,
        sum(d.ad_launch_pv) as adLaunchPv, sum(d.ad_launch_uv) as adLaunchUv, sum(d.exposure_pv) as exposurePv, sum(d.exposure_uv) as exposureUv,
        sum(d.click_pv) as clickPv, sum(d.click_uv) as clickUv, sum(d.billing_click_pv) as billingClickPv, sum(d.billing_click_uv) as billingClickUv, sum(d.consume) as consume,
        sum(d.lp_exposure_pv) as lpExposurePv, sum(d.lp_exposure_uv) as lpExposureUv, sum(d.lp_click_pv) as lpClickPv, sum(d.lp_click_uv) as lpClickUv,
        sum(d.take_pv) as takePv, sum(d.take_uv) as takeUv,
        sum(d.register) as register, sum(d.pay) as pay, sum(d.refund) as refund, sum(d.complain) as complain,
        sum(d.app_active) as appActive, sum(d.pay_price) as extPrice
        from tb_orient_hour_data d
        <where>
            <if test="orientId != null"> and orient_id = #{orientId}</if>
            <if test="startDate != null ">and d.cur_date &gt;= #{startDate}</if>
            <if test="endDate != null ">and d.cur_date &lt;= #{endDate}</if>
            <if test="orientIds != null and orientIds.size() > 0">
                and d.orient_id in
                <foreach collection="orientIds" item="orientId" open="(" separator="," close=")">
                    #{orientId}
                </foreach>
            </if>
            <if test="advertIds != null and advertIds.size() > 0">
                and d.advert_id in
                <foreach collection="advertIds" item="advertId" open="(" separator="," close=")">
                    #{advertId}
                </foreach>
            </if>
            <if test="slotIds != null and slotIds.size() > 0">
                and d.slot_id in
                <foreach collection="slotIds" item="slotId" open="(" separator="," close=")">
                    #{slotId}
                </foreach>
            </if>
        </where>
        group by d.cur_date, d.advert_id, d.orient_id, d.app_id, d.slot_id
        order by curDate desc, adLaunchPv desc
    </select>

    <select id="selectStatisticOrientData" parameterType="com.ruoyi.system.bo.advert.OrientHourDataParam" resultType="com.ruoyi.system.bo.advert.OrientDayDataBo">
        select sum(d.ad_launch_pv) as adLaunchPv, sum(d.ad_launch_uv) as adLaunchUv, sum(d.exposure_pv) as exposurePv, sum(d.exposure_uv) as exposureUv,
        sum(d.click_pv) as clickPv, sum(d.click_uv) as clickUv, sum(d.billing_click_pv) as billingClickPv, sum(d.billing_click_uv) as billingClickUv, sum(d.consume) as consume,
        sum(d.lp_exposure_pv) as lpExposurePv, sum(d.lp_exposure_uv) as lpExposureUv, sum(d.lp_click_pv) as lpClickPv, sum(d.lp_click_uv) as lpClickUv,
        sum(d.take_pv) as takePv, sum(d.take_uv) as takeUv,
        sum(d.register) as register, sum(d.pay) as pay, sum(d.refund) as refund, sum(d.complain) as complain,
        sum(d.app_active) as appActive, sum(d.pay_price) as extPrice
        from tb_orient_hour_data d
        <where>
            <if test="advertId != null"> and advert_id = #{advertId}</if>
            <if test="orientId != null"> and orient_id = #{orientId}</if>
            <if test="curDate != null ">and d.cur_date = #{curDate}</if>
            <if test="startDate != null ">and d.cur_date &gt;= #{startDate}</if>
            <if test="endDate != null ">and d.cur_date &lt;= #{endDate}</if>
            <if test="orientIds != null and orientIds.size() > 0">
                and d.orient_id in
                <foreach collection="orientIds" item="orientId" open="(" separator="," close=")">
                    #{orientId}
                </foreach>
            </if>
            <if test="advertIds != null and advertIds.size() > 0">
                and d.advert_id in
                <foreach collection="advertIds" item="advertId" open="(" separator="," close=")">
                    #{advertId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectOrientDataForLandpage" resultType="com.ruoyi.system.bo.advert.OrientDayDataBo">
        select d.`advert_id` as advertId, 0 as orientId, d.`cur_date` as curDate,
            d.`lp_exposure_pv` as lpExposurePv, d.`lp_exposure_uv` as lpExposureUv,
            d.`lp_click_pv` as lpClickPv, d.`lp_click_uv` as lpClickUv,
            d.`billing_click_pv` as billingClickPv, d.`billing_click_uv` as billingClickUv,
            ifnull(c.`consume`, 0) as consume, ifnull(cv.pay, 0) as pay
        from `tb_advert_day_data` d
        left join tb_advert_day_consume_data c on c.`advert_id` = d.`advert_id` and c.`cur_date` = d.`cur_date`
        left join (
            select advert_id, cur_date, SUM(`conv_pv`) as pay from tb_advert_slot_conv_day_data
            <where>
                conv_type = 2
                and `cur_date` &lt; '2024-01-01'
                <if test="startDate != null ">and cur_date &gt;= #{startDate}</if>
                <if test="endDate != null ">and cur_date &lt;= #{endDate}</if>
                <if test="advertIds != null and advertIds.size() > 0">
                    and advert_id in
                    <foreach collection="advertIds" item="advertId" open="(" separator="," close=")">
                        #{advertId}
                    </foreach>
                </if>
                <if test="invisibleDateList != null and invisibleDateList.size() > 0">
                    and cur_date not in
                    <foreach collection="invisibleDateList" separator="," item="date" close=")" open="(">
                        #{date}
                    </foreach>
                </if>
            </where>
            GROUP BY `advert_id`, `cur_date`
        ) cv on cv.advert_id = d.advert_id and cv.cur_date = d.`cur_date`
        <where>
            d.`cur_date` &lt; '2024-01-01'
            <if test="startDate != null ">and d.cur_date &gt;= #{startDate}</if>
            <if test="endDate != null ">and d.cur_date &lt;= #{endDate}</if>
            <if test="advertIds != null and advertIds.size() > 0">
                and d.advert_id in
                <foreach collection="advertIds" item="advertId" open="(" separator="," close=")">
                    #{advertId}
                </foreach>
            </if>
            <if test="invisibleDateList != null and invisibleDateList.size() > 0">
                and d.cur_date not in
                <foreach collection="invisibleDateList" separator="," item="date" close=")" open="(">
                    #{date}
                </foreach>
            </if>
        </where>

        union all

        select `advert_id` as advertId, `orient_id` as orientId,`cur_date` as curDate,
            sum(`lp_exposure_pv`) as lpExposurePv, sum(`lp_exposure_uv`) as lpExposureUv,
            sum(`lp_click_pv`) as lpClickPv, sum(`lp_click_uv`) as lpClickUv,
            SUM(`billing_click_pv`) as billingClickPv, sum(`billing_click_uv`) as billingClickUv,
            SUM(`consume`) as consume, SUM(`pay`) as pay
        from `tb_orient_hour_data`
        <where>
            `cur_date` &gt;= '2024-01-01'
            <if test="startDate != null ">and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null ">and cur_date &lt;= #{endDate}</if>
            <if test="advertIds != null and advertIds.size() > 0">
                and advert_id in
                <foreach collection="advertIds" item="advertId" open="(" separator="," close=")">
                    #{advertId}
                </foreach>
            </if>
            <if test="invisibleDateList != null and invisibleDateList.size() > 0">
                and cur_date not in
                <foreach collection="invisibleDateList" separator="," item="date" close=")" open="(">
                    #{date}
                </foreach>
            </if>
        </where>
        GROUP BY `advert_id`, `orient_id`, `cur_date`
    </select>
</mapper>
