<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.datashow.AppActivityDataMapper">

    <resultMap type="com.ruoyi.system.entity.datashow.AppActivityData" id="AppActivityDataResult">
        <result property="id"    column="id"    />
        <result property="curDate"    column="cur_date"    />
        <result property="accountId"    column="account_id"    />
        <result property="appId"    column="app_id"    />
        <result property="activityId"    column="activity_id"    />
        <result property="activityRequestPv"    column="activity_request_pv"    />
        <result property="activityRequestUv"    column="activity_request_uv"    />
        <result property="joinPv"    column="join_pv"    />
        <result property="joinUv"    column="join_uv"    />
        <result property="adLaunch" column="ad_launch"    />
        <result property="adExposurePv"    column="ad_exposure_pv"    />
        <result property="adExposureUv"    column="ad_exposure_uv"    />
        <result property="adClickPv"    column="ad_click_pv"    />
        <result property="adClickUv"    column="ad_click_uv"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectAppActivityDataVo">
        select id, cur_date, account_id, app_id, activity_id,
               activity_request_pv, activity_request_uv, join_pv, join_uv, ad_launch,
               ad_exposure_pv, ad_exposure_uv, ad_click_pv, ad_click_uv,
               gmt_create, gmt_modified from tb_app_activity_data
    </sql>

    <select id="selectBy" resultMap="AppActivityDataResult">
        <include refid="selectAppActivityDataVo"/>
        where cur_date = #{curDate} and app_id = #{appId} and activity_id = #{activityId}
    </select>

    <select id="selectAppActivityDataList" parameterType="com.ruoyi.system.entity.datashow.AppActivityData" resultMap="AppActivityDataResult">
        <include refid="selectAppActivityDataVo"/>
        <where>
            <if test="curDate != null "> and cur_date = #{curDate}</if>
            <if test="accountId != null "> and account_id = #{accountId}</if>
            <if test="appId != null "> and app_id = #{appId}</if>
            <if test="activityId != null "> and activity_id = #{activityId}</if>
            <if test="activityRequestPv != null "> and activity_request_pv = #{activityRequestPv}</if>
            <if test="activityRequestUv != null "> and activity_request_uv = #{activityRequestUv}</if>
            <if test="joinPv != null "> and join_pv = #{joinPv}</if>
            <if test="joinUv != null "> and join_uv = #{joinUv}</if>
            <if test="adLaunch != null"> and ad_launch = #{adLaunch}</if>
            <if test="adExposurePv != null "> and ad_exposure_pv = #{adExposurePv}</if>
            <if test="adExposureUv != null "> and ad_exposure_uv = #{adExposureUv}</if>
            <if test="adClickPv != null "> and ad_click_pv = #{adClickPv}</if>
            <if test="adClickUv != null "> and ad_click_uv = #{adClickUv}</if>
            <if test="gmtCreate != null "> and gmt_create = #{gmtCreate}</if>
            <if test="gmtModified != null "> and gmt_modified = #{gmtModified}</if>
        </where>
    </select>

    <select id="selectAppActivityDataById" parameterType="String" resultMap="AppActivityDataResult">
        <include refid="selectAppActivityDataVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppActivityData" parameterType="com.ruoyi.system.entity.datashow.AppActivityData" useGeneratedKeys="true" keyProperty="id">
        insert ignore into tb_app_activity_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="curDate != null">cur_date,</if>
            <if test="accountId != null">account_id,</if>
            <if test="appId != null">app_id,</if>
            <if test="activityId != null ">activity_id,</if>
            <if test="activityRequestPv != null">activity_request_pv,</if>
            <if test="activityRequestUv != null">activity_request_uv,</if>
            <if test="joinPv != null">join_pv,</if>
            <if test="joinUv != null">join_uv,</if>
            <if test="adRequest != null">ad_request,</if>
            <if test="adLaunch != null">ad_launch,</if>
            <if test="adExposurePv != null">ad_exposure_pv,</if>
            <if test="adExposureUv != null">ad_exposure_uv,</if>
            <if test="adClickPv != null">ad_click_pv,</if>
            <if test="adClickUv != null">ad_click_uv,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="curDate != null">#{curDate},</if>
            <if test="accountId != null ">#{accountId},</if>
            <if test="appId != null">#{appId},</if>
            <if test="activityId != null">#{activityId},</if>
            <if test="activityRequestPv != null">#{activityRequestPv},</if>
            <if test="activityRequestPv != null">#{activityRequestPv},</if>
            <if test="joinPv != null">#{joinPv},</if>
            <if test="joinUv != null">#{joinUv},</if>
            <if test="adRequest != null">#{adRequest},</if>
            <if test="adLaunch != null">#{adLaunch},</if>
            <if test="adExposurePv != null">#{adExposurePv},</if>
            <if test="adExposureUv != null">#{adExposureUv},</if>
            <if test="adClickPv != null">#{adClickPv},</if>
            <if test="adClickUv != null">#{adClickUv},</if>
         </trim>
    </insert>

    <update id="updateAppActivityData" parameterType="com.ruoyi.system.entity.datashow.AppActivityData">
        update tb_app_activity_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="curDate != null">cur_date = #{curDate},</if>
            <if test="accountId != null">account_id = #{accountId},</if>
            <if test="appId != null">app_id = #{appId},</if>
            <if test="activityId != null ">activity_id = #{activityId},</if>
            <if test="activityRequestPv != null">activity_request_pv = #{activityRequestPv},</if>
            <if test="activityRequestUv != null">activity_request_uv = #{activityRequestUv},</if>
            <if test="joinPv != null">join_pv = #{joinPv},</if>
            <if test="joinUv != null">join_uv = #{joinUv},</if>
            <if test="adRequest != null">ad_request = #{adRequest},</if>
            <if test="adLaunch != null">ad_launch = #{adLaunch},</if>
            <if test="adExposurePv != null">ad_exposure_pv = #{adExposurePv},</if>
            <if test="adExposureUv != null">ad_exposure_uv = #{adExposureUv},</if>
            <if test="adClickPv != null">ad_click_pv = #{adClickPv},</if>
            <if test="adClickUv != null">ad_click_uv = #{adClickUv},</if>
            <if test="activityRequestPvAdd != null">activity_request_pv = activity_request_pv + #{activityRequestPvAdd},</if>
            <if test="activityRequestUvAdd != null">activity_request_uv = activity_request_uv + #{activityRequestUvAdd},</if>
            <if test="joinPvAdd != null">join_pv = join_pv + #{joinPvAdd},</if>
            <if test="joinUvAdd != null">join_uv = join_uv + #{joinUvAdd},</if>
            <if test="adRequestAdd != null">ad_request = ad_request + #{adRequestAdd},</if>
            <if test="adLaunchAdd != null">ad_launch = ad_launch + #{adLaunchAdd},</if>
            <if test="adExposurePvAdd != null">ad_exposure_pv = ad_exposure_pv + #{adExposurePvAdd},</if>
            <if test="adExposureUvAdd != null">ad_exposure_uv = ad_exposure_uv + #{adExposureUvAdd},</if>
            <if test="adClickPvAdd != null">ad_click_pv = ad_click_pv + #{adClickPvAdd},</if>
            <if test="adClickUvAdd != null">ad_click_uv = ad_click_uv + #{adClickUvAdd},</if>
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>

    <select id="selectAppActivityDataSum" resultType="com.ruoyi.system.bo.appactivitydata.AppActivityDataSumBo">
        select app_id as appId,sum(join_pv) as joinPv, sum(join_uv) as joinUv
        from tb_app_activity_data
        where app_id in
        <foreach collection="appIds" separator="," item="appId" open="(" close=")">
            #{appId}
        </foreach>
        and cur_date &gt;= #{startDate}
        and cur_date &lt;= #{endDate}
        group by app_id
    </select>

    <select id="selectAppActivityData" resultMap="AppActivityDataResult">
        <include refid="selectAppActivityDataVo"/>
        where app_id in
        <foreach collection="appIds" separator="," item="appId" open="(" close=")">
            #{appId}
        </foreach>
        and cur_date &gt;= #{startDate}
        and cur_date &lt;= #{endDate}
    </select>
</mapper>
