<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.datashow.AppDataMapper">

    <resultMap type="com.ruoyi.system.entity.datashow.AppData" id="AppDataResult">
        <result property="id"    column="id"    />
        <result property="curDate"    column="cur_date"    />
        <result property="accountId"    column="account_id"    />
        <result property="appId"    column="app_id"    />
        <result property="slotRequestPv"    column="slot_request_pv"    />
        <result property="slotRequestUv"    column="slot_request_uv"    />
        <result property="appSlotClickPv"    column="app_slot_click_pv"    />
        <result property="appSlotClickUv"    column="app_slot_click_uv"    />
        <result property="appSlotExposurePv"    column="app_slot_exposure_pv"    />
        <result property="appSlotExposureUv"    column="app_slot_exposure_uv"    />
        <result property="nhConsume"    column="nh_consume"    />
        <result property="outerConsume"    column="outer_consume"    />
        <result property="totalConsume"    column="total_consume"    />
        <result property="appRevenue"    column="app_revenue"    />
        <result property="nhCost"    column="nh_cost"    />
        <result property="outerCost"    column="outer_cost"    />

    </resultMap>

    <sql id="selectAppDataVo">
        select id, cur_date, account_id, app_id, slot_request_pv, slot_request_uv,app_slot_click_pv,app_slot_click_uv, app_slot_exposure_pv,app_slot_exposure_uv,nh_consume,outer_consume,total_consume,
               app_revenue, nh_cost ,outer_cost from tb_app_data
    </sql>

    <select id="selectAppDataList" parameterType="com.ruoyi.system.entity.datashow.AppData" resultMap="AppDataResult">
        <include refid="selectAppDataVo"/>
        <where>
            <if test="curDate != null "> and cur_date = #{curDate}</if>
            <if test="accountId != null  and accountId != ''"> and account_id = #{accountId}</if>
            <if test="accountIds != null and accountIds.size()>0">
                and account_id in
                <foreach collection="accountIds" item="accountId" open="(" separator="," close=")">
                    #{accountId}
                </foreach>
            </if>
            <if test="appId != null  and appId != ''"> and app_id = #{appId}</if>
            <if test="appIds != null and appIds.size()>0 ">
                and app_id in
                <foreach collection="appIds" item="appId" open="(" separator="," close=")">
                    #{appId}
                </foreach>
            </if>
            <if test="startDate != null "> and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null "> and cur_date &lt;= #{endDate}</if>
        </where>
        order by cur_date desc, app_revenue desc
    </select>

    <select id="selectAppDataById" parameterType="Long" resultMap="AppDataResult">
        <include refid="selectAppDataVo"/>
        where id = #{id}
    </select>

    <select id="selectByAppIdAndDate" resultMap="AppDataResult">
        <include refid="selectAppDataVo"/>
        where app_id = #{appId} and cur_date = #{curDate}
    </select>

    <insert id="insertAppData" parameterType="com.ruoyi.system.entity.datashow.AppData" useGeneratedKeys="true" keyProperty="id">
        insert into tb_app_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="curDate != null">cur_date,</if>
            <if test="accountId != null and accountId != ''">account_id,</if>
            <if test="appId != null and appId != ''">app_id,</if>
            <if test="slotRequestPv != null">slot_request_pv,</if>
            <if test="slotRequestUv != null">slot_request_uv,</if>
            <if test="totalConsume != null">total_consume,</if>
            <if test="appRevenue != null">app_revenue,</if>
            <if test="outerConsume != null">outer_consume,</if>
            <if test="nhCost != null">nh_cost,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="curDate != null">#{curDate},</if>
            <if test="accountId != null and accountId != ''">#{accountId},</if>
            <if test="appId != null and appId != ''">#{appId},</if>
            <if test="slotRequestPv != null">#{slotRequestPv},</if>
            <if test="slotRequestUv != null">#{slotRequestUv},</if>
            <if test="totalConsume != null">#{totalConsume},</if>
            <if test="appRevenue != null">#{appRevenue},</if>
            <if test="outerConsume != null">#{outerConsume},</if>
            <if test="nhCost != null">#{nhCost},</if>
        </trim>
    </insert>

    <update id="updateAppData" parameterType="com.ruoyi.system.entity.datashow.AppData">
        update tb_app_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="curDate != null">cur_date = #{curDate},</if>
            <if test="accountId != null and accountId != ''">account_id = #{accountId},</if>
            <if test="appId != null and appId != ''">app_id = #{appId},</if>
            <if test="slotRequestPv != null">slot_request_pv = #{slotRequestPv},</if>
            <if test="slotRequestUv != null">slot_request_uv = #{slotRequestUv},</if>
            <if test="appSlotClickPv != null">app_slot_click_pv = #{appSlotClickPv},</if>
            <if test="appSlotClickUv != null">app_slot_click_uv = #{appSlotClickUv},</if>
            <if test="appSlotExposurePv != null">app_slot_exposure_pv = #{appSlotExposurePv},</if>
            <if test="appSlotExposureUv != null">app_slot_exposure_uv = #{appSlotExposureUv},</if>
            <if test="slotRequestPvAdd != null">slot_request_pv = slot_request_pv + #{slotRequestPvAdd},</if>
            <if test="slotRequestUvAdd != null">slot_request_uv = slot_request_uv + #{slotRequestUvAdd},</if>
            <if test="nhConsumeAdd != null">nh_consume = nh_consume + #{nhConsumeAdd},</if>
            <if test="totalConsumeAdd != null">total_consume = total_consume + #{totalConsumeAdd},</if>
            <if test="outerConsumeAdd != null">outer_consume = outer_consume + #{outerConsumeAdd},</if>
            <if test="totalConsume != null">total_consume = #{totalConsume},</if>
            <if test="appRevenue != null">app_revenue = #{appRevenue},</if>
            <if test="outerConsume != null">outer_consume = #{outerConsume},</if>
            <if test="nhCost != null">nh_cost = #{nhCost},</if>
            <if test="outerCost != null">outer_cost = #{outerCost},</if>
            <if test="nhCostAdd != null">nh_cost = nh_cost + #{nhCostAdd},</if>
            <if test="outerCostAdd != null">outer_cost = outer_cost + #{outerCostAdd},</if>
            <if test="appRevenueAdd != null">app_revenue = app_revenue + #{appRevenueAdd},</if>
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>
    <update id="updateAppNhCostData" parameterType="com.ruoyi.system.entity.datashow.AppData">
        update tb_app_data
        set nh_cost = nh_cost + #{nhCostAdd}
        where app_id = #{appId} and cur_date = #{curDate}
    </update>

    <insert id="batchInsertOrUpdateAppData" parameterType="com.ruoyi.system.entity.datashow.SlotData">
        insert into tb_app_data(cur_date,account_id,app_id,app_slot_click_pv,app_slot_click_uv, app_slot_exposure_pv,app_slot_exposure_uv)
        values
        <foreach collection="slotDataList" item="entity" separator=",">
            ( #{entity.curDate},#{entity.accountId},#{entity.appId},#{entity.appSlotClickPv},#{entity.appSlotClickUv},#{entity.appSlotExposurePv},#{entity.appSlotExposureUv})
        </foreach>
        ON DUPLICATE KEY UPDATE
        app_slot_click_pv = values(app_slot_click_pv) ,
        app_slot_click_uv = values(app_slot_click_uv) ,
        app_slot_exposure_pv = values(app_slot_exposure_pv) ,
        app_slot_exposure_uv = values(app_slot_exposure_uv)
    </insert>

    <select id="selectDistinctAppIdByDate" resultType="java.lang.Long">
        select distinct app_id
        from tb_app_data
        where cur_date &gt;= #{startDate} and cur_date &lt; #{endDate}
    </select>

    <update id="initAppRevenueByDate">
        update tb_app_data
        set app_revenue = 0
        where cur_date = #{curDate}
    </update>

</mapper>
