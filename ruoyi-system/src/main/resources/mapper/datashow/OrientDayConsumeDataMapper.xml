<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.datashow.OrientDayConsumeDataMapper">

    <resultMap type="com.ruoyi.system.entity.datashow.OrientDayConsumeData" id="OrientDayConsumeDataResult">
        <result property="id"    column="id"    />
        <result property="curDate"    column="cur_date"    />
        <result property="advertId"    column="advert_id"    />
        <result property="orientId"    column="orient_id"    />
        <result property="consume"    column="consume"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectOrientDayConsumeDataVo">
        select id, cur_date, advert_id, orient_id, consume, gmt_create, gmt_modified from tb_orient_day_consume_data
    </sql>

    <select id="selectByDateAndOrientId" resultMap="OrientDayConsumeDataResult">
        <include refid="selectOrientDayConsumeDataVo"/>
        where cur_date = #{curDate} and orient_id = #{orientId}
    </select>

    <update id="addConsumeData" parameterType="com.ruoyi.system.entity.datashow.OrientDayConsumeData">
        update tb_orient_day_consume_data
        <trim prefix="SET" suffixOverrides=",">
            consume = consume + #{consumeAdd},
            gmt_modified = now()
        </trim>
        <where>
            id = #{id}
            <if test="dailyBudget != null"> and consume + #{consumeAdd} &lt;= #{dailyBudget}</if>
        </where>
    </update>

    <select id="selectOrientDayConsumeDataList" parameterType="com.ruoyi.system.entity.datashow.OrientDayConsumeData" resultMap="OrientDayConsumeDataResult">
        <include refid="selectOrientDayConsumeDataVo"/>
        <where>
            <if test="curDate != null "> and cur_date = #{curDate}</if>
            <if test="advertId != null"> and advert_id = #{advertId}</if>
            <if test="orientId != null"> and orient_id = #{orientId}</if>
            <if test="consume != null"> and consume = #{consume}</if>
        </where>
    </select>

    <insert id="insertOrientDayConsumeData" parameterType="com.ruoyi.system.entity.datashow.OrientDayConsumeData" useGeneratedKeys="true" keyProperty="id">
        insert ignore into tb_orient_day_consume_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="curDate != null">cur_date,</if>
            <if test="advertId != null">advert_id,</if>
            <if test="orientId != null">orient_id,</if>
            <if test="consume != null">consume,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="curDate != null">#{curDate},</if>
            <if test="advertId != null">#{advertId},</if>
            <if test="orientId != null">#{orientId},</if>
            <if test="consume != null">#{consume},</if>
         </trim>
    </insert>

    <update id="updateOrientDayConsumeData" parameterType="com.ruoyi.system.entity.datashow.OrientDayConsumeData">
        update tb_orient_day_consume_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="curDate != null">cur_date = #{curDate},</if>
            <if test="advertId != null">advert_id = #{advertId},</if>
            <if test="orientId != null">orient_id = #{orientId},</if>
            <if test="consume != null">consume = #{consume},</if>
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>

    <select id="selectByDateAndOrientIds" resultMap="OrientDayConsumeDataResult">
        <include refid="selectOrientDayConsumeDataVo"/>
        where cur_date = #{curDate} and orient_id in
        <foreach collection="orientIds" item="orientId" open="(" separator="," close=")">
            #{orientId}
        </foreach>
    </select>

</mapper>
