<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.datashow.SlotActivityHourDataMapper">

    <resultMap type="com.ruoyi.system.entity.datashow.SlotActivityHourData" id="SlotActivityHourDataResult">
        <result property="id"    column="id"    />
        <result property="curDate"    column="cur_date"    />
        <result property="curHour"    column="cur_hour"    />
        <result property="accountId"    column="account_id"    />
        <result property="appId"    column="app_id"    />
        <result property="slotId"    column="slot_id"    />
        <result property="activityId"    column="activity_id"    />
        <result property="activityRequestPv"    column="activity_request_pv"    />
        <result property="activityRequestUv"    column="activity_request_uv"    />
        <result property="joinPv"    column="join_pv"    />
        <result property="joinUv"    column="join_uv"    />
        <result property="adRequest" column="ad_request"    />
        <result property="adLaunch" column="ad_launch"    />
        <result property="adExposurePv"    column="ad_exposure_pv"    />
        <result property="adExposureUv"    column="ad_exposure_uv"    />
        <result property="adClickPv"    column="ad_click_pv"    />
        <result property="adClickUv"    column="ad_click_uv"    />
        <result property="consume"    column="consume"    />
        <result property="lpExposurePv"    column="lp_exposure_pv"    />
        <result property="lpExposureUv"    column="lp_exposure_uv"    />
        <result property="lpClickPv"    column="lp_click_pv"    />
        <result property="lpClickUv"    column="lp_click_uv"    />
        <result property="theoryCost"    column="theory_cost"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectSlotActivityHourDataVo">
        select id, cur_date, cur_hour, account_id, app_id, slot_id, activity_id,
               activity_request_pv, activity_request_uv, join_pv, join_uv,
               ad_request, ad_launch, ad_exposure_pv, ad_exposure_uv, ad_click_pv, ad_click_uv,
               consume, lp_exposure_pv, lp_exposure_uv, lp_click_pv, lp_click_uv, theory_cost,
               gmt_create, gmt_modified from tb_slot_activity_hour_data
    </sql>

    <select id="selectBy" resultMap="SlotActivityHourDataResult">
        <include refid="selectSlotActivityHourDataVo"/>
        where cur_date = #{curDate} and cur_hour = #{curHour} and slot_id = #{slotId} and activity_id = #{activityId}
    </select>

    <select id="selectSlotActivityHourDataList" parameterType="com.ruoyi.system.entity.datashow.SlotActivityHourData" resultMap="SlotActivityHourDataResult">
        <include refid="selectSlotActivityHourDataVo"/>
        <where>
            <if test="curDate != null "> and cur_date = #{curDate}</if>
            <if test="curHour != null "> and cur_hour = #{curHour}</if>
            <if test="accountId != null "> and account_id = #{accountId}</if>
            <if test="appId != null"> and app_id = #{appId}</if>
            <if test="slotId != null "> and slot_id = #{slotId}</if>
            <if test="activityId != null "> and activity_id = #{activityId}</if>
            <if test="startDate != null "> and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null "> and cur_date &lt;= #{endDate}</if>
            <if test="slotIds != null and slotIds.size()>0">
                and slot_id in
                <foreach collection="slotIds" item="slotId" open="(" separator="," close=")">
                    #{slotId}
                </foreach>
            </if>
            <if test="appIds != null and appIds.size()>0 ">
                and app_id in
                <foreach collection="appIds" item="appId" open="(" separator="," close=")">
                    #{appId}
                </foreach>
            </if>
            <if test="accountIds != null and accountIds.size()>0 ">
                and account_id in
                <foreach collection="accountIds" item="accountId" open="(" separator="," close=")">
                    #{accountId}
                </foreach>
            </if>
        </where>
        order by cur_date desc, cur_hour desc, activity_request_pv desc, id desc
    </select>

    <insert id="insertSlotActivityHourData" parameterType="com.ruoyi.system.entity.datashow.SlotActivityHourData"
            useGeneratedKeys="true" keyProperty="id">
        insert ignore into tb_slot_activity_hour_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="curDate != null">cur_date,</if>
            <if test="curHour != null">cur_hour,</if>
            <if test="accountId != null">account_id,</if>
            <if test="appId != null">app_id,</if>
            <if test="slotId != null">slot_id,</if>
            <if test="activityId != null">activity_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="curDate != null">#{curDate},</if>
            <if test="curHour != null">#{curHour},</if>
            <if test="accountId != null">#{accountId},</if>
            <if test="appId != null">#{appId},</if>
            <if test="slotId != null">#{slotId},</if>
            <if test="activityId != null">#{activityId},</if>
         </trim>
    </insert>

    <update id="updateSlotActivityHourData" parameterType="com.ruoyi.system.entity.datashow.SlotActivityHourData">
        update tb_slot_activity_hour_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="activityRequestPv != null">activity_request_pv = #{activityRequestPv},</if>
            <if test="activityRequestUv != null">activity_request_uv = #{activityRequestUv},</if>
            <if test="joinPv != null">join_pv = #{joinPv},</if>
            <if test="joinUv != null">join_uv = #{joinUv},</if>
            <if test="adRequest != null">ad_request = #{adRequest},</if>
            <if test="adLaunch != null">ad_launch = #{adLaunch},</if>
            <if test="adExposurePv != null">ad_exposure_pv = #{adExposurePv},</if>
            <if test="adExposureUv != null">ad_exposure_uv = #{adExposureUv},</if>
            <if test="adClickPv != null">ad_click_pv = #{adClickPv},</if>
            <if test="adClickUv != null">ad_click_uv = #{adClickUv},</if>
            <if test="activityRequestPvAdd != null">activity_request_pv = activity_request_pv + #{activityRequestPvAdd},</if>
            <if test="activityRequestUvAdd != null">activity_request_uv = activity_request_uv + #{activityRequestUvAdd},</if>
            <if test="joinPvAdd != null">join_pv = join_pv + #{joinPvAdd},</if>
            <if test="joinUvAdd != null">join_uv = join_uv + #{joinUvAdd},</if>
            <if test="adRequestAdd != null">ad_request = ad_request + #{adRequestAdd},</if>
            <if test="adLaunchAdd != null">ad_launch = ad_launch + #{adLaunchAdd},</if>
            <if test="adExposurePvAdd != null">ad_exposure_pv = ad_exposure_pv + #{adExposurePvAdd},</if>
            <if test="adExposureUvAdd != null">ad_exposure_uv = ad_exposure_uv + #{adExposureUvAdd},</if>
            <if test="adClickPvAdd != null">ad_click_pv = ad_click_pv + #{adClickPvAdd},</if>
            <if test="adClickUvAdd != null">ad_click_uv = ad_click_uv + #{adClickUvAdd},</if>
            <if test="consumeAdd != null">consume = consume + #{consumeAdd},</if>
            <if test="lpExposurePvAdd != null">lp_exposure_pv = lp_exposure_pv + #{lpExposurePvAdd},</if>
            <if test="lpExposureUvAdd != null">lp_exposure_uv = lp_exposure_uv + #{lpExposureUvAdd},</if>
            <if test="lpClickPvAdd != null">lp_click_pv = lp_click_pv + #{lpClickPvAdd},</if>
            <if test="lpClickUvAdd != null">lp_click_uv = lp_click_uv + #{lpClickUvAdd},</if>
            <if test="theoryCostAdd != null">theory_cost = theory_cost + #{theoryCostAdd},</if>
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>
</mapper>
