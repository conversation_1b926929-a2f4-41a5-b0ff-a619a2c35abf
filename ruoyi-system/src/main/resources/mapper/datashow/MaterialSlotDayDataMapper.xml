<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.datashow.MaterialSlotDayDataMapper">

    <resultMap type="com.ruoyi.system.entity.datashow.MaterialSlotDayData" id="MaterialDayDataResult">
        <result property="id"    column="id"    />
        <result property="curDate"    column="cur_date"    />
        <result property="materialId"    column="material_id"    />
        <result property="slotId"    column="slot_id"    />
        <result property="exposurePv"    column="exposure_pv"    />
        <result property="exposureUv"    column="exposure_uv"    />
        <result property="clickPv"    column="click_pv"    />
        <result property="clickUv"    column="click_uv"    />
        <result property="consume"    column="consume"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectMaterialSlotDayDataVo">
        select id, cur_date, material_id, slot_id, exposure_pv, exposure_uv,
               click_pv, click_uv, consume, gmt_create, gmt_modified from tb_material_slot_day_data
    </sql>

    <select id="selectMaterialSlotDayDataList" parameterType="com.ruoyi.system.entity.datashow.MaterialSlotDayData" resultMap="MaterialDayDataResult">
        <include refid="selectMaterialSlotDayDataVo"/>
        <where>
            <if test="curDate != null "> and cur_date = #{curDate}</if>
            <if test="materialId != null"> and material_id = #{materialId}</if>
            <if test="slotId != null"> and slot_id = #{slotId}</if>
        </where>
    </select>

    <select id="selectBy" parameterType="com.ruoyi.system.entity.datashow.MaterialSlotDayData" resultMap="MaterialDayDataResult">
        <include refid="selectMaterialSlotDayDataVo"/>
        <where>
            <if test="curDate != null "> and cur_date = #{curDate}</if>
            <if test="materialId != null"> and material_id = #{materialId}</if>
            <if test="slotId != null"> and slot_id = #{slotId}</if>
        </where>
        limit 1
    </select>

    <insert id="insertMaterialSlotDayData" parameterType="com.ruoyi.system.entity.datashow.MaterialSlotDayData" useGeneratedKeys="true" keyProperty="id">
        insert ignore into tb_material_slot_day_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="curDate != null">cur_date,</if>
            <if test="materialId != null">material_id,</if>
            <if test="slotId != null">slot_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="curDate != null">#{curDate},</if>
            <if test="materialId != null">#{materialId},</if>
            <if test="slotId != null">#{slotId},</if>
         </trim>
    </insert>

    <update id="updateMaterialSlotDayData" parameterType="com.ruoyi.system.entity.datashow.MaterialSlotDayData">
        update tb_material_slot_day_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="exposurePvAdd != null">exposure_pv = exposure_pv + #{exposurePvAdd},</if>
            <if test="exposureUvAdd != null">exposure_uv = exposure_uv + #{exposureUvAdd},</if>
            <if test="clickPvAdd != null">click_pv = click_pv + #{clickPvAdd},</if>
            <if test="clickUvAdd != null">click_uv = click_uv + #{clickUvAdd},</if>
            <if test="consumeAdd != null">consume = consume + #{consumeAdd},</if>
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>
</mapper>
