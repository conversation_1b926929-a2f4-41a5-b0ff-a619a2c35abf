<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.datashow.AdvertHourDataMapper">

    <resultMap type="com.ruoyi.system.entity.datashow.AdvertHourData" id="AdvertHourDataResult">
        <result property="id"    column="id"    />
        <result property="curDate"    column="cur_date"    />
        <result property="curHour"    column="cur_hour"    />
        <result property="advertId"    column="advert_id"    />
        <result property="adLaunchPv"    column="ad_launch_pv"    />
        <result property="adLaunchUv"    column="ad_launch_uv"    />
        <result property="exposurePv"    column="exposure_pv"    />
        <result property="exposureUv"    column="exposure_uv"    />
        <result property="clickPv"    column="click_pv"    />
        <result property="clickUv"    column="click_uv"    />
        <result property="billingClickPv"    column="billing_click_pv"    />
        <result property="billingClickUv"    column="billing_click_uv"    />
        <result property="lpExposurePv"    column="lp_exposure_pv"    />
        <result property="lpExposureUv"    column="lp_exposure_uv"    />
        <result property="lpClickPv"    column="lp_click_pv"    />
        <result property="lpClickUv"    column="lp_click_uv"    />
        <result property="consume"    column="consume"    />
        <result property="takePv"    column="take_pv"    />
        <result property="takeUv"    column="take_uv"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectAdvertHourDataVo">
        select id, cur_date, cur_hour, advert_id, ad_launch_pv, ad_launch_uv, exposure_pv, exposure_uv,
               click_pv, click_uv, billing_click_pv, billing_click_uv, lp_exposure_pv, lp_exposure_uv,
               lp_click_pv, lp_click_uv, consume, take_pv, take_uv, gmt_create, gmt_modified from tb_advert_hour_data
    </sql>

    <select id="selectAdvertHourDataList" parameterType="com.ruoyi.system.entity.datashow.AdvertHourData" resultMap="AdvertHourDataResult">
        <include refid="selectAdvertHourDataVo"/>
        <where>
            <if test="curDate != null "> and cur_date = #{curDate}</if>
            <if test="curHour != null "> and cur_hour = #{curHour}</if>
            <if test="advertId != null"> and advert_id = #{advertId}</if>
            <if test="startDate != null"> and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null"> and cur_date &lt;= #{endDate}</if>
            <if test="advertIds != null and advertIds.size() > 0">
                and advert_id in
                <foreach collection="advertIds" item="advertId" open="(" separator="," close=")">
                    #{advertId}
                </foreach>
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectAdvertHourDataListGroupByDateHour" parameterType="com.ruoyi.system.entity.datashow.AdvertHourData" resultMap="AdvertHourDataResult">
        select cur_date, cur_hour,
            sum(ad_launch_pv) as ad_launch_pv, sum(ad_launch_uv) as ad_launch_uv, sum(exposure_pv) as exposure_pv, sum(exposure_uv) as exposure_uv,
            sum(click_pv) as click_pv, sum(click_uv) as click_uv, sum(billing_click_pv) as billing_click_pv, sum(billing_click_uv) as billing_click_uv,
            sum(lp_exposure_pv) as lp_exposure_pv, sum(lp_exposure_uv) as lp_exposure_uv, sum(take_pv) as take_pv, sum(take_uv) as take_uv,
            sum(lp_click_pv) as lp_click_pv, sum(lp_click_uv) as lp_click_uv, sum(consume) as consume
        from tb_advert_hour_data
        <where>
            <if test="curDate != null "> and cur_date = #{curDate}</if>
            <if test="curHour != null "> and cur_hour = #{curHour}</if>
            <if test="advertId != null"> and advert_id = #{advertId}</if>
            <if test="startDate != null"> and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null"> and cur_date &lt;= #{endDate}</if>
            <if test="advertIds != null and advertIds.size() > 0">
                and advert_id in
                <foreach collection="advertIds" item="advertId" open="(" separator="," close=")">
                    #{advertId}
                </foreach>
            </if>
        </where>
        GROUP BY cur_date, cur_hour
        order by cur_date desc, cur_hour desc
    </select>

    <select id="selectStatisticAdvertHourData" parameterType="com.ruoyi.system.entity.datashow.AdvertHourData" resultMap="AdvertHourDataResult">
        select sum(ad_launch_pv) as ad_launch_pv, sum(ad_launch_uv) as ad_launch_uv, sum(exposure_pv) as exposure_pv, sum(exposure_uv) as exposure_uv,
            sum(click_pv) as click_pv, sum(click_uv) as click_uv, sum(billing_click_pv) as billing_click_pv, sum(billing_click_uv) as billing_click_uv,
            sum(lp_exposure_pv) as lp_exposure_pv, sum(lp_exposure_uv) as lp_exposure_uv, sum(take_pv) as take_pv, sum(take_uv) as take_uv,
            sum(lp_click_pv) as lp_click_pv, sum(lp_click_uv) as lp_click_uv, sum(consume) as consume
        from tb_advert_hour_data
        <where>
            <if test="curDate != null "> and cur_date = #{curDate}</if>
            <if test="curHour != null "> and cur_hour = #{curHour}</if>
            <if test="advertId != null"> and advert_id = #{advertId}</if>
            <if test="startDate != null"> and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null"> and cur_date &lt;= #{endDate}</if>
            <if test="advertIds != null and advertIds.size() > 0">
                and advert_id in
                <foreach collection="advertIds" item="advertId" open="(" separator="," close=")">
                    #{advertId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectBy" parameterType="com.ruoyi.system.entity.datashow.AdvertHourData" resultMap="AdvertHourDataResult">
        <include refid="selectAdvertHourDataVo"/>
        <where>
            <if test="curDate != null "> and cur_date = #{curDate}</if>
            <if test="curHour != null "> and cur_hour = #{curHour}</if>
            <if test="advertId != null"> and advert_id = #{advertId}</if>
        </where>
        limit 1
    </select>

    <insert id="insertAdvertHourData" parameterType="com.ruoyi.system.entity.datashow.AdvertHourData"
            useGeneratedKeys="true" keyProperty="id">
        insert ignore into tb_advert_hour_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="curDate != null">cur_date,</if>
            <if test="curHour != null">cur_hour,</if>
            <if test="advertId != null">advert_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="curDate != null">#{curDate},</if>
            <if test="curHour != null">#{curHour},</if>
            <if test="advertId != null">#{advertId},</if>
         </trim>
    </insert>

    <update id="updateAdvertHourData" parameterType="com.ruoyi.system.entity.datashow.AdvertHourData">
        update tb_advert_hour_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="adLaunchPvAdd != null">ad_launch_pv = ad_launch_pv + #{adLaunchPvAdd},</if>
            <if test="adLaunchPvAdd != null">ad_launch_uv = ad_launch_uv + #{adLaunchUvAdd},</if>
            <if test="exposurePvAdd != null">exposure_pv = exposure_pv + #{exposurePvAdd},</if>
            <if test="exposureUvAdd != null">exposure_uv = exposure_uv + #{exposureUvAdd},</if>
            <if test="clickPvAdd != null">click_pv = click_pv + #{clickPvAdd},</if>
            <if test="clickUvAdd != null">click_uv = click_uv + #{clickUvAdd},</if>
            <if test="billingClickPvAdd != null">billing_click_pv = billing_click_pv + #{billingClickPvAdd},</if>
            <if test="billingClickUvAdd != null">billing_click_uv = billing_click_uv + #{billingClickUvAdd},</if>
            <if test="lpExposurePvAdd != null">lp_exposure_pv = lp_exposure_pv + #{lpExposurePvAdd},</if>
            <if test="lpExposureUvAdd != null">lp_exposure_uv = lp_exposure_uv + #{lpExposureUvAdd},</if>
            <if test="lpClickPvAdd != null">lp_click_pv = lp_click_pv + #{lpClickPvAdd},</if>
            <if test="lpClickUvAdd != null">lp_click_uv = lp_click_uv + #{lpClickUvAdd},</if>
            <if test="consumeAdd != null">consume = consume + #{consumeAdd},</if>
            <if test="takePvAdd != null">take_pv = take_pv + #{takePvAdd},</if>
            <if test="takeUvAdd != null">take_uv = take_uv + #{takeUvAdd},</if>
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>

    <select id="selectLpExposureWarningList" resultType="com.ruoyi.system.bo.advert.AdvertHourWarningDataBo">
        select d.`cur_date` as curDate, d.`cur_hour` as curHour, d.`advert_id` as advertId, a.`advert_name` as advertName,
               a.`landpage_url` as landpageUrl, d.`click_pv` as clickPv, d.`lp_exposure_pv` as lpExposurePv
        from `tb_advert_hour_data` d
        LEFT JOIN `tb_advert` a on d.`advert_id` = a.`id`
        where d.`cur_date` = curdate() and d.`cur_hour` = hour(curtime())  and d.`click_pv` >= 50 and d.`lp_exposure_pv`  / d.`click_pv` &lt; 0.7
        and a.`landpage_url` like '%land/%'
    </select>

    <select id="selectCvrWarningList" resultType="com.ruoyi.system.bo.advert.AdvertHourWarningDataBo">
        select c.`advert_id` as advertId, a.`advert_name` as advertName, a.`landpage_url` as landpageUrl, t_cvr as tCvr, y_cvr as yCvr
        from (
             select t.`advert_id`, t.`cur_hour`, t.`lp_click_pv` / t.`click_pv` as t_cvr , y.`lp_click_pv` / y.`click_pv` as y_cvr
             from tb_advert_hour_data t
             left join tb_advert_hour_data y on t.`advert_id` = y.`advert_id` and t.`cur_hour` = y.`cur_hour`
             where y.`cur_date` = DATE_SUB(CURDATE(), INTERVAL 1 DAY) and y.`cur_hour` = hour(curtime())
               and t.`cur_date` = curdate() and t.`cur_hour` = hour(curtime())
               and t.`click_pv` >= 100) c
        LEFT JOIN `tb_advert` a on c.`advert_id` = a.`id`
        where y_cvr > 0 and y_cvr > t_cvr and (y_cvr - t_cvr) / y_cvr  >= 0.5
    </select>
</mapper>
