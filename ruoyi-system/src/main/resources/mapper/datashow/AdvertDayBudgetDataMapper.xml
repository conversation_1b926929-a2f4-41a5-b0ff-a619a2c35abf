<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.datashow.AdvertDayBudgetDataMapper">

    <resultMap type="com.ruoyi.system.entity.datashow.AdvertDayBudgetData" id="AdvertDayBudgetDataResult">
        <result property="id"    column="id"    />
        <result property="curDate"    column="cur_date"    />
        <result property="advertId"    column="advert_id"    />
        <result property="budget"    column="budget"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectAdvertDayBudgetDataVo">
        select id, cur_date, advert_id, budget, gmt_create, gmt_modified from tb_advert_day_budget_data
    </sql>

    <select id="selectAdvertDayBudgetDataList" parameterType="com.ruoyi.system.entity.datashow.AdvertDayBudgetData" resultMap="AdvertDayBudgetDataResult">
        <include refid="selectAdvertDayBudgetDataVo"/>
        <where>
            <if test="curDate != null "> and cur_date = #{curDate}</if>
            <if test="advertId != null  and advertId != ''"> and advert_id = #{advertId}</if>
            <if test="startDate != null "> and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null "> and cur_date &lt;= #{endDate}</if>
            <if test="advertIds != null and advertIds.size() > 0">
                and advert_id in
                <foreach collection="advertIds" item="advertId" open="(" separator="," close=")">
                    #{advertId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectByDateAndAdvertId" resultMap="AdvertDayBudgetDataResult">
        <include refid="selectAdvertDayBudgetDataVo"/>
        where cur_date = #{curDate} and advert_id = #{advertId}
    </select>

    <insert id="insertAdvertDayBudgetData" parameterType="com.ruoyi.system.entity.datashow.AdvertDayBudgetData"
            useGeneratedKeys="true" keyProperty="id">
        insert ignore into tb_advert_day_budget_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="curDate != null">cur_date,</if>
            <if test="advertId != null">advert_id,</if>
            <if test="budget != null">budget,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="curDate != null">#{curDate},</if>
            <if test="advertId != null">#{advertId},</if>
            <if test="budget != null">#{budget},</if>
         </trim>
    </insert>

    <update id="updateBudget" parameterType="com.ruoyi.system.entity.datashow.AdvertDayBudgetData">
        update tb_advert_day_budget_data
        <trim prefix="SET" suffixOverrides=",">
            budget = #{budget},
            gmt_modified = now()
        </trim>
        where cur_date = #{curDate} and advert_id = #{advertId}
    </update>
</mapper>
