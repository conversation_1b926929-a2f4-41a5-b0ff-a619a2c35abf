<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.datashow.SlotConvOpenDataMapper">

    <resultMap type="com.ruoyi.system.entity.datashow.SlotConvOpenDataEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="curDate" column="cur_date"/>
            <result property="slotId" column="slot_id"/>
            <result property="conv" column="conv"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            cur_date,
            slot_id,
            conv,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" parameterType="com.ruoyi.system.entity.datashow.SlotConvOpenDataEntity" useGeneratedKeys="true" keyProperty="id">
        INSERT IGNORE INTO tb_slot_conv_open_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="curDate != null">
                    cur_date,
                </if>
                <if test="slotId != null">
                    slot_id,
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="curDate != null">
                    #{curDate},
                </if>
                <if test="slotId != null">
                    #{slotId},
                </if>
        </trim>
    </insert>

    <update id="incr" parameterType="Long">
        UPDATE tb_slot_conv_open_data
        <set>
            conv = conv + 1
        </set>
        WHERE id = #{id}
    </update>

    <select id="selectBy" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_slot_conv_open_data
        WHERE slot_id = #{slotId} and cur_date = #{curDate}
    </select>

    <select id="selectListBySlotIdsAndDate" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_slot_conv_open_data
        <where>
            cur_date = #{curDate}
            <if test="slotIds != null and slotIds.size() > 0">
                and slot_id in
                <foreach collection="slotIds" item="slotId" open="(" separator="," close=")">
                    #{slotId}
                </foreach>
            </if>
        </where>
    </select>

</mapper>
