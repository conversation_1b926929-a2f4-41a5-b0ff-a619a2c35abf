<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.datashow.AdvertSlotDayDataMapper">

    <resultMap type="com.ruoyi.system.entity.datashow.AdvertSlotDayData" id="AdvertSlotDayDataResult">
        <result property="id"    column="id"    />
        <result property="curDate"    column="cur_date"    />
        <result property="advertId"    column="advert_id"    />
        <result property="slotId"    column="slot_id"    />
        <result property="adLaunchPv"    column="ad_launch_pv"    />
        <result property="adLaunchUv"    column="ad_launch_uv"    />
        <result property="exposurePv"    column="exposure_pv"    />
        <result property="exposureUv"    column="exposure_uv"    />
        <result property="clickPv"    column="click_pv"    />
        <result property="clickUv"    column="click_uv"    />
        <result property="billingClickPv"    column="billing_click_pv"    />
        <result property="billingClickUv"    column="billing_click_uv"    />
        <result property="lpExposurePv"    column="lp_exposure_pv"    />
        <result property="lpExposureUv"    column="lp_exposure_uv"    />
        <result property="lpClickPv"    column="lp_click_pv"    />
        <result property="lpClickUv"    column="lp_click_uv"    />
        <result property="consume"    column="consume"    />
        <result property="takePv"    column="take_pv"    />
        <result property="takeUv"    column="take_uv"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectAdvertSlotDayDataVo">
        select id, cur_date, advert_id, slot_id, ad_launch_pv, ad_launch_uv, exposure_pv, exposure_uv,
               click_pv, click_uv, billing_click_pv, billing_click_uv, lp_exposure_pv, lp_exposure_uv,
               lp_click_pv, lp_click_uv, consume, take_pv, take_uv, gmt_create, gmt_modified from tb_advert_slot_day_data
    </sql>

    <select id="selectAdvertSlotDayDataList" parameterType="com.ruoyi.system.entity.datashow.AdvertSlotDayData" resultMap="AdvertSlotDayDataResult">
        <include refid="selectAdvertSlotDayDataVo"/>
        <where>
            <if test="curDate != null "> and cur_date = #{curDate}</if>
            <if test="advertId != null"> and advert_id = #{advertId}</if>
            <if test="slotId != null"> and slot_id = #{slotId}</if>
            <if test="startDate != null "> and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null "> and cur_date &lt;= #{endDate}</if>
            <if test="slotIds != null and slotIds.size()>0">
                and slot_id in
                <foreach collection="slotIds" item="slotId" open="(" separator="," close=")">
                    #{slotId}
                </foreach>
            </if>
            <if test="advertIds != null and advertIds.size()>0">
                and advert_id in
                <foreach collection="advertIds" item="advertId" open="(" separator="," close=")">
                    #{advertId}
                </foreach>
            </if>
        </where>
        order by cur_date desc, ad_launch_pv desc, id desc
    </select>

    <select id="selectStatisticAdvertSlotDayData" parameterType="com.ruoyi.system.entity.datashow.AdvertSlotDayData" resultMap="AdvertSlotDayDataResult">
        select sum(ad_launch_pv) as ad_launch_pv, sum(ad_launch_uv) as ad_launch_uv, sum(exposure_pv) as exposure_pv, sum(exposure_uv) as exposure_uv,
            sum(click_pv) as click_pv, sum(click_uv) as click_uv, sum(billing_click_pv) as billing_click_pv, sum(billing_click_uv) as billing_click_uv,
            sum(lp_exposure_pv) as lp_exposure_pv, sum(lp_exposure_uv) as lp_exposure_uv, sum(take_pv) as take_pv, sum(take_uv) as take_uv,
            sum(lp_click_pv) as lp_click_pv, sum(lp_click_uv) as lp_click_uv, sum(consume) as consume
        from tb_advert_slot_day_data
        <where>
            <if test="curDate != null "> and cur_date = #{curDate}</if>
            <if test="advertId != null"> and advert_id = #{advertId}</if>
            <if test="slotId != null"> and slot_id = #{slotId}</if>
            <if test="startDate != null "> and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null "> and cur_date &lt;= #{endDate}</if>
            <if test="slotIds != null and slotIds.size()>0">
                and slot_id in
                <foreach collection="slotIds" item="slotId" open="(" separator="," close=")">
                    #{slotId}
                </foreach>
            </if>
            <if test="advertIds != null and advertIds.size()>0">
                and advert_id in
                <foreach collection="advertIds" item="advertId" open="(" separator="," close=")">
                    #{advertId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectSumLpDataBySlotIdsAndDate" resultMap="AdvertSlotDayDataResult">
        select cur_date, slot_id, sum(lp_exposure_pv) as lp_exposure_pv, sum(lp_exposure_uv) as lp_exposure_uv,
            sum(lp_click_pv) as lp_click_pv, sum(lp_click_uv) as lp_click_uv
        from tb_advert_slot_day_data
        <where>
            <if test="slotIds != null  and slotIds.size > 0">
                slot_id in
                <foreach collection="slotIds" separator="," item="slotId" open="(" close=")">
                    #{slotId}
                </foreach>
            </if>
            <if test="startDate != null">
                and cur_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                and cur_date &lt;= #{endDate}
            </if>
        </where>
        group by cur_date, slot_id
    </select>

    <select id="sumLpDataBySlotIdsAndDate" resultMap="AdvertSlotDayDataResult">
        select sum(lp_exposure_pv) as lp_exposure_pv, sum(lp_exposure_uv) as lp_exposure_uv,
            sum(lp_click_pv) as lp_click_pv, sum(lp_click_uv) as lp_click_uv
        from tb_advert_slot_day_data
        <where>
            <if test="slotIds != null  and slotIds.size > 0">
                slot_id in
                <foreach collection="slotIds" separator="," item="slotId" open="(" close=")">
                    #{slotId}
                </foreach>
            </if>
            <if test="startDate != null">
                and cur_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                and cur_date &lt;= #{endDate}
            </if>
        </where>
    </select>

    <select id="selectBy" parameterType="com.ruoyi.system.entity.datashow.AdvertSlotDayData" resultMap="AdvertSlotDayDataResult">
        <include refid="selectAdvertSlotDayDataVo"/>
        <where>
            <if test="curDate != null "> and cur_date = #{curDate}</if>
            <if test="advertId != null"> and advert_id = #{advertId}</if>
            <if test="slotId != null"> and slot_id = #{slotId}</if>
        </where>
        limit 1
    </select>

    <insert id="insertAdvertSlotDayData" parameterType="com.ruoyi.system.entity.datashow.AdvertSlotDayData"
            useGeneratedKeys="true" keyProperty="id">
        insert ignore into tb_advert_slot_day_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="curDate != null">cur_date,</if>
            <if test="advertId != null">advert_id,</if>
            <if test="slotId != null">slot_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="curDate != null">#{curDate},</if>
            <if test="advertId != null">#{advertId},</if>
            <if test="slotId != null">#{slotId},</if>
         </trim>
    </insert>

    <update id="updateAdvertSlotDayData" parameterType="com.ruoyi.system.entity.datashow.AdvertSlotDayData">
        update tb_advert_slot_day_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="adLaunchPvAdd != null">ad_launch_pv = ad_launch_pv + #{adLaunchPvAdd},</if>
            <if test="adLaunchPvAdd != null">ad_launch_uv = ad_launch_uv + #{adLaunchUvAdd},</if>
            <if test="exposurePvAdd != null">exposure_pv = exposure_pv + #{exposurePvAdd},</if>
            <if test="exposureUvAdd != null">exposure_uv = exposure_uv + #{exposureUvAdd},</if>
            <if test="clickPvAdd != null">click_pv = click_pv + #{clickPvAdd},</if>
            <if test="clickUvAdd != null">click_uv = click_uv + #{clickUvAdd},</if>
            <if test="billingClickPvAdd != null">billing_click_pv = billing_click_pv + #{billingClickPvAdd},</if>
            <if test="billingClickUvAdd != null">billing_click_uv = billing_click_uv + #{billingClickUvAdd},</if>
            <if test="lpExposurePvAdd != null">lp_exposure_pv = lp_exposure_pv + #{lpExposurePvAdd},</if>
            <if test="lpExposureUvAdd != null">lp_exposure_uv = lp_exposure_uv + #{lpExposureUvAdd},</if>
            <if test="lpClickPvAdd != null">lp_click_pv = lp_click_pv + #{lpClickPvAdd},</if>
            <if test="lpClickUvAdd != null">lp_click_uv = lp_click_uv + #{lpClickUvAdd},</if>
            <if test="consumeAdd != null">consume = consume + #{consumeAdd},</if>
            <if test="takePvAdd != null">take_pv = take_pv + #{takePvAdd},</if>
            <if test="takeUvAdd != null">take_uv = take_uv + #{takeUvAdd},</if>
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>

    <select id="selectTodaySlotAdvertCpcConsumeMap" resultMap="AdvertSlotDayDataResult">
        select d.slot_id as slot_id, sum(d.consume) as consume
        from tb_advert_slot_day_data  d
        LEFT JOIN tb_advert_cost c on d.`cur_date` = c.`cur_date` and d.`advert_id` = c.`advert_id`
        <where>
            d.`cur_date` = curdate()
            and c.`billing_type` = 1
            <if test="slotIds != null  and slotIds.size > 0">
                and slot_id in
                <foreach collection="slotIds" separator="," item="slotId" open="(" close=")">
                    #{slotId}
                </foreach>
            </if>
        </where>
        group by slot_id
    </select>

    <select id="sumTodaySlotAdvertCpcConsume" resultType="Long">
        select ifnull(sum(d.consume), 0)
        from tb_advert_slot_day_data  d
        LEFT JOIN tb_advert_cost c on d.`cur_date` = c.`cur_date` and d.`advert_id` = c.`advert_id`
        <where>
            d.`cur_date` = curdate()
            and c.`billing_type` = 1
            <if test="slotIds != null  and slotIds.size > 0">
                and slot_id in
                <foreach collection="slotIds" separator="," item="slotId" open="(" close=")">
                    #{slotId}
                </foreach>
            </if>
        </where>
    </select>
</mapper>
