<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.datashow.AdvertAppDayDataMapper">

    <resultMap type="com.ruoyi.system.entity.datashow.AdvertAppDayData" id="AdvertAppDayDataResult">
        <result property="id"    column="id"    />
        <result property="curDate"    column="cur_date"    />
        <result property="advertId"    column="advert_id"    />
        <result property="appId"    column="app_id"    />
        <result property="adLaunchPv"    column="ad_launch_pv"    />
        <result property="adLaunchUv"    column="ad_launch_uv"    />
        <result property="exposurePv"    column="exposure_pv"    />
        <result property="exposureUv"    column="exposure_uv"    />
        <result property="clickPv"    column="click_pv"    />
        <result property="clickUv"    column="click_uv"    />
        <result property="lpExposurePv"    column="lp_exposure_pv"    />
        <result property="lpExposureUv"    column="lp_exposure_uv"    />
        <result property="lpClickPv"    column="lp_click_pv"    />
        <result property="lpClickUv"    column="lp_click_uv"    />
        <result property="consume"    column="consume"    />
        <result property="takePv"    column="take_pv"    />
        <result property="takeUv"    column="take_uv"    />
        <result property="gmtCreate"    column="gmt_create"    />
        <result property="gmtModified"    column="gmt_modified"    />
    </resultMap>

    <sql id="selectAdvertAppDayDataVo">
        select id, cur_date, advert_id, app_id, ad_launch_pv, ad_launch_uv, exposure_pv, exposure_uv,
               click_pv, click_uv, lp_exposure_pv, lp_exposure_uv,
               lp_click_pv, lp_click_uv, consume, take_pv, take_uv, gmt_create, gmt_modified from tb_advert_app_day_data
    </sql>

    <select id="selectAdvertAppDayDataList" parameterType="com.ruoyi.system.entity.datashow.AdvertAppDayData" resultMap="AdvertAppDayDataResult">
        <include refid="selectAdvertAppDayDataVo"/>
        <where>
            <if test="curDate != null "> and cur_date = #{curDate}</if>
            <if test="advertId != null"> and advert_id = #{advertId}</if>
            <if test="appId != null"> and app_id = #{appId}</if>
            <if test="startDate != null "> and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null "> and cur_date &lt;= #{endDate}</if>
            <if test="appIds != null and appIds.size()>0">
                and app_id in
                <foreach collection="appIds" item="appId" open="(" separator="," close=")">
                    #{appId}
                </foreach>
            </if>
        </where>
        order by cur_date desc, ad_launch_pv desc, id desc
    </select>

    <select id="selectStatisticAdvertAppDayData" parameterType="com.ruoyi.system.entity.datashow.AdvertAppDayData" resultMap="AdvertAppDayDataResult">
        select sum(ad_launch_pv) as ad_launch_pv, sum(ad_launch_uv) as ad_launch_uv, sum(exposure_pv) as exposure_pv, sum(exposure_uv) as exposure_uv,
            sum(click_pv) as click_pv, sum(click_uv) as click_uv, sum(lp_exposure_pv) as lp_exposure_pv, sum(lp_exposure_uv) as lp_exposure_uv,
            sum(take_pv) as take_pv, sum(take_uv) as take_uv, sum(lp_click_pv) as lp_click_pv, sum(lp_click_uv) as lp_click_uv, sum(consume) as consume
        from tb_advert_app_day_data
        <where>
            <if test="curDate != null "> and cur_date = #{curDate}</if>
            <if test="advertId != null"> and advert_id = #{advertId}</if>
            <if test="appId != null"> and app_id = #{appId}</if>
            <if test="startDate != null "> and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null "> and cur_date &lt;= #{endDate}</if>
            <if test="appIds != null and appIds.size()>0">
                and app_id in
                <foreach collection="appIds" item="appId" open="(" separator="," close=")">
                    #{appId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectBy" parameterType="com.ruoyi.system.entity.datashow.AdvertAppDayData" resultMap="AdvertAppDayDataResult">
        <include refid="selectAdvertAppDayDataVo"/>
        <where>
            <if test="curDate != null "> and cur_date = #{curDate}</if>
            <if test="advertId != null"> and advert_id = #{advertId}</if>
            <if test="appId != null"> and app_id = #{appId}</if>
        </where>
        limit 1
    </select>

    <insert id="insertAdvertAppDayData" parameterType="com.ruoyi.system.entity.datashow.AdvertAppDayData"
            useGeneratedKeys="true" keyProperty="id">
        insert ignore into tb_advert_app_day_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="curDate != null">cur_date,</if>
            <if test="advertId != null">advert_id,</if>
            <if test="appId != null">app_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="curDate != null">#{curDate},</if>
            <if test="advertId != null">#{advertId},</if>
            <if test="appId != null">#{appId},</if>
         </trim>
    </insert>

    <update id="updateAdvertAppDayData" parameterType="com.ruoyi.system.entity.datashow.AdvertAppDayData">
        update tb_advert_app_day_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="adLaunchPvAdd != null">ad_launch_pv = ad_launch_pv + #{adLaunchPvAdd},</if>
            <if test="adLaunchPvAdd != null">ad_launch_uv = ad_launch_uv + #{adLaunchUvAdd},</if>
            <if test="exposurePvAdd != null">exposure_pv = exposure_pv + #{exposurePvAdd},</if>
            <if test="exposureUvAdd != null">exposure_uv = exposure_uv + #{exposureUvAdd},</if>
            <if test="clickPvAdd != null">click_pv = click_pv + #{clickPvAdd},</if>
            <if test="clickUvAdd != null">click_uv = click_uv + #{clickUvAdd},</if>
            <if test="lpExposurePvAdd != null">lp_exposure_pv = lp_exposure_pv + #{lpExposurePvAdd},</if>
            <if test="lpExposureUvAdd != null">lp_exposure_uv = lp_exposure_uv + #{lpExposureUvAdd},</if>
            <if test="lpClickPvAdd != null">lp_click_pv = lp_click_pv + #{lpClickPvAdd},</if>
            <if test="lpClickUvAdd != null">lp_click_uv = lp_click_uv + #{lpClickUvAdd},</if>
            <if test="consumeAdd != null">consume = consume + #{consumeAdd},</if>
            <if test="takePvAdd != null">take_pv = take_pv + #{takePvAdd},</if>
            <if test="takeUvAdd != null">take_uv = take_uv + #{takeUvAdd},</if>
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>
</mapper>
