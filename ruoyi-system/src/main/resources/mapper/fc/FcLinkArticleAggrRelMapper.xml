<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.fc.FcLinkArticleAggrRelMapper">

    <resultMap type="com.ruoyi.system.entity.fc.FcLinkArticleAggrRelEntity" id="FcLinkArticleAggrRelMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="fcLinkKey" column="fc_link_key" jdbcType="VARCHAR"/>
        <result property="articleAggrLinkId" column="article_aggr_link_id" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="operatorId" column="operator_id" jdbcType="INTEGER"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
        <result property="creatorId" column="creator_id" jdbcType="INTEGER"/>
    </resultMap>





    <select id="selectAggrIdsByFcLinkKey" resultType="java.lang.Long">
        SELECT
            article_aggr_link_id
        FROM
            tb_fc_link_article_aggr_rel
        WHERE
            fc_link_key = #{fcLinkKey}
            AND  status = 0
    </select>
    <select id="selectAggrIdsByFcLinkKeys" resultType="java.lang.Long">
        SELECT
            DISTINCT article_aggr_link_id
        FROM
            tb_fc_link_article_aggr_rel
        WHERE
            1=1
            <if test="fcLinkKeys != null and fcLinkKeys.size() > 0">
                AND fc_link_key IN
                <foreach item="fcLinkKey" collection="fcLinkKeys" separator="," open="(" close=")">
                    #{fcLinkKey}
                </foreach>
            </if>
            AND status = 0
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into tb_fc_link_article_aggr_rel(fc_link_key, article_aggr_link_id, status, operator_id,
                                                gmt_create, gmt_modified, creator_id)
        values (#{fcLinkKey}, #{articleAggrLinkId}, #{status}, #{operatorId}, #{gmtCreate}, #{gmtModified},
                #{creatorId})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into tb_fc_link_article_aggr_rel(fc_link_key, article_aggr_link_id, operator_id,creator_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.fcLinkKey}, #{entity.articleAggrLinkId}, #{entity.operatorId}, #{entity.creatorId})
        </foreach>
    </insert>


    <select id="existsByArticleAggrLinkId" resultType="java.lang.Integer">
        SELECT
            1
        FROM
            tb_fc_link_article_aggr_rel
        WHERE
            article_aggr_link_id = #{articleAggrLinkId} AND status = 0
        LIMIT 1
    </select>

    <update id="updateStatusByFcLinkKeyAndAggrId">
        UPDATE
            tb_fc_link_article_aggr_rel
        SET
            status = #{status}
        WHERE
            fc_link_key = #{fcLinkKey}
            AND article_aggr_link_id = #{aggrId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from tb_fc_link_article_aggr_rel
        where id = #{id}
    </delete>

    <!-- 根据文章id获取关联的丰巢链接key -->
    <select id="selectFcLinkKeysByArticleId" resultType="java.lang.String">
        SELECT
            DISTINCT rel.fc_link_key
        FROM
            tb_article a
            INNER JOIN tb_fc_link_article_aggr_rel rel ON a.link_id = rel.article_aggr_link_id
        WHERE
            a.id = #{articleId}
            AND rel.status = 0
    </select>

    <!-- 根据文章聚合链接ID获取关联的丰巢链接名称列表 -->
    <select id="selectFcLinkNamesByArticleAggrLinkId" resultType="java.lang.String">
        SELECT
            DISTINCT fl.name
        FROM
            tb_fc_link_article_aggr_rel rel
            INNER JOIN tb_fc_link fl ON rel.fc_link_key = fl.key
        WHERE
            rel.article_aggr_link_id = #{articleAggrLinkId}
            AND rel.status = 0
            AND fl.status = 0
    </select>

</mapper>

