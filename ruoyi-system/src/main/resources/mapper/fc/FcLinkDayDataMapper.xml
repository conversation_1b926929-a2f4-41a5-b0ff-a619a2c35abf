<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.fc.FcLinkDayDataMapper">

    <resultMap type="com.ruoyi.system.entity.fc.FcLinkDayDataEntity" id="FcLinkDayDataResult">
        <id property="id" column="id"/>
        <result property="fcLinkKey" column="fc_link_key"/>
        <result property="curDate" column="cur_date"/>
        <result property="uv" column="uv"/>
        <result property="pv" column="pv"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="selectFcLinkDayDataVo">
        SELECT id, fc_link_key, cur_date, uv, pv, gmt_create, gmt_modified
        FROM tb_fc_link_day_data
    </sql>

    <select id="selectFcLinkDayData" resultMap="FcLinkDayDataResult">
        <include refid="selectFcLinkDayDataVo"/>
        WHERE cur_date = #{curDate}
        AND fc_link_key = #{fcLinkKey}
    </select>

    <insert id="insertFcLinkDayData" parameterType="com.ruoyi.system.entity.fc.FcLinkDayDataEntity" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tb_fc_link_day_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fcLinkKey != null">fc_link_key,</if>
            <if test="curDate != null">cur_date,</if>
            <if test="uv != null">uv,</if>
            <if test="pv != null">pv,</if>
            gmt_create
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fcLinkKey != null">#{fcLinkKey},</if>
            <if test="curDate != null">#{curDate},</if>
            <if test="uv != null">#{uv},</if>
            <if test="pv != null">#{pv},</if>
            sysdate()
        </trim>
    </insert>

    <update id="updateFcLinkDayData" parameterType="com.ruoyi.system.entity.fc.FcLinkDayDataEntity">
        UPDATE tb_fc_link_day_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="uv != null">uv = #{uv},</if>
            <if test="pv != null">pv = #{pv},</if>
        </trim>
        WHERE fc_link_key = #{fcLinkKey} AND cur_date = #{curDate}
    </update>

</mapper> 