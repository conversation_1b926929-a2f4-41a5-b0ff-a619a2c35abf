<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.fc.FcLinkMapper">

    <resultMap type="com.ruoyi.system.entity.fc.FcLinkEntity" id="FcLinkMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="key" column="key" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="url" column="url" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="operatorId" column="operator_id" jdbcType="INTEGER"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
        <result property="creatorId" column="creator_id" jdbcType="INTEGER"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="FcLinkMap">
        select id, `key`, name, url, status, operator_id, gmt_create, gmt_modified, creator_id
        from tb_fc_link
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="FcLinkMap">
        select
        id, `key`, name, url, status, operator_id, gmt_create, gmt_modified, creator_id
        from tb_fc_link
        <where>
            <if test="id != null">
                and id =
                #{id}
            </if>
            <if test="key != null and key != ''">
                and `key` =
                #{key}
            </if>
            <if test="name != null and name != ''">
                and name =
                #{name}
            </if>
            <if test="url != null and url != ''">
                and url =
                #{url}
            </if>
            <if test="status != null">
                and status =
                #{status}
            </if>
            <if test="operatorId != null">
                and operator_id =
                #{operatorId}
            </if>
            <if test="gmtCreate != null">
                and gmt_create =
                #{gmtCreate}
            </if>
            <if test="gmtModified != null">
                and gmt_modified =
                #{gmtModified}
            </if>
            <if test="creatorId != null">
                and creator_id =
                #{creatorId}
            </if>
        </where>
        limit #{pageable.offset}, #{pageable.pageSize}
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from tb_fc_link
        <where>
            <if test="id != null">
                and id =
                #{id}
            </if>
            <if test="key != null and key != ''">
                and key =
                #{key}
            </if>
            <if test="name != null and name != ''">
                and name =
                #{name}
            </if>
            <if test="url != null and url != ''">
                and url =
                #{url}
            </if>
            <if test="status != null">
                and status =
                #{status}
            </if>
            <if test="operatorId != null">
                and operator_id =
                #{operatorId}
            </if>
            <if test="gmtCreate != null">
                and gmt_create =
                #{gmtCreate}
            </if>
            <if test="gmtModified != null">
                and gmt_modified =
                #{gmtModified}
            </if>
            <if test="creatorId != null">
                and creator_id =
                #{creatorId}
            </if>
        </where>
    </select>
    <select id="selectTotalKey" resultType="java.lang.String">
        SELECT
            `key`
        FROM
            tb_fc_link
    </select>
    <select id="getFcLinkKeysByStatus" resultType="java.lang.String">
        SELECT
            `key`
        FROM
            tb_fc_link
        WHERE
            1=1
            <if test="status != null">
                AND status = #{status}
            </if>
    </select>
    <select id="getArticleByStatusAndTime" resultType="com.ruoyi.system.vo.fc.FcArticleVo">
        SELECT DISTINCT
            c.id AS articleId,
            c.gmt_create AS gmtCreate,
            c.name AS articleName,
            c.jump_url AS articleUrl,
            c.fc_check_status AS fcCheckStatus,
            c.fc_sync_status AS fcSyncStatus,
            c.fc_reject_reason AS rejectReason,
            c.fc_check_time AS fcCheckTime,
            c.fc_sync_fail_reason AS fcSyncFailReason
        FROM
            tb_fc_link AS a
            INNER JOIN tb_fc_link_article_aggr_rel AS b ON a.key  = b.fc_link_key
            INNER JOIN tb_article AS c ON b.article_aggr_link_id = c.link_id
        WHERE
            a.status = 0
            AND b.status = 0
            AND c.status = 0
            <if test="startTime != null">
                AND c.gmt_create &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND c.gmt_create &lt;= #{endTime}
            </if>
            <if test="fcCheckStatus != null">
                AND c.fc_check_status = #{fcCheckStatus}
            </if>
            <if test="fcSyncStatus != null">
                AND c.fc_sync_status = #{fcSyncStatus}
            </if>
        ORDER BY
            c.gmt_create DESC
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into tb_fc_link(`key`, name, url, operator_id, creator_id)
        values (#{key}, #{name}, #{url}, #{operatorId}, #{creatorId})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into tb_fc_link(`key`, name, url, status, operator_id, gmt_create, gmt_modified, creator_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.key}, #{entity.name}, #{entity.url}, #{entity.status}, #{entity.operatorId}, #{entity.gmtCreate},
            #{entity.gmtModified}, #{entity.creatorId})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into tb_fc_link(`key`, name, url, status, operator_id, gmt_create, gmt_modified, creator_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.key}, #{entity.name}, #{entity.url}, #{entity.status}, #{entity.operatorId}, #{entity.gmtCreate},
            #{entity.gmtModified}, #{entity.creatorId})
        </foreach>
        on duplicate key update
        key = values(key),
        name = values(name),
        url = values(url),
        status = values(status),
        operator_id = values(operator_id),
        gmt_create = values(gmt_create),
        gmt_modified = values(gmt_modified),
        creator_id = values(creator_id)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update tb_fc_link
        <set>
            <if test="key != null and key != ''">
                key =
                #{key},
            </if>
            <if test="name != null and name != ''">
                name =
                #{name},
            </if>
            <if test="url != null and url != ''">
                url =
                #{url},
            </if>
            <if test="status != null">
                status =
                #{status},
            </if>
            <if test="operatorId != null">
                operator_id =
                #{operatorId},
            </if>
            <if test="gmtCreate != null">
                gmt_create =
                #{gmtCreate},
            </if>
            <if test="gmtModified != null">
                gmt_modified =
                #{gmtModified},
            </if>
            <if test="creatorId != null">
                creator_id =
                #{creatorId},
            </if>
        </set>
        where id = #{id}
    </update>
    <update id="updateStatusById">
        UPDATE
            tb_fc_link
        SET
            status = #{status}
        WHERE
            id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from tb_fc_link
        where id = #{id}
    </delete>

    <select id="getFcLinkListOrderByIdDesc" resultType="com.ruoyi.system.vo.fc.FcLinkVo">
        SELECT 
            id as fcLinkId,
            `key` as fcLinkKey,
            url as fcLinkUrl,
            name as fcLinkName
        FROM 
            tb_fc_link
        WHERE
            1=1
            <if test="searchKey != null and searchKey != ''">
                AND (
                    name LIKE CONCAT('%', #{searchKey}, '%')
                    OR url LIKE CONCAT('%', #{searchKey}, '%')
                )
            </if>
            AND status = 0
        ORDER BY 
            id DESC
    </select>
    <select id="getFcLinkInfoByArticleAggrLinkIdList" resultType="com.ruoyi.system.vo.fc.FcLinkVo">
        SELECT
            l.id AS fcLinkId,
            l.name AS fcLinkName,
            l.`key` AS fcLinkKey,
            l.url AS fcLinkUrl,
            l.gmt_create AS gmtCreate,
            l.gmt_modified AS gmtModified,
            l.creator_id as creatorId,
            l.operator_id as operatorId,
            COUNT(a.id) AS articleCount,
            SUM(CASE WHEN a.fc_check_status = 2 THEN 1 ELSE 0 END) AS fcCheckRejectCount,
            SUM(CASE WHEN a.fc_check_status = 1 THEN 1 ELSE 0 END) AS fcCheckPassCount,
            SUM(CASE WHEN a.fc_sync_status = 1 THEN 1 ELSE 0 END) AS fcSyncCount,
            SUM(CASE
                    WHEN a.fc_check_status = 1 AND a.fc_sync_status = 1
                        THEN a.target_request_pv
                    ELSE 0
                END) AS totalTargetRequestPv,
            SUM(CASE
                    WHEN
                        a.status = 0
                            AND a.weight > 0
                            AND a.target_request_pv > 0
                            AND (a.target_request_pv + IFNULL(a.compensate_request_pv, 0)) > IFNULL(hd.today_request_pv, 0)
                            AND a.gmt_create >= CURDATE()
                            AND a.fc_check_status = 1
                            AND a.fc_sync_status = 1
                        THEN 1
                    ELSE 0
                END) AS articleOnLineCount
        FROM
            tb_fc_link l
                LEFT JOIN tb_fc_link_article_aggr_rel rel
                          ON l.`key` = rel.fc_link_key AND rel.status = 0
                LEFT JOIN tb_article a
                          ON rel.article_aggr_link_id = a.link_id AND a.status = 0
                LEFT JOIN (
                SELECT
                    article_id,
                    SUM(request_pv) AS today_request_pv
                FROM
                    tb_article_hour_data
                WHERE
                    cur_date = CURDATE()
                    AND `link_id` in
                    <foreach collection="articleAggrLinkIdList" item="linkId" open="(" separator="," close=")">
                        #{linkId}
                    </foreach>
                GROUP BY
                    article_id
            ) hd ON a.id = hd.article_id
        WHERE
            l.status = 0
            AND l.id IN
            <foreach item="item" collection="fcLinkIdList" separator="," open="(" close=")" index="index">
                #{item}
            </foreach>
        GROUP BY l.id
        ORDER BY l.id DESC
    </select>

    <!--根据key查询丰巢链接-->
    <select id="queryByKey" resultMap="FcLinkMap">
        select id, `key`, name, url, status, operator_id, gmt_create, gmt_modified, creator_id
        from tb_fc_link
        where `key` = #{key} and status = 0
    </select>

</mapper>

