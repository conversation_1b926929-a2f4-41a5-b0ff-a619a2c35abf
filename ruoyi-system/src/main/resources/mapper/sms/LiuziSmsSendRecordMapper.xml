<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.sms.LiuziSmsSendRecordMapper">

    <resultMap type="com.ruoyi.system.entity.sms.LiuziSmsSendRecordEntity" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="liuziRecordId" column="liuzi_record_id"/>
        <result property="type" column="type"/>
        <result property="tpId" column="tp_id"/>
        <result property="msgId" column="msg_id"/>
        <result property="content" column="content"/>
        <result property="result" column="result"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
            liuzi_record_id,
            type,
            tp_id,
            msg_id,
            content,
            result,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.sms.LiuziSmsSendRecordEntity">
        INSERT INTO tb_liuzi_sms_send_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="liuziRecordId != null">
                liuzi_record_id,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="tpId != null">
                tp_id,
            </if>
            <if test="msgId != null">
                msg_id,
            </if>
            <if test="content != null">
                content,
            </if>
            <if test="result != null">
                result
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="liuziRecordId != null">
                #{liuziRecordId},
            </if>
            <if test="type != null">
                #{type},
            </if>
            <if test="tpId != null">
                #{tpId},
            </if>
            <if test="msgId != null">
                #{msgId},
            </if>
            <if test="content != null">
                #{content},
            </if>
            <if test="result != null">
                #{result}
            </if>
        </trim>
    </insert>
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.sms.LiuziSmsSendRecordEntity">
        INSERT INTO tb_liuzi_sms_send_record(`liuzi_record_id`,`type`,`tp_id`,`msg_id`,`content`,`result`)
        values
        <foreach collection="entities" separator="," item="entity">
            (#{entity.liuziRecordId},#{entity.type},#{entity.tpId},#{entity.msgId},#{entity.content},#{entity.result})
        </foreach>
    </insert>

    <delete id="deleteById">
        DELETE
        FROM tb_liuzi_sms_send_record
        WHERE id = #{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.sms.LiuziSmsSendRecordEntity">
        UPDATE tb_liuzi_sms_send_record
        <set>
            <if test="liuziRecordId != null">
                liuzi_record_id = #{liuziRecordId},
            </if>
            <if test="tpId != null">
                tp_id = #{tpId},
            </if>
            <if test="content != null">
                content = #{content},
            </if>
            <if test="result != null">
                result = #{result},
            </if>
            <if test="msgId != null">
                msg_id = #{msgId},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_liuzi_sms_send_record
        WHERE id = #{id}
    </select>
    <select id="selectListByLiuziRecordId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_liuzi_sms_send_record
        WHERE liuzi_record_id = #{liuziRecordId}
        order by id desc
    </select>

    <select id="selectListByLiuziRecordIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_liuzi_sms_send_record
        WHERE liuzi_record_id in
        <foreach collection="liuziRecordIds" open="(" close=")" separator="," item="recordId">
            #{recordId}
        </foreach>
    </select>

    <select id="countByLiuziRecordIds" resultType="com.ruoyi.system.entity.sms.LiuziSmsSendRecordCountBO">
        SELECT count(*) as smsCount, liuzi_record_id as liuziRecordId
        FROM tb_liuzi_sms_send_record
        WHERE liuzi_record_id in
        <foreach collection="liuziRecordIds" open="(" close=")" separator="," item="recordId">
            #{recordId}
        </foreach>
        group by liuzi_record_id
    </select>

    <update id="updateSmsStatusById">
        update tb_liuzi_sms_send_record
        set result = #{status}
        where id = #{id}
    </update>
    <update id="updateSmsStatusByMsgIdAndType">
        update tb_liuzi_sms_send_record
        set result = #{status}
        where type = #{type} and msg_id = #{msgId}
    </update>
    <select id="selectByTypeAndMsgId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_liuzi_sms_send_record
        where type = #{type} and msg_id = #{msgId} limit 1
    </select>

</mapper>
