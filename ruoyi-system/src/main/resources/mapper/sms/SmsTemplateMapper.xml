<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.sms.SmsTemplateMapper">

    <resultMap type="com.ruoyi.system.entity.sms.SmsTemplateEntity" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="tpId" column="tp_id"/>
        <result property="type" column="type"/>
        <result property="content" column="content"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        type,
        tp_id,
        content,
        gmt_create,
        gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.sms.SmsTemplateEntity">
        INSERT INTO tb_sms_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tpId != null">
                tp_id,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="content != null">
                content
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tpId != null">
                #{tpId},
            </if>
            <if test="type != null">
                #{type},
            </if>
            <if test="content != null">
                #{content}
            </if>
        </trim>
    </insert>

    <delete id="deleteById">
        DELETE
        FROM tb_sms_template
        WHERE id = #{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.sms.SmsTemplateEntity">
        UPDATE tb_sms_template
        <set>
            <if test="tpId != null">
                tp_id = #{tpId},
            </if>
            <if test="content != null">
                content = #{content},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_sms_template
        WHERE id = #{id}
    </select>

    <select id="selectAllList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_sms_template
        order by id desc
    </select>

    <select id="selectListByContent" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_sms_template
        where content = #{content}
    </select>

    <select id="selectByTypeAndTpId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_sms_template
        where type = #{type} and tp_id = #{tpId}
    </select>

    <insert id="batchInsertOrUpdate" useGeneratedKeys="true" keyProperty="id">
        insert into tb_sms_template(tp_id,content)
        values
        <foreach collection="entities" item="entity" separator=",">
            ( #{entity.tpId},#{entity.content})
        </foreach>
        ON DUPLICATE KEY UPDATE
        gmt_modified = now()
    </insert>


</mapper>