<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.oa.staff.StaffInfoMapper">

    <resultMap type="com.ruoyi.system.entity.oa.staff.StaffInfoEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="userId" column="user_id"/>
            <result property="userRole" column="user_role"/>
            <result property="sex" column="sex"/>
            <result property="phone" column="phone"/>
            <result property="personalEmail" column="personal_email"/>
            <result property="nation" column="nation"/>
            <result property="politicsStatus" column="politics_status"/>
            <result property="birthday" column="birthday"/>
            <result property="maritalStatus" column="marital_status"/>
            <result property="hometown" column="hometown"/>
            <result property="nowResidence" column="now_residence"/>
            <result property="urgentContact" column="urgent_contact"/>
            <result property="urgentContactPhone" column="urgent_contact_phone"/>
            <result property="urgentContactRelation" column="urgent_contact_relation"/>
            <result property="graduateDate" column="graduate_date"/>
            <result property="graduateSchool" column="graduate_school"/>
            <result property="specialty" column="specialty"/>
            <result property="certificateNumber" column="certificate_number"/>
            <result property="idCard" column="id_card"/>
            <result property="idCardAddress" column="id_card_address"/>
            <result property="idCardFront" column="id_card_front"/>
            <result property="idCardReverse" column="id_card_reverse"/>
            <result property="postId" column="post_id"/>
            <result property="departmentId" column="department_id"/>
            <result property="companyId" column="company_id"/>
            <result property="entryDate" column="entry_date"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            user_id,
            user_role,
            sex,
            phone,
            personal_email,
            nation,
            politics_status,
            birthday,
            marital_status,
            hometown,
            now_residence,
            urgent_contact,
            urgent_contact_phone,
            urgent_contact_relation,
            graduate_date,
            graduate_school,
            specialty,
            certificate_number,
            id_card,
            id_card_address,
            id_card_front,
            id_card_reverse,
            post_id,
            department_id,
            company_id,
            entry_date,
            gmt_create,
            gmt_modified
    </sql>

    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_oa_staff_info
        WHERE user_id = #{userId}
    </select>

    <select id="selectByDepartmentId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_oa_staff_info
        <where>
            <if test="departmentId != null">
                department_id = #{departmentId}
            </if>
            <if test="companyId != null">
                and company_id = #{companyId}
            </if>
        </where>
    </select>
    <select id="selectByUserIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_oa_staff_info
        where user_id in
        <foreach collection="userIds" open="(" separator="," close=")" item="userId">
            #{userId}
        </foreach>
    </select>

    <select id="selectPostStaffNumByPostIds" resultType="com.ruoyi.system.bo.account.PostStaffNumBo">
        select post_id as postId,count(*) as num
        from tb_oa_staff_info
        where post_id in
        <foreach collection="postIds" open="(" separator="," close=")" item="postId">
            #{postId}
        </foreach>
        group by post_id
    </select>
    <select id="selectByPhone" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_oa_staff_info
        where phone = #{phone}
        limit  1
    </select>

    <select id="selectEmailByDepartmentIds" resultType="String">
        select u.`email`
        from `tb_oa_staff_info` s
        left join `tb_user` u on s.`user_id` = u.id
        where s.`department_id` in
        <foreach collection="departmentIds" open="(" separator="," close=")" item="departmentId">
            #{departmentId}
        </foreach>
    </select>
</mapper>
