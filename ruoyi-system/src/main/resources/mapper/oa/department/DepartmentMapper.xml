<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.oa.department.DepartmentMapper">

    <resultMap type="com.ruoyi.system.entity.oa.department.DepartmentEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="departmentName" column="department_name"/>
            <result property="companyId" column="company_id"/>
            <result property="departmentKey" column="department_key"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            department_name,
            company_id,
            department_key,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.oa.department.DepartmentEntity">
        INSERT INTO tb_oa_department
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="departmentName != null">
                    department_name,
                </if>
                <if test="companyId != null">
                    company_id,
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="departmentName != null">
                    #{departmentName}
                </if>
                <if test="companyId != null">
                    #{companyId}
                </if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.ruoyi.system.entity.oa.department.DepartmentEntity">
        UPDATE tb_oa_department
        <set>
            <if test="departmentName != null">
                department_name = #{departmentName},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_oa_department
        WHERE id = #{id}
    </select>
    <select id="selectByIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_oa_department
        where id in
        <foreach collection="ids" open="(" separator="," item="id" close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectAllDepartment" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_oa_department
    </select>

    <select id="selectByCompanyIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_oa_department
        where company_id in
        <foreach collection="companyIds" open="(" item="companyId" close=")" separator=",">
             #{companyId}
        </foreach>
    </select>

    <select id="selectDepartmentIds" resultType="Long">
        select id
        from tb_oa_department
        where department_key in
        <foreach collection="departmentKeys" open="(" item="departmentKey" close=")" separator=",">
            #{departmentKey}
        </foreach>
    </select>
</mapper>
