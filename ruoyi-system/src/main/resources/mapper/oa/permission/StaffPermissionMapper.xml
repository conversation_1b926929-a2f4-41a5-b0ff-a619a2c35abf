<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.oa.permission.StaffPermissionMapper">

    <resultMap type="com.ruoyi.system.entity.oa.permission.StaffPermissionEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="staffId" column="staff_id"/>
            <result property="systemId" column="system_id"/>
            <result property="permissions" column="permissions"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            staff_id,
            system_id,
            permissions,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.oa.permission.StaffPermissionEntity">
        INSERT INTO tb_staff_permission
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="staffId != null">
                staff_id,
            </if>
            <if test="systemId != null">
                system_id,
            </if>
            <if test="permissions != null">
                permissions
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="staffId != null">
                #{staffId},
            </if>
            <if test="systemId != null">
                #{systemId},
            </if>
            <if test="permissions != null">
                #{permissions}
            </if>
        </trim>
    </insert>

    <delete id="deleteById">
        DELETE FROM tb_staff_permission WHERE id=#{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.oa.permission.StaffPermissionEntity">
        UPDATE tb_staff_permission
        <set>
            <if test="permissions != null">
                permissions = #{permissions},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_staff_permission
        WHERE id = #{id}
    </select>

    <select id="selectByStaffIdAndSystemId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_staff_permission
        WHERE staff_id = #{staffId} and system_id = #{systemId}
    </select>

</mapper>
