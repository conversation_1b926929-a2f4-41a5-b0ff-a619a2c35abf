<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.oa.permission.PostPermissionMapper">

    <resultMap type="com.ruoyi.system.entity.oa.permission.PostPermissionEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="postId" column="post_id"/>
            <result property="systemId" column="system_id"/>
            <result property="permissions" column="permissions"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            post_id,
            system_id,
            permissions,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.oa.permission.PostPermissionEntity">
        INSERT INTO tb_post_permission
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="postId != null">
                post_id,
            </if>
            <if test="systemId != null">
                system_id,
            </if>
            <if test="permissions != null">
                permissions
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="postId != null">
                #{postId},
            </if>
            <if test="systemId != null">
                #{systemId},
            </if>
            <if test="permissions != null">
                #{permissions}
            </if>
        </trim>
    </insert>

    <delete id="deleteById">
        DELETE FROM tb_post_permission WHERE id=#{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.oa.permission.PostPermissionEntity">
        UPDATE tb_post_permission
        <set>
            <if test="permissions != null">
                permissions = #{permissions},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_post_permission
        WHERE id = #{id}
    </select>

    <select id="selectByPostIdAndSystemId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_post_permission
        WHERE post_id = #{postId} and system_id = #{systemId}
    </select>

</mapper>
