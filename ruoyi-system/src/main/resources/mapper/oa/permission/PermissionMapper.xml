<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.oa.permission.PermissionMapper">

    <resultMap type="com.ruoyi.system.entity.oa.permission.PermissionEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="systemId" column="system_id"/>
            <result property="permissionKey" column="permission_key"/>
            <result property="level" column="level"/>
            <result property="permissionName" column="permission_name"/>
            <result property="parentId" column="parent_id"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            system_id,
            permission_key,
            level,
            permission_name,
            parent_id,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.oa.permission.PermissionEntity">
        INSERT INTO tb_permission
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="systemId != null">
                system_id,
            </if>
            <if test="permissionKey != null">
                permission_key,
            </if>
            <if test="level != null">
                level,
            </if>
            <if test="permissionName != null">
                permission_name,
            </if>
            <if test="parentId != null">
                parent_id
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="systemId != null">
                #{systemId},
            </if>
            <if test="permissionKey != null">
                #{permissionKey},
            </if>
            <if test="level != null">
                #{level},
            </if>
            <if test="permissionName != null">
                #{permissionName},
            </if>
            <if test="parentId != null">
                #{parentId}
            </if>
        </trim>
    </insert>

    <delete id="deleteById">
        DELETE FROM tb_permission WHERE id=#{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.oa.permission.PermissionEntity">
        UPDATE tb_permission
        <set>
            <if test="systemId != null">
                system_id = #{systemId},
            </if>
            <if test="permissionKey != null">
                permission_key = #{permissionKey},
            </if>
            <if test="level != null">
                level = #{level},
            </if>
            <if test="permissionName != null">
                permission_name = #{permissionName},
            </if>
            <if test="parentId != null">
                parent_id = #{parentId},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_permission
        WHERE id = #{id}
    </select>

    <select id="selectBySystemId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_permission
        WHERE system_id = #{systemId}
    </select>

    <select id="selectByParentId" resultType="Long">
        SELECT id
        FROM tb_permission
        WHERE parent_id = #{parentId}
    </select>

    <select id="selectParentIdsByIds" resultType="Long">
        SELECT distinct parent_id
        FROM tb_permission
        WHERE level &gt; 1 and id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
    </select>
    <select id="selectPermissionKeyByIds" resultType="java.lang.String">
        SELECT distinct permission_key
        FROM tb_permission
        where id in
        <foreach collection="ids" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </select>
</mapper>
