<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.oa.user.UserMapper">

    <resultMap type="com.ruoyi.system.entity.oa.user.UserEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="email" column="email"/>
            <result property="passwd" column="passwd"/>
            <result property="userName" column="user_name"/>
            <result property="avatar" column="avatar"/>
            <result property="userStatus" column="user_status"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            email,
            passwd,
            user_name,
            avatar,
            user_status,
            gmt_create,
            gmt_modified
    </sql>

    <select id="selectByEmail" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_user
        WHERE email = #{email}
    </select>

    <select id="selectByEmails" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_user
        WHERE email in
        <foreach collection="emails" open="(" separator="," close=")" item="email">
            #{email}
        </foreach>
        and user_status = 0
    </select>

    <select id="selectByLikeEmail" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_user
        WHERE email like concat('%', #{email}, '%')
    </select>
    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_user
        WHERE id = #{id}
    </select>

</mapper>
