<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.oa.post.PostMapper">

    <resultMap type="com.ruoyi.system.entity.oa.post.PostEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="companyId" column="company_id"/>
            <result property="departmentId" column="department_id"/>
            <result property="postName" column="post_name"/>
            <result property="postKey" column="post_key"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            department_id,
            company_id,
            post_name,
            post_key,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.oa.post.PostEntity">
        INSERT INTO tb_oa_post
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="departmentId != null">
                    department_id,
                </if>
                <if test="companyId != null">
                    company_id,
                </if>
                <if test="postName != null">
                    post_name
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="departmentId != null">
                    #{departmentId},
                </if>
                <if test="companyId != null">
                    #{companyId},
                </if>
                <if test="postName != null">
                    #{postName}
                </if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.ruoyi.system.entity.oa.post.PostEntity">
        UPDATE tb_oa_post
        <set>
            <if test="departmentId != null">
                department_id = #{departmentId},
            </if>
            <if test="postName != null">
                post_name = #{postName},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_oa_post
        WHERE id = #{id}
    </select>

    <select id="selectByIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_oa_post
        where company_id = #{companyId} and id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectListByDepartmentIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_oa_post
        where department_id in
        <foreach collection="departmentIds" open="(" separator="," item="departmentId" close=")">
            #{departmentId}
        </foreach>
    </select>

    <select id="selectAllPost" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_oa_post
        where company_id = #{companyId}
    </select>

    <select id="selectByPostName" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_oa_post
        where post_name like concat('%', #{postName}, '%')
    </select>
</mapper>
