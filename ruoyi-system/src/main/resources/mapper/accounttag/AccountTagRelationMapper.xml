<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.accounttag.AccountTagRelationMapper">

    <resultMap type="com.ruoyi.system.entity.accounttag.AccountTagRelationEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="accountId" column="account_id"/>
            <result property="tagType" column="tag_type"/>
            <result property="tagId" column="tag_id"/>
            <result property="isDeleted" column="is_deleted"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            account_id,
            tag_type,
            tag_id,
            is_deleted,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.accounttag.AccountTagRelationEntity">
        INSERT INTO tb_account_tag_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="accountId != null">
                    account_id,
                </if>
                <if test="tagType != null">
                    tag_type,
                </if>
                <if test="tagId != null">
                    tag_id,
                </if>
                <if test="isDeleted != null">
                    is_deleted
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="accountId != null">
                    #{accountId},
                </if>
                <if test="tagType != null">
                    #{tagType},
                </if>
                <if test="tagId != null">
                    #{tagId},
                </if>
                <if test="isDeleted != null">
                    #{isDeleted}
                </if>
        </trim>
    </insert>

    <delete id="deleteById">
        DELETE FROM tb_account_tag_relation WHERE id=#{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.accounttag.AccountTagRelationEntity">
        UPDATE tb_account_tag_relation
        <set>
                    <if test="accountId != null">
                        account_id = #{accountId},
                    </if>
                    <if test="tagType != null">
                        tag_type = #{tagType},
                    </if>
                    <if test="tagId != null">
                        tag_id = #{tagId},
                    </if>
                    <if test="isDeleted != null">
                        is_deleted = #{isDeleted},
                    </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_account_tag_relation
        WHERE id = #{id}
    </select>

    <delete id="deleteByAccountIdAndType">
        delete from tb_account_tag_relation where account_id = #{accountId} and tag_type = #{type}
    </delete>

    <insert id="batchInsert">
        insert into tb_account_tag_relation(`account_id`,`tag_type`,`tag_id`)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.accountId}, #{entity.tagType},#{entity.tagId})
        </foreach>
    </insert>

    <select id="selectListByAccountType" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_account_tag_relation
        <where>
            <if test="accountId != null">
                account_id =  #{accountId}
            </if>
            <if test="tagType != null">
                and tag_type = #{tagType}
            </if>
        </where>
    </select>

    <select id="selectListByTagIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_account_tag_relation
        where tag_id in
        <foreach collection="tagIds" close=")" separator="," open="(" item="tagId">
            #{tagId}
        </foreach>
    </select>

</mapper>