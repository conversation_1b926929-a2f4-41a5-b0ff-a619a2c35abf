<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.system.SysConfigDomainMapper">

    <resultMap type="com.ruoyi.system.entity.system.SysConfigDomainEntity" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="configName" column="config_name"/>
        <result property="configKey" column="config_key"/>
        <result property="dataJson" column="data_json"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
            config_name,
            config_key,
            data_json,
            create_time,
            update_time,
            remark
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.system.SysConfigDomainEntity">
        INSERT INTO sys_config_domain
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="configName != null">
                config_name,
            </if>
            <if test="configKey != null">
                config_key,
            </if>
            <if test="dataJson != null">
                data_json,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="remark != null">
                remark
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="configName != null">
                #{configName},
            </if>
            <if test="configKey != null">
                #{configKey},
            </if>
            <if test="dataJson != null">
                #{dataJson},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="remark != null">
                #{remark}
            </if>
        </trim>
        ON DUPLICATE KEY UPDATE data_json=#{dataJson}
    </insert>

    <delete id="deleteById">
        DELETE
        FROM sys_config_domain
        WHERE id = #{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.system.SysConfigDomainEntity">
        UPDATE sys_config_domain
        <set>
            <if test="configName != null">
                config_name = #{configName},
            </if>
            <if test="configKey != null">
                config_key = #{configKey},
            </if>
            <if test="dataJson != null">
                data_json = #{dataJson},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sys_config_domain
        WHERE id = #{id}
    </select>

    <select id="selectByKey" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sys_config_domain
        WHERE config_key = #{configKey}
    </select>

</mapper>