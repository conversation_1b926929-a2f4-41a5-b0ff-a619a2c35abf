<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.appdata.AppMonthDataMapper">

    <resultMap type="com.ruoyi.system.entity.appdata.AppMonthDataEntity" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="monthDate" column="month_date"/>
        <result property="accountId" column="account_id"/>
        <result property="appId" column="app_id"/>
        <result property="slotRequestPv" column="slot_request_pv"/>
        <result property="slotRequestUv" column="slot_request_uv"/>
        <result property="appSlotClickPv"    column="app_slot_click_pv"    />
        <result property="appSlotClickUv"    column="app_slot_click_uv"    />
        <result property="appSlotExposurePv"    column="app_slot_exposure_pv"    />
        <result property="appSlotExposureUv"    column="app_slot_exposure_uv"    />
        <result property="slotRequestUvCalculate" column="slot_request_uv_calculate"/>
        <result property="joinPv" column="join_pv"/>
        <result property="joinUv" column="join_uv"/>
        <result property="totalConsume" column="total_consume"/>
        <result property="nhConsume" column="nh_consume"/>
        <result property="nhCost" column="nh_cost"/>
        <result property="outerCost" column="outer_cost"/>
        <result property="outerConsume" column="outer_consume"/>
        <result property="appRevenue" column="app_revenue"/>
        <result property="confirmStatus" column="confirm_status"/>
        <result property="withdrawRecordId" column="withdraw_record_id"/>
        <result property="payType" column="pay_type"/>
        <result property="prepayAmount" column="prepay_amount"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        month_date,
        account_id,
        app_id,
        slot_request_pv,
        slot_request_uv,
        app_slot_click_pv,
        app_slot_click_uv,
        app_slot_exposure_pv,
        app_slot_exposure_uv,
        slot_request_uv_calculate,
        join_pv,
        join_uv,
        total_consume,
        nh_consume,
        nh_cost,
        outer_cost,
        outer_consume,
        app_revenue,
        confirm_status,
        withdraw_record_id,
        pay_type,
        prepay_amount,
        gmt_create,
        gmt_modified
    </sql>

    <insert id="batchInsertOrUpdate">
        INSERT INTO tb_app_month_data
        (month_date,account_id,app_id,slot_request_pv,slot_request_uv,app_slot_click_pv,app_slot_click_uv,app_slot_exposure_pv,
        app_slot_exposure_uv,slot_request_uv_calculate,join_pv, join_uv,total_consume,nh_consume, outer_consume,app_revenue,nh_cost,outer_cost,
        pay_type, prepay_amount)
        values
        <foreach collection="dataEntities" item="entity" separator=",">
            ( #{entity.monthDate},#{entity.accountId},#{entity.appId}, #{entity.slotRequestPv},#{entity.slotRequestUv},#{entity.appSlotClickPv},#{entity.appSlotClickUv}
            ,#{entity.appSlotExposurePv},#{entity.appSlotExposureUv},#{entity.slotRequestUvCalculate},
            #{entity.joinPv}, #{entity.joinUv},#{entity.totalConsume},#{entity.nhConsume},#{entity.outerConsume},#{entity.appRevenue},#{entity.nhCost},#{entity.outerCost},
            #{entity.payType},#{entity.prepayAmount})
        </foreach>
        ON DUPLICATE KEY UPDATE
        slot_request_pv = values(slot_request_pv),
        slot_request_uv = values(slot_request_uv),
        app_slot_click_pv = values(app_slot_click_pv) ,
        app_slot_click_uv = values(app_slot_click_uv) ,
        app_slot_exposure_pv = values(app_slot_exposure_pv) ,
        app_slot_exposure_uv = values(app_slot_exposure_uv) ,
        slot_request_uv_calculate = values(slot_request_uv_calculate),
        join_pv = values(join_pv),
        join_uv = values(join_uv),
        total_consume = values(total_consume),
        nh_consume = values(nh_consume),
        outer_consume = values(outer_consume),
        app_revenue = values(app_revenue),
        nh_cost = values(nh_cost),
        outer_cost = values(outer_cost),
        pay_type = values(pay_type),
        prepay_amount = values(prepay_amount)
    </insert>

    <update id="batchUpdateCost">
        <foreach collection="dataEntities" item="entity" separator=";">
           update tb_app_month_data
           set nh_cost = #{entity.nhCost}
           where app_id = #{entity.appId} and month_date = #{entity.monthDate}
        </foreach>
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tb_app_month_data
        WHERE id = #{id}
    </select>

    <select id="selectByAppIdAndMonth" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tb_app_month_data
        WHERE app_id = #{appId} and month_date = #{month}
    </select>

    <select id="selectAppMonthList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_app_month_data
        <where>
            <if test="accountIds != null and accountIds.size > 0">
                account_id in
                <foreach collection="accountIds" separator="," item="accountId" close=")" open="(">
                    #{accountId}
                </foreach>
            </if>
            <if test="appIds != null and appIds.size > 0">
                and app_id in
                <foreach collection="appIds" open="(" separator="," close=")" item="appId">
                    #{appId}
                </foreach>
            </if>
            <if test="startMonth != null">
                and month_date &gt;= #{startMonth}
            </if>
            <if test="endMonth != null">
                and month_date &lt;= #{endMonth}
            </if>
            <if test="confirmStatus != null and confirmStatus !=0">
                and confirm_status = #{confirmStatus}
            </if>
            <if test="payType != null">
                and pay_type = #{payType}
            </if>
        </where>
        order by month_date desc, slot_request_pv desc, id desc
    </select>
    <update id="confirmStatement">
        update tb_app_month_data
        set confirm_status = #{status}
        where id = #{id}
    </update>

    <select id="selectByWithdrawId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_app_month_data
        where withdraw_record_id = #{withdrawId}
        order by month_date desc
    </select>
    <select id="selectNoWithdrawByAccountId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_app_month_data
        <where>
            account_id = #{accountId}
            and confirm_status in
            <foreach collection="status" item="state" close=")" separator="," open="(">
                 #{state}
            </foreach>
            <if test="withdrawIds != null and withdrawIds.size>0">
                and withdraw_record_id in
                <foreach collection="withdrawIds" open="(" separator="," close=")" item="withdrawId">
                    #{withdrawId}
                </foreach>
            </if>
            <if test="ids != null and ids.size > 0">
                and id in
                <foreach collection="ids" open="(" separator="," close=")" item="id">
                    #{id}
                </foreach>
            </if>
        </where>
        order by month_date desc
    </select>

    <select id="countNoConfirmByAccountId" resultType="Integer">
        select count(1)
        from tb_app_month_data
        <where>
            account_id = #{accountId} and confirm_status = 1
        </where>
    </select>

    <update id="relevanceWithdrawRecord">
        update tb_app_month_data
        set withdraw_record_id = #{withdrawId},confirm_status = #{confirmStatus}
        where id in
        <foreach collection="ids" item="id" close=")" separator="," open="(">
            #{id}
        </foreach>
    </update>

    <update id="updateConfirmStatus">
        update tb_app_month_data
        set confirm_status = #{confirmStatus}
        where withdraw_record_id = #{withdrawId}
    </update>

    <select id="selectListByPage" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_app_month_data
        where id > #{id} and month_date = #{monthDay}
        order by id asc
        limit #{pageSize}
    </select>


    <insert id="insert" parameterType="com.ruoyi.system.entity.appdata.AppMonthDataEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_app_month_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="monthDate != null">month_date,</if>
            <if test="accountId != null">account_id,</if>
            <if test="appId != null">app_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="monthDate != null">#{monthDate},</if>
            <if test="accountId != null">#{accountId},</if>
            <if test="appId != null">#{appId},</if>
        </trim>
    </insert>

    <update id="update" parameterType="com.ruoyi.system.entity.appdata.AppMonthDataEntity">
        update tb_app_month_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="slotRequestPv != null">slot_request_pv = #{slotRequestPv},</if>
            <if test="slotRequestUv != null">slot_request_uv = #{slotRequestUv},</if>
            <if test="nhCost != null">nh_cost = #{nhCost},</if>
            <if test="outerCost != null">outer_cost = #{outerCost},</if>
            <if test="appRevenue != null">app_revenue = #{appRevenue},</if>
            <if test="payType != null">pay_type = #{payType},</if>
            <if test="prepayAmount != null">prepay_amount = #{prepayAmount},</if>
            gmt_modified = now()
        </trim>
        where id = #{id}
    </update>
</mapper>
