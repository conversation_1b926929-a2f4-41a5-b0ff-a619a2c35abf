<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.convert.ConvertUploadRecordMapper">

    <resultMap type="com.ruoyi.system.entity.convert.ConvertUploadRecordEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="curDate" column="cur_date"/>
            <result property="slotId" column="slot_id"/>
            <result property="advertId" column="advert_id"/>
            <result property="orderId" column="order_id"/>
            <result property="convertTime" column="convert_time"/>
            <result property="slotParam" column="slot_param"/>
            <result property="uploadStatus" column="upload_status"/>
            <result property="uploadTime" column="upload_time"/>
            <result property="uploadResult" column="upload_result"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            cur_date,
            slot_id,
            advert_id,
            order_id,
            convert_time,
            slot_param,
            upload_status,
            upload_time,
            upload_result,
            gmt_create,
            gmt_modified
    </sql>

    <select id="selectList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_convert_upload_record
        <where>
            <if test="startDate != null"> and cur_date &gt;= #{startDate}</if>
            <if test="endDate != null"> and cur_date &lt;= #{endDate}</if>
            <if test="slotId != null"> and slot_id = #{slotId}</if>
        </where>
        ORDER BY id DESC
    </select>

    <select id="selectSummaryData" resultType="com.ruoyi.system.bo.publisher.ConvertUploadSummaryDataBo">
        select curDate, slotId, convertCount, unConvertCount
        from (
            SELECT cur_date as curDate, slot_id as slotId,
            count(case when `upload_status` = 1 then order_id end) as convertCount,
            count(case when `upload_status` in (0,2) then order_id end) as unConvertCount
            FROM tb_convert_upload_record
            <where>
                <if test="startDate != null"> and cur_date &gt;= #{startDate}</if>
                <if test="endDate != null"> and cur_date &lt;= #{endDate}</if>
                <if test="slotId != null"> and slot_id = #{slotId}</if>
            </where>
            GROUP BY `cur_date`, `slot_id`
        ) t
        ORDER BY curDate desc, convertCount desc
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.convert.ConvertUploadRecordEntity">
        INSERT INTO tb_convert_upload_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="curDate != null">
                    cur_date,
                </if>
                <if test="slotId != null">
                    slot_id,
                </if>
                <if test="advertId != null">
                    advert_id,
                </if>
                <if test="orderId != null">
                    order_id,
                </if>
                <if test="convertTime != null">
                    convert_time,
                </if>
                <if test="slotParam != null">
                    slot_param,
                </if>
                <if test="uploadStatus != null">
                    upload_status,
                </if>
                <if test="uploadTime != null">
                    upload_time,
                </if>
                <if test="uploadResult != null">
                    upload_result
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="curDate != null">
                    #{curDate},
                </if>
                <if test="slotId != null">
                    #{slotId},
                </if>
                <if test="advertId != null">
                    #{advertId},
                </if>
                <if test="orderId != null">
                    #{orderId},
                </if>
                <if test="convertTime != null">
                    #{convertTime},
                </if>
                <if test="slotParam != null">
                    #{slotParam},
                </if>
                <if test="uploadStatus != null">
                    #{uploadStatus},
                </if>
                <if test="uploadTime != null">
                    #{uploadTime},
                </if>
                <if test="uploadResult != null">
                    #{uploadResult}
                </if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.ruoyi.system.entity.convert.ConvertUploadRecordEntity">
        UPDATE tb_convert_upload_record
        <set>
            <if test="uploadStatus != null">
                upload_status = #{uploadStatus},
            </if>
            <if test="uploadTime != null">
                upload_time = #{uploadTime},
            </if>
            <if test="uploadResult != null">
                upload_result = #{uploadResult},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_convert_upload_record
        WHERE id = #{id}
    </select>

</mapper>
