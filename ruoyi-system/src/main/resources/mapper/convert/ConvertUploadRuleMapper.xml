<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.convert.ConvertUploadRuleMapper">

    <resultMap type="com.ruoyi.system.entity.convert.ConvertUploadRuleEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="slotId" column="slot_id"/>
            <result property="ratio" column="ratio"/>
            <result property="creatorId" column="creator_id"/>
            <result property="createTime" column="create_time"/>
            <result property="operatorId" column="operator_id"/>
            <result property="operateTime" column="operate_time"/>
            <result property="isDeleted" column="is_deleted"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            slot_id,
            ratio,
            creator_id,
            create_time,
            operator_id,
            operate_time,
            is_deleted,
            gmt_create,
            gmt_modified
    </sql>

    <select id="selectList" resultMap="BaseResultMap" parameterType="com.ruoyi.system.req.manager.ConvertUploadRuleListReq">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_convert_upload_rule
        <where>
            is_deleted = 0
            <if test="slotIds != null and slotIds.size > 0">
                and slot_id in
                <foreach collection="slotIds" separator="," item="slotId" open="(" close=")">
                    #{slotId}
                </foreach>
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="selectHistory" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_convert_upload_rule
        <where>
            slot_id = #{slotId}
        </where>
        ORDER BY operate_time DESC
    </select>

    <select id="selectBySlotId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_convert_upload_rule
        WHERE slot_id = #{slotId} and is_deleted = 0
        LIMIT 1
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.convert.ConvertUploadRuleEntity">
        INSERT INTO tb_convert_upload_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="slotId != null">
                slot_id,
            </if>
            <if test="ratio != null">
                ratio,
            </if>
            <if test="creatorId != null">
                creator_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="operatorId != null">
                operator_id,
            </if>
            <if test="operateTime != null">
                operate_time,
            </if>
            <if test="isDeleted != null">
                is_deleted
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="slotId != null">
                #{slotId},
            </if>
            <if test="ratio != null">
                #{ratio},
            </if>
            <if test="creatorId != null">
                #{creatorId},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="operatorId != null">
                #{operatorId},
            </if>
            <if test="operateTime != null">
                #{operateTime},
            </if>
            <if test="isDeleted != null">
                #{isDeleted}
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.ruoyi.system.entity.convert.ConvertUploadRuleEntity">
        UPDATE tb_convert_upload_rule
        <set>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted},
            </if>
        </set>
        WHERE id=#{id}
    </update>

</mapper>
