<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.consumer.ConsumerMapper">

    <resultMap type="com.ruoyi.system.domain.consumer.Consumer" id="ConsumerResult">
        <result property="id"    column="id"    />
        <result property="consumerId"    column="consumer_id"    />
        <result property="appId"    column="app_id"    />
        <result property="deviceId"    column="device_id"    />
    </resultMap>

    <select id="selectByConsumerId" parameterType="com.ruoyi.system.domain.consumer.Consumer" resultMap="ConsumerResult">
        select id, consumer_id, app_id, device_id
        from tb_consumer_${tbSuffix}
        where consumer_id = #{consumerId}
    </select>

    <select id="selectByAppAndDevice" parameterType="com.ruoyi.system.domain.consumer.Consumer" resultMap="ConsumerResult">
        select id, consumer_id, app_id, device_id
        from tb_consumer_${tbSuffix}
        where app_id = #{appId} and device_id = #{deviceId}
    </select>

    <insert id="insertConsumer" parameterType="com.ruoyi.system.domain.consumer.Consumer"
            useGeneratedKeys="true" keyProperty="id">
        insert into tb_consumer_${tbSuffix}
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="consumerId != null">consumer_id,</if>
            <if test="appId != null and appId != ''">app_id,</if>
            <if test="deviceId != null">device_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="consumerId != null">#{consumerId},</if>
            <if test="appId != null and appId != ''">#{appId},</if>
            <if test="deviceId != null">#{deviceId},</if>
         </trim>
    </insert>
</mapper>
