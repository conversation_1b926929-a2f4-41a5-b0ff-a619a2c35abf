<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.shorturl.ShortUrlDataMapper">

    <resultMap type="com.ruoyi.system.entity.shorturl.ShortUrlDataEntity" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="curDate" column="cur_date"/>
        <result property="shortUrlId" column="short_url_id"/>
        <result property="requestPv" column="request_pv"/>
        <result property="requestUv" column="request_uv"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
            cur_date,
            short_url_id,
            request_pv,
            request_uv,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.shorturl.ShortUrlDataEntity">
        INSERT INTO tb_short_url_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="curDate != null">
                cur_date,
            </if>
            <if test="shortUrlId != null">
                short_url_id,
            </if>
            <if test="requestPv != null">
                request_pv,
            </if>
            <if test="requestUv != null">
                request_uv
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="curDate != null">
                #{curDate},
            </if>
            <if test="shortUrlId != null">
                #{shortUrlId},
            </if>
            <if test="requestPv != null">
                #{requestPv},
            </if>
            <if test="requestUv != null">
                #{requestUv}
            </if>
        </trim>
    </insert>

    <delete id="deleteById">
        DELETE
        FROM tb_short_url_data
        WHERE id = #{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.shorturl.ShortUrlDataEntity">
        UPDATE tb_short_url_data
        <set>
            <if test="curDate != null">
                cur_date = #{curDate},
            </if>
            <if test="shortUrlId != null">
                short_url_id = #{shortUrlId},
            </if>
            <if test="requestPv != null">
                request_pv = #{requestPv},
            </if>
            <if test="requestUv != null">
                request_uv = #{requestUv},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <update id="update">
        UPDATE tb_short_url_data
        SET request_pv = request_pv + #{pvAdd},
            request_uv = request_uv + #{uvAdd}
        WHERE id = #{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_short_url_data
        WHERE id = #{id}
    </select>

    <select id="selectBy" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_short_url_data
        <where>
            <if test="curDate != null">and cur_date = #{curDate}</if>
            <if test="shortUrlId != null">and short_url_id = #{shortUrlId}</if>
        </where>
        LIMIT 1
    </select>
    <select id="selectListByParam" parameterType="com.ruoyi.system.param.shorturl.ShortUrlDataParam"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_short_url_data
        <where>
            <if test="shortUrlIds != null and shortUrlIds.size > 0">
                short_url_id in
                <foreach collection="shortUrlIds" open="(" separator="," close=")" item="shortUrlId">
                    #{shortUrlId}
                </foreach>
            </if>
            <if test="startDate != null">
                and cur_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                and cur_date &lt;= #{endDate}
            </if>
        </where>
        order by gmt_create desc
    </select>
</mapper>
