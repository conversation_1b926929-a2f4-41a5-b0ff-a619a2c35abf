<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.shorturl.ShortUrlMapper">

    <resultMap type="com.ruoyi.system.entity.shorturl.ShortUrlEntity" id="BaseResultMap">
            <result property="id" column="id"/>
            <result property="urlType" column="url_type"/>
            <result property="urlStatus" column="url_status"/>
            <result property="originUrl" column="origin_url"/>
            <result property="originUrlMd5" column="origin_url_md5"/>
            <result property="shortUrl" column="short_url"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            url_type,
            url_status,
            origin_url,
            origin_url_md5,
            short_url,
            gmt_create,
            gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.ruoyi.system.entity.shorturl.ShortUrlEntity">
        INSERT INTO tb_short_url
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="urlType != null">
                    url_type,
                </if>
                <if test="urlStatus != null">
                    url_status,
                </if>
                <if test="originUrl != null">
                    origin_url,
                </if>
                <if test="originUrlMd5 != null">
                    origin_url_md5,
                </if>
                <if test="shortUrl != null">
                    short_url,
                </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="urlType != null">
                    #{urlType},
                </if>
                <if test="urlStatus != null">
                    #{urlStatus},
                </if>
                <if test="originUrl != null">
                    #{originUrl},
                </if>
                <if test="originUrlMd5 != null">
                    #{originUrlMd5},
                </if>
                <if test="shortUrl != null">
                    #{shortUrl},
                </if>
        </trim>
    </insert>
    <insert id="insertBatch" useGeneratedKeys="true" keyProperty="id"  parameterType="com.ruoyi.system.entity.shorturl.ShortUrlEntity">
        insert into tb_short_url (url_type,origin_url,origin_url_md5) values
        <foreach collection="list" separator="," item="item">
            (#{item.urlType},#{item.originUrl},#{item.originUrlMd5})
        </foreach>
    </insert>

    <delete id="deleteById">
        DELETE FROM tb_short_url WHERE id=#{id}
    </delete>

    <update id="updateById" parameterType="com.ruoyi.system.entity.shorturl.ShortUrlEntity">
        UPDATE tb_short_url
        <set>
            <if test="urlStatus != null">
                url_status = #{urlStatus},
            </if>
            <if test="shortUrl != null">
                short_url = #{shortUrl},
            </if>
        </set>
        WHERE id=#{id}
    </update>
    <update id="updateBatchById">
        <if test="list != null and list.size() > 0 ">UPDATE tb_short_url SET short_url = CASE id <foreach collection="list" item="item">
            WHEN #{item.id} THEN #{item.shortUrl}
        </foreach>
        </if>
        END WHERE id IN
        <if test="list != null and list.size() > 0 ">
            <foreach collection="list" item="item" separator="," open="(" close=")">#{item.id}</foreach>
        </if>
    </update>


    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_short_url
        WHERE id = #{id}
    </select>

    <select id="selectBy" resultMap="BaseResultMap" parameterType="com.ruoyi.system.entity.shorturl.ShortUrlEntity">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_short_url
        <where>
            <if test="originUrlMd5 != null">and origin_url_md5 = #{originUrlMd5}</if>
        </where>
    </select>

    <select id="selectList" resultMap="BaseResultMap" parameterType="com.ruoyi.system.entity.shorturl.ShortUrlEntity">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_short_url
        <where>
            <if test="urlStatus != null">and url_status = #{urlStatus}</if>
            <if test="urlType != null">and url_type = #{urlType}</if>
            <if test="originUrl != null and originUrl != ''"> and origin_url like concat('%', #{originUrl}, '%')</if>
            <if test="shortUrl != null and shortUrl != ''"> and short_url like concat('%', #{shortUrl}, '%')</if>
            and url_type in (1, 2)
        </where>
        order by id desc
    </select>

    <select id="selectListByIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_short_url
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectByOriginUrlMd5List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_short_url where origin_url_md5 in
        <foreach collection="list" open="(" close=")" item="urlMd5" separator=",">
            #{urlMd5}
        </foreach>
    </select>
</mapper>
