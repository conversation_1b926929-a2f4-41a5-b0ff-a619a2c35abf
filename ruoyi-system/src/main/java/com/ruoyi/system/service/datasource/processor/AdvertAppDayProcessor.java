package com.ruoyi.system.service.datasource.processor;

import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.DataDimensionEnum;
import com.ruoyi.common.enums.InnerLogType;
import com.ruoyi.common.utils.BizUtils;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.system.domain.datashow.DataStatContext;
import com.ruoyi.system.entity.datashow.AdvertAppDayData;
import com.ruoyi.system.req.datashow.DataStatReq;
import com.ruoyi.system.service.datasource.AdvertAppDayDataService;
import com.ruoyi.system.service.datasource.DataStatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

import static com.ruoyi.common.enums.DataDimensionEnum.ADVERT_APP_DAY;

/**
 * 广告媒体维度日数据处理器
 *
 * <AUTHOR>
 * @date 2021/10/15
 */
@Slf4j
@Service
public class AdvertAppDayProcessor implements DataProcessor, InitializingBean {

    @Autowired
    private AdvertAppDayDataService advertAppDayDataService;

    @Autowired
    private RedisCache redisCache;

    @Override
    public DataDimensionEnum getDimension() {
        return ADVERT_APP_DAY;
    }

    @Override
    public boolean validate(DataStatContext context) {
        DataStatReq req = context.getReq();
        return null != req.getDate() && null != req.getAdvertId() && null != req.getAppId() && null != req.getConsumerId();
    }

    @Override
    public boolean process(DataStatContext context) {
        DataStatReq req = context.getReq();
        InnerLogType type = context.getType();
        int pv = 1;
        int uv = 0;

        // 计算uv
        switch (type) {
            case ADVERT_LAUNCH:
            case ADVERT_EXPOSURE:
            case ADVERT_CLICK:
            case LANDPAGE_EXPOSURE:
            case LANDPAGE_CLICK:
            case BLIND_BOX_POPUP_CLICK:
                String uvKey = EngineRedisKeyFactory.K003.join(type.getType(), req.getAdvertId(), req.getAppId(), req.getDateStr());
                uv = BizUtils.countUv(uvKey, String.valueOf(req.getConsumerId()));
                break;
            default:
                break;
        }

        // 数据
        AdvertAppDayData updateAdvertData = new AdvertAppDayData();
        updateAdvertData.setId(getDataId(req));

        switch (type) {
            case ADVERT_LAUNCH:
                updateAdvertData.setAdLaunchPvAdd(pv);
                updateAdvertData.setAdLaunchUvAdd(uv);
                break;
            case ADVERT_EXPOSURE:
                updateAdvertData.setExposurePvAdd(pv);
                updateAdvertData.setExposureUvAdd(uv);
                break;
            case ADVERT_CLICK:
                updateAdvertData.setClickPvAdd(pv);
                updateAdvertData.setClickUvAdd(uv);
                break;
            case LANDPAGE_EXPOSURE:
                updateAdvertData.setLpExposurePvAdd(pv);
                updateAdvertData.setLpExposureUvAdd(uv);
                break;
            case LANDPAGE_CLICK:
                updateAdvertData.setLpClickPvAdd(pv);
                updateAdvertData.setLpClickUvAdd(uv);
                break;
            case ADVERT_BILLING:
                updateAdvertData.setConsumeAdd(NumberUtils.defaultInt(req.getUnitPrice()));
                break;
            case BLIND_BOX_POPUP_CLICK:
                updateAdvertData.setTakePvAdd(pv);
                updateAdvertData.setTakeUvAdd(uv);
                break;
            default:
                break;
        }
        return advertAppDayDataService.updateAdvertAppDayData(updateAdvertData) > 0;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        DataStatService.register(this);
    }

    /**
     * 通过缓存获取数据ID
     */
    private Long getDataId(DataStatReq req) {
        String key = EngineRedisKeyFactory.K022.join("AdvertAppDayData", req.getDateStr(), req.getAdvertId(), req.getAppId());
        Long dataId = redisCache.getCacheObject(key);
        if (null != dataId) {
            return dataId;
        }

        // 查询并初始数据
        AdvertAppDayData param = new AdvertAppDayData();
        param.setCurDate(req.getDate());
        param.setAdvertId(req.getAdvertId());
        param.setAppId(req.getAppId());
        AdvertAppDayData data = advertAppDayDataService.selectBy(param);
        if (null == data) {
            data = new AdvertAppDayData();
            data.setCurDate(req.getDate());
            data.setAdvertId(req.getAdvertId());
            data.setAppId(req.getAppId());
            advertAppDayDataService.insertAdvertAppDayData(data);
            data = advertAppDayDataService.selectBy(param);
        }
        redisCache.setCacheObject(key, data.getId(), 1, TimeUnit.DAYS);
        return data.getId();
    }
}
