package com.ruoyi.system.service.landpage;

import com.ruoyi.system.entity.landpage.LandpageSkinEntity;

import java.util.List;

/**
 * 落地页皮肤表 Service
 *
 * <AUTHOR>
 * @date 2023-4-3 16:56:23
 */
public interface LandpageSkinService {

    /**
     * 新增记录
     */
    int insert(LandpageSkinEntity entity);

    /**
     * 根据id更新
     */
    int updateById(LandpageSkinEntity entity);

    /**
     * 根据id获取
     */
    LandpageSkinEntity selectById(Long id);

    /**
     * 查询列表
     */
    List<LandpageSkinEntity> selectList(LandpageSkinEntity param);
}
