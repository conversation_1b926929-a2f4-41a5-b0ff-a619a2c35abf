package com.ruoyi.system.service.advertiser;

import com.ruoyi.system.bo.advertiser.finance.AdvertiserConsumeOffsetBo;
import com.ruoyi.system.entity.advertiser.DspAdvertiserConsumeRecordEntity;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * DSP广告主消费记录 Service
 *
 * <AUTHOR>
 * @date 2022-7-13 17:30:15
 */
public interface DspAdvertiserConsumeRecordService {

    /**
     * 新增记录
     */
    Boolean insert(DspAdvertiserConsumeRecordEntity entity);

    /**
     * 根据id更新
     */
    Boolean updateById(DspAdvertiserConsumeRecordEntity entity);

    /**
     * 根据广告主ID和日期获取
     *
     * @param advertiserId 广告主ID
     * @param curDate 日期
     * @return 广告主消费记录
     */
    DspAdvertiserConsumeRecordEntity selectByAdvertiserIdAndDate(Long advertiserId, Date curDate);

    /**
     * 查询广告主消费记录不可见的日期
     *
     * @param advertiserId 广告主ID
     * @return 日期列表
     */
    List<Date> selectInvisibleDateList(Long advertiserId);

    /**
     * 查询广告主消费记录不可见的日期
     *
     * @param advertiserIds 广告主ID列表
     * @return 广告主ID-日期列表映射
     */
    Map<Long, List<Date>> selectInvisibleDateMap(List<Long> advertiserIds);

    /**
     * 查询广告主消费记录列表映射
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param advertiserId 广告主ID
     * @return 日期-广告主消费记录映射
     */
    Map<Date, DspAdvertiserConsumeRecordEntity> selectVisibleDataMap(Date startDate, Date endDate, Long advertiserId);

    /**
     * 查询广告主消费记录列表映射
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param advertiserIds 广告主ID列表
     * @return <日期_广告主ID,广告主消费记录>映射
     */
    Map<String, DspAdvertiserConsumeRecordEntity> selectMap(Date startDate, Date endDate, List<Long> advertiserIds);

    /**
     * 离线广告主消费记录修改后，消费总额的差额计算
     *
     * @param advertiserId 广告主ID
     * @return 新消费-原消费的差额
     */
    Integer consumeOffset(Long advertiserId);

    /**
     * 离线广告主消费记录修改后，消费总额的差额计算(CRM使用，包含不展示的)
     *
     * @param advertiserId 广告主ID
     * @return 新消费-原消费的差额
     */
    Integer consumeOffsetForCrm(Long advertiserId);

    /**
     * 离线广告主消费记录修改后，消费总额的差额计算(CRM使用，包含不展示的)
     *
     * @param advertiserIds 广告主ID列表
     * @return <广告主ID, 消费差额>映射
     */
    Map<Long, Integer> batchConsumeOffsetForCrm(List<Long> advertiserIds);

    /**
     * 离线广告主消费记录修改后，消费总额的差额计算(DSP使用)
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param advertiserIds 广告主ID列表
     * @return <日期_广告主ID,消费差额>映射
     */
    Map<String, AdvertiserConsumeOffsetBo> batchConsumeOffset(Date startDate, Date endDate, List<Long> advertiserIds);

    /**
     * 离线广告主消费记录修改后，消费总额的差额计算(DSP使用)
     *
     * @param endDate 结束日期
     * @param advertiserIds 广告主ID列表
     * @return <广告主ID, 消费差额列表>映射
     */
    Map<Long, List<AdvertiserConsumeOffsetBo>> batchConsumeOffset(Date endDate, List<Long> advertiserIds);

    /**
     * 离线广告主消费记录修改后，广告主的消费总额的差额计算(CRM使用)
     *
     * @param advertiserIds 广告主ID列表
     * @return 广告主ID-消费差额映射
     */
    Map<Long, Integer> advertiserConsumeOffsetForCrm(List<Long> advertiserIds);

    /**
     * 离线广告主消费记录修改后，广告主的消费总额的差额总和计算(CRM使用)
     *
     * @param advertiserIds 广告主ID列表
     * @param excludeAccountIds 排除的广告主ID列表
     * @return 消费差额总和
     */
    Long sumAdvertiserConsumeOffsetForCrm(List<Long> advertiserIds, List<Long> excludeAccountIds);

    /**
     * 离线广告主消费记录修改后，广告主的消费总额的差额计算(DSP使用)
     *
     * @param advertiserIds 广告主ID列表
     * @return 广告主ID-消费差额映射
     */
    Map<Long, Integer> advertiserConsumeOffset(List<Long> advertiserIds);

    /**
     * 离线广告主消费记录修改后，广告主的消费总额的差额总和计算(DSP使用)
     *
     * @param advertiserIds 广告主ID列表
     * @return 消费差额总和
     */
    Integer advertiserConsumeOffsetSum(List<Long> advertiserIds);
}
