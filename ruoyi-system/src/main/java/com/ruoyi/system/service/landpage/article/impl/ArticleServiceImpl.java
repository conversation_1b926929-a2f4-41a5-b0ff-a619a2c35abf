package com.ruoyi.system.service.landpage.article.impl;

import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.bo.landpage.article.ArticleCountBo;
import com.ruoyi.system.bo.landpage.article.ArticleListBo;
import com.ruoyi.system.bo.landpage.article.ArticleListParamBo;
import com.ruoyi.system.entity.landpage.article.ArticleEntity;
import com.ruoyi.system.mapper.landpage.article.ArticleMapper;
import com.ruoyi.system.service.landpage.article.ArticleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.ruoyi.system.bo.fc.FcArticleStatusCountBo;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 文章表 Service
 *
 * <AUTHOR>
 * @date 2023-12-1 15:13:01
 */
@Slf4j
@Service
public class ArticleServiceImpl implements ArticleService {

    @Autowired
    private ArticleMapper articleMapper;

    @Override
    public List<ArticleEntity> selectListByLinkId(Long linkId) {
        if (null == linkId) {
            return Collections.emptyList();
        }
        ArticleListParamBo param = new ArticleListParamBo();
        param.setLinkId(linkId);
        return articleMapper.selectList(param);
    }

    @Override
    public List<ArticleEntity> selectList(ArticleListParamBo param) {
        return articleMapper.selectList(param);
    }

    @Override
    public List<ArticleListBo> selectListWithData(ArticleListParamBo param) {
        return articleMapper.selectListWithData(param);
    }

    @Override
    public ArticleListBo selectStatisticData(ArticleListParamBo param) {
        return articleMapper.selectStatisticData(param);
    }

    @Override
    public Integer selectTodaySyIncrRequestPvSum(Long linkId) {
        return articleMapper.selectTodaySyIncrRequestPvSum(linkId);
    }

    @Override
    public Boolean insert(ArticleEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return articleMapper.insert(entity) > 0;
    }

    @Override
    public Boolean batchInsert(List<ArticleEntity> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return false;
        }
        return articleMapper.batchInsert(entities) > 0;
    }

    @Override
    public Boolean deleteById(Long id) {
        if (Objects.isNull(id)) {
            return false;
        }
        return articleMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean updateById(ArticleEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return articleMapper.updateById(entity) > 0;
    }

    @Override
    public void updateActualRequestPv(Long articleId, Integer actualRequestPv) {
        if (actualRequestPv < 1) {
            return;
        }
        ArticleEntity updateArticle = new ArticleEntity();
        updateArticle.setId(articleId);
        updateArticle.setActualRequestPv(actualRequestPv);
        articleMapper.updateById(updateArticle);
    }

    @Override
    public ArticleEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return articleMapper.selectById(id);
    }

    @Override
    public Map<Long, ArticleEntity> selectMapByLinkId(Long linkId) {
        if (null == linkId) {
            return Collections.emptyMap();
        }
        ArticleListParamBo param = new ArticleListParamBo();
        param.setLinkId(linkId);
        List<ArticleEntity> list = articleMapper.selectList(param);
        return list.stream().collect(Collectors.toMap(ArticleEntity::getId, Function.identity(), (v1, v2) -> v2));
    }

    @Override
    public Integer getWeightSum(Long linkId) {
        return articleMapper.selectWeightSum(linkId);
    }

    @Override
    public Map<Long, ArticleCountBo> countByLinkId(List<Long> linkIds) {
        if (CollectionUtils.isEmpty(linkIds)) {
            return Collections.emptyMap();
        }
        List<ArticleCountBo> list = articleMapper.countByLinkId(linkIds);
        return list.stream().collect(Collectors.toMap(ArticleCountBo::getLinkId, Function.identity(), (v1, v2) -> v2));
    }

    @Override
    public ArticleEntity selectArticleWithoutProfile() {
        return articleMapper.selectArticleWithoutProfile();
    }

    @Override
    public void updateArticleProfileAsync(Long articleId, String articleUrl) {
        if (null == articleId || StringUtils.isBlank(articleUrl) || !articleUrl.startsWith("https://mp.weixin.qq.com/")) {
            return;
        }
        GlobalThreadPool.longTimeExecutorService.submit(() -> {
            try {
                JSONObject meta = getArticleProfile(articleUrl);
                ArticleEntity article = new ArticleEntity();
                article.setId(articleId);
                article.setProfile(meta.toString());
                articleMapper.updateById(article);
            } catch (Exception e) {
                log.error("updateArticleProfileAsync error, id={}, url={}", articleId, articleUrl, e);
            }
        });
    }

    @Override
    public List<Long> selectBgZeroTodayArticleLinkId() {
        return articleMapper.selectBgZeroTodayArticleLinkId();
    }

    @Override
    public JSONObject getArticleProfile(String url) {
        if (StringUtils.isBlank(url) || !url.contains("mp.weixin.qq.com")) {
            return new JSONObject();
        }
        if (url.contains("#")) {
            url = StrUtil.subPre(url, url.indexOf("#"));
        }
        if (url.contains("·")) {
            url = StrUtil.subPre(url, url.indexOf("·"));
        }
//        if (!url.contains("mp.weixin.qq.com/s?") && url.contains("?")) {
//            url = StrUtil.subPre(url, url.indexOf("?"));
//        }
        String resp = HttpUtil.get(url);
        if (null == resp) {
            return new JSONObject();
        }
        JSONObject meta = new JSONObject();
        meta.put("title", ReUtil.getGroup1("<meta property=\"og:title\" content=\"(.*?)\" />", resp));
        meta.put("url", ReUtil.getGroup1("<meta property=\"og:url\" content=\"(.*?)\" />", resp));
        meta.put("nickname", ReUtil.getGroup1("<strong class=\"profile_nickname\">(.*?)</strong>", resp));
        meta.put("wechat", ReUtil.getGroup1("<span class=\"profile_meta_value\">(.*?)</span>", resp));
        meta.put("createTime", ReUtil.getGroup1("createTime = '(.*?)';", resp));
        meta.put("provinceName", ReUtil.getGroup1("provinceName: '(.*?)',", resp));
        meta.put("ct", ReUtil.getGroup1("var ct = \"(.*?)\";", resp));
        meta.put("user_name", ReUtil.getGroup1( "var user_name = \"(.*?)\";", resp));
        if (StringUtils.isBlank(meta.getString("nickname"))) {
            meta.put("nickname", ReUtil.getGroup1("var nickname = htmlDecode\\(\"(.*?)\"\\);", resp));
        }
        return meta;
    }

    @Override
    public List<ArticleEntity> selectListByLinkIds(List<Long> linkIds) {
        return articleMapper.selectListByLinkIds(linkIds);
    }

    @Override
    public void updateFcCheckStatus(Long articleId, Integer checkStatus, String rejectReason) {
        if ( Objects.isNull(articleId) || Objects.isNull(checkStatus) ) {
            return;
        }
        articleMapper.updateFcCheckStatus(articleId, checkStatus, rejectReason);
    }

    @Override
    public void updateFcSyncStatus(Long articleId, Integer syncStatus, String syncFailReason) {
        if ( Objects.isNull(articleId) || Objects.isNull(syncStatus) ) {
            return;
        }
        articleMapper.updateFcSyncStatus(articleId, syncStatus, syncFailReason);
    }

    @Override
    public Boolean hasFcSyncSuccessArticleByLinkId(Long linkId) {
        if (Objects.isNull(linkId)) {
            return false;
        }
        return articleMapper.hasFcSyncSuccessArticleByLinkId(linkId);
    }

    @Override
    public Boolean hasFcSyncSuccessArticleByLinkIds(List<Long> linkIds) {
        if (CollectionUtils.isEmpty(linkIds)) {
            return false;
        }
        return articleMapper.hasFcSyncSuccessArticleByLinkIds(linkIds);
    }

    @Override
    public Map<Long, FcArticleStatusCountBo> countFcStatusByLinkIds(List<Long> linkIds) {
        if (CollectionUtils.isEmpty(linkIds)) {
            return Collections.emptyMap();
        }
        List<FcArticleStatusCountBo> list = articleMapper.countFcStatusByLinkIds(linkIds);
        return list.stream().collect(Collectors.toMap(FcArticleStatusCountBo::getLinkId, Function.identity(), (v1, v2) -> v2));
    }

    @Override
    public List<ArticleEntity> selectListByDateRange(Date startDate, Date endDate) {
        return articleMapper.selectListByDateRange(startDate, endDate);
    }
}
