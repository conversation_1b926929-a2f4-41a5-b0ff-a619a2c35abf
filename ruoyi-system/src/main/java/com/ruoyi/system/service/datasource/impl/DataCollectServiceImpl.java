package com.ruoyi.system.service.datasource.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.UrlUtils;
import com.ruoyi.system.service.datasource.DataCollectService;
import com.ruoyi.system.service.engine.cache.ActivityCacheService;
import com.ruoyi.system.util.LandpageUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * 数据收集接口实现
 *
 * <AUTHOR>
 * @date 2022-09-15
 */
@Slf4j
@Service
public class DataCollectServiceImpl implements DataCollectService {

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private ActivityCacheService activityCacheService;

    @Override
    public void slotRequestInWechat(Long slotId, boolean isWxEnv) {
        if (!isWxEnv) {
            return;
        }
        GlobalThreadPool.executorService.submit(() -> {
            String key = EngineRedisKeyFactory.K052.join(DateUtil.today());
            boolean exist = redisCache.hasKey(key);
            redisCache.addCacheSet(key, String.valueOf(slotId));
            if (!exist) {
                redisCache.expire(key, 3, TimeUnit.DAYS);
            }
        });
    }

    @Override
    public void slotRequestRedirectActivityInWechat(final String activityUrl, boolean isWxEnv) {
        if (!isWxEnv) {
            return;
        }
        GlobalThreadPool.executorService.submit(() -> {
            String key =  EngineRedisKeyFactory.K050.join(DateUtil.thisHour(true));
            boolean exist = redisCache.hasKey(key);
            if (!exist) {
                redisCache.addCacheSet(key, UrlUtils.extractUrl(activityUrl));
                redisCache.expire(key, 3, TimeUnit.HOURS);
            }
        });
    }

    /**
     * 获取当前时刻
     */
    private int getCurQuarter() {
        return DateUtil.thisHour(true) * 4 + DateUtil.thisMinute() / 15;
    }
}
