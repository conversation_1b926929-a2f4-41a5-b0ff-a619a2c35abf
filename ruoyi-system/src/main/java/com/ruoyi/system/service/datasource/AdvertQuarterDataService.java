package com.ruoyi.system.service.datasource;

import com.ruoyi.system.entity.datashow.AdvertQuarterDataEntity;

import java.util.Date;

/**
 * 广告维度时刻数据表 Service
 *
 * <AUTHOR>
 * @date 2022-10-21 14:39:16
 */
public interface AdvertQuarterDataService {

    /**
     * 查询广告维度时刻数据
     *
     * @param param 查询条件
     * @return 广告维度时刻数据
     */
    AdvertQuarterDataEntity selectBy(AdvertQuarterDataEntity param);

    /**
     * 查询广告日期数据
     *
     * @param advertId 广告ID
     * @param curDate 日期
     * @return 广告维度时刻数据
     */
    AdvertQuarterDataEntity selectByAdvertAndDate(Long advertId, Date curDate);

    /**
     * 查询广告维度时刻数据
     *
     * @param advertId 广告ID
     * @param curDate 日期
     * @param curQuarter 时刻
     * @return 广告维度时刻数据
     */
    AdvertQuarterDataEntity selectHourDataByAdvertAndDateQuarter(Long advertId, Date curDate, Integer curQuarter);

    /**
     * 新增记录
     */
    int insert(AdvertQuarterDataEntity entity);

    /**
     * 根据id更新
     */
    int updateById(AdvertQuarterDataEntity entity);

    /**
     * 根据id获取
     */
    AdvertQuarterDataEntity selectById(Long id);
}
