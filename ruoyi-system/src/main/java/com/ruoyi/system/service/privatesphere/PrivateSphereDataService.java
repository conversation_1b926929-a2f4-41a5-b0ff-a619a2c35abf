package com.ruoyi.system.service.privatesphere;

import com.ruoyi.system.bo.privatesphere.PrivateSphereDataListBO;
import com.ruoyi.system.entity.privatesphere.PrivateSphereDataEntity;

import java.util.Date;
import java.util.List;

/**
 * 私域数据表 Service
 *
 * <AUTHOR>
 * @date 2023-2-10 14:18:11
 */
public interface PrivateSphereDataService {
    /**
     * 新增记录
     */
    Boolean insert(PrivateSphereDataEntity entity);

    /**
     * 根据id删除
     */
    Boolean deleteById(Long id);

    /**
     * 根据id更新
     */
    Boolean updateById(PrivateSphereDataEntity entity);

    /**
     * 根据id获取
     */
    PrivateSphereDataEntity selectById(Long id);

    /**
     * 根据日期和产品查询
     * @param curDate
     * @param productId
     * @return
     */
    PrivateSphereDataEntity selectByDateAndProduct(Date curDate ,Long accountId,Long productId);

    /**
     * 根据条件查询私域数据
     * @param bo
     * @return
     */
    List<PrivateSphereDataEntity> selectListByParam(PrivateSphereDataListBO bo);

    /**
     * 统计总数据
     * @param bo
     * @return
     */
    PrivateSphereDataEntity statisticsByParam(PrivateSphereDataListBO bo);

    /**
     * 根据条件查询id列表
     * @param bo
     * @return
     */
    List<Long> selectIdsByParam(PrivateSphereDataListBO bo);

    /**
     * 根据产品id查询数据数，删除产品校验
     * @param productId
     * @return
     */
    int countByProductId(Long productId);
}
