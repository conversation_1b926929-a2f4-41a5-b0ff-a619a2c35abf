package com.ruoyi.system.service.datasource.impl;

import com.ruoyi.system.entity.datashow.AdvertAppDayData;
import com.ruoyi.system.mapper.datashow.AdvertAppDayDataMapper;
import com.ruoyi.system.service.datasource.AdvertAppDayDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 广告媒体日数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-10-15
 */
@Service
public class AdvertAppDataServiceImpl implements AdvertAppDayDataService {

    @Autowired
    private AdvertAppDayDataMapper advertAppDayDataMapper;

    @Override
    public AdvertAppDayData selectBy(AdvertAppDayData param) {
        return advertAppDayDataMapper.selectBy(param);
    }

    /**
     * 查询广告媒体日数据列表
     *
     * @param param 查询条件
     * @return 广告媒体日数据
     */
    @Override
    public List<AdvertAppDayData> selectAdvertAppDayDataList(AdvertAppDayData param) {
        return advertAppDayDataMapper.selectAdvertAppDayDataList(param);
    }

    @Override
    public AdvertAppDayData selectStatisticAdvertAppDayData(AdvertAppDayData param) {
        return advertAppDayDataMapper.selectStatisticAdvertAppDayData(param);
    }

    /**
     * 新增广告媒体日数据
     *
     * @param param 广告媒体日数据
     * @return 结果
     */
    @Override
    public int insertAdvertAppDayData(AdvertAppDayData param) {
        if (null == param.getCurDate() || null == param.getAdvertId()) {
            return 0;
        }
        return advertAppDayDataMapper.insertAdvertAppDayData(param);
    }

    /**
     * 修改广告媒体日数据
     *
     * @param param 广告媒体日数据
     * @return 结果
     */
    @Override
    public int updateAdvertAppDayData(AdvertAppDayData param) {
        if (null == param.getId()) {
            return 0;
        }
        return advertAppDayDataMapper.updateAdvertAppDayData(param);
    }
}
