package com.ruoyi.system.service.datasource.processor;

import com.alibaba.excel.util.StringUtils;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.DataDimensionEnum;
import com.ruoyi.common.enums.InnerLogType;
import com.ruoyi.common.enums.advert.ConvType;
import com.ruoyi.common.utils.BizUtils;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.datashow.DataStatContext;
import com.ruoyi.system.entity.datashow.LandpageDayData;
import com.ruoyi.system.req.datashow.DataStatReq;
import com.ruoyi.system.service.datasource.DataStatService;
import com.ruoyi.system.service.datasource.LandpageDayDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

import static com.ruoyi.common.enums.DataDimensionEnum.LANDPAGE_DAY;

/**
 * 广告位插件维度广告日数据处理器
 *
 * <AUTHOR>
 * @date 2021/12/31
 */
@Slf4j
@Service
public class LandpageDayProcessor implements DataProcessor, InitializingBean {

    @Autowired
    private LandpageDayDataService landpageDayDataService;

    @Autowired
    private RedisCache redisCache;

    @Override
    public DataDimensionEnum getDimension() {
        return LANDPAGE_DAY;
    }

    @Override
    public boolean validate(DataStatContext context) {
        DataStatReq req = context.getReq();
        return null != req.getDate() && null != req.getConsumerId();
    }

    @Override
    public boolean process(DataStatContext context) {
        DataStatReq req = context.getReq();
        InnerLogType type = context.getType();
        int pv = 1;
        int uv = 0;

        if (StringUtils.isBlank(req.getLandpageKey())) {
            return true;
        }

        // 计算uv
        switch (type) {
            case ADVERT_LAUNCH:
            case ADVERT_EXPOSURE:
            case ADVERT_CLICK:
            case LANDPAGE_EXPOSURE:
            case LANDPAGE_CLICK:
            case BLIND_BOX_JOIN:
            case BLIND_BOX_POPUP_EXPOSURE:
            case BLIND_BOX_POPUP_CLICK:
                String datStr = DateUtils.dateTime(req.getDate());
                String uvKey = EngineRedisKeyFactory.K012.join(type.getType(), req.getLandpageKey(), datStr);
                uv = BizUtils.countUv(uvKey, String.valueOf(req.getConsumerId()));
                break;
            default:
                break;
        }

        // 数据
        LandpageDayData updateData = new LandpageDayData();
        updateData.setId(getDataId(req));

        switch (type) {
            case ADVERT_LAUNCH:
                updateData.setAdLaunchPvAdd(pv);
                updateData.setAdLaunchUvAdd(uv);
                break;
            case ADVERT_EXPOSURE:
                updateData.setAdExposurePvAdd(pv);
                updateData.setAdExposureUvAdd(uv);
                break;
            case ADVERT_CLICK:
                updateData.setAdClickPvAdd(pv);
                updateData.setAdClickUvAdd(uv);
                break;
            case LANDPAGE_EXPOSURE:
                updateData.setLpExposurePvAdd(pv);
                updateData.setLpExposureUvAdd(uv);
                break;
            case LANDPAGE_CLICK:
                updateData.setLpClickPvAdd(pv);
                updateData.setLpClickUvAdd(uv);
                break;
            case BLIND_BOX_JOIN:
                updateData.setLpJoinPvAdd(pv);
                updateData.setLpJoinUvAdd(uv);
                break;
            case BLIND_BOX_POPUP_EXPOSURE:
                updateData.setPopupExposurePvAdd(pv);
                updateData.setPopupExposureUvAdd(uv);
                break;
            case BLIND_BOX_POPUP_CLICK:
                updateData.setPopupClickPvAdd(pv);
                updateData.setPopupClickUvAdd(uv);
                break;
            case CONVERT_EVENT:
                if (ConvType.isPay(req.getConvType())) {
                    updateData.setPayAdd(pv);
                } else if (ConvType.isRegister(req.getConvType())) {
                    updateData.setRegisterAdd(pv);
                }
                break;
            default:
                break;
        }
        return landpageDayDataService.updateById(updateData) > 0;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        DataStatService.register(this);
    }

    /**
     * 通过缓存获取数据ID
     */
    private Long getDataId(DataStatReq req) {
        String key = EngineRedisKeyFactory.K022.join("LandpageDayData", req.getDateStr(), req.getLandpageKey());
        Long dataId = redisCache.getCacheObject(key);
        if (null != dataId) {
            return dataId;
        }

        // 查询并初始数据
        LandpageDayData param = new LandpageDayData();
        param.setCurDate(req.getDate());
        param.setLandpageKey(req.getLandpageKey());
        LandpageDayData data = landpageDayDataService.selectBy(param);
        if (null == data) {
            data = new LandpageDayData();
            data.setCurDate(req.getDate());
            data.setLandpageKey(req.getLandpageKey());
            landpageDayDataService.insert(data);
            data = landpageDayDataService.selectBy(param);
        }
        redisCache.setCacheObject(key, data.getId(), 1, TimeUnit.DAYS);
        return data.getId();
    }
}
