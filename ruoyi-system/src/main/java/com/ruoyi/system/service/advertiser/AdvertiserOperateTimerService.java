package com.ruoyi.system.service.advertiser;

import com.ruoyi.system.entity.advertiser.AdvertiserOperateTimerEntity;

import java.util.List;
import java.util.Map;

/**
 * 广告主操作定时任务 Service
 *
 * <AUTHOR>
 * @date 2023-4-20 11:39:56
 */
public interface AdvertiserOperateTimerService {

    /**
     * 新增定时任务
     *
     * @param entity 参数
     * @return 影响行数
     */
    int add(AdvertiserOperateTimerEntity entity);

    /**
     * 执行定时任务
     *
     * @param entity 参数
     * @return 影响行数
     */
    int execTimer(AdvertiserOperateTimerEntity entity);

    /**
     * 根据id更新
     */
    Boolean updateById(AdvertiserOperateTimerEntity entity);

    /**
     * 根据id获取
     */
    AdvertiserOperateTimerEntity selectById(Long id);

    /**
     * 查询广告主待执行的定时任务
     *
     * @param advertiserIds 广告主ID列表
     * @return 广告主ID-定时任务列表映射
     */
    Map<Long, List<AdvertiserOperateTimerEntity>> selectPlanMapByAdvertiserIds(List<Long> advertiserIds);

    /**
     * 查询可以执行的定时任务
     *
     * @return 定时任务列表
     */
    List<AdvertiserOperateTimerEntity> selectReadyTimer();
}
