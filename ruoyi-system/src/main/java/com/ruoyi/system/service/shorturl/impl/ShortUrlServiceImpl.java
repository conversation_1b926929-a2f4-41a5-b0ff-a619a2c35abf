package com.ruoyi.system.service.shorturl.impl;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.shorturl.ShortUrlEntity;
import com.ruoyi.system.mapper.shorturl.ShortUrlMapper;
import com.ruoyi.system.service.shorturl.ShortUrlService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 短链表 Service
 *
 * <AUTHOR>
 * @date 2022-10-8 14:16:35
 */
@Service
public class ShortUrlServiceImpl implements ShortUrlService {

    @Autowired
    private ShortUrlMapper shortUrlMapper;

    @Override
    public Boolean insert(ShortUrlEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return shortUrlMapper.insert(entity) > 0;
    }

    @Override
    public Boolean deleteById(Long id) {
        if (Objects.isNull(id)) {
            return false;
        }
        return shortUrlMapper.deleteById(id) > 0;
    }

    @Override
    public int updateById(ShortUrlEntity entity) {
        if (Objects.isNull(entity)) {
            return 0;
        }
        return shortUrlMapper.updateById(entity);
    }

    @Override
    public ShortUrlEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return shortUrlMapper.selectById(id);
    }

    @Override
    public ShortUrlEntity selectByOriginUrlMd5(String originUrlMd5) {
        if (StringUtils.isBlank(originUrlMd5)) {
            return null;
        }
        ShortUrlEntity param = new ShortUrlEntity();
        param.setOriginUrlMd5(originUrlMd5);
        return shortUrlMapper.selectBy(param);
    }

    @Override
    public List<ShortUrlEntity> selectList(ShortUrlEntity param) {
        return shortUrlMapper.selectList(param);
    }

    @Override
    public List<ShortUrlEntity> selectListByIds(List<Long> ids) {
        if(CollectionUtils.isEmpty(ids)){
            return Collections.emptyList();
        }
        return shortUrlMapper.selectListByIds(ids);
    }

    @Override
    public List<ShortUrlEntity> selectByOriginUrlMd5List(List<String> originUrlMd5List) {
        if (CollectionUtils.isEmpty(originUrlMd5List)){
            return Collections.emptyList();
        }
        return shortUrlMapper.selectByOriginUrlMd5List(originUrlMd5List);
    }

    @Override
    public Boolean insertBatch(List<ShortUrlEntity> shortUrlEntities) {
        if (CollectionUtils.isEmpty(shortUrlEntities)){
            return false;
        }
        return shortUrlMapper.insertBatch(shortUrlEntities);
    }

    @Override
    public Boolean updateBatchById(List<ShortUrlEntity> shortUrlEntities) {
        if (CollectionUtils.isEmpty(shortUrlEntities)){
            return false;
        }
        return shortUrlMapper.updateBatchById(shortUrlEntities);
    }
}