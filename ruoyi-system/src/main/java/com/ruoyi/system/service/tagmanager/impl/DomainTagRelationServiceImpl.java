package com.ruoyi.system.service.tagmanager.impl;

import com.ruoyi.common.utils.NumberUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.tagmanager.DomainTagRelationService;
import com.ruoyi.system.entity.tagmanager.DomainTagRelationEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import com.ruoyi.system.mapper.tagmanager.DomainTagRelationMapper;

/**
 * 域名标签关联表 Service
 *
 * <AUTHOR>
 * @date 2022-12-26 14:32:58
 */
@Service
public class DomainTagRelationServiceImpl implements DomainTagRelationService {
    @Autowired
    private DomainTagRelationMapper domainTagRelationMapper;

    @Override
    public Boolean insert(DomainTagRelationEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return domainTagRelationMapper.insert(entity) > 0;
    }

    @Override
    public Boolean deleteById(Long id) {
        if (Objects.isNull(id)) {
            return false;
        }
        return domainTagRelationMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean updateById(DomainTagRelationEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return domainTagRelationMapper.updateById(entity) > 0;
    }

    @Override
    public DomainTagRelationEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return domainTagRelationMapper.selectById(id);
    }

    @Override
    public int countByTagId(Long tagId) {
        if (NumberUtils.isNullOrLteZero(tagId)) {
            return 0;
        }
        return domainTagRelationMapper.countByTagId(tagId);
    }

    @Override
    public List<Long> selectTagIdsByTagIds(List<Long> tagIds) {
        if(CollectionUtils.isEmpty(tagIds)){
            return Collections.emptyList();
        }
        return domainTagRelationMapper.selectTagIdsByTagIds(tagIds);
    }

    @Override
    public List<Long> selectTagIdsByDomainId(Long domainId) {
        if(NumberUtils.isNullOrLteZero(domainId)){
            return Collections.emptyList();
        }
        return domainTagRelationMapper.selectTagIdsByDomainId(domainId);
    }

    @Override
    public Boolean deleteByDomainId(Long domainId) {
        return domainTagRelationMapper.deleteByDomainId(domainId) > 0;
    }

    @Override
    public Boolean batchInsert(List<DomainTagRelationEntity> entities) {
        if(CollectionUtils.isEmpty(entities)){
            return false;
        }
        return domainTagRelationMapper.batchInsert(entities) > 0;
    }
}
