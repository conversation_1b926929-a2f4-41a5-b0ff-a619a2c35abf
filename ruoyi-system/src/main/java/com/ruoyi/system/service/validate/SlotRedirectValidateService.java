package com.ruoyi.system.service.validate;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.text.StrSplitter;
import com.alibaba.fastjson.JSON;
import com.ruoyi.common.enums.advert.AdvertCategory;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.slot.AreaTargetRedirectItem;
import com.ruoyi.system.domain.slot.ShuntRedirectItem;
import com.ruoyi.system.entity.activity.ActivityPlan;
import com.ruoyi.system.entity.advert.Advert;
import com.ruoyi.system.entity.advert.AdvertOrientation;
import com.ruoyi.system.service.advert.AdvertOrientationService;
import com.ruoyi.system.service.manager.ActivityPlanService;
import com.ruoyi.system.service.advert.AdvertService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;

import static com.ruoyi.common.constant.BizConstants.CHINA_AREA_NUM;
import static com.ruoyi.common.enums.activity.ActPlanStatusEnum.isActPlanOpen;
import static com.ruoyi.common.enums.slot.SlotRedirectType.redirectToActivity;
import static com.ruoyi.common.enums.slot.SlotRedirectType.redirectToAdvert;
import static com.ruoyi.common.enums.slot.SlotRedirectType.redirectToAdvertOrient;

/**
 * 广告位跳转校验服务
 *
 * <AUTHOR>
 * @date 2022-04-27
 */
@Service
public class SlotRedirectValidateService {

    @Autowired
    private ActivityPlanService activityPlanService;

    @Autowired
    private AdvertService advertService;

    @Autowired
    private AdvertOrientationService advertOrientationService;

    /**
     * 检查分流跳转配置
     *
     * @param items 分流跳转项列表
     */
    public void checkSlotShuntRedirect(List<ShuntRedirectItem> items) {
        if (CollectionUtils.isEmpty(items)) {
            throw new CustomException("活动投放配置不能为空");
        }
        int percent = 0;
        for (ShuntRedirectItem item : items) {
            Integer ratio = item.getRatio();
            if (null == ratio || ratio < 0 || ratio > 100) {
                throw new CustomException("分流比例必须为[0,100]的整数");
            }
            if (redirectToActivity(item.getRedirectType()) && !checkActivityId(item.getRedirectValue())) {
                throw new CustomException("您输入的活动ID不存在或活动计划未开启");
            } else if (redirectToAdvert(item.getRedirectType()) && !checkAdvertIds(item.getRedirectValue())) {
                throw new CustomException("您输入的广告ID不存在");
            } else if (StringUtils.isBlank(item.getRedirectValue())) {
                throw new CustomException("跳转链接不能为空");
            }
            percent += ratio;
        }
        if (percent != 100) {
            throw new CustomException("请检查分流比例之和需为100%");
        }
    }

    /**
     * 检查地域定向跳转配置
     *
     * @param items 投放配置列表
     */
    public void checkSlotAreaTargetRedirect(List<AreaTargetRedirectItem> items) {
        boolean hasDefault = false;
        Set<String> areaSet = new HashSet<>();
        for (AreaTargetRedirectItem item : items) {
            // 检查地域
            if (CollectionUtils.isEmpty(item.getTargetArea())) {
                if (hasDefault) {
                    throw new CustomException("只能有一个默认地域配置");
                }
                hasDefault = true;
            } else if (item.getTargetArea().contains(CHINA_AREA_NUM)) {
                throw new CustomException("请选择具体的省市");
            } else {
                if (CollUtil.containsAny(areaSet, item.getTargetArea())) {
                    throw new CustomException("不同配置请勿选择相同省市");
                }
                areaSet.addAll(item.getTargetArea());
            }
            // 检查分流跳转配置
            checkSlotShuntRedirect(item.getRedirectValue());
        }
        if (!hasDefault) {
            throw new CustomException("默认配置不能选择地域");
        }
    }

    /**
     * 检查是否包含直投广告
     *
     * @param redirectValue 地域定向跳转配置
     * @param advertId 广告ID
     * @return true.包含,false.不包含
     */
    public boolean isContainsAdvert(String redirectValue, String advertId) {
        if (StringUtils.isBlank(redirectValue) || !StringUtils.isNumeric(advertId)) {
            return false;
        }
        List<AreaTargetRedirectItem> items = JSON.parseArray(redirectValue, AreaTargetRedirectItem.class);
        if (CollectionUtils.isEmpty(items)) {
            return false;
        }
        for (AreaTargetRedirectItem item : items) {
            if (isContainsAdvert(item.getRedirectValue(), advertId)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查是否包含直投广告
     *
     * @param items 分流跳转项列表
     * @param advertId 广告ID
     * @return true.包含,false.不包含
     */
    public boolean isContainsAdvert(List<ShuntRedirectItem> items, String advertId) {
        if (CollectionUtils.isEmpty(items)) {
            return false;
        }
        for (ShuntRedirectItem item : items) {
            if (redirectToAdvert(item.getRedirectType()) && Objects.equals(item.getRedirectValue(), advertId)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查是否包含直投广告配置
     *
     * @param items 分流跳转项列表
     * @param orientId 广告配置ID
     * @return true.包含,false.不包含
     */
    public boolean isContainsAdvertOrient(List<ShuntRedirectItem> items, String orientId) {
        if (CollectionUtils.isEmpty(items)) {
            return false;
        }
        for (ShuntRedirectItem item : items) {
            if (redirectToAdvertOrient(item.getRedirectType()) && Objects.equals(item.getRedirectValue(), orientId)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查是否包含直投广告配置
     *
     * @param redirectValue 地域定向跳转配置
     * @param orientId 配置ID
     * @return true.包含,false.不包含
     */
    public boolean isContainsAdvertOrient(String redirectValue, String orientId) {
        if (StringUtils.isBlank(redirectValue) || !StringUtils.isNumeric(orientId)) {
            return false;
        }
        List<AreaTargetRedirectItem> items = JSON.parseArray(redirectValue, AreaTargetRedirectItem.class);
        if (CollectionUtils.isEmpty(items)) {
            return false;
        }
        for (AreaTargetRedirectItem item : items) {
            if (isContainsAdvertOrient(item.getRedirectValue(), orientId)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 校验是否有效的活动ID
     *
     * @param activityIdStr 活动ID字符串
     * @return 是否校验通过
     */
    public boolean checkActivityId(String activityIdStr) {
        if (!StringUtils.isNumeric(activityIdStr)) {
            return false;
        }
        Long activityId = Long.valueOf(activityIdStr);

        // 查询活动对应的推广计划
        ActivityPlan actPlan = activityPlanService.selectByActivityId(activityId);
        return null != actPlan && isActPlanOpen(actPlan.getStatus());
    }

    /**
     * 校验是否有效的广告ID
     *
     * @param advertIdStr 广告ID字符串
     * @return 是否校验通过
     */
    public boolean checkAdvertId(String advertIdStr) {
        if (!StringUtils.isNumeric(advertIdStr)) {
            return false;
        }
        Long advertId = Long.valueOf(advertIdStr);

        // 查询广告
        Advert advert = advertService.selectAdvertById(advertId);
        return null != advert && !Objects.equals(advert.getAdvertCategory(), AdvertCategory.MINI_PROGRAM.getType());
    }

    /**
     * 校验是否有效的广告ID列表
     *
     * @param advertIdStr 广告ID字符串
     * @return 是否校验通过
     */
    public boolean checkAdvertIds(String advertIdStr) {
        if (StringUtils.isBlank(advertIdStr))  {
            return false;
        }
        if (StringUtils.isNumeric(advertIdStr)) {
            return checkAdvertId(advertIdStr);
        }
        List<Long> advertIds = StrSplitter.split(advertIdStr, ',', -1, true, Long::valueOf);
        List<Advert> adverts = advertService.selectByIds(advertIds, Function.identity());
        if (CollectionUtils.isEmpty(advertIds)) {
            return false;
        }
        for (Advert advert : adverts) {
            if (Objects.equals(advert.getAdvertCategory(), AdvertCategory.MINI_PROGRAM.getType())) {
                return false;
            }
        }
        return true;
    }

    /**
     * 校验是否有效的广告配置ID
     *
     * @param orientIdStr 广告配置ID字符串
     * @return 是否校验通过
     */
    public boolean checkOrientId(String orientIdStr) {
        if (!StringUtils.isNumeric(orientIdStr)) {
            return false;
        }
        AdvertOrientation orient = advertOrientationService.selectAdvertOrientationById(Long.valueOf(orientIdStr));
        return null != orient && Objects.equals(orient.getIsDeleted(), 0);
    }

    /**
     * 校验是否有效的广告配置ID列表
     *
     * @param orientIdStr 广告配置ID字符串
     * @return 校验结果
     */
    public Pair<Boolean, Object> checkOrientIds(String orientIdStr) {
        if (StringUtils.isBlank(orientIdStr))  {
            return Pair.of(false, orientIdStr);
        }
        if (StringUtils.isNumeric(orientIdStr)) {
            return Pair.of(checkOrientId(orientIdStr), orientIdStr);
        }
        List<Long> orientIds = StrSplitter.split(orientIdStr, ',', -1, true, Long::valueOf);
        Map<Long, AdvertOrientation> orientMap = advertOrientationService.selectMapByIds(orientIds);
        for (Long orientId : orientIds) {
            AdvertOrientation orient = orientMap.get(orientId);
            if (null == orient || Objects.equals(orient.getIsDeleted(), 1)) {
                return Pair.of(false, orientId);
            }
        }
        return Pair.of(true, null);
    }
}
