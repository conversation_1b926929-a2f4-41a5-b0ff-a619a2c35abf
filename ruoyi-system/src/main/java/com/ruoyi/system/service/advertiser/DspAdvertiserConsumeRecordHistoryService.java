package com.ruoyi.system.service.advertiser;

import com.ruoyi.system.entity.advertiser.DspAdvertiserConsumeRecordHistoryEntity;

/**
 * DSP广告主消费修改记录表 Service
 *
 * <AUTHOR>
 * @date 2022-7-13 17:28:38
 */
public interface DspAdvertiserConsumeRecordHistoryService {

    /**
     * 新增记录
     */
    Boolean insert(DspAdvertiserConsumeRecordHistoryEntity entity);

    /**
     * 根据id更新
     */
    Boolean updateById(DspAdvertiserConsumeRecordHistoryEntity entity);

    /**
     * 根据id获取
     */
    DspAdvertiserConsumeRecordHistoryEntity selectById(Long id);
}
