package com.ruoyi.system.service.datasource.processor;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.enums.DataDimensionEnum;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.system.domain.datashow.DataStatContext;
import com.ruoyi.system.req.datashow.DataStatReq;
import com.ruoyi.system.service.datasource.DataStatService;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

import static com.ruoyi.common.enums.DataDimensionEnum.ORIENT_CONSUME_DAY;

/**
 * 配置消耗日数据处理器
 *
 * <AUTHOR>
 * @date 2023/7/5
 */
@Service
public class OrientConsumeDayProcessor implements DataProcessor, InitializingBean {

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Override
    public DataDimensionEnum getDimension() {
        return ORIENT_CONSUME_DAY;
    }

    @Override
    public boolean validate(DataStatContext context) {
        DataStatReq req = context.getReq();
        return null != req.getDate() && null != req.getOrientId();
    }

    @Override
    public boolean process(DataStatContext context) {
        DataStatReq req = context.getReq();

        int unitPrice = NumberUtils.defaultInt(req.getUnitPrice());
        redisAtomicClient.incrBy(EngineRedisKeyFactory.K082.join(DateUtil.formatDate(req.getDate()), req.getOrientId()), unitPrice, 1,  TimeUnit.DAYS);
        return true;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        DataStatService.register(this);
    }
}
