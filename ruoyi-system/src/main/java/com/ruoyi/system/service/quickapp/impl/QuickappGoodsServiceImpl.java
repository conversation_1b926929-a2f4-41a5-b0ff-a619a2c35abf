package com.ruoyi.system.service.quickapp.impl;

import org.springframework.stereotype.Service;
import com.ruoyi.system.service.quickapp.QuickappGoodsService;
import com.ruoyi.system.entity.quickapp.QuickappGoodsEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;

import com.ruoyi.system.mapper.quickapp.QuickappGoodsMapper;

/**
 * 快应用商品表 Service
 *
 * <AUTHOR>
 * @date 2022-8-15 14:15:35
 */
@Service
public class QuickappGoodsServiceImpl implements QuickappGoodsService {
    @Autowired
    private QuickappGoodsMapper quickappGoodsMapper;

    @Override
    public Boolean insert(QuickappGoodsEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return quickappGoodsMapper.insert(entity) > 0;
    }

    @Override
    public Boolean deleteById(Long id) {
        if (Objects.isNull(id)) {
            return false;
        }
        return quickappGoodsMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean updateById(QuickappGoodsEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return quickappGoodsMapper.updateById(entity) > 0;
    }

    @Override
    public QuickappGoodsEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return quickappGoodsMapper.selectById(id);
    }

    @Override
    public List<QuickappGoodsEntity> selectList() {
        return quickappGoodsMapper.selectList();
    }
}
