package com.ruoyi.system.service.landpage;

import com.ruoyi.system.entity.datashow.LandpageFormSendRecordEntity;

import java.util.List;
import java.util.Map;

/**
 * 落地页表单上报记录 Service
 *
 * <AUTHOR>
 * @date 2021-12-18 16:22:06
 */
public interface LandpageFormSendRecordService {

    /**
     * 新增记录
     *
     * @param entity 新增对象
     * @return 结果
     */
    int insert(LandpageFormSendRecordEntity entity);

    /**
     * 根据id更新
     *
     * @param entity 更新对象
     * @return 结果
     */
    Boolean updateById(LandpageFormSendRecordEntity entity);

    /**
     * 根据id获取
     *
     * @param id id
     * @return 结果
     */
    LandpageFormSendRecordEntity selectById(Long id);

    /**
     * 根据表单ID获取
     *
     * @param recordId 表单ID
     * @return 结果
     */
    LandpageFormSendRecordEntity selectByRecordId(Long recordId);

    /**
     * 根据广告主ID和回传记录ID判断是否存在
     *
     * @param advertiserId 广告主ID
     * @param recordId 回传记录ID
     * @return 是否存在
     */
    Boolean existByAdvertiserIdAndRecordId(Long advertiserId, Long recordId);

    /**
     * 根据回传记录ID判断是否存在
     *
     * @param recordId 回传记录ID
     * @return 是否存在
     */
    Boolean existByRecordId(Long recordId);

    /**
     * 查询记录发送的广告主映射
     *
     * @param recordIds 记录ID列表
     * @return 记录ID-回传记录映射
     */
    Map<Long, LandpageFormSendRecordEntity> selectMapByRecordIds(List<Long> recordIds);

    /**
     * 查询所有的上报广告主ID列表
     *
     * @return 广告ID列表
     */
    List<Long> selectTotalTargetAdvertiserIds();
}
