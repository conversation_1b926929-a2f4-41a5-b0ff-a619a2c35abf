package com.ruoyi.system.service.landpage.article;

import com.ruoyi.system.entity.landpage.article.ArticleRetDataEntity;

import java.util.Date;

/**
 * 文章返回拦截数据表 Service
 *
 * <AUTHOR>
 * @date 2024-2-29 17:39:13
 */
public interface ArticleRetDataService {

    /**
     * 新增记录
     */
    Boolean insert(ArticleRetDataEntity entity);

    /**
     * 根据id更新
     */
    Boolean updateById(ArticleRetDataEntity entity);

    /**
     * 根据id获取
     */
    ArticleRetDataEntity selectById(Long id);

    /**
     * 查询返回拦截数据
     */
    ArticleRetDataEntity selectBy(Date curDate, Long linkId, String retUrlMd5);

    /**
     * 更新数据
     */
    Boolean update(Long id, Integer requestPv, Integer requestUv);
}
