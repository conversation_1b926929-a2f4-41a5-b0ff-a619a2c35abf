package com.ruoyi.system.service.datasource;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.ruoyi.system.entity.datashow.AppData;

/**
 * 媒体数据Service接口
 *
 * <AUTHOR>
 * @date 2021-07-04
 */
public interface AppDataService {

    /**
     * 查询媒体数据
     *
     * @param id 媒体数据ID
     * @return 媒体数据
     */
    AppData selectAppDataById(Long id);

    /**
     * 查询媒体数据
     *
     * @param appId 媒体Id
     * @param curDate 日期
     * @return 广告位数据
     */
    AppData selectByAppIdAndDate(Long appId, Date curDate);

    /**
     * 查询媒体数据列表
     *
     * @param appData 媒体数据
     * @param isExport 是否导出
     * @return 媒体数据集合
     */
    List<AppData> selectAppDataList(AppData appData, boolean isExport);

    /**
     * 新增媒体数据
     *
     * @param appData 媒体数据
     * @return 结果
     */
    int insertAppData(AppData appData);

    /**
     * 修改媒体数据
     *
     * @param appData 媒体数据
     * @return 结果
     */
    int updateAppData(AppData appData);
    /**
     * 修改诺禾消耗数据
     *
     * @param appData 媒体数据
     * @return 结果
     */
    int updateAppNhCostData(AppData appData);

    /**
     * 获取指定日期的媒体账号对应的媒体数据
     *
     * @param accountIds 媒体账号ID列表
     * @param date 日期
     * @return 媒体账号ID-媒体数据映射
     */
    Map<Long, AppData> groupByAccountId(List<Long> accountIds, Date date);

    /**
     * 获取指定日期的媒体数据
     *
     * @param appIds 媒体ID列表
     * @param date 日期
     * @return 媒体ID-媒体数据映射
     */
    Map<Long, AppData> groupByAppId(List<Long> appIds, Date date);

    /**
     * 查询媒体数据列表
     *
     * @param appData 媒体数据
     * @return 媒体数据集合
     */
    List<AppData> selectAppDataList(AppData appData);

    /**
     * 根据日期筛选指定月份的所有媒体id数据
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 媒体id列表
     */
    List<Long> selectDistinctAppIdByDate(Date startDate, Date endDate);

    /**
     * 批量新增更新
     * @param datas
     * @return
     */
    boolean batchInsertOrUpdate(List<AppData> datas);
}
