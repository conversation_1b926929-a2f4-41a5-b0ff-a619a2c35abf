package com.ruoyi.system.service.datasource.impl;

import com.ruoyi.system.bo.advert.OrientDayDataBo;
import com.ruoyi.system.bo.advert.OrientHourDataParam;
import com.ruoyi.system.bo.advert.OrientHourDataUpdateParam;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.datasource.OrientHourDataService;
import com.ruoyi.system.entity.datashow.OrientHourDataEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import com.ruoyi.system.mapper.datashow.OrientHourDataMapper;

/**
 * 广告配置数据Service实现
 *
 * <AUTHOR>
 * @date 2023-7-11
 */
@Service
public class OrientHourDataServiceImpl implements OrientHourDataService {

    @Autowired
    private OrientHourDataMapper orientHourDataMapper;

    @Override
    public OrientHourDataEntity selectBy(OrientHourDataEntity param) {
        return orientHourDataMapper.selectBy(param);
    }

    @Override
    public List<OrientDayDataBo> selectOrientDayDataList(OrientHourDataParam param) {
        return orientHourDataMapper.selectOrientDayDataList(param);
    }

    @Override
    public List<OrientDayDataBo> selectOrientHourDataList(OrientHourDataParam param) {
        return orientHourDataMapper.selectOrientHourDataList(param);
    }

    @Override
    public List<OrientDayDataBo> selectOrientHourDataListGroupByDateHour(OrientHourDataParam param) {
        return orientHourDataMapper.selectOrientHourDataListGroupByDateHour(param);
    }

    @Override
    public List<OrientDayDataBo> selectOrientAppDayDataList(OrientHourDataParam param) {
        return orientHourDataMapper.selectOrientAppDayDataList(param);
    }

    @Override
    public List<OrientDayDataBo> selectOrientSlotDayDataList(OrientHourDataParam param) {
        return orientHourDataMapper.selectOrientSlotDayDataList(param);
    }

    @Override
    public OrientDayDataBo selectStatisticOrientData(OrientHourDataParam param) {
        return orientHourDataMapper.selectStatisticOrientData(param);
    }

    @Override
    public boolean isExistBy(OrientHourDataParam param) {
        return orientHourDataMapper.isExistBy(param) != null;
    }

    @Override
    public int insert(OrientHourDataEntity entity) {
        if (Objects.isNull(entity)) {
            return 0;
        }
        return orientHourDataMapper.insert(entity);
    }

    @Override
    public int updateById(OrientHourDataUpdateParam param) {
        if (Objects.isNull(param)) {
            return 0;
        }
        return orientHourDataMapper.updateById(param);
    }

    @Override
    public List<OrientDayDataBo> selectOrientDataForLandpage(List<Long> advertIds, Date startDate, Date endDate, List<Date> invisibleDateList) {
        if (CollectionUtils.isEmpty(advertIds)) {
            return Collections.emptyList();
        }
        return orientHourDataMapper.selectOrientDataForLandpage(advertIds, startDate, endDate, invisibleDateList);
    }
}
