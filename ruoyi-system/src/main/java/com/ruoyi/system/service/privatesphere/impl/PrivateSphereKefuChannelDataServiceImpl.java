package com.ruoyi.system.service.privatesphere.impl;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.privatesphere.PrivateSphereKefuChannelDataService;
import com.ruoyi.system.entity.privatesphere.PrivateSphereKefuChannelDataEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import com.ruoyi.system.mapper.privatesphere.PrivateSphereKefuChannelDataMapper;

/**
 * 私域客服渠道数据表 Service
 *
 * <AUTHOR>
 * @date 2023-3-7 10:30:06
 */
@Service
public class PrivateSphereKefuChannelDataServiceImpl implements PrivateSphereKefuChannelDataService {
    @Autowired
    private PrivateSphereKefuChannelDataMapper privateSphereKefuChannelDataMapper;

    @Override
    public Boolean insert(PrivateSphereKefuChannelDataEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return privateSphereKefuChannelDataMapper.insert(entity) > 0;
    }

    @Override
    public Boolean deleteById(Long id) {
        if (Objects.isNull(id)) {
            return false;
        }
        return privateSphereKefuChannelDataMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean updateById(PrivateSphereKefuChannelDataEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return privateSphereKefuChannelDataMapper.updateById(entity) > 0;
    }

    @Override
    public PrivateSphereKefuChannelDataEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return privateSphereKefuChannelDataMapper.selectById(id);
    }

    @Override
    public int batchInsertOrUpdate(List<PrivateSphereKefuChannelDataEntity> entities) {
        if(CollectionUtils.isEmpty(entities)){
            return 0;
        }
        return privateSphereKefuChannelDataMapper.batchInsertOrUpdate(entities);
    }

    @Override
    public List<PrivateSphereKefuChannelDataEntity> selectListByDataIds(List<Long> ids) {
        if(CollectionUtils.isEmpty(ids)){
            return Collections.emptyList();
        }
        return privateSphereKefuChannelDataMapper.selectListByDataIds(ids);
    }

    @Override
    public List<PrivateSphereKefuChannelDataEntity> statisticsByDataIds(List<Long> dataIds) {
        if(CollectionUtils.isEmpty(dataIds)){
            return null;
        }
        return privateSphereKefuChannelDataMapper.statisticsByDataIds(dataIds);
    }
}
