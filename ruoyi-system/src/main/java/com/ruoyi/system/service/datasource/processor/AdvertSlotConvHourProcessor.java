package com.ruoyi.system.service.datasource.processor;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.enums.DataDimensionEnum;
import com.ruoyi.common.enums.InnerLogType;
import com.ruoyi.common.utils.BizUtils;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.datashow.DataStatContext;
import com.ruoyi.system.entity.datashow.AdvertSlotConvHourDataEntity;
import com.ruoyi.system.req.datashow.DataStatReq;
import com.ruoyi.system.service.datasource.AdvertSlotConvHourDataService;
import com.ruoyi.system.service.datasource.DataStatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.concurrent.TimeUnit;

import static com.ruoyi.common.enums.DataDimensionEnum.ADVERT_SLOT_CONV_HOUR;

/**
 * 广告广告位维度后端转化时段数据处理器
 *
 * <AUTHOR>
 * @date 2023/10/14
 */
@Slf4j
@Service
public class AdvertSlotConvHourProcessor implements DataProcessor, InitializingBean {

    @Autowired
    private AdvertSlotConvHourDataService advertSlotConvHourDataService;

    @Override
    public DataDimensionEnum getDimension() {
        return ADVERT_SLOT_CONV_HOUR;
    }

    @Override
    public boolean validate(DataStatContext context) {
        DataStatReq req = context.getReq();
        return null != req.getDate() && null != req.getHour() && null != req.getAdvertId() && null != req.getSlotId()
                && null != req.getConvType() && null != req.getConsumerId();
    }

    @Override
    public boolean process(DataStatContext context) {
        DataStatReq req = context.getReq();
        InnerLogType type = context.getType();
        int pv = 1;

        // 查询并初始数据
        AdvertSlotConvHourDataEntity param = new AdvertSlotConvHourDataEntity();
        param.setCurDate(req.getDate());
        param.setCurHour(req.getHour());
        param.setAdvertId(req.getAdvertId());
        param.setSlotId(req.getSlotId());
        param.setConvType(req.getConvType());
        AdvertSlotConvHourDataEntity data = advertSlotConvHourDataService.selectBy(param);
        if (null == data) {
            data = new AdvertSlotConvHourDataEntity();
            data.setCurDate(req.getDate());
            data.setCurHour(req.getHour());
            data.setAdvertId(req.getAdvertId());
            data.setSlotId(req.getSlotId());
            data.setConvType(req.getConvType());
            advertSlotConvHourDataService.insert(data);
            data = advertSlotConvHourDataService.selectBy(param);
        }

        // 计算uv
        String datStr = DateUtils.dateTime(req.getDate());
        String uvKey = EngineRedisKeyFactory.K012.join(type.getType(), req.getConvType(), req.getAdvertId(), req.getSlotId(), datStr, req.getHour());
        int uv = BizUtils.countUv(uvKey, String.valueOf(req.getConsumerId()), 1, TimeUnit.HOURS);

        // 数据
        AdvertSlotConvHourDataEntity updateData = new AdvertSlotConvHourDataEntity();
        updateData.setId(data.getId());
        updateData.setConvPvAdd(pv);
        updateData.setConvUvAdd(uv);
        updateData.setConvPriceAdd(parsePayPrice(req.getConvExt()));
        return advertSlotConvHourDataService.updateById(updateData) > 0;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        DataStatService.register(this);
    }

    /**
     * 从转化扩展参数中获取支付金额
     */
    private BigDecimal parsePayPrice(JSONObject convExt) {
        if (null != convExt) {
            try {
                return convExt.getBigDecimal("price");
            } catch (Exception e) {
                log.error("parsePayPrice error");
            }
        }
        return null;
    }
}
