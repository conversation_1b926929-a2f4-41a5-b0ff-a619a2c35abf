package com.ruoyi.system.service.datasource.impl;

import com.ruoyi.system.entity.datashow.SlotActivityHourData;
import com.ruoyi.system.mapper.datashow.SlotActivityHourDataMapper;
import com.ruoyi.system.service.datasource.SlotActivityHourDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 广告位维度分时段活动数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2021-07-21
 */
@Service
public class SlotActivityHourDataServiceImpl implements SlotActivityHourDataService {

    @Autowired
    private SlotActivityHourDataMapper slotActivityHourDataMapper;

    /**
     * 查询广告位维度活动数据列表
     * 
     * @param slotActivityHourData 广告位维度活动数据
     * @return 广告位维度活动数据
     */
    @Override
    public List<SlotActivityHourData> selectSlotActivityHourDataList(SlotActivityHourData slotActivityHourData) {
        return slotActivityHourDataMapper.selectSlotActivityHourDataList(slotActivityHourData);
    }

    @Override
    public SlotActivityHourData selectBy(Long slotId, Long activityId, Date curDate, Integer curHour) {
        return slotActivityHourDataMapper.selectBy(slotId, activityId, curDate, curHour);
    }

    /**
     * 新增广告位维度活动数据
     * 
     * @param slotActivityHourData 广告位维度活动数据
     * @return 结果
     */
    @Override
    public int insertSlotActivityHourData(SlotActivityHourData slotActivityHourData) {
        return slotActivityHourDataMapper.insertSlotActivityHourData(slotActivityHourData);
    }

    /**
     * 修改广告位维度活动数据
     * 
     * @param slotActivityHourData 广告位维度活动数据
     * @return 结果
     */
    @Override
    public int updateSlotActivityHourData(SlotActivityHourData slotActivityHourData) {
        if (null == slotActivityHourData.getId()) {
            return 0;
        }
        return slotActivityHourDataMapper.updateSlotActivityHourData(slotActivityHourData);
    }
}
