package com.ruoyi.system.service.quickapp.impl;

import org.springframework.stereotype.Service;
import com.ruoyi.system.service.quickapp.QuickappDynamicService;
import com.ruoyi.system.entity.quickapp.QuickappDynamicEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;

import com.ruoyi.system.mapper.quickapp.QuickappDynamicMapper;

/**
 * 快应用发布动态表 Service
 *
 * <AUTHOR>
 * @date 2022-8-8 10:58:02
 */
@Service
public class QuickappDynamicServiceImpl implements QuickappDynamicService {
    @Autowired
    private QuickappDynamicMapper quickappDynamicMapper;

    @Override
    public Boolean insert(QuickappDynamicEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return quickappDynamicMapper.insert(entity) > 0;
    }

    @Override
    public Boolean deleteById(Long id) {
        if (Objects.isNull(id)) {
            return false;
        }
        return quickappDynamicMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean updateById(QuickappDynamicEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return quickappDynamicMapper.updateById(entity) > 0;
    }

    @Override
    public QuickappDynamicEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return quickappDynamicMapper.selectById(id);
    }

    @Override
    public List<QuickappDynamicEntity> selectList() {
        return quickappDynamicMapper.selectList();
    }
}
