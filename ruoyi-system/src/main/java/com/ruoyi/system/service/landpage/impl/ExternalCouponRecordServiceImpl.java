package com.ruoyi.system.service.landpage.impl;

import org.springframework.stereotype.Service;
import com.ruoyi.system.service.landpage.ExternalCouponRecordService;
import com.ruoyi.system.entity.landpage.ExternalCouponRecordEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

import com.ruoyi.system.mapper.landpage.ExternalCouponRecordMapper;

/**
 * 捷停车发券记录表 Service
 *
 * <AUTHOR>
 * @date 2024-4-18 14:07:03
 */
@Service
public class ExternalCouponRecordServiceImpl implements ExternalCouponRecordService {

    @Autowired
    private ExternalCouponRecordMapper externalCouponRecordMapper;

    @Override
    public Boolean insert(ExternalCouponRecordEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return externalCouponRecordMapper.insert(entity) > 0;
    }

    @Override
    public Boolean updateById(ExternalCouponRecordEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return externalCouponRecordMapper.updateById(entity) > 0;
    }

    @Override
    public ExternalCouponRecordEntity selectByOrderId(String orderId) {
        if (null == orderId) {
            return null;
        }
        return externalCouponRecordMapper.selectByOrderId(orderId);
    }
}
