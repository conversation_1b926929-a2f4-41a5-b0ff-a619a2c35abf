package com.ruoyi.system.service.advertiser.impl;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.system.entity.datashow.AdvertiserConsumeData;
import com.ruoyi.system.service.datasource.AdvertiserConsumeDataService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.advertiser.AdvertiserBudgetService;
import com.ruoyi.system.entity.advertiser.AdvertiserBudgetEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.ruoyi.system.mapper.advertiser.AdvertiserBudgetMapper;
import org.springframework.transaction.annotation.Transactional;

/**
 * 广告主预算表 Service
 *
 * <AUTHOR>
 * @date 2023-4-17 11:42:58
 */
@Service
public class AdvertiserBudgetServiceImpl implements AdvertiserBudgetService {

    @Autowired
    private AdvertiserBudgetMapper advertiserBudgetMapper;

    @Autowired
    private AdvertiserConsumeDataService advertiserConsumeDataService;

    @Override
    public Boolean insert(AdvertiserBudgetEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return advertiserBudgetMapper.insert(entity) > 0;
    }

    @Override
    public Boolean updateById(AdvertiserBudgetEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return advertiserBudgetMapper.updateById(entity) > 0;
    }

    @Override
    public AdvertiserBudgetEntity selectByAccountId(Long accountId) {
        if (Objects.isNull(accountId)) {
            return null;
        }
        return advertiserBudgetMapper.selectByAccountId(accountId);
    }

    @Override
    public Long selectBudgetByAccountId(Long accountId) {
        if (null == accountId) {
            return null;
        }
        AdvertiserBudgetEntity entity = advertiserBudgetMapper.selectByAccountId(accountId);
        return null != entity ? entity.getBudget() : null;
    }

    @Override
    public Map<Long, Long> selectBudgetMapByAccountIds(List<Long> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return Collections.emptyMap();
        }
        List<AdvertiserBudgetEntity> list = advertiserBudgetMapper.selectListByAccountId(accountIds);
        return list.stream()
                .filter(s -> null != s.getBudget())
                .collect(Collectors.toMap(AdvertiserBudgetEntity::getAccountId, AdvertiserBudgetEntity::getBudget, (v1, v2) -> v2));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAdvertiserBudget(Long accountId, Long budget) {
        AdvertiserBudgetEntity budgetEntity = advertiserBudgetMapper.selectByAccountId(accountId);
        if (null == budgetEntity) {
            budgetEntity = new AdvertiserBudgetEntity();
            budgetEntity.setAccountId(accountId);
            budgetEntity.setBudget(budget);
            advertiserBudgetMapper.insert(budgetEntity);
        } else {
            budgetEntity.setBudget(budget);
            advertiserBudgetMapper.updateById(budgetEntity);
        }

        AdvertiserConsumeData consumeData = advertiserConsumeDataService.selectByDateAndAdvertiserId(DateUtil.beginOfDay(new Date()), accountId);
        if (null != consumeData) {
            advertiserConsumeDataService.updateBudget(consumeData.getId(), budget);
        }

    }
}
