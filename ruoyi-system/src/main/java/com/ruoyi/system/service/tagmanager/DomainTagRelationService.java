package com.ruoyi.system.service.tagmanager;

import com.ruoyi.system.entity.tagmanager.DomainTagRelationEntity;

import java.util.List;

/**
 * 域名标签关联表 Service
 *
 * <AUTHOR>
 * @date 2022-12-26 14:32:58
 */
public interface DomainTagRelationService {
    /**
     * 新增记录
     */
    Boolean insert(DomainTagRelationEntity entity);

    /**
     * 根据id删除
     */
    Boolean deleteById(Long id);

    /**
     * 根据id更新
     */
    Boolean updateById(DomainTagRelationEntity entity);

    /**
     * 根据id获取
     */
    DomainTagRelationEntity selectById(Long id);

    /**
     * 根据标签id查询使用的总数
     * @param tagId
     * @return
     */
    int countByTagId(Long tagId);

    /**
     * 根据标签id列表查询被使用的标签id列表 ，删除标签时判断用
     * @param tagIds
     * @return
     */
    List<Long> selectTagIdsByTagIds(List<Long> tagIds);

    /**
     * 根据域名id查询标签列表
     * @param domainId
     * @return
     */
    List<Long> selectTagIdsByDomainId(Long domainId);

    /**
     * 根据域名id删除标签
     * @param domainId
     * @return
     */
    Boolean deleteByDomainId(Long domainId);

    /**
     * 批量新增
     * @param entities
     * @return
     */
    Boolean batchInsert(List<DomainTagRelationEntity> entities);


}
