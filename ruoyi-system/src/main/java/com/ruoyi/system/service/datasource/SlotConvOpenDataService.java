package com.ruoyi.system.service.datasource;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 媒体可见的转化数据表 Service
 *
 * <AUTHOR>
 * @date 2023-3-10 11:10:04
 */
public interface SlotConvOpenDataService {

    /**
     * 根据广告位ID和日期自增
     *
     * @param slotId 广告位ID
     * @param curDate 日期
     * @return 影响行数
     */
    int incr(Long slotId, Date curDate);

    /**
     * 查询广告位转化映射
     *
     * @param slotIds 广告位Id列表
     * @param curDate 日期(必填)
     * @return 广告位-转化数映射
     */
    Map<Long, Integer> selectMap(List<Long> slotIds, Date curDate);
}
