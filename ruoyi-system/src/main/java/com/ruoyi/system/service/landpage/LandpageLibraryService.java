package com.ruoyi.system.service.landpage;

import com.ruoyi.system.entity.landpage.Landpage;

import java.util.List;
import java.util.Map;

/**
 * 落地页表Service接口
 *
 * <AUTHOR>
 * @date 2022-02-10
 */
public interface LandpageLibraryService {

    /**
     * 查询落地页
     *
     * @param id 落地页ID
     * @return 落地页
     */
    Landpage selectById(Long id);

    /**
     * 查询落地页
     *
     * @param url 落地页链接
     * @return 落地页
     */
    Landpage selectByUrl(String url);

    /**
     * 根据标识查询落地页
     *
     * @param key 落地页标识
     * @return 落地页详情
     */
    Landpage selectByKey(String key);

    /**
     * 根据标识查询落地页列表
     *
     * @param keys 落地页标识列表
     * @return 落地页列表
     */
    List<Landpage> selectByKeys(List<String> keys);

    /**
     * 根据广告主落地页查询落地页列表
     *
     * @param targetLandpage 广告主落地页
     * @return 落地页列表
     */
    List<Landpage> selectByTargetLandpage(String targetLandpage);

    /**
     * 查询落地页列表
     *
     * @param param 参数
     * @return 落地页列表
     */
    List<Landpage> selectList(Landpage param);

    /**
     * 查询落地页映射
     *
     * @return 落地页映射
     */
    Map<String, Landpage> selectKeyMap();

    /**
     * 新增落地页
     *
     * @param param 参数
     * @return 结果
     */
    int insertLandpage(Landpage param);

    /**
     * 根据皮肤新增落地页
     *
     * @param param 参数
     * @return 结果
     */
    int insertLandpageBySkin(Landpage param);

    /**
     * 修改落地页
     *
     * @param param 参数
     * @return 结果
     */
    int updateLandpage(Landpage param);

    /**
     * 修改落地页链接
     *
     * @param id 落地页ID
     * @param key 落地页标识
     * @param url 落地页链接
     * @return 结果
     */
    int updateLandpageUrl(Long id, String key, String url);

    /**
     * 根据皮肤修改落地页
     *
     * @param param 参数
     * @return 结果
     */
    int updateLandpageBySkin(Landpage param);

    /**
     * 判断落地页是否已存在
     *
     * @param url 落地页链接
     * @return 是否存在
     */
    boolean isLandpageExist(String url);

    /**
     * 查询落地页标签
     *
     * @param key 落地页标识
     * @return 落地页标签
     */
    String getLandpageTag(String key);

    /**
     * 获取落地页标签映射
     *
     * @return 落地页标识-落地页标签映射
     */
    Map<String, String> getLandpageTagMap();

    /**
     * 标记落地页无效
     *
     * @param landpageId 落地页ID
     * @return 结果
     */
    int invalidateLandpage(Long landpageId);

    /**
     * 查询落地页标签
     *
     * @return 落地页标签
     */
    List<String> getLandpageTags();
}
