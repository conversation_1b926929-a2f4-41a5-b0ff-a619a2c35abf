package com.ruoyi.system.service.landpage;

import com.ruoyi.system.entity.landpage.CreditCardLandpageFormRecordEntity;
import com.ruoyi.system.req.datashow.CreditCardLandpageFromRecordReq;

import java.util.List;

/**
 * 信用卡落地页表单记录 Service
 *
 * <AUTHOR>
 * @date 2023-6-2 17:14:04
 */
public interface CreditCardLandpageFormRecordService {

    /**
     * 查询列表
     *
     * @param param 查询条件
     * @return 落地页单记录集合
     */
    List<CreditCardLandpageFormRecordEntity> selectList(CreditCardLandpageFromRecordReq param);

    /**
     * 新增记录
     */
    Boolean insert(CreditCardLandpageFormRecordEntity entity);

    /**
     * 根据id更新
     */
    Boolean updateById(CreditCardLandpageFormRecordEntity entity);

    /**
     * 根据id获取
     */
    CreditCardLandpageFormRecordEntity selectById(Long id);

    /**
     * 查询所有的广告ID列表
     *
     * @return 广告ID列表
     */
    List<Long> selectTotalAdvertIds();

    /**
     * 查询所有的广告主ID列表
     *
     * @return 广告ID列表
     */
    List<Long> selectTotalAdvertiserIds();

    /**
     * 查询所有的媒体ID列表
     *
     * @return 广告ID列表
     */
    List<Long> selectTotalAppIds();

    /**
     * 查询所有的广告位ID列表
     *
     * @return 广告ID列表
     */
    List<Long> selectTotalSlotIds();
}
