package com.ruoyi.system.service.landpage;

import com.ruoyi.system.entity.landpage.IspVipLandpageFormRecordEntity;

/**
 * 运营商会员落地页记录 Service
 *
 * <AUTHOR>
 * @date 2023-11-7 11:41:45
 */
public interface IspVipLandpageFormRecordService {

    /**
     * 新增记录
     */
    Boolean insert(IspVipLandpageFormRecordEntity entity);

    /**
     * 根据id更新
     */
    Boolean updateById(IspVipLandpageFormRecordEntity entity);

    /**
     * 查询记录
     */
    IspVipLandpageFormRecordEntity selectById(Long id);

    /**
     * 查询记录
     */
    IspVipLandpageFormRecordEntity selectBy(String orderId, String phone);

    /**
     * 查询记录
     */
    IspVipLandpageFormRecordEntity selectByBizOrderNoAndOrderId(String bizOrderNo, String orderId);

    /**
     * 查询记录
     */
    IspVipLandpageFormRecordEntity selectByOrderId(String orderId);
}
