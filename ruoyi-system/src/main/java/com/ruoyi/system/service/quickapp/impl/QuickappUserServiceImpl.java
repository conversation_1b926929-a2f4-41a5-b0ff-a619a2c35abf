package com.ruoyi.system.service.quickapp.impl;

import com.ruoyi.common.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.quickapp.QuickappUserService;
import com.ruoyi.system.entity.quickapp.QuickappUserEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import com.ruoyi.system.mapper.quickapp.QuickappUserMapper;

/**
 * 快应用用户表 Service
 *
 * <AUTHOR>
 * @date 2022-8-8 10:57:00
 */
@Service
public class QuickappUserServiceImpl implements QuickappUserService {
    @Autowired
    private QuickappUserMapper quickappUserMapper;

    @Override
    public Boolean insert(QuickappUserEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return quickappUserMapper.insert(entity) > 0;
    }

    @Override
    public Boolean deleteById(Long id) {
        if (Objects.isNull(id)) {
            return false;
        }
        return quickappUserMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean updateById(QuickappUserEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return quickappUserMapper.updateById(entity) > 0;
    }

    @Override
    public QuickappUserEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return quickappUserMapper.selectById(id);
    }

    @Override
    public QuickappUserEntity selectByEmail(String email) {
        if(StringUtils.isBlank(email)){
            return null;
        }
        return quickappUserMapper.selectByEmail(email);
    }

    @Override
    public List<QuickappUserEntity> selectByUserIds(List<Long> userIds) {
        if(CollectionUtils.isEmpty(userIds)){
            return Collections.emptyList();
        }
        return quickappUserMapper.selectByUserIds(userIds);
    }
}
