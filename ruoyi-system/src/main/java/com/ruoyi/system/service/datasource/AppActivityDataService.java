package com.ruoyi.system.service.datasource;

import com.ruoyi.system.bo.appactivitydata.AppActivityDataSumBo;
import com.ruoyi.system.entity.datashow.AppActivityData;

import java.util.Date;
import java.util.List;

/**
 * 媒体维度活动数据Service接口
 * 
 * <AUTHOR>
 * @date 2021-07-21
 */
public interface AppActivityDataService {

    /**
     * 查询媒体维度活动数据
     *
     * @param appId 媒体ID
     * @param activityId 活动ID
     * @param curDate 日期
     * @return 广告位数据
     */
    AppActivityData selectBy(Long appId, Long activityId, Date curDate);

    /**
     * 查询媒体维度活动数据
     * 
     * @param id 媒体维度活动数据ID
     * @return 媒体维度活动数据
     */
    AppActivityData selectAppActivityDataById(String id);

    /**
     * 查询媒体维度活动数据列表
     * 
     * @param appActivityData 媒体维度活动数据
     * @return 媒体维度活动数据集合
     */
    List<AppActivityData> selectAppActivityDataList(AppActivityData appActivityData);

    /**
     * 新增媒体维度活动数据
     * 
     * @param appActivityData 媒体维度活动数据
     * @return 结果
     */
    int insertAppActivityData(AppActivityData appActivityData);

    /**
     * 修改媒体维度活动数据
     * 
     * @param appActivityData 媒体维度活动数据
     * @return 结果
     */
    int updateAppActivityData(AppActivityData appActivityData);

    /**
     * 统计媒体活动数据 总计
     * @param appIds 媒体id列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 总计
     */
    List<AppActivityDataSumBo> selectAppActivityDataSum(List<Long> appIds, Date startDate, Date endDate);

    /**
     * 查询媒体活动数据
     * @param appIds 媒体id列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 结果
     */
    List<AppActivityData> selectAppActivityData(List<Long> appIds, Date startDate, Date endDate);



}
