package com.ruoyi.system.service.datasource.impl;

import com.ruoyi.system.entity.datashow.SlotHourData;
import com.ruoyi.system.mapper.datashow.SlotHourDataMapper;
import com.ruoyi.system.service.datasource.SlotHourDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 广告位分时段数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-08-17
 */
@Service
public class SlotHourDataServiceImpl implements SlotHourDataService {

    @Autowired
    private SlotHourDataMapper slotHourDataMapper;

    @Override
    public SlotHourData selectBySlotIdAndDateHour(Long slotId, Date curDate, Integer curHour) {
        return slotHourDataMapper.selectBySlotIdAndDateHour(slotId, curDate, curHour);
    }

    /**
     * 新增广告位数据
     *
     * @param slotHourData 广告位数据
     * @return 结果
     */
    @Override
    public int insertSlotHourData(SlotHourData slotHourData) {
        return slotHourDataMapper.insertSlotHourData(slotHourData);
    }

    /**
     * 修改广告位数据
     *
     * @param slotHourData 广告位数据
     * @return 结果
     */
    @Override
    public int updateSlotHourData(SlotHourData slotHourData) {
        return slotHourDataMapper.updateSlotHourData(slotHourData);
    }
}
