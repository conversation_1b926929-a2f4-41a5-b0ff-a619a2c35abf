package com.ruoyi.system.service.datasource;

import com.ruoyi.system.entity.datashow.AdvertiserConsumeData;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 广告主日消耗数据Service接口
 *
 * <AUTHOR>
 * @date 2021-08-19
 */
public interface AdvertiserConsumeDataService {

    /**
     * 查询广告主日消耗数据列表
     *
     * @param param 广告主消耗请求参数
     * @return 广告主日消耗数据集合
     */
    List<AdvertiserConsumeData> selectAdvertiserConsumeDataList(AdvertiserConsumeData param);

    /**
     * 查询广告主日消耗数据
     *
     * @param curDate 日期
     * @param advertiserId 广告主ID
     * @return 广告配置消耗数据
     */
    AdvertiserConsumeData selectByDateAndAdvertiserId(Date curDate, Long advertiserId);

    /**
     * 新增广告主日消耗数据
     *
     * @param advertiserConsumeData 广告主日消耗数据
     * @return 结果
     */
    int insertAdvertiserConsumeData(AdvertiserConsumeData advertiserConsumeData);

    /**
     * 修改广告主日消耗数据
     *
     * @param advertiserConsumeData 广告主日消耗数据
     * @return 结果
     */
    int updateAdvertiserConsumeData(AdvertiserConsumeData advertiserConsumeData);

    /**
     * 修改广告主日消耗数据
     *
     * @param id 数据记录ID
     * @param budget 广告主日预算
     * @return 结果
     */
    int updateBudget(Long id, Long budget);

    /**
     * 修改广告主日消耗数据
     *
     * @param advertiserConsumeData 广告主日消耗数据
     * @return 结果
     */
    int addConsumeData(AdvertiserConsumeData advertiserConsumeData);

    /**
     * 批量获取广告主的消耗
     *
     * @param advertiserIds 广告主ID列表
     * @param curDate 日期
     * @return 广告主ID-广告主消耗映射
     */
    Map<Long, Long> selectConsumeMapByAdvertiserIdAndDate(List<Long> advertiserIds, Date curDate);
}
