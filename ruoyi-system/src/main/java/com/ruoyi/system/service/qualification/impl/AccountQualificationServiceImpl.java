package com.ruoyi.system.service.qualification.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.constant.ErrorCode;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.enums.account.AccountMainType;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.bo.account.DataPermissionBo;
import com.ruoyi.system.entity.account.Account;
import com.ruoyi.system.entity.qualification.AccountQualificationEntity;
import com.ruoyi.system.manager.account.DataPermissionManager;
import com.ruoyi.system.mapper.qualification.AccountQualificationMapper;
import com.ruoyi.system.req.qualification.QualificationListReq;
import com.ruoyi.system.req.qualification.QualificationReq;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.service.qualification.AccountQualificationService;
import com.ruoyi.system.vo.qualification.QualificationInfoVO;
import com.ruoyi.system.vo.qualification.QualificationListVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.account.DataPermissionType.hasPartialPermission;

/**
 * 提现账号资质信息 Service
 *
 * <AUTHOR>
 * @date 2021-9-9 16:58:50
 */
@Service
public class AccountQualificationServiceImpl implements AccountQualificationService{

    @Autowired
    private AccountQualificationMapper accountQualificationMapper;

    @Autowired
    private AccountService accountService;

    @Autowired
    private DataPermissionManager dataPermissionManager;

    @Override
    public AccountQualificationEntity selectById(Long id){
        if(Objects.isNull(id)){
            return null;
        }
        return accountQualificationMapper.selectById(id);
    }

    @Override
    public boolean insertOrUpdate(QualificationReq req) {
        LoginUser user = SecurityUtils.getLoginUser();

        AccountQualificationEntity entity = BeanUtil.copyProperties(req, AccountQualificationEntity.class);
        if(NumberUtils.isNullOrLteZero(req.getId())){
            entity.setAccountId(user.getCrmAccountId());
            return accountQualificationMapper.insert(entity) > 0;
        }
        AccountQualificationEntity qualificationEntity = selectById(req.getId());
        if(Objects.isNull(qualificationEntity)){
            throw new CustomException(ErrorCode.E104001);
        }
        if(!Objects.equals(qualificationEntity.getAccountId(),user.getCrmAccountId())){
            throw new CustomException(ErrorCode.E104001);
        }
        return accountQualificationMapper.updateById(entity) > 0;
    }

    @Override
    public AccountQualificationEntity selectByAccountId(Long accountId) {
        if(NumberUtils.isNullOrLteZero(accountId)){
            return null;
        }
        return accountQualificationMapper.selectByAccountId(accountId);
    }

    @Override
    public QualificationInfoVO selectQualificationInfoByAccountId(Long accountId) {
        if(NumberUtils.isNullOrLteZero(accountId)){
            throw new CustomException(ErrorCode.ARGS);
        }
        Account account = accountService.selectAccountById(accountId);
        if(Objects.isNull(account)){
            throw new CustomException(ErrorCode.E104002);
        }
        AccountQualificationEntity entity = accountQualificationMapper.selectByAccountId(accountId);
        if(Objects.isNull(entity)){
            return null;
        }
        QualificationInfoVO vo = BeanUtil.copyProperties(entity, QualificationInfoVO.class);
        vo.setContact(account.getContact());
        vo.setEmail(account.getEmail());
        return vo;
    }

    @Override
    public Map<Long, AccountQualificationEntity> selectMapByAccountIds(List<Long> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return Collections.emptyMap();
        }
        List<AccountQualificationEntity> list = accountQualificationMapper.selectQualificationList(null, null, accountIds);
        return list.stream().collect(Collectors.toMap(AccountQualificationEntity::getAccountId, Function.identity(), (v1, v2) -> v1));
    }

    @Override
    public PageInfo<QualificationListVO> selectQualificationList(QualificationListReq req) {
        List<Long> accountIds = null;
        if (StringUtils.isNotEmpty(req.getEmail()) || NumberUtils.isNonNullAndGtZero(req.getAccountId())) {
            Account account = new Account();
            account.setEmail(req.getEmail());
            account.setId(req.getAccountId());
            account.setMainType(AccountMainType.PUBLISHER.getType());
            List<Account> accounts = accountService.selectAccountList(account);
            if(CollectionUtils.isEmpty(accounts)){
                return PageInfoUtils.buildReturnList(Collections.emptyList());
            }
            accountIds = accounts.stream().map(Account::getId).collect(Collectors.toList());
        }
        // 数据权限控制
        DataPermissionBo permission = dataPermissionManager.selectAccount();
        if (hasPartialPermission(permission.getType())) {
            if (CollectionUtils.isEmpty(permission.getValues())) {
                return PageInfoUtils.buildReturnList(Collections.emptyList());
            }
            if (null == accountIds) {
                accountIds = permission.getValues();
            } else {
                accountIds.retainAll(permission.getValues());
            }
        }
        if (null != accountIds && accountIds.size() == 0) {
            return PageInfoUtils.buildReturnList(Collections.emptyList());
        }

        //分页
        TableSupport.startPage();
        req.setStartDate(Optional.ofNullable(req.getStartDate()).map(DateUtil::beginOfDay).orElse(null));
        req.setEndDate(Optional.ofNullable(req.getEndDate()).map(DateUtil::endOfDay).orElse(null));

        List<AccountQualificationEntity> entityList = accountQualificationMapper.selectQualificationList(req.getStartDate(),req.getEndDate(),accountIds);

        accountIds = entityList.stream().map(AccountQualificationEntity::getAccountId).collect(Collectors.toList());
        List<Account> accounts = accountService.selectListByIds(accountIds);
        Map<Long, String> emailMap = accounts.stream().collect(Collectors.toMap(Account::getId, Account::getEmail));
        return PageInfoUtils.dto2Vo(entityList, entity ->{
            QualificationListVO vo = BeanUtil.copyProperties(entity,QualificationListVO.class);
            vo.setEmail(emailMap.get(entity.getAccountId()));
            return vo;
        });

    }

    @Override
    public List<Long> selectAccountIdsByCompanyName(String company) {
        if(StringUtils.isEmpty(company)){
            return Collections.emptyList();
        }
        return accountQualificationMapper.selectAccountIdsByCompanyName(company);
    }
}
