package com.ruoyi.system.service.advertiser.impl;

import com.ruoyi.system.req.advertiser.finance.AdvertiserFianceStatisticsRecordListReq;
import com.ruoyi.system.req.advertiser.finance.AdvertiserFianceStatisticsRecordUpdateReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.advertiser.fiance.AdvertiserFianceStatisticsRecordService;
import com.ruoyi.system.entity.advertiser.finance.AdvertiserFianceStatisticsRecordEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;
import java.util.Objects;

import com.ruoyi.system.mapper.advertiser.finance.AdvertiserFianceStatisticsRecordMapper;

/**
 * 广告主财务汇总记录表 Service
 *
 * <AUTHOR>
 * @date 2022-3-18 17:58:27
 */
@Slf4j
@Service
public class AdvertiserFianceStatisticsRecordServiceImpl implements AdvertiserFianceStatisticsRecordService {

    @Autowired
    private AdvertiserFianceStatisticsRecordMapper advertiserFianceStatisticsRecordMapper;

    @Override
    public AdvertiserFianceStatisticsRecordEntity select(Long accountId, Date curDate) {
        if (Objects.isNull(accountId) || Objects.isNull(curDate)) {
            return null;
        }
        return advertiserFianceStatisticsRecordMapper.selectByAccountIdAndDate(accountId, curDate);
    }

    @Override
    public AdvertiserFianceStatisticsRecordEntity selectLatest(Long accountId) {
        if (Objects.isNull(accountId)) {
            return null;
        }
        return advertiserFianceStatisticsRecordMapper.selectLatestByAccountId(accountId);
    }

    @Override
    public int insert(Long accountId, Date curDate, Integer cashBalance, Integer rebateBalance) {
        if (Objects.isNull(accountId) || Objects.isNull(curDate)) {
            return 0;
        }
        AdvertiserFianceStatisticsRecordEntity entity = new AdvertiserFianceStatisticsRecordEntity();
        entity.setAccountId(accountId);
        entity.setCurDate(curDate);
        entity.setCashBalance(cashBalance);
        entity.setRebateBalance(rebateBalance);
        return advertiserFianceStatisticsRecordMapper.insert(entity);
    }

    @Override
    public int update(AdvertiserFianceStatisticsRecordUpdateReq req) {
        if (Objects.isNull(req) || Objects.isNull(req.getId())) {
            return 0;
        }
        return advertiserFianceStatisticsRecordMapper.update(req);
    }

    @Override
    public List<AdvertiserFianceStatisticsRecordEntity> selectList(AdvertiserFianceStatisticsRecordListReq req) {
        return advertiserFianceStatisticsRecordMapper.selectList(req);
    }
}
