package com.ruoyi.system.service.permission;

import com.ruoyi.system.entity.permission.PermissionEntity;

import java.util.List;

/**
 * 权限表 Service
 *
 * <AUTHOR>
 * @date 2022-6-23 16:24:50
 */
public interface SspPermissionService {

    /**
     * 新增记录
     */
    Boolean insert(PermissionEntity entity);

    /**
     * 根据id删除
     */
    Boolean deleteById(Long id);

    /**
     * 根据id更新
     */
    Boolean updateById(PermissionEntity entity);

    /**
     * 根据id获取
     */
    PermissionEntity selectById(Long id);

    /**
     * 根据id列表查询所有权限
     *
     * @param ids id列表
     * @return 权限列表
     */
    List<PermissionEntity> selectByIds(List<Long> ids);

    /**
     * 查询所有权限
     *
     * @return 权限列表
     */
    List<PermissionEntity> selectAllPermission();
}
