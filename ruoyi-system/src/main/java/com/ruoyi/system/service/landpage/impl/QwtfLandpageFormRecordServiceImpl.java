package com.ruoyi.system.service.landpage.impl;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.bo.landpage.QwtfLandpageFormRecordSelectBo;
import com.ruoyi.system.entity.landpage.QwtfLandpageFormRecordEntity;
import com.ruoyi.system.mapper.landpage.QwtfLandpageFormRecordMapper;
import com.ruoyi.system.service.landpage.QwtfLandpageFormRecordService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 企微囤粉表单记录 Service
 *
 * <AUTHOR>
 * @date 2023-9-22 15:38:47
 */
@Service
public class QwtfLandpageFormRecordServiceImpl implements QwtfLandpageFormRecordService {

    @Autowired
    private QwtfLandpageFormRecordMapper qwtfLandpageFormRecordMapper;

    @Override
    public Boolean insert(QwtfLandpageFormRecordEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return qwtfLandpageFormRecordMapper.insert(entity) > 0;
    }

    @Override
    public List<QwtfLandpageFormRecordEntity> selectList(QwtfLandpageFormRecordSelectBo param) {
        return qwtfLandpageFormRecordMapper.selectList(param);
    }

    @Override
    public Boolean updateById(QwtfLandpageFormRecordEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return qwtfLandpageFormRecordMapper.updateById(entity) > 0;
    }

    @Override
    public int batchUpdateFriendStatus(List<String> unionIds) {
        if (CollectionUtils.isEmpty(unionIds)) {
            return 0;
        }
        return qwtfLandpageFormRecordMapper.batchUpdateFriendStatus(unionIds);
    }

    @Override
    public int batchUpdateFriendStatusByOrderIds(List<String> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return 0;
        }
        return qwtfLandpageFormRecordMapper.batchUpdateFriendStatusByOrderIds(orderIds);
    }

    @Override
    public QwtfLandpageFormRecordEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return qwtfLandpageFormRecordMapper.selectById(id);
    }

    @Override
    public QwtfLandpageFormRecordEntity selectByUnionId(String unionId) {
        if (StringUtils.isBlank(unionId)) {
            return null;
        }
        return qwtfLandpageFormRecordMapper.selectByUnionId(unionId);
    }

    @Override
    public List<QwtfLandpageFormRecordEntity> selectNotFriendByUnionIds(List<String> unionIds) {
        if (CollectionUtils.isEmpty(unionIds)) {
            return Collections.emptyList();
        }
        return qwtfLandpageFormRecordMapper.selectNotFriendByUnionIds(unionIds);
    }

    @Override
    public List<QwtfLandpageFormRecordEntity> selectNotFriendByOrderIds(List<String> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return Collections.emptyList();
        }
        return qwtfLandpageFormRecordMapper.selectNotFriendByOrderIds(orderIds);
    }

    @Override
    public List<Long> selectTotalAdvertIds() {
        return qwtfLandpageFormRecordMapper.selectTotalAdvertIds();
    }

    @Override
    public List<Long> selectTotalAppIds() {
        return qwtfLandpageFormRecordMapper.selectTotalAppIds();
    }

    @Override
    public List<Long> selectTotalSlotIds() {
        return qwtfLandpageFormRecordMapper.selectTotalSlotIds();
    }
}
