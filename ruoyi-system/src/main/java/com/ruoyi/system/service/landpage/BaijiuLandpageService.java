package com.ruoyi.system.service.landpage;

import com.ruoyi.system.req.engine.BaijiuLandPageFormReq;

/**
 * 白酒落地页接口
 *
 * <AUTHOR>
 * @date 2022/01/24
 */
public interface BaijiuLandpageService {

    /**
     * 落地页表单
     *
     * @param req 参数
     */
    Long formSubmit(BaijiuLandPageFormReq req);

    /**
     * 获取返回挽留开关
     *
     * @param orderId 订单号
     * @return 返回挽留开关
     */
    Integer getRetSwitch(String orderId);

    /**
     * 设置返回挽留开关
     *
     * @param slotId 广告位ID
     * @param status 返回挽留开关
     */
    void setRetSwitch(Long slotId, Integer status);
}
