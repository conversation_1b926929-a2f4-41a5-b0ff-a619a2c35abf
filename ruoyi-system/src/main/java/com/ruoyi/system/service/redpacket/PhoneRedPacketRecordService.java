package com.ruoyi.system.service.redpacket;

import com.ruoyi.system.entity.redpacket.PhoneRedPacketRecordEntity;
import com.ruoyi.system.req.cashback.CashBackListReq;

import java.util.List;

/**
 * 表单用户领红包记录表 Service
 *
 * <AUTHOR>
 * @date 2022-5-26 17:50:17
 */
public interface PhoneRedPacketRecordService {

    /**
     * 新增记录
     *
     * @param entity 记录
     * @return 影响行数
     */
    int insert(PhoneRedPacketRecordEntity entity);

    /**
     * 根据id更新记录
     *
     * @param entity 记录
     * @return 影响行数
     */
    int updateById(PhoneRedPacketRecordEntity entity);

    /**
     * 查询手机号对应的记录
     *
     * @param phone 下单手机号
     * @return 记录
     */
    PhoneRedPacketRecordEntity selectByPhone(String phone);

    /**
     * 根据参数分页查询返现列表 后台用
     * @param req
     * @return
     */
    List<PhoneRedPacketRecordEntity> selectByReq(CashBackListReq req);
}
