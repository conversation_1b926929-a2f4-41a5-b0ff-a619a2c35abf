package com.ruoyi.system.service.advertiser.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.ruoyi.common.config.DingWebhookConfig;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.account.AccountRelationType;
import com.ruoyi.common.enums.advertiser.AdvertiserConsumeType;
import com.ruoyi.common.enums.advertiser.FormAdvertiserPriceType;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.entity.account.Account;
import com.ruoyi.system.entity.account.AccountExtInfo;
import com.ruoyi.system.mapper.manager.AccountMapper;
import com.ruoyi.system.req.account.AccountRegisterReq;
import com.ruoyi.system.req.agent.client.AgentClientAddReq;
import com.ruoyi.system.service.advertiser.AdvertiserService;
import com.ruoyi.system.service.advertiser.AdvertiserTagService;
import com.ruoyi.system.service.manager.AccountRelationService;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.util.DingRobotUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.account.AccountMainType.ADVERTISER;

/**
 * 广告主Service业务层处理
 *
 * <AUTHOR>
 * @date 2021/8/9
 */
@Service
public class AdvertiserServiceImpl implements AdvertiserService {

    @Autowired
    private AccountMapper accountMapper;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private AccountService accountService;

    @Autowired
    private AccountRelationService accountRelationService;

    @Autowired
    private AdvertiserTagService advertiserTagService;

    @Override
    public List<Account> selectList(Account param) {
        param.setMainType(ADVERTISER.getType());
        return accountMapper.selectAccountList(param);
    }

    @Override
    public List<Account> selectTotalAdvertiserList() {
        Account param = new Account();
        param.setMainType(ADVERTISER.getType());
        return accountMapper.selectSimpleAccountList(param);
    }

    @Override
    public Map<Long, String> selectAdvertiserNameMap(List<Long> advertiserIds) {
        if (CollectionUtils.isEmpty(advertiserIds)) {
            return Collections.emptyMap();
        }

        List<Account> list = accountService.selectListByIds(advertiserIds);
        return list.stream().collect(Collectors.toMap(Account::getId, Account::getCompanyName, (oldVal, newVal) -> newVal));
    }

    @Override
    public String selectAdvertiserName(Long advertiserId) {
        if (null == advertiserId) {
            return "";
        }
        Account account = accountMapper.selectAccountById(advertiserId);
        return Optional.ofNullable(account).map(Account::getCompanyName).orElse("");
    }

    @Override
    public List<Long> selectAdvertiserIdsBySearch(String advertiserSearch) {
        return accountService.selectIdsBySearch(advertiserSearch, ADVERTISER.getType());
    }

    @Override
    public List<Long> selectAdvertiserIdsByName(String advertiserName) {
        if (StringUtils.isBlank(advertiserName)) {
            return Collections.emptyList();
        }

        return accountMapper.selectIdByCompanyName(advertiserName.trim(), ADVERTISER.getType());
    }

    @Override
    public List<Long> selectAdvertiserIdsByEmail(String email) {
        if (StringUtils.isBlank(email)) {
            return Collections.emptyList();
        }

        return accountMapper.selectIdByEmail(email.trim(), ADVERTISER.getType());
    }

    @Override
    public void advertiserAppRetConfig(Long advertiserId, Integer appRet) {
        Account account = accountMapper.selectAccountById(advertiserId);
        if (null == account || StringUtils.isBlank(account.getExtInfo())) {
            return;
        }

        AccountExtInfo extInfo = JSON.parseObject(account.getExtInfo(), AccountExtInfo.class);
        if (null == extInfo) {
            return;
        }

        extInfo.setAppRet(appRet);

        Account updateAccount = new Account();
        updateAccount.setId(advertiserId);
        updateAccount.setExtInfo(JSON.toJSONString(extInfo));
        int result = accountMapper.updateAccount(updateAccount);

        StringBuilder sbr = new StringBuilder();
        sbr.append("广告主回传媒体信息配置更新\n")
                .append("\n广告主ID: ").append(advertiserId)
                .append("\n广告主名称: ").append(account.getCompanyName())
                .append("\n是否回传: ").append(Objects.equals(appRet, 1) ? "是" : "否")
                .append("\n更新结果: ").append(result > 0 ? "成功" : "失败");
        DingRobotUtil.sendText(DingWebhookConfig.getCrmBiz(), sbr.toString());
    }

    @Override
    public boolean updateLpCallbackUrl(Long advertiserId, String lpCallbackUrl) {
        if (null == advertiserId || StringUtils.isBlank(lpCallbackUrl)) {
            return false;
        }
        Account advertiser = accountMapper.selectAccountById(advertiserId);
        if (null == advertiser) {
            return false;
        }

        AccountExtInfo extInfo = JSON.parseObject(advertiser.getExtInfo(), AccountExtInfo.class);
        extInfo.setLpCallbackUrl(lpCallbackUrl);

        Account updateAccount = new Account();
        updateAccount.setId(advertiserId);
        updateAccount.setExtInfo(JSON.toJSONString(extInfo));
        boolean result = accountMapper.updateAccount(updateAccount) > 0;

        if (result) {
            StringBuilder sbr = new StringBuilder();
            sbr.append("广告主回传链接更新\n")
                    .append("\n广告主ID: ").append(advertiserId)
                    .append("\n广告主名称: ").append(advertiser.getCompanyName())
                    .append("\n回传链接: ").append(lpCallbackUrl);
            DingRobotUtil.sendText(DingWebhookConfig.getCrmBiz(), sbr.toString());
        }
        return result;
    }

    @Override
    public boolean updateFormPrice(Long advertiserId, Integer formPrice, Integer priceType, Double weight) {
        if (null == advertiserId || formPrice < 0) {
            return false;
        }
        Account advertiser = accountMapper.selectAccountById(advertiserId);
        if (null == advertiser) {
            return false;
        }

        AccountExtInfo extInfo = JSON.parseObject(advertiser.getExtInfo(), AccountExtInfo.class);
        Integer oldFormPrice = extInfo.getFormPrice();
        Integer oldPriceType = extInfo.getPriceType();
        Double oldWeight = extInfo.getFormWeight();
        extInfo.setFormPrice(formPrice);
        extInfo.setPriceType(NumberUtils.defaultInt(priceType));
        extInfo.setFormWeight(weight);

        Account updateAccount = new Account();
        updateAccount.setId(advertiserId);
        updateAccount.setExtInfo(JSON.toJSONString(extInfo));
        boolean result = accountMapper.updateAccount(updateAccount) > 0;

        if (result) {
            redisCache.deleteObject(EngineRedisKeyFactory.K028.toString());

            StringBuilder sbr = new StringBuilder();
            sbr.append("广告主表单价格更新\n")
                    .append("\n广告主ID: ").append(advertiserId)
                    .append("\n广告主名称: ").append(advertiser.getCompanyName());
            sbr.append("\n表单价格: ");
            if (null != oldFormPrice && !Objects.equals(oldFormPrice, formPrice)) {
                sbr.append(NumberUtils.fenToYuan(oldFormPrice)).append(" -> ");
            }
            sbr.append(NumberUtils.fenToYuan(formPrice)).append("元");
            sbr.append("\n计费类型: ");
            if (!Objects.equals(oldPriceType, priceType)) {
                sbr.append(FormAdvertiserPriceType.getDescByType(NumberUtils.defaultInt(oldPriceType))).append(" -> ");
            }
            sbr.append(FormAdvertiserPriceType.getDescByType(priceType));
            sbr.append("\n权重: ");
            if (!Objects.equals(oldWeight, weight)) {
                sbr.append(oldWeight).append(" -> ");
            }
            sbr.append(weight);
            DingRobotUtil.sendText(DingWebhookConfig.getCrmBiz(), sbr.toString());
        }
        return result;
    }

    @Override
    public Map<Long, Integer> selectFormPriceMap(List<Long> advertiserIds) {
        if (CollectionUtils.isEmpty(advertiserIds)) {
            return Collections.emptyMap();
        }

        List<Account> list = accountService.selectListByIds(advertiserIds);
        return list.stream().collect(Collectors.toMap(Account::getId, account -> {
            AccountExtInfo extInfo = JSON.parseObject(account.getExtInfo(), AccountExtInfo.class);
            return null != extInfo ? NumberUtils.defaultInt(extInfo.getFormPrice()) : 0;
        }, (v1, v2) -> v1));
    }

    @Override
    public Map<Long, Double> selectFormWeightMap(List<Long> advertiserIds) {
        if (CollectionUtils.isEmpty(advertiserIds)) {
            return Collections.emptyMap();
        }

        List<Account> list = accountService.selectListByIds(advertiserIds);
        return list.stream().collect(Collectors.toMap(Account::getId, account -> {
            AccountExtInfo extInfo = JSON.parseObject(account.getExtInfo(), AccountExtInfo.class);
            return null != extInfo && null != extInfo.getFormWeight() ? extInfo.getFormWeight() : 1.0;
        }, (v1, v2) -> v2));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertByAgent(Long agentId, AgentClientAddReq req) {
        // 新增广告主账户
        AccountRegisterReq param = BeanUtil.copyProperties(req, AccountRegisterReq.class);
        param.setMainType(ADVERTISER.getType());
        param.setPasswd(SecurityUtils.encryptPassword(req.getPasswd()));
        // 设置为CPC结算
        param.setConsumeType(AdvertiserConsumeType.CPC.getType());
        Long advertiserId = accountService.insertAccount(param);
        if (null != advertiserId) {
            // 自动绑定代理商关系
            accountRelationService.updateRelation(agentId, advertiserId, AccountRelationType.ADVERT_AGENT.getType());
        }
        return advertiserId;
    }

    @Override
    public boolean updateConsumeType(Long advertiserId, Integer consumeType) {
        if (null == advertiserId || null == consumeType) {
            return false;
        }
        Account advertiser = accountMapper.selectAccountById(advertiserId);
        if (null == advertiser) {
            return false;
        }
        AccountExtInfo extInfo = JSON.parseObject(advertiser.getExtInfo(), AccountExtInfo.class);
        extInfo.setConsumeType(consumeType);

        Account updateAccount = new Account();
        updateAccount.setId(advertiserId);
        updateAccount.setExtInfo(JSON.toJSONString(extInfo));
        return accountMapper.updateAccount(updateAccount) > 0;
    }

    @Override
    public boolean isCpcAdvertiser(Long advertiserId) {
        if (null == advertiserId) {
            return false;
        }
        Set<String> tags = advertiserTagService.get(advertiserId);
        if (tags.contains("CPC结算")) {
            return true;
        }
        AccountExtInfo extInfo = accountService.selectAccountExtInfoById(advertiserId);
        if (CollectionUtils.isNotEmpty(tags)) {
            return null != extInfo && AdvertiserConsumeType.isCPC(extInfo.getConsumeType());
        }
        return null == extInfo || null == extInfo.getConsumeType() || AdvertiserConsumeType.isCPC(extInfo.getConsumeType());
    }
}
