package com.ruoyi.system.service.datasource.impl;

import com.ruoyi.system.entity.datashow.AdvertQuarterDataEntity;
import com.ruoyi.system.mapper.datashow.AdvertQuarterDataMapper;
import com.ruoyi.system.service.datasource.AdvertQuarterDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

/**
 * 广告维度时刻数据表 Service
 *
 * <AUTHOR>
 * @date 2022-10-21 14:39:16
 */
@Service
public class AdvertQuarterDataServiceImpl implements AdvertQuarterDataService {

    @Autowired
    private AdvertQuarterDataMapper advertQuarterDataMapper;

    @Override
    public AdvertQuarterDataEntity selectBy(AdvertQuarterDataEntity param) {
        return advertQuarterDataMapper.selectBy(param);
    }

    @Override
    public AdvertQuarterDataEntity selectByAdvertAndDate(Long advertId, Date curDate) {
        return advertQuarterDataMapper.selectByAdvertAndDate(advertId, curDate);
    }

    @Override
    public AdvertQuarterDataEntity selectHourDataByAdvertAndDateQuarter(Long advertId, Date curDate, Integer curQuarter) {
        AdvertQuarterDataEntity param = new AdvertQuarterDataEntity();
        param.setAdvertId(advertId);
        param.setCurDate(curDate);
        param.setCurQuarter(curQuarter);
        return selectBy(param);
    }

    @Override
    public int insert(AdvertQuarterDataEntity entity) {
        return advertQuarterDataMapper.insert(entity);
    }

    @Override
    public int updateById(AdvertQuarterDataEntity entity) {
        return advertQuarterDataMapper.updateById(entity);
    }

    @Override
    public AdvertQuarterDataEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return advertQuarterDataMapper.selectById(id);
    }
}
