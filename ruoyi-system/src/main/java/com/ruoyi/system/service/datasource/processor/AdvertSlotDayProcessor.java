package com.ruoyi.system.service.datasource.processor;

import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.DataDimensionEnum;
import com.ruoyi.common.enums.InnerLogType;
import com.ruoyi.common.utils.BizUtils;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.system.domain.datashow.DataStatContext;
import com.ruoyi.system.entity.datashow.AdvertSlotDayData;
import com.ruoyi.system.req.datashow.DataStatReq;
import com.ruoyi.system.service.datasource.AdvertSlotDayDataService;
import com.ruoyi.system.service.datasource.DataStatService;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

import static com.ruoyi.common.enums.DataDimensionEnum.ADVERT_SLOT_DAY;

/**
 * 广告广告位维度日数据处理器
 *
 * <AUTHOR>
 * @date 2021/10/15
 */
@Service
public class AdvertSlotDayProcessor implements DataProcessor, InitializingBean {

    @Autowired
    private AdvertSlotDayDataService advertSlotDayDataService;

    @Autowired
    private RedisCache redisCache;

    @Override
    public DataDimensionEnum getDimension() {
        return ADVERT_SLOT_DAY;
    }

    @Override
    public boolean validate(DataStatContext context) {
        DataStatReq req = context.getReq();
        return null != req.getDate() && null != req.getAdvertId() && null != req.getSlotId() && null != req.getConsumerId();
    }

    @Override
    public boolean process(DataStatContext context) {
        DataStatReq req = context.getReq();
        InnerLogType type = context.getType();
        int pv = 1;
        int uv = 0;

        // 计算uv
        switch (type) {
            case ADVERT_LAUNCH:
            case ADVERT_EXPOSURE:
            case ADVERT_CLICK:
            case LANDPAGE_EXPOSURE:
            case LANDPAGE_CLICK:
            case ADVERT_BILLING:
            case BLIND_BOX_POPUP_CLICK:
                String uvKey = EngineRedisKeyFactory.K004.join(type.getType(), req.getAdvertId(), req.getSlotId(), req.getDateStr());
                uv = BizUtils.countUv(uvKey, String.valueOf(req.getConsumerId()));
                break;
            default:
                break;
        }

        // 数据
        AdvertSlotDayData updateAdvertData = new AdvertSlotDayData();
        updateAdvertData.setId(getDataId(req));

        switch (type) {
            case ADVERT_LAUNCH:
                updateAdvertData.setAdLaunchPvAdd(pv);
                updateAdvertData.setAdLaunchUvAdd(uv);
                break;
            case ADVERT_EXPOSURE:
                updateAdvertData.setExposurePvAdd(pv);
                updateAdvertData.setExposureUvAdd(uv);
                break;
            case ADVERT_CLICK:
                updateAdvertData.setClickPvAdd(pv);
                updateAdvertData.setClickUvAdd(uv);
                break;
            case LANDPAGE_EXPOSURE:
                updateAdvertData.setLpExposurePvAdd(pv);
                updateAdvertData.setLpExposureUvAdd(uv);
                break;
            case LANDPAGE_CLICK:
                updateAdvertData.setLpClickPvAdd(pv);
                updateAdvertData.setLpClickUvAdd(uv);
                break;
            case ADVERT_BILLING:
                updateAdvertData.setBillingClickPvAdd(pv);
                updateAdvertData.setBillingClickUvAdd(uv);
                updateAdvertData.setConsumeAdd(NumberUtils.defaultInt(req.getUnitPrice()));
                break;
            case BLIND_BOX_POPUP_CLICK:
                updateAdvertData.setTakePvAdd(pv);
                updateAdvertData.setTakeUvAdd(uv);
                break;
            default:
                break;
        }
        return advertSlotDayDataService.updateAdvertSlotDayData(updateAdvertData) > 0;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        DataStatService.register(this);
    }

    /**
     * 通过缓存获取数据ID
     */
    private Long getDataId(DataStatReq req) {
        String key = EngineRedisKeyFactory.K022.join("AdvertSlotDayData", req.getDateStr(), req.getAdvertId(), req.getSlotId());
        Long dataId = redisCache.getCacheObject(key);
        if (null != dataId) {
            return dataId;
        }

        // 查询并初始数据
        AdvertSlotDayData param = new AdvertSlotDayData();
        param.setCurDate(req.getDate());
        param.setAdvertId(req.getAdvertId());
        param.setSlotId(req.getSlotId());
        AdvertSlotDayData data = advertSlotDayDataService.selectBy(param);
        if (null == data) {
            data = new AdvertSlotDayData();
            data.setCurDate(req.getDate());
            data.setAdvertId(req.getAdvertId());
            data.setSlotId(req.getSlotId());
            advertSlotDayDataService.insertAdvertSlotDayData(data);
            data = advertSlotDayDataService.selectBy(param);
        }
        redisCache.setCacheObject(key, data.getId(), 1, TimeUnit.DAYS);
        return data.getId();
    }
}
