package com.ruoyi.system.service.datasource;


import com.ruoyi.system.entity.fc.FcLinkDayDataEntity;

public interface FcLinkDayDataService {


    /**
     * 获取丰巢链接日数据
     * @param date
     * @param fcLinkKey
     * @return
     */
    FcLinkDayDataEntity getFcLinkDayData(String date, String fcLinkKey);

    /**
     * 统计丰巢链接日数据
     * @param date
     * @param fcLinkKey
     * @param userId
     */
    void stataDayDate(String date, String fcLinkKey, String userId);

    /**
     * 从Redis同步丰巢链接PV和UV数据到MySQL
     * @param date 日期，格式：yyyy-MM-dd
     * @param fcLinkKey 丰巢链接唯一key
     * @return 同步是否成功
     */
    boolean syncRedisMetricsToDb(String date, String fcLinkKey);
    
    /**
     * 清理Redis中丰巢链接的PV和UV缓存数据
     * @param date 日期，格式：yyyy-MM-dd
     * @param fcLinkKey 丰巢链接唯一key
     */
    void cleanRedisCache(String date, String fcLinkKey);

}
