package com.ruoyi.system.service.datasource;

import com.github.pagehelper.PageInfo;
import com.ruoyi.system.bo.slot.CrmSlotDataBo;
import com.ruoyi.system.entity.datashow.SlotData;
import com.ruoyi.system.req.datashow.SlotDataReq;
import com.ruoyi.system.vo.datashow.CrmSlotDataStatisticsVO;
import com.ruoyi.system.vo.datashow.CrmSlotDataVO;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 广告位数据Service接口
 *
 * <AUTHOR>
 * @date 2021-07-04
 */
public interface SlotDataService {

    /**
     * 查询广告位数据
     *
     * @param id 广告位数据ID
     * @return 广告位数据
     */
    SlotData selectSlotDataById(Long id);

    /**
     * 查询广告位数据列表
     *
     * @param slotData 广告位数据
     * @param isExport 是否导出
     * @return 广告位数据集合
     */
    List<SlotData> selectSlotDataList(SlotData slotData, boolean isExport);

    /**
     * 查询广告位数据列表(CRM使用)
     *
     * @param slotData 广告位数据
     * @param isExport 是否导出
     * @return 广告位数据集合
     */
    List<CrmSlotDataBo> selectCrmSlotDataList(SlotData slotData, boolean isExport);

    /**
     * 查询所有符合条件的广告位列表，不分页
     * @param slotData
     * @return
     */
    List<SlotData> selectAllSlotDataList(SlotData slotData);
    /**
     * 查询广告位数据
     *
     * @param slotId 广告位ID
     * @param curDate 日期
     * @return 广告位数据
     */
    SlotData selectBySlotIdAndDate(Long slotId, Date curDate);

    /**
     * 新增广告位数据
     *
     * @param slotData 广告位数据
     * @return 结果
     */
    int insertSlotData(SlotData slotData);

    /**
     * 修改广告位数据
     *
     * @param slotData 广告位数据
     * @return 结果
     */
    int updateSlotData(SlotData slotData);

    /**
     * 更新诺禾消耗
     * @param slotData
     * @return
     */
    int updateNhCost(SlotData slotData);

    /**
     * 获取指定日期的广告位对应的广告位数据
     *
     * @param slotIds 广告位ID列表
     * @param date 日期
     * @return 媒体ID-广告位数据映射
     */
    Map<Long, SlotData> groupBySlotId(List<Long> slotIds, Date date);

    /**
     * 查询广告位数据列表 crm专用
     *
     * @param req 请求参数
     * @param isExport 是否导出
     * @return 广告位数据集合
     */
    PageInfo<CrmSlotDataVO> selectCrmSlotDataList(SlotDataReq req, boolean isExport);

    /**
     * 查询广告位数据汇总
     *
     * @param req 请求参数
     * @return 广告位数据汇总
     */
    CrmSlotDataVO selectStatisticCrmSlotData(SlotDataReq req);

    /**
     * 查询广告位数据列表
     *
     * @param slotData 广告位数据
     * @return 广告位数据集合
     */
    List<SlotData> selectSlotDataList(SlotData slotData);

    /**
     * 查询广告位数据列表(CRM使用)
     *
     * @param slotData 广告位数据
     * @return 广告位数据集合
     */
    List<CrmSlotDataBo> selectCrmSlotDataList(SlotData slotData);

    /**
     * 根据日期筛选指定月份的所有广告位id数据
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 广告位列表
     */
    List<Long> selectDistinctSlotIdByDate(Date startDate, Date endDate);

    /**
     * 查询媒体账号对应的媒体收益总和(昨日及之前)
     *
     * @param accountId 账号ID
     * @return 账号ID-媒体收益总和映射
     */
    Long sumAppRevenueByAccountId(Long accountId);

    /**
     * 查询媒体账号对应的媒体收益总和(昨日及之前)
     *
     * @param accountIds 账号ID列表
     * @return 账号ID-媒体收益总和映射
     */
    Map<Long, Long> sumAppRevenueByAccountId(List<Long> accountIds);

    /**
     * 统计广告位数据
     * @param req 请求参数
     * @return 统计结果
     */
    CrmSlotDataStatisticsVO statisticsCrmSlotData(SlotDataReq req);

    /**
     * 根据媒体id和日期查询广告位数据
     *
     * @param appId 媒体ID
     * @param curDate 日期
     * @return 广告位数据
     */
    List<SlotData> selectDataByAppIdAndDate(Long appId,Date curDate);

    /**
     * 批量更新广告位访问pv和uv
     * @param slotDataList
     * @return
     */
    int batchUpdateSlotRequestPvAndUv(List<SlotData> slotDataList);

    /**
     * 批量更新广告位媒体收益
     * @param slotDataList
     * @return
     */
    int batchUpdateSlotAppRevenue(List<SlotData> slotDataList);

    /**
     * 查询广告位今日的诺禾结算款
     *
     * @param slotIds 广告位ID列表
     * @return 广告位ID-诺禾结算款映射
     */
    Map<Long, Long> selectTodayNhCostMap(List<Long> slotIds);

    /**
     * 查询广告位今日的诺禾结算款汇总
     *
     * @param slotIds 广告位ID列表
     * @return 广告位今日的诺禾结算款汇总
     */
    Long sumTodayNhCostMap(List<Long> slotIds);

    /**
     * 批量新增更新媒体广告位反馈数据
     * @param datas
     * @return
     */
    int batchInsertOrUpdateAppSlotRequestData(List<SlotData> datas);

    /**
     * 根据媒体id列表和日期查询所有广告位日数据
     * @param appIds
     * @param curDate
     * @return
     */
    List<SlotData> selectListByAppIdsAndDates(Set<Long> appIds, Set<String> curDate);
}
