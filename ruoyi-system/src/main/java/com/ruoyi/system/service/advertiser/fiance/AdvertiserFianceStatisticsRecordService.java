package com.ruoyi.system.service.advertiser.fiance;

import com.ruoyi.system.entity.advertiser.finance.AdvertiserFianceStatisticsRecordEntity;
import com.ruoyi.system.req.advertiser.finance.AdvertiserFianceStatisticsRecordListReq;
import com.ruoyi.system.req.advertiser.finance.AdvertiserFianceStatisticsRecordUpdateReq;

import java.util.Date;
import java.util.List;

/**
 * 广告主财务汇总记录表 Service
 *
 * <AUTHOR>
 * @date 2022-3-18 17:58:27
 */
public interface AdvertiserFianceStatisticsRecordService {

    /**
     * 查询广告主的财务汇总记录
     *
     * @param accountId 广告主ID
     * @param curDate 日期
     * @return 广告主财务汇总记录
     */
    AdvertiserFianceStatisticsRecordEntity select(Long accountId, Date curDate);

    /**
     * 查询广告主最新的财务汇总记录
     *
     * @param accountId 广告主ID
     * @return 广告主财务汇总记录
     */
    AdvertiserFianceStatisticsRecordEntity selectLatest(Long accountId);

    /**
     * 新增广告主财务汇总记录
     *
     * @param accountId 广告主ID
     * @param curDate 日期
     * @param cashBalance 现金余额
     * @param rebateBalance 返货余额
     * @return 影响行数
     */
    int insert(Long accountId, Date curDate, Integer cashBalance, Integer rebateBalance);

    /**
     * 更新广告主财务汇总记录
     *
     * @param req 更新参数
     * @return 影响行数
     */
    int update(AdvertiserFianceStatisticsRecordUpdateReq req);

    /**
     * 查询广告主财务汇总记录列表
     *
     * @param req 参数
     * @return 广告主财务汇总记录列表
     */
    List<AdvertiserFianceStatisticsRecordEntity> selectList(AdvertiserFianceStatisticsRecordListReq req);
}
