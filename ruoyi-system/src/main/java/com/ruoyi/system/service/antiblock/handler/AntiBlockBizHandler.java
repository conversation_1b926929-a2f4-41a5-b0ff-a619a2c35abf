package com.ruoyi.system.service.antiblock.handler;

import javax.servlet.http.HttpServletRequest;

/**
 * 防风控跳链业务处理器接口
 * 每个业务实现自己的目标URL获取逻辑
 *
 * <AUTHOR>
 * @date 2025/07/29
 */
public interface AntiBlockBizHandler {

    /**
     * 获取业务代码
     *
     * @return 业务代码
     */
    String getBizCode();

    /**
     * 获取业务名称
     *
     * @return 业务名称
     */
    String getBizName();

    /**
     * 获取目标URL
     * 每个业务自己实现获取目标URL的逻辑
     *
     * @param targetCode 目标代码，业务可根据此参数选择具体文章
     * @param request HTTP请求对象，可以从中获取参数、头信息等
     * @return 目标URL
     */
    String getTargetUrl(String targetCode, HttpServletRequest request);

    /**
     * 是否启用加密
     *
     * @return 是否启用加密
     */
    boolean isEncryptEnabled();

    /**
     * 是否启用该业务
     *
     * @return 是否启用
     */
    boolean isEnabled();

    /**
     * 获取业务描述
     *
     * @return 业务描述
     */
    default String getDescription() {
        return getBizName() + "业务处理器";
    }
}
