package com.ruoyi.system.service.datasource.processor;

import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.DataDimensionEnum;
import com.ruoyi.common.enums.InnerLogType;
import com.ruoyi.common.utils.BizUtils;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.system.domain.datashow.DataStatContext;
import com.ruoyi.system.entity.datashow.MaterialSlotDayData;
import com.ruoyi.system.req.datashow.DataStatReq;
import com.ruoyi.system.service.datasource.DataStatService;
import com.ruoyi.system.service.datasource.MaterialSlotDayDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.ruoyi.common.enums.DataDimensionEnum.MATERIAL_SLOT_DAY;

/**
 * 素材广告位维度日数据处理器
 *
 * <AUTHOR>
 * @date 2021/10/15
 */
@Slf4j
@Service
public class MaterialSlotDayProcessor implements DataProcessor, InitializingBean {

    @Autowired
    private MaterialSlotDayDataService materialSlotDayDataService;

    @Autowired
    private RedisCache redisCache;

    @Override
    public DataDimensionEnum getDimension() {
        return MATERIAL_SLOT_DAY;
    }

    @Override
    public boolean validate(DataStatContext context) {
        DataStatReq req = context.getReq();
        return null != req.getDate() && null != req.getMaterialId() && null != req.getSlotId() && null != req.getConsumerId();
    }

    @Override
    public boolean process(DataStatContext context) {
        DataStatReq req = context.getReq();
        InnerLogType type = context.getType();
        int pv = 1;
        int uv = 0;

        // 直投广告不统计数据
        if (Objects.equals(req.getMaterialId(), 0L)) {
            return true;
        }

        // 计算uv
        switch (type) {
            case ADVERT_EXPOSURE:
            case ADVERT_CLICK:
                String uvKey = EngineRedisKeyFactory.K005.join(type.getType(), req.getMaterialId(), req.getSlotId(), req.getDateStr());
                uv = BizUtils.countUv(uvKey, String.valueOf(req.getConsumerId()));
                break;
            default:
                break;
        }

        // 数据
        MaterialSlotDayData updateMaterialData = new MaterialSlotDayData();
        updateMaterialData.setId(getDataId(req));

        switch (type) {
            case ADVERT_EXPOSURE:
                updateMaterialData.setExposurePvAdd(pv);
                updateMaterialData.setExposureUvAdd(uv);
                break;
            case ADVERT_CLICK:
                updateMaterialData.setClickPvAdd(pv);
                updateMaterialData.setClickUvAdd(uv);
                break;
            case ADVERT_BILLING:
                updateMaterialData.setConsumeAdd(NumberUtils.defaultInt(req.getUnitPrice()));
                break;
            default:
                break;
        }
        return materialSlotDayDataService.updateMaterialSlotDayData(updateMaterialData) > 0;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        DataStatService.register(this);
    }

    /**
     * 通过缓存获取数据ID
     */
    private Long getDataId(DataStatReq req) {
        String key = EngineRedisKeyFactory.K022.join("MaterialSlotDayData", req.getDateStr(), req.getSlotId(), req.getMaterialId());
        Long dataId = redisCache.getCacheObject(key);
        if (null != dataId) {
            return dataId;
        }

        // 查询并初始数据
        MaterialSlotDayData param = new MaterialSlotDayData();
        param.setCurDate(req.getDate());
        param.setMaterialId(req.getMaterialId());
        param.setSlotId(req.getSlotId());
        MaterialSlotDayData data = materialSlotDayDataService.selectBy(param);
        if (null == data) {
            data = new MaterialSlotDayData();
            data.setCurDate(req.getDate());
            data.setMaterialId(req.getMaterialId());
            data.setSlotId(req.getSlotId());
            materialSlotDayDataService.insertMaterialSlotDayData(data);
            data = materialSlotDayDataService.selectBy(param);
        }
        redisCache.setCacheObject(key, data.getId(), 1, TimeUnit.DAYS);
        return data.getId();
    }
}
