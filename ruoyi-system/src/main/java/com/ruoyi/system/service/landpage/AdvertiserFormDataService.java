package com.ruoyi.system.service.landpage;

import com.ruoyi.system.entity.landpage.AdvertiserFormDataEntity;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 广告主日维度表单数据表 Service
 *
 * <AUTHOR>
 * @date 2023-2-15 11:35:07
 */
public interface AdvertiserFormDataService {

    /**
     * 更新广告主表单数据
     *
     * @param curDate 日期
     * @param advertiserId 广告主ID
     * @param isSuccess 是否成功,1.成功,0.失败
     * @param formPrice 表单价格(分)
     * @return 影响行数
     */
    int incr(Date curDate, Long advertiserId, Integer isSuccess, Integer formPrice);

    /**
     * 根据id获取
     */
    AdvertiserFormDataEntity selectById(Long id);

    /**
     * 查询广告主落地页表单数据映射
     *
     * @param startDate 起始日期
     * @param endDate 结束日期
     * @param advertiserIds 广告主ID列表
     * @return 日期_广告主ID-落地页表单数据
     */
    Map<String, AdvertiserFormDataEntity> selectMapByDateAndAdvertiserIds(Date startDate, Date endDate, List<Long> advertiserIds);

    /**
     * 查询广告主落地页表单数据汇总
     *
     * @param startDate 起始日期
     * @param endDate 结束日期
     * @param advertiserIds 广告主ID列表
     * @return 落地页表单数据汇总
     */
    AdvertiserFormDataEntity sumFormDataByDateAndAdvertiserIds(Date startDate, Date endDate, List<Long> advertiserIds);
}
