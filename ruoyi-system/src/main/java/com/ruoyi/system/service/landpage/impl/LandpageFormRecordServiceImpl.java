package com.ruoyi.system.service.landpage.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.ruoyi.common.config.DingWebhookConfig;
import com.ruoyi.common.constant.CrmRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.landpage.AssignTaskInfo;
import com.ruoyi.system.entity.landpage.LandpageFormAreaCount;
import com.ruoyi.system.entity.landpage.LandpageFormCount;
import com.ruoyi.system.entity.landpage.LandpageFormFullRecord;
import com.ruoyi.system.mapper.landpage.LandpageFormFullRecordMapper;
import com.ruoyi.system.mapper.landpage.LandpageFormRecordMapper;
import com.ruoyi.system.req.landpage.LpManualAssignReq;
import com.ruoyi.system.service.advertiser.fiance.AdvertiserBalanceService;
import com.ruoyi.system.service.common.IdCardService;
import com.ruoyi.system.service.landpage.LandpageFormRecordService;
import com.ruoyi.system.service.landpage.LandpageLibraryService;
import com.ruoyi.system.service.manager.AppService;
import com.ruoyi.system.service.manager.SlotService;
import com.ruoyi.system.util.DingRobotUtil;
import com.ruoyi.system.util.LandpageUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 落地页单记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-08-31
 */
@Service
public class LandpageFormRecordServiceImpl implements LandpageFormRecordService {

    @Autowired
    private LandpageFormFullRecordMapper landpageFormFullRecordMapper;

    @Autowired
    private IdCardService idCardService;

    @Autowired
    private AppService appService;

    @Autowired
    private SlotService slotService;

    @Autowired
    public RedisCache redisCache;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private AdvertiserBalanceService advertiserBalanceService;

    @Autowired
    private LandpageLibraryService landpageLibraryService;

    @Autowired
    private LandpageFormRecordMapper landpageFormRecordMapper;

    /**
     * 查询落地页单记录列表
     *
     * @param param 查询条件
     * @return 落地页单记录
     */
    @Override
    public List<LandpageFormFullRecord> selectList(LandpageFormFullRecord param) {
        if (Objects.equals(param.getId(), -1L)) {
            return Collections.emptyList();
        }

        List<LandpageFormFullRecord> records = landpageFormFullRecordMapper.selectList(param);
        if (CollectionUtils.isEmpty(records)) {
            return records;
        }

        Date now = new Date();
        Map<String, String> lpTagMap = landpageLibraryService.getLandpageTagMap();
        Map<Long, String> appNameMap = appService.selectAppNameMap();
        Map<Long, String> slotNameMap = slotService.selectSlotNameMap();

        records.forEach(record -> {
            // 身份证解密
            record.setIdCard(idCardService.decrypt(record.getIdCard()));
            record.setAge(IdcardUtil.getAgeByIdCard(record.getIdCard(), now));
            // 落地页标签
            record.setIsp(record.getLandpageTag());
            if (StringUtils.isBlank(record.getIsp())) {
                String lpk = LandpageUtil.extractLpk(record.getLandpageUrl());
                record.setIsp(lpTagMap.get(StringUtils.defaultString(lpk)));
            }
            // 媒体信息
            record.setAppName(appNameMap.get(record.getAppId()));
            record.setSlotName(slotNameMap.get(record.getSlotId()));
        });
        return records;
    }

    @Override
    public Map<String, Integer> countByDateAndSlotId(Date startDate, Date endDate, List<Long> slotIds) {
        if (CollectionUtils.isEmpty(slotIds)) {
            return Collections.emptyMap();
        }
        LandpageFormFullRecord param = new LandpageFormFullRecord();
        if(Objects.nonNull(startDate)){
            param.setStartDate(DateUtil.beginOfDay(startDate));
        }
        if(Objects.nonNull(endDate)){
            param.setEndDate(DateUtil.endOfDay(endDate));
        }

        param.setSlotIds(slotIds);
        List<LandpageFormCount> countList = landpageFormFullRecordMapper.countByDateAndSlotId(param);
        if (CollectionUtils.isEmpty(countList)) {
            return Collections.emptyMap();
        }
        return countList.stream().collect(Collectors.toMap(
                s -> s.getSlotId() + "_" + DateUtil.formatDate(s.getAssignDate()), LandpageFormCount::getFormCount, (v1, v2) -> v2));
    }

    @Override
    public Integer sumByDateAndSlotId(Date startDate, Date endDate, List<Long> slotIds) {
        LandpageFormFullRecord param = new LandpageFormFullRecord();
        if (Objects.nonNull(startDate)) {
            param.setStartDate(DateUtil.beginOfDay(startDate));
        }
        if (Objects.nonNull(endDate)) {
            param.setEndDate(DateUtil.endOfDay(endDate));
        }
        param.setSlotIds(slotIds);
        return landpageFormFullRecordMapper.sumByDateAndSlotId(param);
    }

    @Override
    public Map<String, Integer> countBySlotIdAndAdvertId(Date date) {
        if (null == date) {
            return Collections.emptyMap();
        }
        LandpageFormFullRecord param = new LandpageFormFullRecord();
        param.setStartDate(DateUtil.beginOfDay(date));
        param.setEndDate(DateUtil.endOfDay(date));
        List<LandpageFormCount> countList = landpageFormFullRecordMapper.countByDate(param);
        if (CollectionUtils.isEmpty(countList)) {
            return Collections.emptyMap();
        }
        return countList.stream().collect(Collectors.toMap(
                s -> s.getSlotId() + "_" + s.getAdvertId(), LandpageFormCount::getFormCount, (v1, v2) -> v2));
    }

    @Override
    public void manualAssign(LpManualAssignReq req) {
        List<Long> advertiserIds = req.getList().stream().map(LpManualAssignReq.AdvertiserRate::getAdvertiserId).collect(Collectors.toList());
        Map<Long, Integer> balanceMap = advertiserBalanceService.selectAdvertiserBalanceMap(advertiserIds);

        // 校验广告主余额
        for (LpManualAssignReq.AdvertiserRate advertiserRate : req.getList()) {
            int balanceAmount = NumberUtils.defaultInt(balanceMap.get(advertiserRate.getAdvertiserId()));
            int sumPrice = NumberUtils.defaultInt(advertiserRate.getFormPrice()) * req.getRecordIds().size() * advertiserRate.getRate() / 100;
            if (sumPrice > balanceAmount) {
                throw new CustomException("广告主(ID:" + advertiserRate.getAdvertiserId() + ")余额不足,剩余" + NumberUtils.fenToYuan(balanceAmount) + "元");
            }
        }

        if (Objects.equals(req.getMax(), 0) || Objects.equals(req.getMin(), req.getMax())) {
            req.setMax(req.getMin() + 1);
        }
        String redisKey = CrmRedisKeyFactory.K013.toString();
        long timestamp = System.currentTimeMillis();
        Long operatorId = SecurityUtils.getLoginUser().getCrmAccountId();

        for (Long recordId : req.getRecordIds()) {
            int rate = RandomUtil.randomInt(0, 100);
            for (LpManualAssignReq.AdvertiserRate advertiser : req.getList()) {
                if (rate < advertiser.getRate()) {
                    AssignTaskInfo taskBo = new AssignTaskInfo();
                    taskBo.setRecordId(recordId);
                    taskBo.setAdvertiserId(advertiser.getAdvertiserId());
                    taskBo.setFormPrice(advertiser.getFormPrice());
                    taskBo.setRemark(advertiser.getRemark());
                    taskBo.setOperatorId(operatorId);
                    redisCache.addCacheZSet(redisKey, JSON.toJSONString(taskBo), timestamp / 1000.0);
                    redisCache.addCacheSet(CrmRedisKeyFactory.K023.join(advertiser.getAdvertiserId()), String.valueOf(recordId));
                    break;
                }
                rate -= advertiser.getRate();
            }
            timestamp = timestamp + RandomUtil.randomInt(req.getMin() * 1000, req.getMax() * 1000);
        }

        // 每分钟最多传20条消息，不然会被屏蔽10分钟
        Long times = redisAtomicClient.incrBy(CrmRedisKeyFactory.K018.join(DateUtil.format(new Date(), "HHmm")), 1, 2, TimeUnit.MINUTES);
        if (times > 20) {
            return;
        }

        StringBuilder sbr = new StringBuilder();
        sbr.append("落地页表单手动分配\n")
                .append("\n数量: ").append(req.getRecordIds().size())
                .append("\n间隔: ").append(req.getMin()).append("-").append(req.getMax()).append("秒")
                .append("\n广告主: ");
        req.getList().forEach(s -> {
            sbr.append("    \n").append(s.getAdvertiserId()).append(" ").append(s.getRate()).append("%");
            if (null != s.getFormPrice()) {
                sbr.append(" 表单价格:").append(NumberUtils.fenToYuan(s.getFormPrice())).append("元");
            }
            if (StringUtils.isNotBlank(s.getRemark())) {
                sbr.append("(").append(s.getRemark()).append(")");
            }
        });
        sbr.append("\n待分配总数: ").append(redisCache.countCacheZSet(redisKey));
        DingRobotUtil.sendText(DingWebhookConfig.getLpNotice(), sbr.toString());
    }

    @Override
    public Map<String, Integer> countByDateAndAdvertiserId(Date startDate, Date endDate, List<Long> advertiserIds, Integer isSuccess) {
        if (CollectionUtils.isEmpty(advertiserIds)) {
            return Collections.emptyMap();
        }
        if (Objects.nonNull(startDate)) {
            startDate = DateUtil.beginOfDay(startDate);
        }
        if (Objects.nonNull(endDate)) {
            endDate = DateUtil.endOfDay(endDate);
        }
        List<LandpageFormCount> countList = landpageFormFullRecordMapper.countByDateAndAdvertiserId(advertiserIds, startDate, endDate, isSuccess);
        if (CollectionUtils.isEmpty(countList)) {
            return Collections.emptyMap();
        }
        return countList.stream().collect(Collectors.toMap(
                s -> DateUtil.formatDate(s.getAssignDate()) + "_" + s.getAdvertiserId(), LandpageFormCount::getFormCount, (v1, v2) -> v2));
    }

    @Override
    public List<LandpageFormFullRecord> selectByOrderIds(List<String> orderIds) {
        if(CollectionUtils.isEmpty(orderIds)){
            return Collections.emptyList();
        }
        return landpageFormFullRecordMapper.selectByOrderIds(orderIds);
    }

    @Override
    public List<LandpageFormAreaCount> selectFormCountGroupByArea(List<Long> advertiserIds, Date startDate, Date endDate,List<String> areaList) {
        return landpageFormFullRecordMapper.selectFormCountGroupByArea(advertiserIds, startDate, endDate,areaList);
    }

    @Override
    public LandpageFormAreaCount selectFormCountSummary(List<Long> advertiserIds, Date startDate, Date endDate, List<String> areaList) {
        return landpageFormFullRecordMapper.selectFormCountSummary(advertiserIds, startDate, endDate,areaList);
    }

    @Override
    public List<Long> selectTotalAdvertIds() {
        return landpageFormRecordMapper.selectTotalAdvertIds();
    }

    @Override
    public List<Long> selectTotalAppIds() {
        return landpageFormRecordMapper.selectTotalAppIds();
    }

    @Override
    public List<Long> selectTotalSlotIds() {
        return landpageFormRecordMapper.selectTotalSlotIds();
    }
}
