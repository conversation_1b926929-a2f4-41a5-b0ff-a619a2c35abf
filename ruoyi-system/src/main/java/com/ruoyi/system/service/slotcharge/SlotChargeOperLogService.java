package com.ruoyi.system.service.slotcharge;

import com.ruoyi.system.entity.slotcharge.SlotChargeOperLogEntity;

import java.util.List;

/**
 * 广告位每日计费方式操作日志 Service
 *
 * <AUTHOR>
 * @date 2022-3-8 16:50:40
 */
public interface SlotChargeOperLogService {

    /**
     * 新增记录
     */
    Boolean insert(SlotChargeOperLogEntity entity);

    /**
     * 根据广告位id获取操作日志
     *
     * @param slotId 广告位ID
     * @return 广告位每日计费方式操作日志
     */
    List<SlotChargeOperLogEntity> selectListBySlotId(Long slotId);
}
