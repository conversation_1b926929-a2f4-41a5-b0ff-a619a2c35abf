package com.ruoyi.system.service.privatesphere;

import com.ruoyi.system.entity.privatesphere.PrivateSphereChannelNumberEntity;

import java.util.List;

/**
 * 私域渠道号表 Service
 *
 * <AUTHOR>
 * @date 2023-2-8 17:03:55
 */
public interface PrivateSphereChannelNumberService {
    /**
     * 新增记录
     */
    Boolean insert(PrivateSphereChannelNumberEntity entity);

    /**
     * 根据id删除
     */
    Boolean deleteById(Long id);

    /**
     * 根据id更新
     */
    Boolean updateById(PrivateSphereChannelNumberEntity entity);

    /**
     * 根据id获取
     */
    PrivateSphereChannelNumberEntity selectById(Long id);

    /**
     * 批量新增更新
     * @param entities
     * @return
     */
    int batchInsertOrUpdate(List<PrivateSphereChannelNumberEntity> entities);

    /**
     * 根据渠道id列表查询渠道编号列表
     * @param channelIds
     * @return
     */
    List<PrivateSphereChannelNumberEntity> selectByChannelIds(List<Long> channelIds);

}
