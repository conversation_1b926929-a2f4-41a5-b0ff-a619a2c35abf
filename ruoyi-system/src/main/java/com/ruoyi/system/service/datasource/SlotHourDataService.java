package com.ruoyi.system.service.datasource;

import com.ruoyi.system.entity.datashow.SlotHourData;

import java.util.Date;

/**
 * 广告位分时段数据Service接口
 *
 * <AUTHOR>
 * @date 2021-08-17
 */
public interface SlotHourDataService {

    /**
     * 查询广告位数据
     *
     * @param slotId 广告位ID
     * @param curDate 日期
     * @param curHour 小时
     * @return 广告位数据
     */
    SlotHourData selectBySlotIdAndDateHour(Long slotId, Date curDate, Integer curHour);

    /**
     * 新增广告位数据
     *
     * @param slotHourData 广告位数据
     * @return 结果
     */
    int insertSlotHourData(SlotHourData slotHourData);

    /**
     * 修改广告位数据
     *
     * @param slotHourData 广告位数据
     * @return 结果
     */
    int updateSlotHourData(SlotHourData slotHourData);
}
