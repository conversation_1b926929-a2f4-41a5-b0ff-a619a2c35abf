package com.ruoyi.system.service.datasource;

import com.ruoyi.system.bo.landpage.ConvDataBo;
import com.ruoyi.system.entity.datashow.AdvertSlotConvHourDataEntity;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 广告广告位维度后端转化时段数据表 Service
 *
 * <AUTHOR>
 * @date 2023-2-9 10:55:35
 */
public interface AdvertSlotConvHourDataService {

    /**
     * 新增记录
     */
    int insert(AdvertSlotConvHourDataEntity entity);

    /**
     * 根据id更新
     */
    int updateById(AdvertSlotConvHourDataEntity entity);

    /**
     * 查询数据
     *
     * @param param 查询条件
     * @return 数据
     */
    AdvertSlotConvHourDataEntity selectBy(AdvertSlotConvHourDataEntity param);

    /**
     * 统计广告-时段维度的后端转化数据
     *
     * @param curDate 日期
     * @param advertId 广告ID
     * @return 时段-后端转化数据
     */
    Map<Integer, ConvDataBo> countByDateAndAdvertId(Date curDate, Long advertId);

    /**
     * 统计广告-日期-时段维度的后端转化数据
     *
     * @param dateList 日期列表
     * @param advertIds 广告ID列表
     * @return 日期_时段_广告ID-后端转化数据
     */
    Map<String, ConvDataBo> countByDateListAndAdvertIds(List<Date> dateList, List<Long> advertIds);

    /**
     * 统计日期-时段维度的后端转化数据
     *
     * @param dateList 日期列表
     * @param advertIds 广告ID列表
     * @return 日期_时段-后端转化数据
     */
    Map<String, ConvDataBo> countByDateListAndAdvertIdsGroupByDateHour(List<Date> dateList, List<Long> advertIds);
}
