package com.ruoyi.system.service.datasource.impl;

import com.ruoyi.system.req.landpage.LandpageDataReq;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.datasource.LandpageDayDataService;
import com.ruoyi.system.entity.datashow.LandpageDayData;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;

import com.ruoyi.system.mapper.datashow.LandpageDayDataMapper;

/**
 * 落地页维度日数据表 Service
 *
 * <AUTHOR>
 * @date 2022-9-21 16:17:57
 */
@Service
public class LandpageDayDataServiceImpl implements LandpageDayDataService {

    @Autowired
    private LandpageDayDataMapper landpageDayDataMapper;

    @Override
    public List<LandpageDayData> selectList(LandpageDataReq param) {
        return landpageDayDataMapper.selectList(param);
    }

    @Override
    public int insert(LandpageDayData entity) {
        if (Objects.isNull(entity)) {
            return 0;
        }
        return landpageDayDataMapper.insert(entity);
    }

    @Override
    public int updateById(LandpageDayData entity) {
        if (Objects.isNull(entity)) {
            return 0;
        }
        return landpageDayDataMapper.updateById(entity);
    }

    @Override
    public LandpageDayData selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return landpageDayDataMapper.selectById(id);
    }

    @Override
    public LandpageDayData selectBy(LandpageDayData param) {
        return landpageDayDataMapper.selectBy(param);
    }
}
