package com.ruoyi.system.service.landpage.impl;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.datashow.BaijiuLandpageFormRecord;
import com.ruoyi.system.entity.landpage.LandpageFormCount;
import com.ruoyi.system.mapper.landpage.BaijiuLandpageFormRecordMapper;
import com.ruoyi.system.service.landpage.BaijiuLandpageFormRecordService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 白酒落地页单记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-24
 */
@Service
public class BaijiuLandpageFormRecordServiceImpl implements BaijiuLandpageFormRecordService {

    @Autowired
    private BaijiuLandpageFormRecordMapper baijiuLandpageFormRecordMapper;


    /**
     * 查询落地页单记录列表
     *
     * @param param 查询条件
     * @return 落地页单记录
     */
    @Override
    public List<BaijiuLandpageFormRecord> selectList(BaijiuLandpageFormRecord param) {
        return baijiuLandpageFormRecordMapper.selectList(param);
    }


    @Override
    public List<BaijiuLandpageFormRecord> selectListByAdvertiserIdAndDate(Date startDate, Date endDate, List<Long> advertId) {

        return baijiuLandpageFormRecordMapper.selectListByAdvertIdAndDate(startDate,endDate,advertId);
    }

    @Override
    public Map<String, Integer> countByDateAndSlotId(Date startDate, Date endDate, List<Long> slotIds) {
        if (CollectionUtils.isEmpty(slotIds)) {
            return Collections.emptyMap();
        }
        BaijiuLandpageFormRecord param = new BaijiuLandpageFormRecord();
        if(Objects.nonNull(startDate)){
            param.setStartDate(DateUtil.beginOfDay(startDate));
        }
        if(Objects.nonNull(endDate)){
            param.setEndDate(DateUtil.endOfDay(endDate));
        }

        param.setSlotIds(slotIds);
        List<LandpageFormCount> countList = baijiuLandpageFormRecordMapper.countByDateAndSlotId(param);
        if (CollectionUtils.isEmpty(countList)) {
            return Collections.emptyMap();
        }
        return countList.stream().collect(Collectors.toMap(
                s -> s.getSlotId() + "_" + DateUtil.formatDate(s.getAssignDate()), LandpageFormCount::getFormCount, (v1, v2) -> v2));
    }

    @Override
    public Integer sumByDateAndSlotId(Date startDate, Date endDate, List<Long> slotIds) {
        BaijiuLandpageFormRecord param = new BaijiuLandpageFormRecord();
        if (Objects.nonNull(startDate)) {
            param.setStartDate(DateUtil.beginOfDay(startDate));
        }
        if (Objects.nonNull(endDate)) {
            param.setEndDate(DateUtil.endOfDay(endDate));
        }
        param.setSlotIds(slotIds);
        return baijiuLandpageFormRecordMapper.sumByDateAndSlotId(param);
    }

    @Override
    public Map<String, Integer> countBySlotIdAndAdvertId(Date date) {
        if (null == date) {
            return Collections.emptyMap();
        }
        BaijiuLandpageFormRecord param = new BaijiuLandpageFormRecord();
        param.setStartDate(DateUtil.beginOfDay(date));
        param.setEndDate(DateUtil.endOfDay(date));
        List<LandpageFormCount> countList = baijiuLandpageFormRecordMapper.countByDate(param);
        if (CollectionUtils.isEmpty(countList)) {
            return Collections.emptyMap();
        }
        return countList.stream().collect(Collectors.toMap(
                s -> s.getSlotId() + "_" + s.getAdvertId(), LandpageFormCount::getFormCount, (v1, v2) -> v2));
    }

    @Override
    public Boolean updateTradeNo(Long id, String tradeNo) {
        if(NumberUtils.isNullOrLteZero(id) || StringUtils.isNull(tradeNo)){
            return false;
        }
        return baijiuLandpageFormRecordMapper.updateTradeNo(id, tradeNo) >0;
    }

    @Override
    public Boolean updateTradeStatus(BaijiuLandpageFormRecord record) {
        return baijiuLandpageFormRecordMapper.updateTradeStatus(record) > 0;
    }

    @Override
    public BaijiuLandpageFormRecord selectById(Long id) {
        if(NumberUtils.isNullOrLteZero(id)){
            return null;
        }
        return baijiuLandpageFormRecordMapper.selectLandpageFormRecordById(id);
    }

    @Override
    public BaijiuLandpageFormRecord selectByTradeNo(String tradeNo) {
        if(StringUtils.isBlank(tradeNo)){
            return null;
        }
        return baijiuLandpageFormRecordMapper.selectByTradeNo(tradeNo);
    }

    @Override
    public List<Long> selectTotalAdvertIds() {
        return baijiuLandpageFormRecordMapper.selectTotalAdvertIds();
    }

    @Override
    public List<Long> selectTotalAppIds() {
        return baijiuLandpageFormRecordMapper.selectTotalAppIds();
    }

    @Override
    public List<Long> selectTotalSlotIds() {
        return baijiuLandpageFormRecordMapper.selectTotalSlotIds();
    }
}
