package com.ruoyi.system.service.landpage.impl;

import com.ruoyi.system.entity.datashow.LandpageFormSendRecordEntity;
import com.ruoyi.system.mapper.landpage.LandpageFormSendRecordMapper;
import com.ruoyi.system.service.landpage.LandpageFormSendRecordService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 落地页表单上报记录 Service
 *
 * <AUTHOR>
 * @date 2021-12-18 16:22:06
 */
@Service
public class LandpageFormSendRecordServiceImpl implements LandpageFormSendRecordService {

    @Autowired
    private LandpageFormSendRecordMapper landpageFormSendRecordMapper;

    @Override
    public int insert(LandpageFormSendRecordEntity entity) {
        if (Objects.isNull(entity)) {
            return 0;
        }
        return landpageFormSendRecordMapper.insert(entity);
    }

    @Override
    public Boolean updateById(LandpageFormSendRecordEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return landpageFormSendRecordMapper.updateById(entity) > 0;
    }

    @Override
    public LandpageFormSendRecordEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return landpageFormSendRecordMapper.selectById(id);
    }

    @Override
    public LandpageFormSendRecordEntity selectByRecordId(Long recordId) {
        if (Objects.isNull(recordId)) {
            return null;
        }
        return landpageFormSendRecordMapper.selectByRecordId(recordId);
    }

    @Override
    public Boolean existByAdvertiserIdAndRecordId(Long advertiserId, Long recordId) {
        if (null == advertiserId || null == recordId) {
            return false;
        }
        return null != landpageFormSendRecordMapper.existByAdvertiserIdAndRecordId(advertiserId, recordId);
    }

    @Override
    public Boolean existByRecordId(Long recordId) {
        if (null == recordId) {
            return false;
        }
        return null != landpageFormSendRecordMapper.existByRecordId(recordId);
    }

    @Override
    public Map<Long, LandpageFormSendRecordEntity> selectMapByRecordIds(List<Long> recordIds) {
        if (CollectionUtils.isEmpty(recordIds)) {
            return Collections.emptyMap();
        }
        List<LandpageFormSendRecordEntity> list = landpageFormSendRecordMapper.selectMapByRecordIds(recordIds);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(LandpageFormSendRecordEntity::getRecordId, Function.identity(), (oldVal, newVal) -> newVal));
    }

    @Override
    public List<Long> selectTotalTargetAdvertiserIds() {
        return landpageFormSendRecordMapper.selectTotalTargetAdvertiserIds();
    }
}
