package com.ruoyi.system.service.slotcharge.impl;

import com.ruoyi.common.utils.NumberUtils;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.slotcharge.SlotChargeOperLogService;
import com.ruoyi.system.entity.slotcharge.SlotChargeOperLogEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import com.ruoyi.system.mapper.slotcharge.SlotChargeOperLogMapper;

/**
 * 广告位每日计费方式操作日志 Service
 *
 * <AUTHOR>
 * @date 2022-3-8 16:50:40
 */
@Service
public class SlotChargeOperLogServiceImpl implements SlotChargeOperLogService {

    @Autowired
    private SlotChargeOperLogMapper slotChargeOperLogMapper;

    @Override
    public Boolean insert(SlotChargeOperLogEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return slotChargeOperLogMapper.insert(entity) > 0;
    }

    @Override
    public List<SlotChargeOperLogEntity> selectListBySlotId(Long slotId) {
        if(NumberUtils.isNullOrLteZero(slotId)){
            return Collections.emptyList();
        }
        return slotChargeOperLogMapper.selectListBySlotId(slotId);
    }
}
