package com.ruoyi.system.service.datasource;

import com.ruoyi.system.bo.landpage.AdvertConvDataParamBo;
import com.ruoyi.system.bo.landpage.ConvDataBo;
import com.ruoyi.system.entity.datashow.AdvertSlotConvDayDataEntity;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 广告广告位维度后端转化日数据表 Service
 *
 * <AUTHOR>
 * @date 2023-2-9 10:54:16
 */
public interface AdvertSlotConvDayDataService {

    /**
     * 新增记录
     */
    int insert(AdvertSlotConvDayDataEntity entity);

    /**
     * 根据id更新
     */
    int updateById(AdvertSlotConvDayDataEntity entity);

    /**
     * 查询数据
     *
     * @param param 查询条件
     * @return 数据
     */
    AdvertSlotConvDayDataEntity selectBy(AdvertSlotConvDayDataEntity param);

    /**
     * 统计广告位_日期维度的后端转化数据
     *
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param slotIds 广告位ID列表
     * @return 广告位ID_日期-后端转化数据
     */
    Map<String, ConvDataBo> countByDateAndSlotIds(Date startDate, Date endDate, List<Long> slotIds);

    /**
     * 统计广告_日期维度的后端转化数据
     *
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param advertIds 广告ID列表
     * @return 广告ID_日期-后端转化数据
     */
    Map<String, ConvDataBo> countByDateAndAdvertIds(Date startDate, Date endDate, List<Long> advertIds);

    /**
     * 统计日期维度的后端转化数据
     *
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param advertIds 广告ID列表
     * @return 日期-后端转化数据
     */
    Map<Date, ConvDataBo> countByDateAndAdvertIds2(Date startDate, Date endDate, List<Long> advertIds);

    /**
     * 统计广告_媒体_日期维度的后端转化数据
     *
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param advertId 广告ID
     * @param appIds 媒体ID列表
     * @return 媒体ID_日期-后端转化数据
     */
    Map<String, ConvDataBo> countByDateAndAdvertIdAndAppIds(Date startDate, Date endDate, Long advertId, List<Long> appIds);

    /**
     * 统计广告_广告位_日期维度的后端转化数据
     *
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param advertId 广告ID
     * @param slotIds 广告位ID列表
     * @return 广告位ID_日期-后端转化数据
     */
    Map<String, ConvDataBo> countByDateAndAdvertIdAndSlotIds(Date startDate, Date endDate, Long advertId, List<Long> slotIds);

    /**
     * 统计广告_广告位_日期维度的后端转化数据
     *
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param advertIds 广告ID列表
     * @param slotIds 广告位ID列表
     * @return 广告位ID_广告ID_日期-后端转化数据
     */
    Map<String, ConvDataBo> countByDateAndAdvertIdAndSlotIds(Date startDate, Date endDate, List<Long> advertIds, List<Long> slotIds);

    /**
     * 查询后端转化汇总数据
     *
     * @param param 参数
     * @return 后端转化汇总数据
     */
    ConvDataBo selectStatisticAdvertConvData(AdvertConvDataParamBo param);
}
