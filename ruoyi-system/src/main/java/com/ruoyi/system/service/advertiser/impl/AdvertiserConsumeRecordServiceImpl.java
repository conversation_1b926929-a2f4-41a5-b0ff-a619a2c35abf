package com.ruoyi.system.service.advertiser.impl;

import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.system.req.advertiser.finance.AdvertiserConsumeListReq;
import com.ruoyi.system.req.advertiser.finance.AdvertiserConsumeRecordUpdateReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.advertiser.fiance.AdvertiserConsumeRecordService;
import com.ruoyi.system.entity.advertiser.finance.AdvertiserConsumeRecordEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.ruoyi.system.mapper.advertiser.finance.AdvertiserConsumeRecordMapper;

/**
 * 广告主消费记录表 Service
 *
 * <AUTHOR>
 * @date 2022-3-18 17:58:07
 */
@Slf4j
@Service
public class AdvertiserConsumeRecordServiceImpl implements AdvertiserConsumeRecordService {

    @Autowired
    private AdvertiserConsumeRecordMapper advertiserConsumeRecordMapper;

    @Override
    public int update(Long accountId, Date curDate, Integer consumeAmount) {
        if (null == accountId || null == curDate || null == consumeAmount || consumeAmount < 0) {
            return 0;
        }

        AdvertiserConsumeRecordEntity record = selectByAccountIdAndDate(accountId, curDate);
        if (null == record) {
            record = new AdvertiserConsumeRecordEntity();
            record.setAccountId(accountId);
            record.setCurDate(curDate);
            record.setConsumeAmount(0);
            try {
                advertiserConsumeRecordMapper.insert(record);
            } catch (DuplicateKeyException e) {
                log.error("AdvertiserConsumeRecordEntity 插入冲突");
                record = selectByAccountIdAndDate(accountId, curDate);
            }
        }

        AdvertiserConsumeRecordUpdateReq updateReq = new AdvertiserConsumeRecordUpdateReq();
        updateReq.setId(record.getId());
        updateReq.setConsumeAmountAdd(consumeAmount);
        return advertiserConsumeRecordMapper.updateByReq(updateReq);
    }

    @Override
    public AdvertiserConsumeRecordEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return advertiserConsumeRecordMapper.selectById(id);
    }

    @Override
    public AdvertiserConsumeRecordEntity selectByAccountIdAndDate(Long accountId, Date curDate) {
        if (null == accountId || null == curDate) {
            return null;
        }
        return advertiserConsumeRecordMapper.selectByAccountIdAndDate(accountId, curDate);
    }

    @Override
    public List<AdvertiserConsumeRecordEntity> selectList(AdvertiserConsumeListReq req) {
        return advertiserConsumeRecordMapper.selectList(req);
    }

    @Override
    public Integer sumConsumeAmount(Long accountId) {
        return advertiserConsumeRecordMapper.sumConsumeAmount(accountId);
    }

    @Override
    public Integer sumConsumeAmountByAccountIds(List<Long> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return 0;
        }
        List<AdvertiserConsumeRecordEntity> list = advertiserConsumeRecordMapper.sumConsumeAmountByAccountIds(accountIds);
        return list.stream().mapToInt(AdvertiserConsumeRecordEntity::getConsumeAmount).sum();
    }

    @Override
    public Map<Long, Integer> sumConsumeAmountGroupByAccountIds(List<Long> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return Collections.emptyMap();
        }
        List<AdvertiserConsumeRecordEntity> list = advertiserConsumeRecordMapper.sumConsumeAmountByAccountIds(accountIds);
        return list.stream().collect(Collectors.toMap(AdvertiserConsumeRecordEntity::getAccountId, AdvertiserConsumeRecordEntity::getConsumeAmount, (v1, v2) -> v2));
    }

    @Override
    public Map<Long, Integer> sumConsumeAmountGroupByAccountIdsAndDate(List<Long> accountIds, Date date) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return Collections.emptyMap();
        }
        List<AdvertiserConsumeRecordEntity> list = advertiserConsumeRecordMapper.sumConsumeAmountByAccountIdsAndDate(accountIds, date);
        return list.stream().collect(Collectors.toMap(AdvertiserConsumeRecordEntity::getAccountId, AdvertiserConsumeRecordEntity::getConsumeAmount, (v1, v2) -> v2));
    }
}
