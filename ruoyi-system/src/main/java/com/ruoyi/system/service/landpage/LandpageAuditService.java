package com.ruoyi.system.service.landpage;

import com.ruoyi.system.entity.manager.LandpageAuditRecord;
import com.ruoyi.system.req.manager.LandpageAuditRecordReq;

import java.util.List;
import java.util.Map;

/**
 * 落地页审核服务接口
 *
 * <AUTHOR>
 * @date 2021-09-10
 */
@Deprecated
public interface LandpageAuditService {

    /**
     * TODO 后面优化
     * 查询爱奇艺审核通过的落地页链接
     *
     * @return 原链接-爱奇艺链接映射
     */
    Map<String, String> queryIqiyiUrlMap();

    /**
     * 查询落地页送审记录列表
     *
     * @param req 请求参数
     * @return 落地页送审记录集合
     */
    List<LandpageAuditRecord> selectLandpageAuditRecordList(LandpageAuditRecordReq req);

    /**
     * 落地页链接送审
     *
     * @param urlList 落地页链接
     * @return 成功送审的链接数量
     */
    int landpageAudit(List<LandpageAuditRecord> urlList);

    /**
     * 重新送审
     *
     * @param recordId 记录ID
     */
    void reAudit(Long recordId);

    /**
     * 落地页送审回调
     *
     * @param content 落地页链接
     */
    void callback(String content);
}
