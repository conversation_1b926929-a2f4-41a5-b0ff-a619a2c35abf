package com.ruoyi.system.service.datasource.processor;

import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.DataDimensionEnum;
import com.ruoyi.common.enums.InnerLogType;
import com.ruoyi.common.utils.BizUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.datashow.DataStatContext;
import com.ruoyi.system.entity.datashow.MobileHapDataEntity;
import com.ruoyi.system.req.datashow.DataStatReq;
import com.ruoyi.system.service.datasource.DataStatService;
import com.ruoyi.system.service.datasource.MobileHapDataService;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

import static com.ruoyi.common.enums.DataDimensionEnum.MOBILE_HAP_DATA;

/**
 * 设备维度快应用数据处理器
 *
 * <AUTHOR>
 * @date 2022/11/14
 */
@Service
public class MobileHapDataProcessor implements DataProcessor, InitializingBean {

    @Autowired
    private MobileHapDataService mobileHapDataService;

    @Autowired
    private RedisCache redisCache;

    @Override
    public DataDimensionEnum getDimension() {
        return MOBILE_HAP_DATA;
    }

    @Override
    public boolean process(DataStatContext context) {
        DataStatReq req = context.getReq();
        InnerLogType type = context.getType();
        int pv = 1;
        int uv = 0;

        // 过滤掉非快应用的数据
        if (StringUtils.isBlank(req.getMobileModel())) {
            return true;
        }

        // 计算uv
        switch (type) {
            case ADVERT_LAUNCH:
            case ADVERT_EXPOSURE:
            case ADVERT_CLICK:
            case QUICKAPP_LAUNCH:
            case LANDPAGE_EXPOSURE:
            case LANDPAGE_CLICK:
                String uvKey = EngineRedisKeyFactory.K012.join(type.getType(), req.getMobileModel(), req.getDateStr());
                uv = BizUtils.countUv(uvKey, String.valueOf(req.getConsumerId()));
                break;
            default:
                break;
        }

        // 数据
        MobileHapDataEntity updateData = new MobileHapDataEntity();
        updateData.setId(getDataId(req));

        switch (type) {
            case ADVERT_LAUNCH:
                updateData.setAdLaunchPvAdd(pv);
                updateData.setAdLaunchUvAdd(uv);
                break;
            case ADVERT_EXPOSURE:
                updateData.setAdExposurePvAdd(pv);
                updateData.setAdExposureUvAdd(uv);
                break;
            case ADVERT_CLICK:
                updateData.setAdClickPvAdd(pv);
                updateData.setAdClickUvAdd(uv);
                break;
            case QUICKAPP_LAUNCH:
                updateData.setHapLaunchPvAdd(pv);
                updateData.setHapLaunchUvAdd(uv);
                break;
            case LANDPAGE_EXPOSURE:
                updateData.setLpExposurePvAdd(pv);
                updateData.setLpExposureUvAdd(uv);
                break;
            case LANDPAGE_CLICK:
                updateData.setLpClickPvAdd(pv);
                updateData.setLpClickUvAdd(uv);
                break;
            default:
                break;
        }
        return mobileHapDataService.updateById(updateData) > 0;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        DataStatService.register(this);
    }

    /**
     * 通过缓存获取数据ID
     */
    private Long getDataId(DataStatReq req) {
        String key = EngineRedisKeyFactory.K022.join("MobileHapDataEntity", req.getMobileModel());
        Long dataId = redisCache.getCacheObject(key);
        if (null != dataId) {
            return dataId;
        }

        // 查询并初始数据
        MobileHapDataEntity param = new MobileHapDataEntity();
        param.setModel(req.getMobileModel());
        MobileHapDataEntity data = mobileHapDataService.selectBy(param);
        if (null == data) {
            data = new MobileHapDataEntity();
            data.setModel(req.getMobileModel());
            mobileHapDataService.insert(data);
            data = mobileHapDataService.selectBy(param);
        }
        redisCache.setCacheObject(key, data.getId(), 30, TimeUnit.DAYS);
        return data.getId();
    }
}
