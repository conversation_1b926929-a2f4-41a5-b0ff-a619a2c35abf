package com.ruoyi.system.service.privatesphere;

import com.ruoyi.system.entity.privatesphere.PrivateSphereKefuChannelDataEntity;

import java.util.List;

/**
 * 私域客服渠道数据表 Service
 *
 * <AUTHOR>
 * @date 2023-3-7 10:30:06
 */
public interface PrivateSphereKefuChannelDataService {
    /**
     * 新增记录
     */
    Boolean insert(PrivateSphereKefuChannelDataEntity entity);

    /**
     * 根据id删除
     */
    Boolean deleteById(Long id);

    /**
     * 根据id更新
     */
    Boolean updateById(PrivateSphereKefuChannelDataEntity entity);

    /**
     * 根据id获取
     */
    PrivateSphereKefuChannelDataEntity selectById(Long id);

    /**
     * 批量新增更新
     * @param entities
     * @return
     */
    int batchInsertOrUpdate(List<PrivateSphereKefuChannelDataEntity> entities);


    /**
     * 根据渠道id列表查询渠道数据
     * @param ids
     * @return
     */
    List<PrivateSphereKefuChannelDataEntity> selectListByDataIds(List<Long> ids);

    /**
     * 根据数据id列表查询统计
     * @param dataIds
     * @return
     */
    List<PrivateSphereKefuChannelDataEntity> statisticsByDataIds(List<Long> dataIds);

}
