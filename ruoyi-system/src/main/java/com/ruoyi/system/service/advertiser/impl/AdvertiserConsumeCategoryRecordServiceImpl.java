package com.ruoyi.system.service.advertiser.impl;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.system.bo.advertiser.finance.AdvertiserConsumeCategorySumBo;
import com.ruoyi.system.entity.advertiser.finance.AdvertiserConsumeCategoryRecordEntity;
import com.ruoyi.system.mapper.advertiser.finance.AdvertiserConsumeCategoryRecordMapper;
import com.ruoyi.system.req.advertiser.finance.AdvertiserConsumeRecordUpdateReq;
import com.ruoyi.system.service.advertiser.fiance.AdvertiserConsumeCategoryRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 广告主结算分类消费记录表 Service
 *
 * <AUTHOR>
 * @date 2022-04-06
 */
@Slf4j
@Service
public class AdvertiserConsumeCategoryRecordServiceImpl implements AdvertiserConsumeCategoryRecordService {

    @Autowired
    private AdvertiserConsumeCategoryRecordMapper advertiserConsumeCategoryRecordMapper;

    @Override
    public int update(Long accountId, Date curDate, Integer billingType, Integer consumeAmount) {
        if (null == accountId || null == curDate || null == billingType || NumberUtils.isNullOrLteZero(consumeAmount)) {
            return 0;
        }

        AdvertiserConsumeCategoryRecordEntity record = selectBy(accountId, curDate, billingType);
        if (null == record) {
            record = new AdvertiserConsumeCategoryRecordEntity();
            record.setAccountId(accountId);
            record.setCurDate(curDate);
            record.setBillingType(billingType);
            record.setConsumeAmount(0);
            try {
                advertiserConsumeCategoryRecordMapper.insert(record);
            } catch (DuplicateKeyException e) {
                log.error("AdvertiserConsumeCategoryRecordEntity 插入冲突");
                record = selectBy(accountId, curDate, billingType);
            }
        }

        AdvertiserConsumeRecordUpdateReq updateReq = new AdvertiserConsumeRecordUpdateReq();
        updateReq.setId(record.getId());
        updateReq.setConsumeAmountAdd(consumeAmount);
        return advertiserConsumeCategoryRecordMapper.updateByReq(updateReq);
    }

    @Override
    public AdvertiserConsumeCategoryRecordEntity selectBy(Long accountId, Date curDate, Integer billingType) {
        if (null == accountId || null == curDate || null == billingType) {
            return null;
        }
        return advertiserConsumeCategoryRecordMapper.selectBy(accountId, curDate, billingType);
    }

    @Override
    public List<AdvertiserConsumeCategorySumBo> selectSumByDateAndAccountId(Long accountId, Date startDate, Date endDate, Integer billingType) {
        if(NumberUtils.isNullOrLteZero(accountId)){
            return Collections.emptyList();
        }
        if(Objects.nonNull(startDate)){
            startDate = DateUtil.beginOfDay(startDate);
        }
        if(Objects.nonNull(endDate)){
            endDate = DateUtil.endOfDay(endDate);
        }
        return advertiserConsumeCategoryRecordMapper.selectSumByDateAndAccountId(accountId, startDate,endDate,billingType);
    }
}
