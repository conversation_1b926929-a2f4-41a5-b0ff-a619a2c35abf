package com.ruoyi.system.service.landpage.impl;

import com.ruoyi.system.vo.datashow.CrmExternalLandpageRecordHistoryVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.landpage.ExternalLandpageRecordHistoryService;
import com.ruoyi.system.entity.landpage.ExternalLandpageRecordHistoryEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.ruoyi.system.mapper.landpage.ExternalLandpageRecordHistoryMapper;

/**
 * 外部落地页表单修改历史 Service
 *
 * <AUTHOR>
 * @date 2023-4-11 15:38:42
 */
@Service
public class ExternalLandpageRecordHistoryServiceImpl implements ExternalLandpageRecordHistoryService {

    @Autowired
    private ExternalLandpageRecordHistoryMapper externalLandpageRecordHistoryMapper;

    @Override
    public Boolean insert(ExternalLandpageRecordHistoryEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return externalLandpageRecordHistoryMapper.insert(entity) > 0;
    }

    @Override
    public Boolean updateById(ExternalLandpageRecordHistoryEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return externalLandpageRecordHistoryMapper.updateById(entity) > 0;
    }

    @Override
    public ExternalLandpageRecordHistoryEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return externalLandpageRecordHistoryMapper.selectById(id);
    }

    @Override
    public Map<Long, List<CrmExternalLandpageRecordHistoryVO>> selectMapByOriginRecordId(List<Long> originRecordIds) {
        if (CollectionUtils.isEmpty(originRecordIds)) {
            return Collections.emptyMap();
        }
        List<CrmExternalLandpageRecordHistoryVO> list = externalLandpageRecordHistoryMapper.selectListByOriginRecordId(originRecordIds);
        return list.stream().collect(Collectors.groupingBy(CrmExternalLandpageRecordHistoryVO::getOriginRecordId, Collectors.toList()));
    }
}
