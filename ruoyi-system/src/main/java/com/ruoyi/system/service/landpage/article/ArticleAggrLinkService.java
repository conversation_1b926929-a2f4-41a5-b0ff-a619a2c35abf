package com.ruoyi.system.service.landpage.article;

import com.ruoyi.system.bo.landpage.article.ArticleAggrLinkListParamBo;
import com.ruoyi.system.entity.landpage.article.ArticleAggrLinkEntity;

import java.util.List;
import java.util.Set;

/**
 * 文章聚合链接表 Service
 *
 * <AUTHOR>
 * @date 2023-12-1 15:12:44
 */
public interface ArticleAggrLinkService {

    /**
     * 生成链接唯一标识
     */
    String generateKey();

    /**
     * 查询列表
     */
    List<ArticleAggrLinkEntity> selectList(ArticleAggrLinkListParamBo param);

    /**
     * 新增记录
     */
    Boolean insert(ArticleAggrLinkEntity entity);

    /**
     * 根据id删除
     */
    Boolean deleteById(Long id);

    /**
     * 根据id更新
     */
    Boolean updateById(ArticleAggrLinkEntity entity);

    /**
     * 根据id获取
     */
    ArticleAggrLinkEntity selectById(Long id);

    /**
     * 根据Key获取
     */
    ArticleAggrLinkEntity selectByKey(String key);

    /**
     * 查询所有的Key
     */
    Set<String> selectTotalKey();

    /**
     * 链接名称是否已存在
     */
    boolean isNameExist(String name, Long advertiserId, Long id);

    /**
     * 根据广告主id查询当天的投放链接
     */
    ArticleAggrLinkEntity selectTodayLinkByAdvertiserId(Long advertiserId);
}
