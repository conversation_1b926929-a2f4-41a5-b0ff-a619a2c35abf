package com.ruoyi.system.service.slotcharge;

import com.ruoyi.system.entity.datashow.SlotData;
import com.ruoyi.system.entity.slotcharge.SlotChargeEntity;
import com.ruoyi.system.req.slot.SlotChargeDataUpdateReq;

import java.util.Date;
import java.util.List;

/**
 * 广告位每日计费方式 Service
 *
 * <AUTHOR>
 * @date 2022-3-7 19:42:45
 */
public interface SlotChargeService {

    /**
     * 新增记录
     */
    Boolean insert(SlotChargeEntity entity);

    /**
     * 根据id更新
     */
    Boolean updateById(SlotChargeEntity entity);

    /**
     * 根据id获取
     */
    SlotChargeEntity selectById(Long id);

    /**
     * 根据广告位id列表和日期查询
     * @param slotIds
     * @return
     */
    List<SlotChargeEntity> selectListBySlotIdsAndYesterday(List<Long> slotIds);

    /**
     * 根据广告位id和日期查询
     * @param slotId
     * @param date
     * @return
     */
    SlotChargeEntity selectBySlotIdAndDate(Long slotId, Date date);

    /**
     * 更新广告位结算设置
     * @param req
     * @return
     */
    Boolean updateSlotChargeData(SlotChargeDataUpdateReq req);

    /**
     * 计算广告位媒体收益
     * @param slotChargeEntity
     * @param slotData
     * @return
     */
    long calculateAppRevenue(SlotChargeEntity slotChargeEntity, SlotData slotData);

    /**
     * 根据广告位id列表和日期范围查询
     * @param slotIds
     * @param dates
     * @return
     */
    List<SlotChargeEntity> selectListBySlotIdsAndDateList(List<Long> slotIds,List<Date> dates);

    /**
     * 根据广告位id列表和日期范围查询
     * @param slotIds
     * @param startDate
     * @param endDate
     * @return
     */
    List<SlotChargeEntity> selectListBySlotIdsAndDateRange(List<Long> slotIds, Date startDate, Date endDate);

    /**
     * 分页查询列表
     * @param id
     * @param pageSize
     * @return
     */
    List<SlotChargeEntity> selectListByDate(Long id,Integer pageSize,Date date);

    /**
     * 批量新增更新
     * @param entities
     * @return
     */
    int batchInsertOrUpdate(List<SlotChargeEntity> entities);
}
