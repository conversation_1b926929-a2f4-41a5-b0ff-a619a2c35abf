package com.ruoyi.system.service.landpage.article;

import com.ruoyi.system.bo.landpage.article.ArticleAggrLinkDataBo;
import com.ruoyi.system.bo.landpage.article.ArticleAggrLinkDataParamBo;
import com.ruoyi.system.entity.landpage.article.ArticleAggrLinkHourDataEntity;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 文章聚合链接时段数据表 Service
 *
 * <AUTHOR>
 * @date 2023-12-1 15:13:58
 */
public interface ArticleAggrLinkHourDataService {

    /**
     * 新增记录
     */
    Boolean insert(ArticleAggrLinkHourDataEntity entity);

    /**
     * 根据id更新
     */
    Boolean updateById(ArticleAggrLinkHourDataEntity entity);

    /**
     * 更新数据
     */
    Boolean update(Long id, Integer requestPv, Integer requestUv);

    /**
     * 根据id获取
     */
    ArticleAggrLinkHourDataEntity selectById(Long id);

    /**
     * 查询聚合链接数据
     */
    ArticleAggrLinkHourDataEntity selectBy(Date curDate, Integer curHour, Long linkId);

    /**
     * 根据条件查询汇总数据
     */
    ArticleAggrLinkDataBo selectSumBy(ArticleAggrLinkDataParamBo param);

    /**
     * 查询链接数据
     */
    List<ArticleAggrLinkDataBo> selectDataBy(ArticleAggrLinkDataParamBo param);

    /**
     * 查询链接今日数据
     *
     * @param linkIds 链接ID列表
     * @return 链接ID-今日数据映射
     */
    Map<Long, ArticleAggrLinkDataBo> selectTodayDataByLinkIds(List<Long> linkIds);
}
