package com.ruoyi.system.service.advertiser;

import com.ruoyi.system.entity.advertiser.AdvertiserBudgetEntity;

import java.util.List;
import java.util.Map;

/**
 * 广告主预算表 Service
 *
 * <AUTHOR>
 * @date 2023-4-17 11:42:58
 */
public interface AdvertiserBudgetService {

    /**
     * 新增记录
     */
    Boolean insert(AdvertiserBudgetEntity entity);

    /**
     * 根据id更新
     */
    Boolean updateById(AdvertiserBudgetEntity entity);

    /**
     * 获取广告主的预算
     *
     * @param accountId 广告主ID
     * @return 广告主预算
     */
    AdvertiserBudgetEntity selectByAccountId(Long accountId);

    /**
     * 获取广告主的预算
     *
     * @param accountId 广告主ID
     * @return 广告主预算，null为不限
     */
    Long selectBudgetByAccountId(Long accountId);

    /**
     * 批量获取广告主的预算
     *
     * @param accountIds 广告主ID列表
     * @return 广告主ID-广告主预算映射
     */
    Map<Long, Long> selectBudgetMapByAccountIds(List<Long> accountIds);

    /**
     * 更新广告主日预算
     *
     * @param accountId 广告主ID
     * @param budget 预算(分),null为不限
     */
    void updateAdvertiserBudget(Long accountId, Long budget);
}
