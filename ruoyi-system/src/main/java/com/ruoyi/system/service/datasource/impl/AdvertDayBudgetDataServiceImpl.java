package com.ruoyi.system.service.datasource.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.entity.datashow.AdvertDayBudgetData;
import com.ruoyi.system.mapper.datashow.AdvertDayBudgetDataMapper;
import com.ruoyi.system.service.datasource.AdvertDayBudgetDataService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 广告日预算数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-10-19
 */
@Service
public class AdvertDayBudgetDataServiceImpl implements AdvertDayBudgetDataService {

    @Autowired
    private AdvertDayBudgetDataMapper advertDayBudgetDataMapper;

    @Override
    public AdvertDayBudgetData selectByDateAndAdvertId(Date curDate, Long advertId) {
        return advertDayBudgetDataMapper.selectByDateAndAdvertId(curDate, advertId);
    }

    /**
     * 查询广告日预算数据列表
     *
     * @param advertDayBudgetData 广告日预算数据
     * @return 广告日预算数据
     */
    @Override
    public List<AdvertDayBudgetData> selectAdvertDayBudgetDataList(AdvertDayBudgetData advertDayBudgetData) {
        return advertDayBudgetDataMapper.selectAdvertDayBudgetDataList(advertDayBudgetData);
    }

    @Override
    public Map<String, Integer> selectBudgetByDateAndAdvertIds(Date startDate, Date endDate, List<Long> advertIds) {
        if (CollectionUtils.isEmpty(advertIds)) {
            return Collections.emptyMap();
        }

        AdvertDayBudgetData param = new AdvertDayBudgetData();
        param.setStartDate(startDate);
        param.setEndDate(endDate);
        param.setAdvertIds(advertIds);
        List<AdvertDayBudgetData> list = selectAdvertDayBudgetDataList(param);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        return list.stream().filter(data -> null != data.getBudget()).collect(Collectors.toMap(
                data -> data.getAdvertId() + "-" + DateUtils.dateTime(data.getCurDate()), AdvertDayBudgetData::getBudget,
                (oldVal, newVal) -> newVal));
    }

    /**
     * 新增广告日预算数据
     *
     * @param advertDayBudgetData 广告日预算数据
     * @return 结果
     */
    @Override
    public int insertAdvertDayBudgetData(AdvertDayBudgetData advertDayBudgetData) {
        return advertDayBudgetDataMapper.insertAdvertDayBudgetData(advertDayBudgetData);
    }

    /**
     * 更新广告日预算数据
     *
     * @param param 广告日预算数据
     * @return 结果
     */
    @Override
    public int updateBudget(AdvertDayBudgetData param) {
        AdvertDayBudgetData data = selectByDateAndAdvertId(param.getCurDate(), param.getAdvertId());
        if (null != data) {
            return advertDayBudgetDataMapper.updateBudget(param);
        } else {
            return advertDayBudgetDataMapper.insertAdvertDayBudgetData(param);
        }
    }
}
