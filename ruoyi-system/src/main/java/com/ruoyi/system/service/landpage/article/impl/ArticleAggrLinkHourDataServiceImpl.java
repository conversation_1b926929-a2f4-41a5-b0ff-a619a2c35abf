package com.ruoyi.system.service.landpage.article.impl;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.system.bo.landpage.article.ArticleAggrLinkDataBo;
import com.ruoyi.system.bo.landpage.article.ArticleAggrLinkDataParamBo;
import com.ruoyi.system.bo.landpage.article.ArticleAggrLinkDataUpdateParamBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.landpage.article.ArticleAggrLinkHourDataService;
import com.ruoyi.system.entity.landpage.article.ArticleAggrLinkHourDataEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.ruoyi.system.mapper.landpage.article.ArticleAggrLinkHourDataMapper;

/**
 * 文章聚合链接时段数据表 Service
 *
 * <AUTHOR>
 * @date 2023-12-1 15:13:58
 */
@Slf4j
@Service
public class ArticleAggrLinkHourDataServiceImpl implements ArticleAggrLinkHourDataService {

    @Autowired
    private ArticleAggrLinkHourDataMapper articleAggrLinkHourDataMapper;

    @Override
    public Boolean insert(ArticleAggrLinkHourDataEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return articleAggrLinkHourDataMapper.insert(entity) > 0;
    }

    @Override
    public Boolean updateById(ArticleAggrLinkHourDataEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return articleAggrLinkHourDataMapper.updateById(entity) > 0;
    }

    @Override
    public Boolean update(Long id, Integer requestPv, Integer requestUv) {
        if (null == id) {
            return false;
        }
        try {
            ArticleAggrLinkDataUpdateParamBo param = new ArticleAggrLinkDataUpdateParamBo();
            param.setId(id);
            param.setRequestPvAdd(requestPv);
            param.setRequestUvAdd(requestUv);
            return articleAggrLinkHourDataMapper.update(param) > 0;
        } catch (Exception e) {
            log.error("文章聚合数据更新异常, id={}", id, e);
        }
        return false;
    }

    @Override
    public ArticleAggrLinkHourDataEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return articleAggrLinkHourDataMapper.selectById(id);
    }

    @Override
    public ArticleAggrLinkHourDataEntity selectBy(Date curDate, Integer curHour, Long linkId) {
        return articleAggrLinkHourDataMapper.selectBy(curDate, curHour, linkId);
    }

    @Override
    public ArticleAggrLinkDataBo selectSumBy(ArticleAggrLinkDataParamBo param) {
        return articleAggrLinkHourDataMapper.selectSumBy(param);
    }

    @Override
    public List<ArticleAggrLinkDataBo> selectDataBy(ArticleAggrLinkDataParamBo param) {
        if (null == param || CollectionUtils.isEmpty(param.getLinkIds())) {
            return Collections.emptyList();
        }
        return articleAggrLinkHourDataMapper.selectDayDataBy(param);
    }

    @Override
    public Map<Long, ArticleAggrLinkDataBo> selectTodayDataByLinkIds(List<Long> linkIds) {
        if (CollectionUtils.isEmpty(linkIds)) {
            return Collections.emptyMap();
        }
        Date today = DateUtil.beginOfDay(new Date());
        ArticleAggrLinkDataParamBo param = new ArticleAggrLinkDataParamBo();
        param.setLinkIds(linkIds);
        param.setStartDate(today);
        param.setEndDate(today);
        List<ArticleAggrLinkDataBo> list = articleAggrLinkHourDataMapper.selectDayDataBy(param);
        return list.stream().collect(Collectors.toMap(ArticleAggrLinkDataBo::getLinkId, Function.identity(), (v1, v2) -> v2));
    }
}
