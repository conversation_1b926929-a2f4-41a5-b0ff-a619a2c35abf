package com.ruoyi.system.service.datasource.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.ruoyi.common.constant.ErrorCode;
import com.ruoyi.common.enums.ConfirmStatusEnum;
import com.ruoyi.common.enums.common.SwitchStatusEnum;
import com.ruoyi.common.enums.common.WhitelistType;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.bo.data.AppDataUpdateBO;
import com.ruoyi.system.domain.slot.SlotDataSnapshot;
import com.ruoyi.system.entity.account.finance.AccountRevenueEntity;
import com.ruoyi.system.entity.app.App;
import com.ruoyi.system.entity.appdata.AppMonthDataEntity;
import com.ruoyi.system.entity.datashow.AppData;
import com.ruoyi.system.entity.datashow.SlotData;
import com.ruoyi.system.entity.slot.Slot;
import com.ruoyi.system.entity.slot.SlotDataEditHistory;
import com.ruoyi.system.entity.slot.SlotSwitchConfig;
import com.ruoyi.system.entity.slotcharge.SlotChargeEntity;
import com.ruoyi.system.entity.slotdata.SlotMonthDataEntity;
import com.ruoyi.system.job.AppMonthDataSyncJob;
import com.ruoyi.system.job.SlotMonthDataSyncJob;
import com.ruoyi.system.manager.publisher.PublisherManager;
import com.ruoyi.system.mapper.manager.SlotMapper;
import com.ruoyi.system.mapper.slot.SlotDataEditHistoryMapper;
import com.ruoyi.system.req.slot.SlotDataUpdateReq;
import com.ruoyi.system.req.slot.SlotMonthDataUpdateReq;
import com.ruoyi.system.service.appdata.AppMonthDataService;
import com.ruoyi.system.service.common.WhitelistService;
import com.ruoyi.system.service.datasource.AppDataService;
import com.ruoyi.system.service.datasource.SlotAppDataService;
import com.ruoyi.system.service.datasource.SlotDataService;
import com.ruoyi.system.service.finance.AccountRevenueService;
import com.ruoyi.system.service.manager.AppService;
import com.ruoyi.system.service.manager.SlotConfigService;
import com.ruoyi.system.service.slotcharge.SlotChargeService;
import com.ruoyi.system.service.slotdata.SlotMonthDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static cn.hutool.core.util.BooleanUtil.isTrue;
import static com.ruoyi.common.enums.publisher.PayType.POSTPAID;
import static com.ruoyi.common.enums.publisher.PayType.PREPAY;
import static com.ruoyi.common.enums.publisher.PayType.isPrepay;
import static com.ruoyi.common.enums.slot.SlotDataVisibleEnum.VISIBLE;

/**
 * 广告位媒体数据更新接口实现
 *
 * <AUTHOR>
 * @date 2021-07-04
 */
@Slf4j
@Service
public class SlotAppDataServiceImpl implements SlotAppDataService {

    @Autowired
    private AppService appService;

    @Autowired
    private SlotMapper slotMapper;

    @Autowired
    private AppDataService appDataService;

    @Autowired
    private SlotDataService slotDataService;

    @Autowired
    private SlotMonthDataService slotMonthDataService;

    @Autowired
    private AppMonthDataService appMonthDataService;

    @Autowired
    private SlotChargeService slotChargeService;

    @Autowired
    private SlotDataEditHistoryMapper slotDataEditHistoryMapper;

    @Autowired
    public TransactionTemplate transactionTemplate;

    @Autowired
    private WhitelistService whitelistService;

    @Autowired
    private AccountRevenueService accountRevenueService;

    @Autowired
    private PublisherManager publisherManager;

    @Autowired
    private SlotConfigService slotConfigService;

    @Autowired
    private SlotMonthDataSyncJob slotMonthDataSyncJob;

    @Autowired
    private AppMonthDataSyncJob appMonthDataSyncJob;

    @Override
    public void updateSlotAppData(SlotDataUpdateReq req) {
        // 参数校验
        if (null == req.getSlotDataId() && null == req.getSlotId() && null == req.getCurDate()) {
            throw new CustomException(ErrorCode.ARGS);
        }

        // 查询广告位数据
        SlotData slotData;
        if (null != req.getSlotDataId()) {
            slotData = slotDataService.selectSlotDataById(req.getSlotDataId());
        } else {
            slotData = selectOrInsertSlotData(req.getSlotId(), req.getCurDate());
        }
        if (Objects.isNull(slotData)) {
            throw new CustomException(ErrorCode.E108001);
        }
        Long appId = slotData.getAppId();
        AppMonthDataEntity appMonthDataEntity = appMonthDataService.selectByAppIdAndMonth(appId, DateUtils.dateTimeMonth(req.getCurDate()));

        // 已确认的月账单数据无法修改
        if (Objects.nonNull(appMonthDataEntity) && !Objects.equals(appMonthDataEntity.getConfirmStatus(), ConfirmStatusEnum.NO_CONFIRM.getStatus())) {
            throw new CustomException(ErrorCode.E108007);
        }
        // 广告位开关设置
        SlotSwitchConfig switchConfig = slotConfigService.selectSwitchConfigBySlotId(req.getSlotId());

        // 更新数据
        Boolean success = transactionTemplate.execute(status -> {
            try {
                // 生成快照
                SlotDataSnapshot beforeSnapshot = SlotDataSnapshot.create(slotData);

                // 更新广告位数据
                if (null != req.getSlotRequestPv()) {
                    slotData.setSlotRequestPv(req.getSlotRequestPv());
                }
                if (null != req.getSlotRequestUv()) {
                    slotData.setSlotRequestUv(req.getSlotRequestUv());
                }
                if (null != req.getNhCost()) {
                    slotData.setNhCost(req.getNhCost());
                }
                if (null != req.getOuterCost()) {
                    slotData.setOuterCost(req.getOuterCost());
                }
                if(Objects.nonNull(req.getAppSlotClickPv())){
                    slotData.setAppSlotClickPv(req.getAppSlotClickPv());
                }
                if(Objects.nonNull(req.getAppSlotClickUv())){
                    slotData.setAppSlotClickUv(req.getAppSlotClickUv());
                }
                if(Objects.nonNull(req.getAppSlotExposurePv())){
                    slotData.setAppSlotExposurePv(req.getAppSlotExposurePv());
                }
                if(Objects.nonNull(req.getAppSlotExposureUv())){
                    slotData.setAppSlotExposureUv(req.getAppSlotExposureUv());
                }
                slotData.setAppRevenue(calculateAppRevenue(slotData));
                // 白名单广告位，修改后可见
                if (SwitchStatusEnum.isSwitchOn(switchConfig.getAdjust())
                        || whitelistService.contains(WhitelistType.ADJUST_DATA_SLOT, slotData.getSlotId())) {
                    slotData.setIsVisible(VISIBLE.getStatus());
                }

                slotData.setOperateTime(new Date());
                int result = slotDataService.updateSlotData(slotData);
                if (result < 1) {
                    status.setRollbackOnly();
                    return false;
                }

                // 插入更新记录
                SlotDataEditHistory history = new SlotDataEditHistory();
                history.setCurDate(slotData.getCurDate());
                history.setSlotId(slotData.getSlotId());
                history.setBeforeEdit(JSON.toJSONString(beforeSnapshot));
                history.setAfterEdit(JSON.toJSONString(SlotDataSnapshot.create(slotDataService.selectSlotDataById(slotData.getId()))));
                history.setSource(req.getSource());
                history.setOperatorId(SecurityUtils.getLoginUser().getCrmAccountId());
                slotDataEditHistoryMapper.insert(history);

                // 更新媒体数据
                updateAppData(slotData.getAppId(), slotData.getCurDate());
                //如果月账单已生成，则更新
                if(Objects.nonNull(appMonthDataEntity)){
                    DateTime startDate = DateUtil.beginOfMonth(req.getCurDate());
                    DateTime endDate = DateUtil.endOfMonth(req.getCurDate());
                    slotMonthDataSyncJob.statisticsSlotMonthData(startDate,endDate, Lists.newArrayList(req.getSlotId()));
                    appMonthDataSyncJob.statisticsAppMonthData(startDate,endDate,Lists.newArrayList(appId));
                }
                return true;
            } catch (Exception e) {
                log.error("广告位媒体数据更新异常，req={}", JSON.toJSONString(req), e);
                status.setRollbackOnly();
                return false;
            }
        });

        if (!isTrue(success)) {
            throw new CustomException(ErrorCode.E108005);
        }
    }

    @Override
    public void updateSlotAppMonthData(SlotMonthDataUpdateReq req) {
        // 参数校验
        if (null == req.getSlotId() || null == req.getCurDate()) {
            throw new CustomException(ErrorCode.ARGS);
        }

        // 查询广告位
        Slot slot = slotMapper.selectSlotById(req.getSlotId());
        if (null == slot) {
            throw new CustomException(ErrorCode.E107002);
        }

        // 更新数据
        Boolean success = transactionTemplate.execute(status -> {
            try {
                // 查询广告位数据
                SlotData slotData = slotDataService.selectBySlotIdAndDate(req.getSlotId(), req.getCurDate());
                if (Objects.isNull(slotData)) {
                    slotData = new SlotData();
                    slotData.setCurDate(req.getCurDate());
                    slotData.setAccountId(slot.getAccountId());
                    slotData.setAppId(slot.getAppId());
                    slotData.setSlotId(req.getSlotId());
                    slotDataService.insertSlotData(slotData);
                    slotData = slotDataService.selectBySlotIdAndDate(req.getSlotId(), req.getCurDate());
                }

                // 生成快照
                SlotDataSnapshot beforeSnapshot = SlotDataSnapshot.create(slotData);

                // 更新广告位数据
                slotData.setSlotRequestPv(req.getSlotRequestPv());
                slotData.setSlotRequestUv(req.getSlotRequestUv());
                slotData.setNhCost(req.getNhCost());
                slotData.setOuterCost(req.getOuterCost());
                slotData.setAppRevenue(calculateAppRevenue(slotData));
                slotData.setIsVisible(VISIBLE.getStatus());
                slotData.setOperateTime(new Date());
                int result = slotDataService.updateSlotData(slotData);
                if (result < 1) {
                    status.setRollbackOnly();
                    return false;
                }

                // 插入更新记录
                SlotDataEditHistory history = new SlotDataEditHistory();
                history.setCurDate(slotData.getCurDate());
                history.setSlotId(slotData.getSlotId());
                history.setBeforeEdit(JSON.toJSONString(beforeSnapshot));
                history.setAfterEdit(JSON.toJSONString(SlotDataSnapshot.create(slotData)));
                history.setSource(4);
                history.setOperatorId(SecurityUtils.getLoginUser().getCrmAccountId());
                slotDataEditHistoryMapper.insert(history);

                // 更新媒体数据
                updateAppData(slotData.getAppId(), slotData.getCurDate());

                // 更新广告位月账单
                updateSlotMonthData(req.getSlotId(), req.getCurDate());

                // 更新媒体月账单
                updateAppMonthData(slotData.getAppId(), slotData.getCurDate());
                return true;
            } catch (Exception e) {
                log.error("广告位媒体数据更新异常，req={}", JSON.toJSONString(req), e);
                status.setRollbackOnly();
                return false;
            }
        });

        if (!isTrue(success)) {
            throw new CustomException(ErrorCode.E108005);
        }
    }

    @Override
    public void updateAppData(Long appId, Date curDate) {
        // 查询媒体信息
        App app = appService.selectAppById(appId);
        if (null == app) {
            return;
        }

        // 查询并计算该媒体的所有广告位数据之和
        SlotData param = new SlotData();
        param.setAppId(appId);
        param.setCurDate(curDate);
        param.setIsVisible(VISIBLE.getStatus());
        List<SlotData> slotDataList = slotDataService.selectSlotDataList(param);
        if (CollectionUtils.isEmpty(slotDataList)) {
            return;
        }

        Integer slotRequestPvSum = 0;
        Integer slotRequestUvSum = 0;
        Integer appSlotClickPvSum = 0;
        Integer appSlotClickUvSum = 0;
        Integer appSlotExposurePvSum = 0;
        Integer appSlotExposureUvSum = 0;
        Long nhCostSum = 0L;
        Long outerCostSum = 0L;
        Long appRevenueSum = 0L;
        for (SlotData data : slotDataList) {
            slotRequestPvSum += data.getSlotRequestPv();
            slotRequestUvSum += data.getSlotRequestUv();
            appSlotClickUvSum += data.getAppSlotClickUv();
            appSlotClickPvSum += data.getAppSlotClickPv();
            appSlotExposurePvSum += data.getAppSlotExposurePv();
            appSlotExposureUvSum += data.getAppSlotExposureUv();
            nhCostSum += data.getNhCost();
            outerCostSum += data.getOuterCost();
            appRevenueSum += data.getAppRevenue();
        }

        // 新增/更新媒体数据
        AppData appData = appDataService.selectByAppIdAndDate(appId, curDate);
        if (Objects.isNull(appData)) {
            appData = new AppData();
            appData.setCurDate(curDate);
            appData.setAccountId(app.getAccountId());
            appData.setAppId(appId);
            appDataService.insertAppData(appData);
        }
        AppData updateAppData = new AppData();
        updateAppData.setId(appData.getId());
        updateAppData.setSlotRequestPv(slotRequestPvSum);
        updateAppData.setSlotRequestUv(slotRequestUvSum);
        updateAppData.setAppSlotClickPv(appSlotClickPvSum);
        updateAppData.setAppSlotClickUv(appSlotClickUvSum);
        updateAppData.setAppSlotExposurePv(appSlotExposurePvSum);
        updateAppData.setAppSlotExposureUv(appSlotExposureUvSum);
        updateAppData.setNhCost(nhCostSum);
        updateAppData.setOuterCost(outerCostSum);
        updateAppData.setAppRevenue(appRevenueSum);
        appDataService.updateAppData(updateAppData);
    }

    @Override
    public void batchUpdate(List<AppDataUpdateBO> bos) {
        Set<Long> appIds = bos.stream().map(AppDataUpdateBO::getAppId).collect(Collectors.toSet());
        Set<String> datas = bos.stream().map(bo -> DateUtil.formatDate(bo.getCurDate())).collect(Collectors.toSet());

        List<SlotData> slotData = slotDataService.selectListByAppIdsAndDates(appIds, datas);
        Map<String, List<SlotData>> slotDataMap = slotData.stream().collect(Collectors.groupingBy(data -> data.getAppId() + "-" + DateUtil.formatDate(data.getCurDate())));

        List<AppData> updateList = bos.stream().map(bo -> {
            List<SlotData> dataList = slotDataMap.get(bo.getAppId() + "-" + DateUtil.formatDate(bo.getCurDate()));
            if (CollectionUtils.isEmpty(dataList)) {
                return null;
            }
            Integer appSlotClickPvSum = 0;
            Integer appSlotClickUvSum = 0;
            Integer appSlotExposurePvSum = 0;
            Integer appSlotExposureUvSum = 0;

            for (SlotData data : dataList) {
                appSlotClickUvSum += data.getAppSlotClickUv();
                appSlotClickPvSum += data.getAppSlotClickPv();
                appSlotExposurePvSum += data.getAppSlotExposurePv();
                appSlotExposureUvSum += data.getAppSlotExposureUv();
            }

            // 新增/更新媒体数据
            AppData updateAppData = new AppData();
            updateAppData.setAppId(bo.getAppId());
            updateAppData.setCurDate(bo.getCurDate());
            updateAppData.setAccountId(dataList.get(0).getAccountId());
            updateAppData.setAppSlotClickPv(appSlotClickPvSum);
            updateAppData.setAppSlotClickUv(appSlotClickUvSum);
            updateAppData.setAppSlotExposurePv(appSlotExposurePvSum);
            updateAppData.setAppSlotExposureUv(appSlotExposureUvSum);
            return updateAppData;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        appDataService.batchInsertOrUpdate(updateList);
    }

    /**
     * 更新广告位月账单
     *
     * @param slotId 广告位ID
     * @param curDate 日期
     */
    private void updateSlotMonthData(Long slotId, Date curDate) {
        // 查询广告位信息
        Slot slot = slotMapper.selectSlotById(slotId);
        if (null == slot) {
            return;
        }

        // 查询并计算广告位该月的所有数据之和
        SlotData param = new SlotData();
        param.setSlotId(slotId);
        param.setStartDate(DateUtil.beginOfMonth(curDate));
        param.setEndDate(DateUtil.endOfMonth(curDate));
        param.setIsVisible(VISIBLE.getStatus());
        List<SlotData> slotDataList = slotDataService.selectSlotDataList(param);
        Integer slotRequestPvSum = 0;
        Integer slotRequestUvSum = 0;
        Long appRevenueSum = 0L;
        for (SlotData data : slotDataList) {
            slotRequestPvSum += data.getSlotRequestPv();
            slotRequestUvSum += data.getSlotRequestUv();
            appRevenueSum += data.getAppRevenue();
        }

        // 新增/更新广告位月账单
        Integer month =  DateUtils.dateTimeMonth(curDate);
        SlotMonthDataEntity monthData = slotMonthDataService.selectBySlotIdAndMonth(slotId, month);
        if (null == monthData) {
            monthData = new SlotMonthDataEntity();
            monthData.setMonthDate(month);
            monthData.setAccountId(slot.getAccountId());
            monthData.setAppId(slot.getAppId());
            monthData.setSlotId(slotId);
            slotMonthDataService.insert(monthData);
        }
        SlotMonthDataEntity updateData = new SlotMonthDataEntity();
        updateData.setId(monthData.getId());
        updateData.setSlotRequestPv(slotRequestPvSum);
        updateData.setSlotRequestUv(slotRequestUvSum);
        updateData.setAppRevenue(appRevenueSum.intValue());
        slotMonthDataService.update(updateData);
    }

    /**
     * 更新广告位月账单
     *
     * @param appId 媒体ID
     * @param curDate 日期
     */
    private void updateAppMonthData(Long appId, Date curDate) {
        // 查询媒体信息
        App app = appService.selectAppById(appId);
        if (null == app) {
            return;
        }

        // 查询并计算媒体的该月所有广告位数据之和
        SlotData param = new SlotData();
        param.setAppId(appId);
        param.setStartDate(DateUtil.beginOfMonth(curDate));
        param.setEndDate(DateUtil.endOfMonth(curDate));
        param.setIsVisible(VISIBLE.getStatus());
        List<SlotData> slotDataList = slotDataService.selectSlotDataList(param);
        Integer slotRequestPvSum = 0;
        Integer slotRequestUvSum = 0;
        Long nhCostSum = 0L;
        Long outerCostSum = 0L;
        Long appRevenueSum = 0L;
        for (SlotData data : slotDataList) {
            slotRequestPvSum += data.getSlotRequestPv();
            slotRequestUvSum += data.getSlotRequestUv();
            nhCostSum += data.getNhCost();
            nhCostSum += data.getOuterCost();
            appRevenueSum += data.getAppRevenue();
        }

        // 新增/更新媒体月账单
        Integer month =  DateUtils.dateTimeMonth(curDate);
        AppMonthDataEntity monthData = appMonthDataService.selectByAppIdAndMonth(appId, month);
        if (null == monthData) {
            monthData = new AppMonthDataEntity();
            monthData.setMonthDate(month);
            monthData.setAccountId(app.getAccountId());
            monthData.setAppId(appId);
            appMonthDataService.insert(monthData);
        }
        AppMonthDataEntity updateData = new AppMonthDataEntity();
        updateData.setId(monthData.getId());
        updateData.setSlotRequestPv(slotRequestPvSum);
        updateData.setSlotRequestUv(slotRequestUvSum);
        updateData.setNhCost(nhCostSum);
        updateData.setOuterCost(outerCostSum);
        updateData.setAppRevenue(appRevenueSum.intValue());

        // 预付款媒体处理
        AccountRevenueEntity revenue = accountRevenueService.selectByAccountId(app.getAccountId());
        if (null != revenue && isPrepay(revenue.getPayType())) {
            updateData.setPayType(PREPAY.getType());
            // 计算预付款欠款金额=历史结算金额扣除后还欠的预付款金额-当前结算金额
            updateData.setPrepayAmount(Math.max(publisherManager.calculatePrepayDebtAmount(app.getAccountId(), revenue.getPrepayAmount()) - updateData.getAppRevenue(), 0L));
        } else {
            updateData.setPayType(POSTPAID.getType());
            updateData.setPrepayAmount(0L);
        }
        appMonthDataService.update(updateData);
    }

    /**
     * 计算媒体收益
     */
    private long calculateAppRevenue(SlotData slotData) {
        // 查询结算设置
        SlotChargeEntity chargeEntity = slotChargeService.selectBySlotIdAndDate(slotData.getSlotId(), slotData.getCurDate());
        // 计算媒体收益
        return slotChargeService.calculateAppRevenue(chargeEntity, slotData);
    }

    /**
     * 查询否则新增广告位数据
     *
     * @param slotId 广告位ID
     * @param curDate 日期
     * @return 广告位数据
     */
    private SlotData selectOrInsertSlotData(Long slotId, Date curDate) {
        SlotData slotData = slotDataService.selectBySlotIdAndDate(slotId, curDate);
        if (null != slotData) {
            return slotData;
        }

        Slot slot = slotMapper.selectSlotById(slotId);

        slotData = new SlotData();
        slotData.setCurDate(curDate);
        slotData.setAccountId(slot.getAccountId());
        slotData.setAppId(slot.getAppId());
        slotData.setSlotId(slotId);
        slotDataService.insertSlotData(slotData);
        return slotDataService.selectBySlotIdAndDate(slotId, curDate);
    }
}
