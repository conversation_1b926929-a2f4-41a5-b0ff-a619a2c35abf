package com.ruoyi.system.service.datasource;

import com.ruoyi.system.entity.datashow.AdvertHourData;

import java.util.List;

/**
 * 广告时段数据Service接口
 *
 * <AUTHOR>
 * @date 2021-10-14
 */
public interface AdvertHourDataService {

    /**
     * 查询广告时段数据
     *
     * @param param 查询条件
     * @return 广告时段数据
     */
    AdvertHourData selectBy(AdvertHourData param);

    /**
     * 查询广告时段数据列表
     *
     * @param param 查询条件
     * @return 广告时段数据集合
     */
    List<AdvertHourData> selectAdvertHourDataList(AdvertHourData param);

    /**
     * 查询广告时段数据列表
     *
     * @param param 查询条件
     * @return 广告时段数据集合
     */
    List<AdvertHourData> selectAdvertHourDataListGroupByDateHour(AdvertHourData param);

    /**
     * 查询广告时段数据汇总
     *
     * @param param 参数
     * @return 广告时段数据汇总
     */
    AdvertHourData selectStatisticAdvertHourData(AdvertHourData param);

    /**
     * 新增广告时段数据
     *
     * @param param 广告时段数据
     * @return 结果
     */
    int insertAdvertHourData(AdvertHourData param);

    /**
     * 修改广告时段数据
     *
     * @param param 广告时段数据
     * @return 结果
     */
    int updateAdvertHourData(AdvertHourData param);
}
