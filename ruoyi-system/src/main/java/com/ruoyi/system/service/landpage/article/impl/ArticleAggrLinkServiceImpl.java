package com.ruoyi.system.service.landpage.article.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.bo.landpage.article.ArticleAggrLinkListParamBo;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.landpage.article.ArticleAggrLinkService;
import com.ruoyi.system.entity.landpage.article.ArticleAggrLinkEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import com.ruoyi.system.mapper.landpage.article.ArticleAggrLinkMapper;

/**
 * 文章聚合链接表 Service
 *
 * <AUTHOR>
 * @date 2023-12-1 15:12:44
 */
@Service
public class ArticleAggrLinkServiceImpl implements ArticleAggrLinkService {

    @Autowired
    private ArticleAggrLinkMapper articleAggrLinkMapper;

    @Override
    public String generateKey() {
        Set<String> existKeys = selectTotalKey();
        String key = RandomUtil.randomString(6);
        while (existKeys.contains(key)) {
            key = RandomUtil.randomString(6);
        }
        return key;
    }

    @Override
    public List<ArticleAggrLinkEntity> selectList(ArticleAggrLinkListParamBo param) {
        return articleAggrLinkMapper.selectList(param);
    }

    @Override
    public Boolean insert(ArticleAggrLinkEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return articleAggrLinkMapper.insert(entity) > 0;
    }

    @Override
    public Boolean deleteById(Long id) {
        if (Objects.isNull(id)) {
            return false;
        }
        return articleAggrLinkMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean updateById(ArticleAggrLinkEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return articleAggrLinkMapper.updateById(entity) > 0;
    }

    @Override
    public ArticleAggrLinkEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return articleAggrLinkMapper.selectById(id);
    }

    @Override
    public ArticleAggrLinkEntity selectByKey(String key) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        return articleAggrLinkMapper.selectByKey(key);
    }

    @Override
    public Set<String> selectTotalKey() {
        List<String> keys = articleAggrLinkMapper.selectTotalKey();
        return null == keys ? Collections.emptySet() : new HashSet<>(keys);
    }

    @Override
    public boolean isNameExist(String name, Long advertiserId, Long id) {
        return null != articleAggrLinkMapper.existByName(name,advertiserId,id);
    }

    @Override
    public ArticleAggrLinkEntity selectTodayLinkByAdvertiserId(Long advertiserId) {
        Date startDate = DateUtil.beginOfDay(new Date());
        Date endDate = DateUtil.endOfDay(startDate);
        return articleAggrLinkMapper.selectTodayLinkByAdvertiserId(advertiserId,startDate,endDate);
    }
}
