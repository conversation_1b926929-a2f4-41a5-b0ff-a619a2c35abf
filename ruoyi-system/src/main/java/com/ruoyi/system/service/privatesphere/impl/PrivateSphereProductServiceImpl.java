package com.ruoyi.system.service.privatesphere.impl;

import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.privatesphere.PrivateSphereProductService;
import com.ruoyi.system.entity.privatesphere.PrivateSphereProductEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.ruoyi.system.mapper.privatesphere.PrivateSphereProductMapper;

/**
 * 私域产品表 Service
 *
 * <AUTHOR>
 * @date 2023-2-8 10:19:20
 */
@Service
public class PrivateSphereProductServiceImpl implements PrivateSphereProductService {
    @Autowired
    private PrivateSphereProductMapper privateSphereProductMapper;

    @Override
    public Boolean insert(PrivateSphereProductEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return privateSphereProductMapper.insert(entity) > 0;
    }

    @Override
    public Boolean deleteById(Long id) {
        if (Objects.isNull(id)) {
            return false;
        }
        return privateSphereProductMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean updateById(PrivateSphereProductEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return privateSphereProductMapper.updateById(entity) > 0;
    }

    @Override
    public PrivateSphereProductEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return privateSphereProductMapper.selectById(id);
    }

    @Override
    public PrivateSphereProductEntity selectByAccountIdAndName(Long accountId, String productName) {
        if(NumberUtils.isNullOrLteZero(accountId) || StringUtils.isBlank(productName)){
            return null;
        }
        return privateSphereProductMapper.selectByAccountIdAndName(accountId, productName);
    }

    @Override
    public List<PrivateSphereProductEntity> selectListByIds(List<Long> ids) {
        if(CollectionUtils.isEmpty(ids)){
            return Collections.emptyList();
        }
        return privateSphereProductMapper.selectListByIds(ids);
    }

    @Override
    public List<PrivateSphereProductEntity> selectListByAccountIds(List<Long> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)){
            return Collections.emptyList();
        }
        return privateSphereProductMapper.selectListByAccountIds(accountIds);
    }

    @Override
    public List<Long> selectProductIdsByName(String productName) {
        return privateSphereProductMapper.selectProductIdsByName(productName);
    }

    @Override
    public Map<Long, String> selectProductNameMapByIds(List<Long> ids) {
        List<PrivateSphereProductEntity> productEntities = selectListByIds(ids);
        return productEntities.stream().collect(Collectors.toMap(PrivateSphereProductEntity::getId, PrivateSphereProductEntity::getProductName));

    }

    @Override
    public List<PrivateSphereProductEntity> selectProductList() {
        return privateSphereProductMapper.selectProductList();
    }
}
