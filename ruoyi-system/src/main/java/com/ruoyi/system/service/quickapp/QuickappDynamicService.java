package com.ruoyi.system.service.quickapp;

import com.ruoyi.system.entity.quickapp.QuickappDynamicEntity;

import java.util.List;

/**
 * 快应用发布动态表 Service
 *
 * <AUTHOR>
 * @date 2022-8-8 10:58:02
 */
public interface QuickappDynamicService {
    /**
     * 新增记录
     */
    Boolean insert(QuickappDynamicEntity entity);

    /**
     * 根据id删除
     */
    Boolean deleteById(Long id);

    /**
     * 根据id更新
     */
    Boolean updateById(QuickappDynamicEntity entity);

    /**
     * 根据id获取
     */
    QuickappDynamicEntity selectById(Long id);

    /**
     * 查询列表
     * @return
     */
    List<QuickappDynamicEntity> selectList();
}
