package com.ruoyi.system.service.tagmanager.impl;

import com.ruoyi.common.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.tagmanager.TagManagerService;
import com.ruoyi.system.entity.tagmanager.TagManagerEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import com.ruoyi.system.mapper.tagmanager.TagManagerMapper;

/**
 * 媒体标签表 Service
 *
 * <AUTHOR>
 * @date 2022-9-23 10:52:10
 */
@Service
public class TagManagerServiceImpl implements TagManagerService {

    @Autowired
    private TagManagerMapper tagManagerMapper;

    @Override
    public Boolean insert(TagManagerEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return tagManagerMapper.insert(entity) > 0;
    }

    @Override
    public Boolean deleteById(Long id) {
        if (Objects.isNull(id)) {
            return false;
        }
        return tagManagerMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean updateById(TagManagerEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return tagManagerMapper.updateById(entity) > 0;
    }

    @Override
    public TagManagerEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return tagManagerMapper.selectById(id);
    }

    @Override
    public List<TagManagerEntity> selectAllTag() {
        return tagManagerMapper.selectAllTag();
    }

    @Override
    public List<TagManagerEntity> selectAllTagByType(Integer type) {
        return tagManagerMapper.selectAllTagByType(type);
    }

    @Override
    public Boolean deleteByIds(List<Long> ids) {
        if(CollectionUtils.isEmpty(ids)){
            return false;
        }
        return tagManagerMapper.deleteByIds(ids);
    }

    @Override
    public Boolean batchUpdate(List<TagManagerEntity> updateList) {
        if(CollectionUtils.isEmpty(updateList)){
            return false;
        }
        return tagManagerMapper.batchUpdate(updateList) > 0;
    }

    @Override
    public Boolean batchInsert(List<TagManagerEntity> insertList) {
        if(CollectionUtils.isEmpty(insertList)){
            return false;
        }
        return tagManagerMapper.batchInsert(insertList) > 0;
    }

    @Override
    public List<TagManagerEntity> selectByIds(List<Long> ids) {
        if(CollectionUtils.isEmpty(ids)){
            return Collections.emptyList();
        }
        return tagManagerMapper.selectByIds(ids);
    }

    @Override
    public List<Long> selectTagIdsByFirstTagName(Integer type, String tagName) {
        if(StringUtils.isBlank(tagName)){
            return Collections.emptyList();
        }
        return tagManagerMapper.selectTagIdsByFirstTagName(type, tagName);
    }

    @Override
    public List<Long> selectTagIdsBySecondTagNames(Integer type, List<String> tagNames) {
        if (null == type || CollectionUtils.isEmpty(tagNames)) {
            return Collections.emptyList();
        }
        return tagManagerMapper.selectTagIdsBySecondTagNames(type, tagNames);
    }

    @Override
    public List<Long> selectIdsByParentIds(List<Long> parentIds) {
        if(CollectionUtils.isEmpty(parentIds)){
            return Collections.emptyList();
        }
        return tagManagerMapper.selectIdsByParentIds(parentIds);
    }
}
