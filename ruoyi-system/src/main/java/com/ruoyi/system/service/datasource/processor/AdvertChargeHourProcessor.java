package com.ruoyi.system.service.datasource.processor;

import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.DataDimensionEnum;
import com.ruoyi.common.enums.InnerLogType;
import com.ruoyi.common.enums.advert.ConvType;
import com.ruoyi.common.utils.BizUtils;
import com.ruoyi.system.domain.datashow.DataStatContext;
import com.ruoyi.system.entity.datashow.AdvertChargeHourDataEntity;
import com.ruoyi.system.req.datashow.DataStatReq;
import com.ruoyi.system.service.datasource.AdvertChargeHourDataService;
import com.ruoyi.system.service.datasource.DataStatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

import static com.ruoyi.common.enums.DataDimensionEnum.ADVERT_CHARGE_HOUR;

/**
 * 广告计费类型维度时段数据处理器
 *
 * <AUTHOR>
 * @date 2022/10/21
 */
@Slf4j
@Service
public class AdvertChargeHourProcessor implements DataProcessor, InitializingBean {

    @Autowired
    private AdvertChargeHourDataService advertChargeHourDataService;

    @Autowired
    private RedisCache redisCache;

    @Override
    public DataDimensionEnum getDimension() {
        return ADVERT_CHARGE_HOUR;
    }

    @Override
    public boolean validate(DataStatContext context) {
        DataStatReq req = context.getReq();
        return null != req.getDate() && null != req.getHour() && null != req.getAdvertId() && null != req.getChargeType() && null != req.getConsumerId();
    }

    @Override
    public boolean process(DataStatContext context) {
        DataStatReq req = context.getReq();
        InnerLogType type = context.getType();
        int pv = 1;
        int uv = 0;

        // 计算uv
        switch (type) {
            case ADVERT_LAUNCH:
            case ADVERT_EXPOSURE:
            case ADVERT_CLICK:
            case ADVERT_BILLING:
            case LANDPAGE_EXPOSURE:
            case LANDPAGE_CLICK:
            case CONVERT_EVENT:
                String uvKey = EngineRedisKeyFactory.K012.join(type.getType(), req.getAdvertId(), req.getChargeType(), req.getDateStr(), req.getHour());
                uv = BizUtils.countUv(uvKey, String.valueOf(req.getConsumerId()), 1, TimeUnit.HOURS);
                break;
            default:
                break;
        }

        // 数据
        AdvertChargeHourDataEntity updateData = new AdvertChargeHourDataEntity();
        updateData.setId(getDataId(req));

        switch (type) {
            case ADVERT_LAUNCH:
                updateData.setAdLaunchPvAdd(pv);
                updateData.setAdLaunchUvAdd(uv);
                break;
            case ADVERT_EXPOSURE:
                updateData.setExposurePvAdd(pv);
                updateData.setExposureUvAdd(uv);
                break;
            case ADVERT_CLICK:
                updateData.setClickPvAdd(pv);
                updateData.setClickUvAdd(uv);
                break;
            case ADVERT_BILLING:
                updateData.setBillingClickPvAdd(pv);
                updateData.setBillingClickUvAdd(uv);
                updateData.setConsumeAdd(req.getUnitPrice());
                break;
            case LANDPAGE_EXPOSURE:
                updateData.setLpExposurePvAdd(pv);
                updateData.setLpExposureUvAdd(uv);
                break;
            case LANDPAGE_CLICK:
                updateData.setLpClickPvAdd(pv);
                updateData.setLpClickUvAdd(uv);
                break;
            case CONVERT_EVENT:
                if (ConvType.isPay(req.getConvType())) {
                    updateData.setPayAdd(pv);
                } else if (ConvType.isRegister(req.getConvType())) {
                    updateData.setRegisterAdd(pv);
                }
                break;
            default:
                break;
        }
        return advertChargeHourDataService.updateById(updateData) > 0;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        DataStatService.register(this);
    }

    /**
     * 通过缓存获取数据ID
     */
    private Long getDataId(DataStatReq req) {
        String key = EngineRedisKeyFactory.K022.join("AdvertChargeHourDataEntity", req.getDateStr(), req.getHour(), req.getAdvertId(), req.getChargeType());
        Long dataId = redisCache.getCacheObject(key);
        if (null != dataId) {
            return dataId;
        }

        // 查询并初始数据
        AdvertChargeHourDataEntity param = new AdvertChargeHourDataEntity();
        param.setCurDate(req.getDate());
        param.setCurHour(req.getHour());
        param.setAdvertId(req.getAdvertId());
        param.setChargeType(req.getChargeType());
        AdvertChargeHourDataEntity data = advertChargeHourDataService.selectBy(param);
        if (null == data) {
            data = new AdvertChargeHourDataEntity();
            data.setCurDate(req.getDate());
            data.setCurHour(req.getHour());
            data.setAdvertId(req.getAdvertId());
            data.setChargeType(req.getChargeType());
            advertChargeHourDataService.insert(data);
            data = advertChargeHourDataService.selectBy(param);
        }
        redisCache.setCacheObject(key, data.getId(), 1, TimeUnit.HOURS);
        return data.getId();
    }
}
