package com.ruoyi.system.service.landpage.article.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.core.redis.RedisLock;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.SpringEnvironmentUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.UrlUtils;
import com.ruoyi.system.service.landpage.article.ArticleApiService;
import com.ruoyi.system.util.DingRobotUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 文章接口实现
 *
 * <AUTHOR>
 * @date 2024-04-15
 */
@Slf4j
@Service
public class ArticleApiServiceImpl implements ArticleApiService {

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    /**
     * 查询文章实际阅读量
     */
    @Override
    public Integer getArticleRealRequestPv(String url) {
        if (!StrUtil.startWith(url, "https://mp.weixin.qq.com")) {
            return 0;
        }
        if (!SpringEnvironmentUtils.isProd()) {
            return 1;
        }
        // 参数处理
        if (url.contains("#")) {
            url = StrUtil.subPre(url, url.indexOf("#"));
        }
        if (url.contains("·")) {
            url = StrUtil.subPre(url, url.indexOf("·"));
        }
        // 缓存查询
        String redisKey = EngineRedisKeyFactory.K138.join(url);
        Integer read = redisCache.getCacheObject(redisKey);
        if (null != read && read > 0) {
            return read;
        }

        // 接口调用
        read = callCkApi(url);
        if (null == read || read < 1) {
            // 降级查询
            read = callJzlApiWithRetry(url);
        }
        if (null != read && read > 0) {
            redisCache.setCacheObject(redisKey, read, 2, TimeUnit.MINUTES);
        }
        return read;
    }

    /**
     * 调用文章阅读量接口(极致了数据，包含重试逻辑)
     */
    private Integer callJzlApiWithRetry(String url) {
        JSONObject result = callJzlApi(url);
        if (null == result) {
            throw new CustomException("文章阅读量接口调用异常");
        }
        // 失败重试
        if (Objects.equals(result.getInteger("code"), 106)) {
            ThreadUtil.sleep(2100);
            result = callJzlApi(url);
        }
        if (Objects.equals(result.getInteger("code"), -1)) {
            ThreadUtil.sleep(5500);
            result = callJzlApi(url);
        }
        if (Objects.equals(result.getInteger("code"), 20003)) {
            result = callJzlApi(UrlUtils.urlEncode(url));
        }
        // 返回结果
        if (!Objects.equals(result.getInteger("code"), 0)) {
            throw new CustomException("文章阅读量接口" + result.getString("msg"));
        }
        JSONObject data = result.getJSONObject("data");
        return data.getInteger("read");
    }

    /**
     * 调用文章阅读量接口(极致了数据)
     */
    private JSONObject callJzlApi(String url) {
        JSONObject param = new JSONObject();
        param.put("key", "JZLac0289e231771784");
        if (StrUtil.containsAny(url, "&", "?")) {
            param.put("url", UrlUtils.urlEncode(url));
        } else {
            param.put("url", url);
        }
        String resp = HttpUtil.createGet("https://www.jzl.com/fbmain/monitor/v3/read_zan")
                .body(param.toString())
                .execute().body();
        log.info("文章阅读量接口调用, url={}, resp={}", url, resp);
        return JSON.parseObject(resp);
    }

    /**
     * 极致了数据余额查询Api
     */
    public JSONObject callJzlRemainMoneyApi() {

        JSONObject body = new JSONObject();
        body.put("key", "JZLac0289e231771784");
        String resp = HttpUtil.post("https://www.dajiala.com/fbmain/monitor/v3/get_remain_money" , body.toString());
        log.info("极致了余额接口调用, resp={}",  resp);
        if ( StringUtils.isBlank(resp) ) {
            return null;
        }
        return JSON.parseObject(resp);
    }


    /**
     * 调用文章阅读量接口
     * {"code":0,"msg":"success","data":{"read":"4311"}}
     * {"code":1,"msg":"访问频繁,请等待3秒后查询","error":"10001"}
     */
    private Integer callCkApi(String url) {
        try {
            if (redisCache.hasKey(EngineRedisKeyFactory.K015.join("callCkApi"))) {
                return 0;
            }
            RedisLock lock = redisAtomicClient.getLock(EngineRedisKeyFactory.K136.toString(), 3, 12, 500);
            if (null == lock) {
                return 0;
            }
            String resp = HttpUtil.get("http://101.37.119.219:3000/read_num_2024_ck?url=" + url, 5000);
            log.info("文章阅读量接口调用-ck, url={}, resp={}", url, resp);
            JSONObject result = JSON.parseObject(resp);
            if (null == result) {
                return 0;
            }
            if (!Objects.equals(result.getInteger("code"), 0) && !Objects.equals(result.getString("error"), "10001")) {
                log.error("文章阅读量接口调用失败-ck, url={}, resp={}", url, resp);
                DingRobotUtil.sendText("https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=fec93c9a-7217-4863-9499-f3db4f7d4e04",
                        StrUtil.format("文章阅读量接口调用失败\n\nurl={}\nresp={}", url, resp));
                return 0;
            }
            JSONObject data = result.getJSONObject("data");
            Integer read = data.getInteger("read");
            if (null != read && read > 0) {
                redisAtomicClient.incrBy(EngineRedisKeyFactory.K137.join(DateUtil.today()), 1, 60, TimeUnit.DAYS);
            }
            return read;
        } catch (Exception e) {
            log.error("文章阅读量接口调用异常-ck, url={}", url, e);
        }
        return 0;
    }
}
