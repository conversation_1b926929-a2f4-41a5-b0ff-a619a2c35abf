package com.ruoyi.system.service.advertiser.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Sets;
import com.ruoyi.common.enums.common.SysConfigKeyEnum;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.entity.system.SysConfig;
import com.ruoyi.system.service.advertiser.AdvertiserTagService;
import com.ruoyi.system.service.system.ISysConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 广告主标签Service实现
 *
 * <AUTHOR>
 * @date 2022/02/21
 */
@Service
public class AdvertiserTagServiceImpl implements AdvertiserTagService {

    @Autowired
    private ISysConfigService sysConfigService;

    @Override
    public boolean add(Long advertiserId, String tag) {
        if (null == advertiserId || StringUtils.isBlank(tag)) {
            return false;
        }

        SysConfig config = sysConfigService.selectByKey(SysConfigKeyEnum.ADVERTISER_TAG.getKey());
        if (null == config) {
            config = new SysConfig();
            config.setConfigName(SysConfigKeyEnum.ADVERTISER_TAG.getDesc());
            config.setConfigKey(SysConfigKeyEnum.ADVERTISER_TAG.getKey());
            config.setConfigType("N");

            Map<Long, Set<String>> map = new HashMap<>();
            map.put(advertiserId, Sets.newHashSet(tag));
            config.setConfigValue(JSON.toJSONString(map));
            return sysConfigService.insertConfig(config) > 0;
        }

        Map<Long, Set<String>> map = JSON.parseObject(config.getConfigValue(), new TypeReference<Map<Long, Set<String>>>() {});
        if (null == map) {
            map = new HashMap<>();
        }
        if (!map.containsKey(advertiserId)) {
            map.put(advertiserId, new HashSet<>());
        }
        map.get(advertiserId).add(tag);
        config.setConfigValue(JSON.toJSONString(map));
        return sysConfigService.updateConfig(config) > 0;

    }

    @Override
    public boolean update(Long advertiserId, Set<String> tags) {
        if (null == advertiserId) {
            return false;
        }

        SysConfig config = sysConfigService.selectByKey(SysConfigKeyEnum.ADVERTISER_TAG.getKey());
        if (null == config) {
            config = new SysConfig();
            config.setConfigName(SysConfigKeyEnum.ADVERTISER_TAG.getDesc());
            config.setConfigKey(SysConfigKeyEnum.ADVERTISER_TAG.getKey());
            config.setConfigType("N");

            Map<Long, Set<String>> map = new HashMap<>();
            map.put(advertiserId, Sets.newHashSet(tags));
            config.setConfigValue(JSON.toJSONString(map));
            return sysConfigService.insertConfig(config) > 0;
        }

        Map<Long, Set<String>> map = JSON.parseObject(config.getConfigValue(), new TypeReference<Map<Long, Set<String>>>() {});
        if (null == map) {
            map = new HashMap<>();
        }
        map.put(advertiserId, Sets.newHashSet(tags));
        config.setConfigValue(JSON.toJSONString(map));
        return sysConfigService.updateConfig(config) > 0;
    }

    @Override
    public boolean remove(Long advertiserId, String tag) {
        if (null == advertiserId || StringUtils.isBlank(tag)) {
            return false;
        }

        SysConfig config = sysConfigService.selectByKey(SysConfigKeyEnum.ADVERTISER_TAG.getKey());
        if (null == config) {
            return true;
        }

        Map<Long, Set<String>> map = JSON.parseObject(config.getConfigValue(), new TypeReference<Map<Long, Set<String>>>() {});
        if (null == map || !map.containsKey(advertiserId)) {
            return true;
        }
        map.getOrDefault(advertiserId, new HashSet<>()).remove(tag);
        config.setConfigValue(JSON.toJSONString(map));
        return sysConfigService.updateConfig(config) > 0;

    }

    @Override
    public Map<Long, List<String>> getMap() {
        SysConfig config = sysConfigService.selectByKey(SysConfigKeyEnum.ADVERTISER_TAG.getKey());
        if (null == config) {
            return Collections.emptyMap();
        }
        return JSON.parseObject(config.getConfigValue(), new TypeReference<Map<Long, List<String>>>() {});
    }

    @Override
    public List<Long> getAdvertiserByTag(String tag) {
        Map<Long, List<String>> map = getMap();
        return map.entrySet().stream().filter(s -> CollUtil.contains(s.getValue(), tag)).map(Map.Entry::getKey).collect(Collectors.toList());
    }

    @Override
    public Set<String> get(Long advertiserId) {
        if (null == advertiserId) {
            return Collections.emptySet();
        }
        SysConfig config = sysConfigService.selectByKey(SysConfigKeyEnum.ADVERTISER_TAG.getKey());
        if (null == config) {
            return Collections.emptySet();
        }
        Map<Long, Set<String>> map = JSON.parseObject(config.getConfigValue(), new TypeReference<Map<Long, Set<String>>>() {});
        return null == map ? Collections.emptySet() : map.getOrDefault(advertiserId, Collections.emptySet());
    }

    @Override
    public boolean isExist(Long advertiserId, String... tags) {
        if (null == advertiserId || ArrayUtil.isEmpty(tags)) {
            return false;
        }
        Set<String> values = get(advertiserId);
        return null != values && CollUtil.containsAny(values, Arrays.asList(tags));
    }
}
