package com.ruoyi.system.service.landpage;

import com.ruoyi.system.bo.landpage.ExternalLandpageRecordBo;
import com.ruoyi.system.bo.landpage.ExternalLandpageRecordSelectBo;
import com.ruoyi.system.entity.landpage.ExternalLandpageRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 外部落地页单记录Service接口
 *
 * <AUTHOR>
 * @date 2021-10-09
 */
public interface ExternalLandpageRecordService {

    /**
     * 查询外部落地页单记录
     *
     * @param id 外部落地页单记录ID
     * @return 外部落地页单记录
     */
    ExternalLandpageRecord selectExternalLandpageRecordById(Long id);

    /**
     * 查询列表
     *
     * @param param 查询条件
     * @return 表单列表
     */
    List<ExternalLandpageRecordBo> selectList(ExternalLandpageRecordSelectBo param);

    /**
     * 查询广告主ID列表
     *
     * @return 广告主ID列表
     */
    List<Long> selectAdvertiserIds();

    /**
     * 查询代理商ID列表
     *
     * @return 代理商ID列表
     */
    List<Long> selectAgentIds();

    /**
     * 新增外部落地页单记录
     *
     * @param externalLandpageRecord 外部落地页单记录
     * @return 结果
     */
    int insertExternalLandpageRecord(ExternalLandpageRecord externalLandpageRecord);

    /**
     * 修改外部落地页单记录
     *
     * @param externalLandpageRecord 外部落地页单记录
     * @return 结果
     */
    int updateExternalLandpageRecord(ExternalLandpageRecord externalLandpageRecord);

    /**
     * 批量新增外部落地页表单
     *
     * @param entities 表单列表
     * @return 结果
     */
    int batchInsert(@Param("entities") List<ExternalLandpageRecord> entities);

    /**
     * 批量更新导出状态
     *
     * @param ids 表单ID列表
     * @return 是否更新成功
     */
    boolean batchUpdateExportStatus(List<Long> ids);

    /**
     * 查询外部表单编号
     *
     * @param externalNoList 外部表单编号列表
     * @return 存在的外部表单编号列表
     */
    Set<String> selectExternalNoSet(List<String> externalNoList);

    /**
     * 查询外部表单映射
     *
     * @param externalNoList 外部表单编号列表
     * @return 外部表单编号-表单映射
     */
    Map<String, ExternalLandpageRecord> selectMapByExternalNo(List<String> externalNoList);
}
