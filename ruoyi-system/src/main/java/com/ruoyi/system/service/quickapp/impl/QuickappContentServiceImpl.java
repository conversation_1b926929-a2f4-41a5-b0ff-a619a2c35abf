package com.ruoyi.system.service.quickapp.impl;

import org.springframework.stereotype.Service;
import com.ruoyi.system.service.quickapp.QuickappContentService;
import com.ruoyi.system.entity.quickapp.QuickappContentEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;

import com.ruoyi.system.mapper.quickapp.QuickappContentMapper;

/**
 * 快应用内容表 Service
 *
 * <AUTHOR>
 * @date 2022-4-1 17:07:39
 */
@Service
public class QuickappContentServiceImpl implements QuickappContentService {
    @Autowired
    private QuickappContentMapper quickappContentMapper;

    @Override
    public Boolean insert(QuickappContentEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return quickappContentMapper.insert(entity) > 0;
    }

    @Override
    public Boolean deleteById(Long id) {
        if (Objects.isNull(id)) {
            return false;
        }
        return quickappContentMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean updateById(QuickappContentEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return quickappContentMapper.updateById(entity) > 0;
    }

    @Override
    public QuickappContentEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return quickappContentMapper.selectById(id);
    }

    @Override
    public QuickappContentEntity selectByTitle(String title) {
        return quickappContentMapper.selectByTitle(title);
    }

    @Override
    public List<QuickappContentEntity> selectList() {
        return quickappContentMapper.selectList();
    }
}
