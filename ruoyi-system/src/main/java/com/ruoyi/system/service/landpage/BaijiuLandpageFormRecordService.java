package com.ruoyi.system.service.landpage;

import com.ruoyi.system.entity.datashow.BaijiuLandpageFormRecord;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 白酒落地页单记录Service接口
 *
 * <AUTHOR>
 * @date 2022-01-24
 */
public interface BaijiuLandpageFormRecordService {

    /**
     * 查询落地页单记录列表
     *
     * @param param 查询条件
     * @return 落地页单记录集合
     */
    List<BaijiuLandpageFormRecord> selectList(BaijiuLandpageFormRecord param);
    /**
     * 查询落地页单记录列表
     *
     * @return 落地页单记录集合
     */
    List<BaijiuLandpageFormRecord> selectListByAdvertiserIdAndDate(Date startDate,Date endDate,List<Long> advertId);

    /**
     * 统计广告位的成功落地页表单数量
     *
     * @param startDate 起始日期
     * @param endDate 结束日期
     * @param slotIds 广告位ID列表
     * @return 广告位ID_日期-成功落地页表单数量映射
     */
    Map<String, Integer> countByDateAndSlotId(Date startDate, Date endDate, List<Long> slotIds);

    /**
     * 统计广告位的成功落地页表单数量
     *
     * @param startDate 起始日期
     * @param endDate 结束日期
     * @param slotIds 广告位ID列表
     * @return 成功落地页表单数量
     */
    Integer sumByDateAndSlotId(Date startDate, Date endDate, List<Long> slotIds);

    /**
     * 统计广告位-广告维度的白酒落地页表单数量
     *
     * @param date 日期
     * @return 广告位ID_广告位-白酒落地页表单数量映射
     */
    Map<String, Integer> countBySlotIdAndAdvertId(Date date);

    /**
     * 更新商户订单号
     * @param id
     * @param tradeNo
     * @return
     */
    Boolean updateTradeNo(Long id,String tradeNo);

    /**
     * 更新支付状态
     * @param record
     * @return
     */
    Boolean updateTradeStatus(BaijiuLandpageFormRecord record);

    /**
     * 根据id查询
     * @param id
     * @return
     */
    BaijiuLandpageFormRecord selectById(Long id);

    /**
     * 根据订单号查询
     * @param tradeNo
     * @return
     */
    BaijiuLandpageFormRecord selectByTradeNo(String tradeNo);

    /**
     * 查询所有的广告ID列表
     *
     * @return 广告ID列表
     */
    List<Long> selectTotalAdvertIds();

    /**
     * 查询所有的媒体ID列表
     *
     * @return 广告ID列表
     */
    List<Long> selectTotalAppIds();

    /**
     * 查询所有的广告位ID列表
     *
     * @return 广告ID列表
     */
    List<Long> selectTotalSlotIds();
}
