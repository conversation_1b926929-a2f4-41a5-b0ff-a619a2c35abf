package com.ruoyi.system.service.datasource.impl;

import com.ruoyi.system.entity.datashow.AdvertHourData;
import com.ruoyi.system.mapper.datashow.AdvertHourDataMapper;
import com.ruoyi.system.service.datasource.AdvertHourDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 广告日时段数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-10-14
 */
@Service
public class AdvertHourDataServiceImpl implements AdvertHourDataService {

    @Autowired
    private AdvertHourDataMapper advertHourDataMapper;

    @Override
    public AdvertHourData selectBy(AdvertHourData param) {
        return advertHourDataMapper.selectBy(param);
    }

    /**
     * 查询广告时段数据列表
     *
     * @param param 查询条件
     * @return 广告时段数据
     */
    @Override
    public List<AdvertHourData> selectAdvertHourDataList(AdvertHourData param) {
        return advertHourDataMapper.selectAdvertHourDataList(param);
    }

    @Override
    public List<AdvertHourData> selectAdvertHourDataListGroupByDateHour(AdvertHourData param) {
        return advertHourDataMapper.selectAdvertHourDataListGroupByDateHour(param);
    }

    @Override
    public AdvertHourData selectStatisticAdvertHourData(AdvertHourData param) {
        return advertHourDataMapper.selectStatisticAdvertHourData(param);
    }

    /**
     * 新增广告时段数据
     *
     * @param param 广告时段数据
     * @return 结果
     */
    @Override
    public int insertAdvertHourData(AdvertHourData param) {
        if (null == param.getCurDate() || null == param.getAdvertId()) {
            return 0;
        }
        return advertHourDataMapper.insertAdvertHourData(param);
    }

    /**
     * 修改广告时段数据
     *
     * @param param 广告时段数据
     * @return 结果
     */
    @Override
    public int updateAdvertHourData(AdvertHourData param) {
        if (null == param.getId()) {
            return 0;
        }
        return advertHourDataMapper.updateAdvertHourData(param);
    }
}
