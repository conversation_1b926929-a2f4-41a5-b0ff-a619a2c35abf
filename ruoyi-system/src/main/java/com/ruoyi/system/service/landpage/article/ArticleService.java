package com.ruoyi.system.service.landpage.article;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.system.bo.landpage.article.ArticleCountBo;
import com.ruoyi.system.bo.landpage.article.ArticleListBo;
import com.ruoyi.system.bo.landpage.article.ArticleListParamBo;
import com.ruoyi.system.entity.landpage.article.ArticleEntity;
import com.ruoyi.system.bo.fc.FcArticleStatusCountBo;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 文章表 Service
 *
 * <AUTHOR>
 * @date 2023-12-1 15:13:01
 */
public interface ArticleService {

    /**
     * 查询列表
     */
    List<ArticleEntity> selectListByLinkId(Long linkId);

    /**
     * 查询列表
     */
    List<ArticleEntity> selectList(ArticleListParamBo param);

    /**
     * 查询列表
     */
    List<ArticleListBo> selectListWithData(ArticleListParamBo param);

    /**
     * 查询文章汇总数据
     */
    ArticleListBo selectStatisticData(ArticleListParamBo param);

    /**
     * 查询今日私域增加阅读量
     */
    Integer selectTodaySyIncrRequestPvSum(Long linkId);

    /**
     * 新增记录
     */
    Boolean insert(ArticleEntity entity);

    /**
     * 批量新增记录
     */
    Boolean batchInsert(List<ArticleEntity> entities);

    /**
     * 根据id删除
     */
    Boolean deleteById(Long id);

    /**
     * 根据id更新
     */
    Boolean updateById(ArticleEntity entity);

    /**
     * 更新文章真实阅读量
     *
     * @param articleId 文章ID
     * @param actualRequestPv 真实阅读量
     */
    void updateActualRequestPv(Long articleId, Integer actualRequestPv);

    /**
     * 根据id获取
     */
    ArticleEntity selectById(Long id);

    /**
     * 根据聚合链接查询文章
     *
     * @param linkId 聚合链接ID
     * @return 文章ID-文章信息映射
     */
    Map<Long, ArticleEntity> selectMapByLinkId(Long linkId);

    /**
     * 查询总权重
     *
     * @param linkId 链接
     */
    Integer getWeightSum(Long linkId);

    /**
     * 查询聚合链接对应的文章数量
     *
     * @param linkIds 聚合链接ID列表
     * @return 链接ID-文章数量映射
     */
    Map<Long, ArticleCountBo> countByLinkId(List<Long> linkIds);

    /**
     * 统计丰巢文章的审核数和同步数
     * @param linkIds
     * @return
     */
    Map<Long, FcArticleStatusCountBo> countFcStatusByLinkIds(List<Long> linkIds);

    /**
     * 查询无信息的文章
     */
    ArticleEntity selectArticleWithoutProfile();

    /**
     * 异步更新文章信息
     */
    void updateArticleProfileAsync(Long articleId, String articleUrl);

    /**
     * 查询当天文章数不为0的链接ID
     */
    List<Long> selectBgZeroTodayArticleLinkId();

    /**
     * 获取微信文章信息
     */
    JSONObject getArticleProfile(String url);


    /**
     * 根据聚合链接id获取文章集合
     * @param linkIds
     * @return
     */
    List<ArticleEntity> selectListByLinkIds(List<Long> linkIds);

    /**
     * 更新丰巢审核状态
     * @param articleId
     * @param checkStatus
     */
    void updateFcCheckStatus(Long articleId, Integer checkStatus, String refuseReason);

    /**
     * 更新丰巢同步状态
     * @param articleId 文章ID
     * @param fcSyncStatus 同步状态
     * @param syncFailReason 同步失败原因
     */
    void updateFcSyncStatus(Long articleId, Integer fcSyncStatus, String syncFailReason);

    /**
     * 通过聚合链接id查询是否有丰巢同步成功的文章
     * @param linkId 聚合链接ID
     * @return 是否存在丰巢同步成功的文章
     * <AUTHOR>
     */
    Boolean hasFcSyncSuccessArticleByLinkId(Long linkId);

    /**
     * 通过多个聚合链接id查询是否有丰巢同步成功的文章
     * @param linkIds 聚合链接ID列表
     * @return 是否存在丰巢同步成功的文章
     * <AUTHOR>
     */
    Boolean hasFcSyncSuccessArticleByLinkIds(List<Long> linkIds);

    /**
     * 根据时间范围查询文章列表
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 文章列表
     * <AUTHOR>
     */
    List<ArticleEntity> selectListByDateRange(Date startDate, Date endDate);
}
