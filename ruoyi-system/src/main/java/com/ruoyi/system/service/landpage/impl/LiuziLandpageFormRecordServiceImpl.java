package com.ruoyi.system.service.landpage.impl;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.system.entity.datashow.LiuziLandpageFormRecord;
import com.ruoyi.system.entity.landpage.LandpageFormCount;
import com.ruoyi.system.mapper.landpage.LiuziLandpageFormRecordMapper;
import com.ruoyi.system.service.landpage.LiuziLandpageFormRecordService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 留资落地页单记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-24
 */
@Service
public class LiuziLandpageFormRecordServiceImpl implements LiuziLandpageFormRecordService {

    @Autowired
    private LiuziLandpageFormRecordMapper liuziLandpageFormRecordMapper;

    @Override
    public int insertLandpageFormRecord(LiuziLandpageFormRecord landpageFormRecord) {
        return liuziLandpageFormRecordMapper.insertLandpageFormRecord(landpageFormRecord);
    }

    /**
     * 查询落地页单记录列表
     *
     * @param param 查询条件
     * @return 落地页单记录
     */
    @Override
    public List<LiuziLandpageFormRecord> selectList(LiuziLandpageFormRecord param) {
        return liuziLandpageFormRecordMapper.selectList(param);
    }

    @Override
    public Map<String, Integer> countByDateAndSlotId(Date startDate, Date endDate, List<Long> slotIds) {
        if (CollectionUtils.isEmpty(slotIds)) {
            return Collections.emptyMap();
        }
        LiuziLandpageFormRecord param = new LiuziLandpageFormRecord();
        if(Objects.nonNull(startDate)){
            param.setStartDate(DateUtil.beginOfDay(startDate));
        }
        if(Objects.nonNull(endDate)){
            param.setEndDate(DateUtil.endOfDay(endDate));
        }

        param.setSlotIds(slotIds);
        List<LandpageFormCount> countList = liuziLandpageFormRecordMapper.countByDateAndSlotId(param);
        if (CollectionUtils.isEmpty(countList)) {
            return Collections.emptyMap();
        }
        return countList.stream().collect(Collectors.toMap(
                s -> s.getSlotId() + "_" + DateUtil.formatDate(s.getAssignDate()), LandpageFormCount::getFormCount, (v1, v2) -> v2));
    }

    @Override
    public Map<String, Integer> countBySlotIdAndAdvertId(Date date) {
        if (null == date) {
            return Collections.emptyMap();
        }
        LiuziLandpageFormRecord param = new LiuziLandpageFormRecord();
        param.setStartDate(DateUtil.beginOfDay(date));
        param.setEndDate(DateUtil.endOfDay(date));
        List<LandpageFormCount> countList = liuziLandpageFormRecordMapper.countByDate(param);
        if (CollectionUtils.isEmpty(countList)) {
            return Collections.emptyMap();
        }
        return countList.stream().collect(Collectors.toMap(
                s -> s.getSlotId() + "_" + s.getAdvertId(), LandpageFormCount::getFormCount, (v1, v2) -> v2));
    }

    @Override
    public List<String> selectOrderIdsByOrderIds(List<String> orderIds) {
        if(CollectionUtils.isEmpty(orderIds)){
            return Collections.emptyList();
        }
        return liuziLandpageFormRecordMapper.selectOrderIdsByOrderIds(orderIds);
    }

    @Override
    public List<LiuziLandpageFormRecord> selectUnSendSmsList() {
        return liuziLandpageFormRecordMapper.selectUnSendSmsList();
    }

    @Override
    public boolean updateFriendStatus(String phone, Integer status) {
        if(StringUtils.isBlank(phone)){
            return false;
        }
        return liuziLandpageFormRecordMapper.updateFriendStatus(phone, status) > 0;
    }

    @Override
    public List<Long> selectTotalAdvertIds() {
        return liuziLandpageFormRecordMapper.selectTotalAdvertIds();
    }

    @Override
    public List<Long> selectTotalAppIds() {
        return liuziLandpageFormRecordMapper.selectTotalAppIds();
    }

    @Override
    public List<Long> selectTotalSlotIds() {
        return liuziLandpageFormRecordMapper.selectTotalSlotIds();
    }

    @Override
    public List<LiuziLandpageFormRecord> selectListByPhone(List<String> phones) {
        return liuziLandpageFormRecordMapper.selectListByPhone(phones);
    }

    @Override
    public int batchInsert(List<LiuziLandpageFormRecord> list) {
        if(CollectionUtils.isEmpty(list)){
            return 0;
        }
        return liuziLandpageFormRecordMapper.batchInsert(list);
    }
}
