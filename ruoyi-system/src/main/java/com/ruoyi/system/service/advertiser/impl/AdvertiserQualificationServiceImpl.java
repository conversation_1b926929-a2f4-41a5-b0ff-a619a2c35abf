package com.ruoyi.system.service.advertiser.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.ruoyi.common.enums.advertiser.QualificationAuditStatus;
import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.system.bo.advertiser.AdvertiserIndustryBo;
import com.ruoyi.system.req.advertiser.qualification.WisQualificationInfoReq;
import com.ruoyi.system.service.common.IndustryQualificationRequireService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.advertiser.AdvertiserQualificationService;
import com.ruoyi.system.entity.advertiser.AdvertiserQualificationEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.ruoyi.system.mapper.advertiser.AdvertiserQualificationMapper;
import org.springframework.transaction.support.TransactionTemplate;

import static cn.hutool.core.util.BooleanUtil.isTrue;

/**
 * 广告主资质信息 Service
 *
 * <AUTHOR>
 * @date 2022-4-28 17:11:16
 */
@Service
public class AdvertiserQualificationServiceImpl implements AdvertiserQualificationService {

    @Autowired
    private AdvertiserQualificationMapper advertiserQualificationMapper;

    @Autowired
    public TransactionTemplate transactionTemplate;

    @Autowired
    public IndustryQualificationRequireService industryQualificationRequireService;

    @Override
    public AdvertiserQualificationEntity selectById(Long id) {
        if (null == id) {
            return null;
        }
        return advertiserQualificationMapper.selectById(id);
    }

    @Override
    public AdvertiserQualificationEntity selectBy(Long accountId, Long industryId, String qualificationName) {
        return advertiserQualificationMapper.selectBy(accountId, industryId, qualificationName);
    }

    @Override
    public int updateById(AdvertiserQualificationEntity entity) {
        return advertiserQualificationMapper.updateById(entity);
    }

    @Override
    public boolean edit(Long accountId, Long industryId, List<WisQualificationInfoReq> qualificationList) {
        if (null == accountId || null == industryId || CollectionUtils.isEmpty(qualificationList)) {
            return false;
        }

        // 查询现有资质列表
        List<AdvertiserQualificationEntity> list = selectListByAccountIdAndIndustryId(accountId, industryId);

        // 需要删除的资质ID列表
        List<Long> existIds = ListUtils.mapToList(list, AdvertiserQualificationEntity::getId);
        // 需要更新的资质列表
        List<AdvertiserQualificationEntity> updateList = new ArrayList<>();
        // 需要新增的资质列表
        List<AdvertiserQualificationEntity> insertList = new ArrayList<>();

        Date now = new Date();
        qualificationList.forEach(obj -> {
            AdvertiserQualificationEntity entity = BeanUtil.copyProperties(obj, AdvertiserQualificationEntity.class);
            entity.setId(obj.getQualificationId());
            entity.setAccountId(accountId);
            entity.setIndustryId(NumberUtils.defaultLong(entity.getIndustryId(), industryId));
            entity.setQualificationImg(JSON.toJSONString(obj.getQualificationImgs()));
            entity.setApplicationTime(now);
            entity.setAuditStatus(0);
            entity.setQualificationType(NumberUtils.isNonNullAndGtZero(entity.getQualificationRequireId()) ? 1 : 2);
            entity.setQualificationRequireId(NumberUtils.defaultLong(entity.getQualificationRequireId()));
            if (NumberUtils.isNullOrLteZero(entity.getId())) {
                insertList.add(entity);
            } else {
                existIds.remove(entity.getId());
                updateList.add(entity);
            }
        });

        return isTrue(transactionTemplate.execute(status -> {
            batchInsert(insertList);
            batchUpdate(updateList);
            batchDelete(accountId, existIds);
            return true;
        }));
    }

    @Override
    public boolean batchInsert(List<AdvertiserQualificationEntity> list) {
        if(CollectionUtils.isEmpty(list)){
            return false;
        }
        return advertiserQualificationMapper.batchInsert(list) > 0;
    }

    @Override
    public boolean batchUpdate(List<AdvertiserQualificationEntity> list) {
        if(CollectionUtils.isEmpty(list)){
            return false;
        }
        return advertiserQualificationMapper.batchUpdate(list) > 0;
    }

    @Override
    public boolean batchDelete(Long accountId, List<Long> ids) {
        if(CollectionUtils.isEmpty(ids)){
            return false;
        }
        return advertiserQualificationMapper.batchDelete(accountId, ids) > 0;
    }

    @Override
    public List<Long> selectIdsByAccountId(Long accountId) {
        if (null == accountId) {
            return Collections.emptyList();
        }
        return advertiserQualificationMapper.selectIdsByAccountId(accountId);
    }

    @Override
    public List<AdvertiserQualificationEntity> selectListByAccountId(Long accountId) {
        if(NumberUtils.isNullOrLteZero(accountId)){
            return Collections.emptyList();
        }
        return advertiserQualificationMapper.selectListByAccountId(accountId);
    }

    @Override
    public List<AdvertiserQualificationEntity> selectListByAccountIdAndIndustryId(Long accountId, Long industryId) {
        if (null == accountId || null == industryId) {
            return Collections.emptyList();
        }
        return advertiserQualificationMapper.selectListByAccountIdAndIndustryId(accountId, industryId);
    }

    @Override
    public List<AdvertiserQualificationEntity> selectListByAccountIds(List<Long> accountIds) {
        if(CollectionUtils.isEmpty(accountIds)){
            return Collections.emptyList();
        }
        return advertiserQualificationMapper.selectListByAccountIds(accountIds);
    }

    @Override
    public Map<Long, List<AdvertiserQualificationEntity>> selectMapByAccountIds(List<Long> accountIds) {
        List<AdvertiserQualificationEntity> qualificationEntityList = selectListByAccountIds(accountIds);
        return qualificationEntityList.stream().collect(Collectors.groupingBy(AdvertiserQualificationEntity::getAccountId));
    }

    @Override
    public List<String> selectIndustryListByAccountId(Long accountId) {
        if (null == accountId) {
            return Collections.emptyList();
        }
        List<AdvertiserIndustryBo> list = advertiserQualificationMapper.selectIndustryListByAccountIds(Collections.singletonList(accountId));
        return list.stream().map(AdvertiserIndustryBo::getIndustryName).collect(Collectors.toList());
    }

    @Override
    public Map<Long, List<String>> selectIndustryMapByAccountIds(List<Long> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return Collections.emptyMap();
        }
        List<AdvertiserIndustryBo> list = advertiserQualificationMapper.selectIndustryListByAccountIds(accountIds);
        return list.stream().collect(Collectors.groupingBy(AdvertiserIndustryBo::getAdvertiserId, Collectors.mapping(AdvertiserIndustryBo::getIndustryName, Collectors.toList())));
    }

    @Override
    public List<AdvertiserIndustryBo> selectIndustryByAccountId(Long accountId) {
        return advertiserQualificationMapper.selectIndustryListByAccountIds(Collections.singletonList(accountId));
    }

    @Override
    public List<Long> selectIndustryIdsByAccountId(Long accountId) {
        return advertiserQualificationMapper.selectIndustryIdsByAccountId(accountId);
    }

    @Override
    public Map<Long, Boolean> industryQualificationRequireCheck(List<Long> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return Collections.emptyMap();
        }
        // 查询行业-必填资质
        Map<Long, List<Long>> mustRequireMap = industryQualificationRequireService.selectMustRequireIdMap();
        // 查询广告主资质
        List<AdvertiserQualificationEntity> list = selectListByAccountIds(accountIds);
        Map<Long, List<AdvertiserQualificationEntity>> qualificationMap = list.stream().collect(Collectors.groupingBy(AdvertiserQualificationEntity::getAccountId));
        // 必填资质校验
        Map<Long, Boolean> checkMap = new HashMap<>();
        for (Long accountId : accountIds) {
            checkMap.put(accountId, true);
            Map<Long, List<Long>> industryQualificationMap = qualificationMap.getOrDefault(accountId, Collections.emptyList()).stream().collect(Collectors.groupingBy(AdvertiserQualificationEntity::getIndustryId, Collectors.mapping(AdvertiserQualificationEntity::getQualificationRequireId, Collectors.toList())));
            industryQualificationMap.forEach((industryId, requireIds) -> {
                if (!CollUtil.containsAll(requireIds, mustRequireMap.get(industryId))) {
                    checkMap.put(accountId, false);
                }
            });
        }
        return checkMap;
    }

    @Override
    public Map<Long, Boolean> industryQualificationAuditCheck(List<Long> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return Collections.emptyMap();
        }
        Map<Long, Boolean> checkMap = new HashMap<>();
        List<AdvertiserQualificationEntity> list = selectListByAccountIds(accountIds);
        for (AdvertiserQualificationEntity qualification : list) {
            if (Objects.equals(qualification.getAuditStatus(), 1)) {
                if (!checkMap.containsKey(qualification.getAccountId())) {
                    checkMap.put(qualification.getAccountId(), true);
                }
            } else {
                checkMap.put(qualification.getAccountId(), false);
            }
        }
        return checkMap;
    }

    @Override
    public Map<Long, Integer> selectAdvertiserAuditStatusMap(List<Long> accountIds) {
        Map<Long, Integer> auditMap = new HashMap<>();
        List<AdvertiserQualificationEntity> list = advertiserQualificationMapper.selectListByAccountIds(accountIds);
        for (AdvertiserQualificationEntity qualification : list) {
            if (Objects.equals(qualification.getAuditStatus(), 2)) {
                auditMap.put(qualification.getAccountId(), 2);
            } else if (Objects.equals(qualification.getAuditStatus(), 1)) {
                if (!auditMap.containsKey(qualification.getAccountId())) {
                    auditMap.put(qualification.getAccountId(), 1);
                }
            } else {
                if (!Objects.equals(auditMap.get(qualification.getAccountId()), 2)) {
                    auditMap.put(qualification.getAccountId(), 0);
                }
            }
        }
        return auditMap;
    }

    @Override
    public boolean isIndustryCheckPass(Long advertiserId) {
        if (null == advertiserId) {
            return false;
        }
        // 查询行业-必填资质
        Map<Long, List<Long>> mustRequireMap = industryQualificationRequireService.selectMustRequireIdMap();
        // 查询广告主资质
        List<AdvertiserQualificationEntity> list = advertiserQualificationMapper.selectListByAccountId(advertiserId);
        // 必填资质校验
        Map<Long, List<Long>> industryQualificationMap = list.stream().collect(Collectors.groupingBy(AdvertiserQualificationEntity::getIndustryId, Collectors.mapping(AdvertiserQualificationEntity::getQualificationRequireId, Collectors.toList())));
        for (Map.Entry<Long, List<Long>> entry : industryQualificationMap.entrySet()) {
            if (!CollUtil.containsAll(entry.getValue(), mustRequireMap.get(entry.getKey()))) {
                return false;
            }
        }
        return true;
    }

    @Override
    public boolean isQualificationAuditPass(Long advertiserId) {
        if (null == advertiserId) {
            return false;
        }
        List<AdvertiserQualificationEntity> list = advertiserQualificationMapper.selectListByAccountId(advertiserId);
        for (AdvertiserQualificationEntity qualification : list) {
            if (!QualificationAuditStatus.isAuditApprove(qualification.getAuditStatus())) {
                return false;
            }
        }
        return true;
    }
}
