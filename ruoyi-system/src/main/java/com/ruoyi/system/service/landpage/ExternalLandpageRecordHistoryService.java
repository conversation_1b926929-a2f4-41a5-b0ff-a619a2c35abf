package com.ruoyi.system.service.landpage;

import com.ruoyi.system.entity.landpage.ExternalLandpageRecordHistoryEntity;
import com.ruoyi.system.vo.datashow.CrmExternalLandpageRecordHistoryVO;

import java.util.List;
import java.util.Map;

/**
 * 外部落地页表单修改历史 Service
 *
 * <AUTHOR>
 * @date 2023-4-11 15:38:42
 */
public interface ExternalLandpageRecordHistoryService {

    /**
     * 新增记录
     */
    Boolean insert(ExternalLandpageRecordHistoryEntity entity);

    /**
     * 根据id更新
     */
    Boolean updateById(ExternalLandpageRecordHistoryEntity entity);

    /**
     * 根据id获取
     */
    ExternalLandpageRecordHistoryEntity selectById(Long id);

    /**
     * 查询表单的修改历史
     *
     * @param originRecordIds 表单ID列表
     * @return 表单ID-修改历史映射
     */
    Map<Long, List<CrmExternalLandpageRecordHistoryVO>> selectMapByOriginRecordId(List<Long> originRecordIds);
}
