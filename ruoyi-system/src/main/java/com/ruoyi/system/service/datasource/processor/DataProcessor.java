package com.ruoyi.system.service.datasource.processor;

import com.ruoyi.common.enums.DataDimensionEnum;
import com.ruoyi.system.domain.datashow.DataStatContext;

/**
 * 数据处理器接口
 *
 * <AUTHOR>
 * @date 2021/9/29
 */
public interface DataProcessor {

    /**
     * 数据维度
     *
     * @return 数据维度枚举
     */
    DataDimensionEnum getDimension();

    /**
     * 参数校验
     *
     * @param context 上下文
     * @return true.校验通过,false.校验不通过
     */
    default boolean validate(DataStatContext context) {
        return true;
    }

    /**
     * 数据处理
     *
     * @param context 上下文
     * @return 是否处理成功
     */
    boolean process(DataStatContext context);
}
