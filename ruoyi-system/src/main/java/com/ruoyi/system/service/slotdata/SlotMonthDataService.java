package com.ruoyi.system.service.slotdata;

import cn.hutool.core.lang.Pair;
import com.ruoyi.system.entity.slotdata.SlotMonthDataEntity;
import com.ruoyi.system.vo.datashow.StatementInfoVO;

import java.util.List;
import java.util.Set;

/**
 * 广告位月账单数据表 Service
 *
 * <AUTHOR>
 * @date 2021-9-9 16:56:55
 */
public interface SlotMonthDataService {

    /**
     * 批量新增
     *
     * @param entities 广告位月数据
     * @return 结果
     */
    int batchInsertOrUpdate(List<SlotMonthDataEntity> entities);

    /**
     * 根据月账单id获取结算单详情
     *
     * @param appMonthDataId 媒体月账单id
     * @return 详情
     */
    StatementInfoVO selectStatementInfoByAppMonthDataId(Long appMonthDataId);

    /**
     * 根据广告位ID和月份查询广告位月账单是否生成
     *
     * @param slotId 广告ID
     * @param month 月份
     * @return 广告位月账单是否生成
     */
    int countBySlotIdAndMonth(Long slotId,Integer month);

    /**
     * 批量查询广告位ID和月份查询广告位月账单是否生成
     *
     * @param slotIds 广告位Id列表
     * @param months 月份列表
     * @return 已生成广告位月账单的广告位Id-月份集合
     */
    Set<Pair<Long, Integer>> existBySlotIdsAndMonths(List<Long> slotIds, List<Integer> months);

    /**
     * 根据广告位列表和日期查询
     *
     * @param slotIds 广告位ID列表
     * @param month 月份日期
     * @return 广告位月账单
     */
    List<SlotMonthDataEntity> selectBySlotIdsAndMonth(List<Long> slotIds,Integer month);

    /**
     * 根据广告位和日期查询广告位月账单
     *
     * @param slotId 广告位Id
     * @param month 年月
     * @return 广告位月账单
     */
    SlotMonthDataEntity selectBySlotIdAndMonth(Long slotId, Integer month);

    /**
     * 新增广告位月账单
     *
     * @param entity 广告位月账单
     * @return 结果
     */
    int insert(SlotMonthDataEntity entity);

    /**
     * 更新广告位月账单
     *
     * @param entity 广告位月账单
     * @return 结果
     */
    int update(SlotMonthDataEntity entity);
}
