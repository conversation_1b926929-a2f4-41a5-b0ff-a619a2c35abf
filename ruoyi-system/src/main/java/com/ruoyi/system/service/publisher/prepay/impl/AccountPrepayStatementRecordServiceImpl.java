package com.ruoyi.system.service.publisher.prepay.impl;

import com.ruoyi.system.bo.publisher.PrepayRecordStatementAmountBo;
import com.ruoyi.system.bo.publisher.PrepayStatementAmountBo;
import com.ruoyi.system.service.publisher.prepay.AccountPrepayStatementRecordService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import com.ruoyi.system.entity.account.finance.AccountPrepayStatementRecordEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.ruoyi.system.mapper.account.AccountPrepayStatementRecordMapper;

/**
 * 媒体预付款结算记录 Service
 *
 * <AUTHOR>
 * @date 2022-7-29 14:45:13
 */
@Service
public class AccountPrepayStatementRecordServiceImpl implements AccountPrepayStatementRecordService {

    @Autowired
    private AccountPrepayStatementRecordMapper accountPrepayStatementRecordMapper;

    @Override
    public boolean insert(AccountPrepayStatementRecordEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return accountPrepayStatementRecordMapper.insert(entity) > 0;
    }

    @Override
    public Boolean updateById(AccountPrepayStatementRecordEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return accountPrepayStatementRecordMapper.updateById(entity) > 0;
    }

    @Override
    public AccountPrepayStatementRecordEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return accountPrepayStatementRecordMapper.selectById(id);
    }

    @Override
    public Integer sumAmountByAccountId(Long accountId) {
        if (null == accountId) {
            return 0;
        }
        return accountPrepayStatementRecordMapper.sumAmountByAccountId(accountId);
    }

    @Override
    public Map<Long, Integer> sumAmountByAccountIds(List<Long> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return Collections.emptyMap();
        }
        List<PrepayStatementAmountBo> list = accountPrepayStatementRecordMapper.sumAmountByAccountIds(accountIds);
        return list.stream().filter(Objects::nonNull).collect(Collectors.toMap(PrepayStatementAmountBo::getAccountId, PrepayStatementAmountBo::getPrepayStatementAmount));
    }

    @Override
    public Map<Long, Integer> sumAmountGroupByPrepayRecordId(Long accountId) {
        if (null == accountId) {
            return Collections.emptyMap();
        }
        List<PrepayRecordStatementAmountBo> list = accountPrepayStatementRecordMapper.sumAmountGroupByPrepayRecordId(accountId);
        return list.stream().filter(e -> null != e && null != e.getPrepayRecordId() && null != e.getPrepayStatementAmount())
                .collect(Collectors.toMap(PrepayRecordStatementAmountBo::getPrepayRecordId, PrepayRecordStatementAmountBo::getPrepayStatementAmount));
    }
}
