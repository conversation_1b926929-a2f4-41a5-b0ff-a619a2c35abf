package com.ruoyi.system.service.landpage;

import com.alibaba.fastjson.JSONObject;

import java.util.List;

/**
 * 企微囤粉微信服务接口
 *
 * <AUTHOR>
 * @date 2023-9-25
 */
public interface QwtfWxService {

    /**
     * 获取微信公众号AccessToken
     * 注：getAccessToken 会导致前一次获取的 access_token 失效，故使用 getStableAccessToken 接口
     * 文档：https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/mp-access-token/getStableAccessToken.html
     *
     * @param appId 微信appId
     * @param secret 微信secret
     * @return accessToken
     */
    String getAccessToken(String appId, String secret);

    /**
     * 获取企微AccessToken
     * 文档：https://developer.work.weixin.qq.com/document/path/91039
     *
     * @param corpid 企微corpid
     * @param corpsecret 企微corpsecret
     * @return accessToken
     */
    String getCorpAccessToken(String corpid, String corpsecret);

    /**
     * 获取微信UnionId
     * 文档：https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/user-login/code2Session.html
     *
     * @param code 微信JS Code
     * @return 微信unionId
     */
    String getUnionId(String code);

    /**
     * 获取用户手机号
     *
     * @param code 微信Code
     * @return 手机号
     */
    String getUserPhoneNumber(String code);

    /**
     * 获取小程序短链
     * 详情 https://developers.weixin.qq.com/miniprogram/dev/api-backend/open-api/url-scheme/urlscheme.generate.html
     *
     * @param path 小程序路径
     * @param query 通过 scheme 码进入小程序时的 query，最大1024个字符，只支持数字，大小写英文以及部分特殊字符：`!#$&'()*+,/:;=?@-._~%`` ，参数拼接用key=value的方式，不能用json
     * @param expireInterval 到期失效的 scheme 码的失效间隔天数
     * @return 小程序短链
     */
    String getUrlScheme(String path, String query, Integer expireInterval);

    /**
     * 获取客户列表
     *
     * @param userid 企业成员userid
     * @return 外部联系人userid列表
     */
    List<String> getExternalContactList(String userid);

    /**
     * 获取客户unionId
     *
     * @param externalUserid 外部联系人userid
     * @return 外部联系人unionId
     */
    String getExternalContactUnionId(String externalUserid);

    /**
     * 获取客户详情
     *
     * @param externalUserid 外部联系人userid
     * @return 外部联系人详情
     */
    JSONObject getExternalContact(String externalUserid);
}
