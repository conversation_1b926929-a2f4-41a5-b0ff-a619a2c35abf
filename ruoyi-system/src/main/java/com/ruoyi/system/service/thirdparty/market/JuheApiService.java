package com.ruoyi.system.service.thirdparty.market;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpException;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.enums.CountMonitorTypeEnum;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.system.bo.thirdparty.IdCardAuditBo;
import com.ruoyi.system.util.CountMonitorUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.ruoyi.common.enums.IdCardAuditApiType.JUHE;

/**
 * 聚合接口
 *
 * <AUTHOR>
 * @date 2021/9/2
 */
@Slf4j
@Service
public class JuheApiService {

    // 身份证实名认证查询地址
    public static String IDCARD_AUTH_URL = "http://op.juhe.cn/idcard/queryMd5?key=";
    // IP解析查询地址
    public static String IP_ANALYSIS_URL = "http://apis.juhe.cn/ip/ipNewV3?key=";
    // 接口请求超时时间
    private static final int HTTP_TIMEOUT = 5000;

    // 聚合-身份证实名认证接口Key
    @Value("${juhe.key.idCardAuth}")
    private String idCardAuthKey;

    // 聚合-IP解析接口Key
    @Value("${juhe.key.ipAnalysis}")
    private String ipAnalysisKey;

    /**
     * 身份证实名认证
     *
     * @param realName 姓名
     * @param idCard 身份证
     * @return 调用结果
     */
    public IdCardAuditBo idCardAuditWrap(String realName, String idCard) {
        JSONObject authResult = idCardAudit(realName, idCard);
        if (null == authResult) {
            return null;
        }
        // 	服务商网络异常，请重试
        if (Objects.equals(authResult.getString("error_code"), "210305")) {
            CountMonitorUtils.errorMonitor(CountMonitorTypeEnum.JUHE_SFZ);
            log.error("身份证实名认证失败, reason:{}", authResult.getString("reason"));
            return null;
        }

        IdCardAuditBo result = new IdCardAuditBo();
        result.setApiType(JUHE.getType());

        if (Objects.equals(authResult.getString("error_code"), "210301")) {
            result.setCode(1);
            result.setMsg("查询无此记录");
        } else if (null != authResult.getJSONObject("result")) {
            Integer res = authResult.getJSONObject("result").getInteger("res");
            if (Objects.equals(res, 1)) {
                result.setCode(0);
                result.setMsg("匹配");
            } else if (Objects.equals(res, 2))  {
                result.setCode(1);
                result.setMsg("不匹配");
            }
        }
        return result;
    }

    /**
     * 身份证实名认证
     *
     * @param realName 姓名
     * @param idCard 身份证
     * @return 调用结果
     */
    public JSONObject idCardAudit(String realName, String idCard) {
        if (StringUtils.isBlank(realName) || StringUtils.isBlank(idCard)) {
            return null;
        }
        // 接口Key判断(只在生产环境配置有效的Key，省钱)
        if (StringUtils.isBlank(idCardAuthKey) || idCardAuthKey.length() < 32) {
            return null;
        }

        // 参数
        Map<String, Object> params = new HashMap<>();
        params.put("realname", Md5Utils.hash(realName));
        params.put("idcard", Md5Utils.hash(idCard.toUpperCase()));
        // 链接
        String url = IDCARD_AUTH_URL + idCardAuthKey + "&" + urlencode(params);
        // 请求
        try {
            String result = HttpUtil.get(url, HTTP_TIMEOUT);
            log.info("聚合-身份证实名认证, req={}, resp={}", JSON.toJSONString(params), result);
            return JSON.parseObject(result);
        } catch (Exception e) {
            log.error("聚合-身份证实名认证异常, req={}", JSON.toJSONString(params), e);
            CountMonitorUtils.errorMonitor(CountMonitorTypeEnum.JUHE_SFZ);
        }
        return null;
    }

    /**
     * IP解析
     *
     * @param ip IP地址
     * @return 解析结果
     */
    public JSONObject ipAnalysis(String ip) {
        if (StringUtils.isBlank(ip)) {
            return null;
        }

        try {
            // 接口Key判断
            if (StringUtils.isBlank(ipAnalysisKey) || ipAnalysisKey.length() < 32) {
                return null;
            }

            // 调用聚合接口解析IP
            String url = IP_ANALYSIS_URL + ipAnalysisKey + "&ip=" + ip;
            String result = HttpUtil.get(url, HTTP_TIMEOUT);
            if (!JSONUtil.isTypeJSONObject(result)) {
                log.error("IP解析异常, 非JSON格式, ip={}, result={}", ip, StrUtil.subPre(result, 100));
                return null;
            }
            return JSON.parseObject(result);
        } catch (HttpException e) {
            log.info("IP解析异常, ip={}", ip, e);
        } catch (Exception e) {
            log.error("IP解析异常, ip={}", ip, e);
        }
        return null;
    }

    // 将map型转为请求参数型
    public static String urlencode(Map<String, ?> data) {
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, ?> i : data.entrySet()) {
            try {
                sb.append(i.getKey()).append("=").append(URLEncoder.encode(i.getValue() + "", "UTF-8")).append("&");
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        }
        String result = sb.toString();
        result = result.substring(0, result.lastIndexOf("&"));
        return result;
    }
}
