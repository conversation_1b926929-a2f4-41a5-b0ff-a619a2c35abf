package com.ruoyi.system.service.advertiser.impl;

import com.ruoyi.common.enums.common.SwitchStatusEnum;
import com.ruoyi.system.service.engine.cache.RefreshCacheService;
import com.ruoyi.system.service.advert.AdvertService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.advertiser.AdvertiserOperateTimerService;
import com.ruoyi.system.entity.advertiser.AdvertiserOperateTimerEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.ruoyi.system.mapper.advertiser.AdvertiserOperateTimerMapper;
import org.springframework.transaction.annotation.Transactional;

/**
 * 广告主操作定时任务 Service
 *
 * <AUTHOR>
 * @date 2023-4-20 11:39:56
 */
@Service
public class AdvertiserOperateTimerServiceImpl implements AdvertiserOperateTimerService {

    @Autowired
    private AdvertiserOperateTimerMapper advertiserOperateTimerMapper;

    @Autowired
    private AdvertService advertService;

    @Autowired
    private RefreshCacheService refreshCacheService;

    @Override
    public int add(AdvertiserOperateTimerEntity entity) {
        if (Objects.isNull(entity)) {
            return 0;
        }
        // 检查是否已存在同广告主同类型未执行的定时任务
        AdvertiserOperateTimerEntity timer = advertiserOperateTimerMapper.selectPlanTimerBy(entity.getAdvertiserId(), entity.getOperType());
        if (null == timer) {
            // 新增定时任务
            return advertiserOperateTimerMapper.insert(entity);
        }
        // 更新定时任务
        AdvertiserOperateTimerEntity updateTimer = new AdvertiserOperateTimerEntity();
        updateTimer.setId(timer.getId());
        updateTimer.setPlanTime(entity.getPlanTime());
        updateTimer.setOperatorId(entity.getOperatorId());
        return advertiserOperateTimerMapper.updateById(updateTimer);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int execTimer(AdvertiserOperateTimerEntity entity) {
        // 更新定时任务执行时间
        int result = advertiserOperateTimerMapper.execTimer(entity.getId());
        if (result > 0 ) {
            // 更新广告主下的广告的投放开关
            int switchStatus = Objects.equals(entity.getOperType(), 1) ? SwitchStatusEnum.OFF.getStatus() : SwitchStatusEnum.ON.getStatus();
            List<Long> advertIds = advertService.selectAllOpenOrCloseAdvertNameByAdvertiserId(entity.getAdvertiserId(), SwitchStatusEnum.toggle(switchStatus));
            advertService.updateSwitchByIds(advertIds, switchStatus);
            refreshCacheService.sendRefreshAdvertCacheMsg(advertIds);
        }
        return result;
    }

    @Override
    public Boolean updateById(AdvertiserOperateTimerEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return advertiserOperateTimerMapper.updateById(entity) > 0;
    }

    @Override
    public AdvertiserOperateTimerEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return advertiserOperateTimerMapper.selectById(id);
    }

    @Override
    public Map<Long, List<AdvertiserOperateTimerEntity>> selectPlanMapByAdvertiserIds(List<Long> advertiserIds) {
        if (CollectionUtils.isEmpty(advertiserIds)) {
            return Collections.emptyMap();
        }
        List<AdvertiserOperateTimerEntity> list = advertiserOperateTimerMapper.selectPlanTimerByAdvertiserIds(advertiserIds);
        return list.stream().collect(Collectors.groupingBy(AdvertiserOperateTimerEntity::getAdvertiserId));
    }

    @Override
    public List<AdvertiserOperateTimerEntity> selectReadyTimer() {
        return advertiserOperateTimerMapper.selectReadyTimer();
    }
}
