package com.ruoyi.system.service.landpage;

import com.ruoyi.system.entity.datashow.LiuziLandpageFormRecord;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 留资落地页单记录Service接口
 *
 * <AUTHOR>
 * @date 2022-01-24
 */
public interface LiuziLandpageFormRecordService {

    /**
     * 新增落地页单记录
     *
     * @param landpageFormRecord 落地页单记录
     * @return 结果
     */
    int insertLandpageFormRecord(LiuziLandpageFormRecord landpageFormRecord);

    /**
     * 查询落地页单记录列表
     *
     * @param param 查询条件
     * @return 落地页单记录集合
     */
    List<LiuziLandpageFormRecord> selectList(LiuziLandpageFormRecord param);

    /**
     * 统计广告位的成功落地页表单数量
     *
     * @param startDate 起始日期
     * @param endDate   结束日期
     * @param slotIds   广告位ID列表
     * @return 广告位ID_日期-成功落地页表单数量映射
     */
    Map<String, Integer> countByDateAndSlotId(Date startDate, Date endDate, List<Long> slotIds);

    /**
     * 统计广告位-广告维度的留资落地页表单数量
     *
     * @param date 日期
     * @return 广告位ID_广告位-留资落地页表单数量映射
     */
    Map<String, Integer> countBySlotIdAndAdvertId(Date date);

    /**
     * 根据订单id列表查询订单id列表，主要用于查询当前订单是否已经记录
     *
     * @param orderIds
     * @return
     */
    List<String> selectOrderIdsByOrderIds(List<String> orderIds);

    /**
     * 查询未发送过短信的支付宝留资留资
     *
     * @return
     */
    List<LiuziLandpageFormRecord> selectUnSendSmsList();

    /**
     * 更新加好友状态
     *
     * @param phone
     * @param status
     * @return
     * @see com.ruoyi.common.enums.QwFriendStatusEnum
     */
    boolean updateFriendStatus(String phone, Integer status);

    /**
     * 查询所有的广告ID列表
     *
     * @return 广告ID列表
     */
    List<Long> selectTotalAdvertIds();

    /**
     * 查询所有的媒体ID列表
     *
     * @return 广告ID列表
     */
    List<Long> selectTotalAppIds();

    /**
     * 查询所有的广告位ID列表
     *
     * @return 广告ID列表
     */
    List<Long> selectTotalSlotIds();

    /**
     * 根据号码查询留资记录
     *
     * @param phones
     * @return
     */
    List<LiuziLandpageFormRecord> selectListByPhone(List<String> phones);

    /**
     * 批量新增
     *
     * @param list
     * @return
     */
    int batchInsert(List<LiuziLandpageFormRecord> list);
}
