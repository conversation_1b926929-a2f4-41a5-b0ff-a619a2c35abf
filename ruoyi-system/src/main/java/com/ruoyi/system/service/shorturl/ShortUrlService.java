package com.ruoyi.system.service.shorturl;

import com.ruoyi.system.entity.shorturl.ShortUrlEntity;

import java.util.List;

/**
 * 短链表 Service
 *
 * <AUTHOR>
 * @date 2022-10-8 14:16:35
 */
public interface ShortUrlService {

    /**
     * 新增记录
     */
    Boolean insert(ShortUrlEntity entity);

    /**
     * 根据id删除
     */
    Boolean deleteById(Long id);

    /**
     * 根据id更新
     */
    int updateById(ShortUrlEntity entity);

    /**
     * 根据id获取
     */
    ShortUrlEntity selectById(Long id);

    /**
     * 根据原链接MD5查询短链
     *
     * @param originUrlMd5 原链接MD5
     * @return 短链
     */
    ShortUrlEntity selectByOriginUrlMd5(String originUrlMd5);

    /**
     * 查询列表
     *
     * @param param 参数
     * @return 短链列表
     */
    List<ShortUrlEntity> selectList(ShortUrlEntity param);

    /**
     * 根据id列表查询
     * @param ids
     * @return
     */
    List<ShortUrlEntity> selectListByIds(List<Long> ids);

    /**
     * 根据原链接md5列表查询
     * @param originUrlMd5List
     * @return
     */
    List<ShortUrlEntity> selectByOriginUrlMd5List(List<String> originUrlMd5List);

    /**
     * 批量新增
     * @param shortUrlEntities
     * @return
     */
    Boolean insertBatch(List<ShortUrlEntity> shortUrlEntities);

    /**
     * 根据ID批量更新对应数据
     * @param shortUrlEntities
     * @return
     */
    Boolean updateBatchById(List<ShortUrlEntity> shortUrlEntities);

}