package com.ruoyi.system.service.qualification;

import com.github.pagehelper.PageInfo;
import com.ruoyi.system.entity.qualification.AccountQualificationEntity;
import com.ruoyi.system.req.qualification.QualificationListReq;
import com.ruoyi.system.req.qualification.QualificationReq;
import com.ruoyi.system.vo.qualification.QualificationInfoVO;
import com.ruoyi.system.vo.qualification.QualificationListVO;

import java.util.List;
import java.util.Map;

/**
 * 提现账号资质信息 Service
 *
 * <AUTHOR>
 * @date 2021-9-9 16:58:50
 */
public interface AccountQualificationService {

    /**
     * 根据id获取
     */
    AccountQualificationEntity selectById(Long id);

    /**
     * 新增编辑账号资质
     *
     * @param req 请求参数
     * @return 结果
     */
    boolean insertOrUpdate(QualificationReq req);

    /**
     * 根据账户id获取资质详情
     *
     * @param accountId 账户id
     * @return 资质详情
     */
    AccountQualificationEntity selectByAccountId(Long accountId);

    /**
     * 根据账号id获取资质信息
     *
     * @param accountId 账号id
     * @return 资质详情
     */
    QualificationInfoVO selectQualificationInfoByAccountId(Long accountId);

    /**
     * 根据账号id获取资质信息
     *
     * @param accountIds 账号ID列表
     * @return 资质详情
     */
    Map<Long, AccountQualificationEntity> selectMapByAccountIds(List<Long> accountIds);

    /**
     * 查询资质列表
     *
     * @param req 请求参数
     * @return 列表
     */
    PageInfo<QualificationListVO> selectQualificationList(QualificationListReq req);

    /**
     * 根据公司名称模糊查询账号id列表
     *
     * @param company 公司名称
     * @return 账号列表
     */
    List<Long> selectAccountIdsByCompanyName(String company);
}
