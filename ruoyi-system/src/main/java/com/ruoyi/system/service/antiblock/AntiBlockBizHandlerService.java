package com.ruoyi.system.service.antiblock;

import com.ruoyi.system.service.antiblock.handler.AntiBlockBizHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 防风控跳链业务处理器管理服务
 *
 * <AUTHOR>
 * @date 2025/07/29
 */
@Slf4j
@Service
public class AntiBlockBizHandlerService {

    /**
     * 业务处理器映射
     */
    private final Map<String, AntiBlockBizHandler> handlerMap = new HashMap<>();

    /**
     * 自动注入所有业务处理器
     */
    @Autowired
    private List<AntiBlockBizHandler> bizHandlers;

    /**
     * 初始化业务处理器映射
     */
    @PostConstruct
    public void initHandlers() {
        log.info("初始化防风控跳链业务处理器...");
        
        for (AntiBlockBizHandler handler : bizHandlers) {
            if (handler.isEnabled()) {
                handlerMap.put(handler.getBizCode(), handler);
                log.info("注册业务处理器: {} - {}", handler.getBizCode(), handler.getBizName());
            } else {
                log.info("跳过禁用的业务处理器: {} - {}", handler.getBizCode(), handler.getBizName());
            }
        }
        
        log.info("业务处理器初始化完成，共注册{}个处理器", handlerMap.size());
    }

    /**
     * 根据业务代码获取处理器
     *
     * @param bizCode 业务代码
     * @return 业务处理器
     */
    public AntiBlockBizHandler getHandler(String bizCode) {
        return handlerMap.get(bizCode);
    }

    /**
     * 检查业务代码是否有效
     *
     * @param bizCode 业务代码
     * @return 是否有效
     */
    public boolean isValidBizCode(String bizCode) {
        return handlerMap.containsKey(bizCode);
    }

    /**
     * 获取目标URL
     *
     * @param bizCode 业务代码
     * @param targetCode 目标代码
     * @param request HTTP请求对象
     * @return 目标URL
     */
    public String getTargetUrl(String bizCode, String targetCode, HttpServletRequest request) {
        AntiBlockBizHandler handler = getHandler(bizCode);
        if (handler == null) {
            log.warn("未找到业务处理器: {}", bizCode);
            return null;
        }

        try {
            String targetUrl = handler.getTargetUrl(targetCode, request);
            log.debug("业务{}获取目标URL: targetCode={}, targetUrl={}", bizCode, targetCode, targetUrl);
            return targetUrl;
        } catch (Exception e) {
            log.error("业务{}获取目标URL失败: targetCode={}", bizCode, targetCode, e);
            return null;
        }
    }

    /**
     * 检查业务是否启用加密
     *
     * @param bizCode 业务代码
     * @return 是否启用加密
     */
    public boolean isEncryptEnabled(String bizCode) {
        AntiBlockBizHandler handler = getHandler(bizCode);
        return handler != null && handler.isEncryptEnabled();
    }

    /**
     * 获取所有启用的业务代码
     *
     * @return 业务代码列表
     */
    public List<String> getAllEnabledBizCodes() {
        return handlerMap.keySet().stream()
                .sorted()
                .collect(Collectors.toList());
    }

    /**
     * 获取所有启用的业务处理器
     *
     * @return 业务处理器列表
     */
    public List<AntiBlockBizHandler> getAllEnabledHandlers() {
        return handlerMap.values().stream()
                .sorted((h1, h2) -> h1.getBizCode().compareTo(h2.getBizCode()))
                .collect(Collectors.toList());
    }

    /**
     * 获取业务信息
     *
     * @param bizCode 业务代码
     * @return 业务信息
     */
    public Map<String, Object> getBizInfo(String bizCode) {
        AntiBlockBizHandler handler = getHandler(bizCode);
        if (handler == null) {
            return null;
        }

        Map<String, Object> info = new HashMap<>();
        info.put("bizCode", handler.getBizCode());
        info.put("bizName", handler.getBizName());
        info.put("description", handler.getDescription());
        info.put("encryptEnabled", handler.isEncryptEnabled());
        info.put("enabled", handler.isEnabled());
        
        return info;
    }

    /**
     * 获取所有业务信息
     *
     * @return 业务信息列表
     */
    public List<Map<String, Object>> getAllBizInfo() {
        return getAllEnabledHandlers().stream()
                .map(handler -> getBizInfo(handler.getBizCode()))
                .collect(Collectors.toList());
    }

    /**
     * 重新加载业务处理器
     */
    public void reloadHandlers() {
        log.info("重新加载业务处理器...");
        handlerMap.clear();
        initHandlers();
    }
}
