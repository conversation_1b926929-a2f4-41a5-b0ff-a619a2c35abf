package com.ruoyi.system.service.datasource.impl;

import com.alibaba.fastjson.JSON;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.system.entity.datashow.*;
import com.ruoyi.system.req.datashow.DataUpdateReq;
import com.ruoyi.system.service.datasource.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * 数据更新接口实现
 *
 * <AUTHOR>
 * @date 2021/7/20
 */
@Slf4j
@Service
public class DataUpdateServiceImpl implements DataUpdateService {

    @Autowired
    private SlotDataService slotDataService;

    @Autowired
    private SlotHourDataService slotHourDataService;

    @Autowired
    private SlotActivityDataService slotActivityDataService;

    @Autowired
    private SlotActivityHourDataService slotActivityHourDataService;

    @Autowired
    private AppActivityDataService appActivityDataService;

    @Autowired
    private RedisCache redisCache;

    @Override
    public void updateSlotRequestPvUv(DataUpdateReq req) {
        // 更新广告位数据
        SlotData updateSlotData = new SlotData();
        updateSlotData.setId(getSlotDataId(req));
        updateSlotData.setSlotRequestPvAdd(req.getPv());
        updateSlotData.setSlotRequestUvAdd(req.getSlotUv());
        slotDataService.updateSlotData(updateSlotData);

        // 更新广告位分时段数据
        SlotHourData updateSlotHourData = new SlotHourData();
        updateSlotHourData.setId(getSlotHourDataId(req));
        updateSlotHourData.setSlotRequestPvAdd(req.getPv());
        updateSlotHourData.setSlotRequestUvAdd(req.getHourSlotUv());
        slotHourDataService.updateSlotHourData(updateSlotHourData);
    }

    @Override
    public void update(DataUpdateReq req) {
        int pv = req.getPv();

        // 更新广告位活动数据
        SlotActivityData updateSlotData = new SlotActivityData();
        updateSlotData.setId(getSlotActivityDataId(req));

        // 更新广告位活动分时段数据
        SlotActivityHourData updateSlotHourData = new SlotActivityHourData();
        updateSlotHourData.setId(getSlotActivityHourDataId(req));

        // 更新媒体活动数据
        AppActivityData updateAppData = new AppActivityData();
        updateAppData.setId(getAppActivityDataId(req));

        switch (req.getType()) {
            case ACTIVITY_REQUEST:
                updateSlotData.setActivityRequestPvAdd(pv);
                updateSlotData.setActivityRequestUvAdd(req.getSlotUv());
                updateSlotHourData.setActivityRequestPvAdd(pv);
                updateSlotHourData.setActivityRequestUvAdd(req.getHourSlotUv());
                updateAppData.setActivityRequestPvAdd(pv);
                updateAppData.setActivityRequestUvAdd(req.getAppUv());
                break;
            case ACTIVITY_JOIN:
                updateSlotData.setJoinPvAdd(pv);
                updateSlotData.setJoinUvAdd(req.getSlotUv());
                updateSlotHourData.setJoinPvAdd(pv);
                updateSlotHourData.setJoinUvAdd(req.getHourSlotUv());
                updateAppData.setJoinPvAdd(pv);
                updateAppData.setJoinUvAdd(req.getAppUv());
                break;
            case ADVERT_REQUEST:
                updateSlotData.setAdRequestAdd(pv);
                updateSlotHourData.setAdRequestAdd(pv);
                updateAppData.setAdRequestAdd(pv);
                break;
            case ADVERT_LAUNCH:
                updateSlotData.setAdLaunchAdd(pv);
                updateSlotHourData.setAdLaunchAdd(pv);
                updateAppData.setAdLaunchAdd(pv);
                break;
            case ADVERT_EXPOSURE:
                updateSlotData.setAdExposurePvAdd(pv);
                updateSlotData.setAdExposureUvAdd(req.getSlotUv());
                updateSlotHourData.setAdExposurePvAdd(pv);
                updateSlotHourData.setAdExposureUvAdd(req.getHourSlotUv());
                updateAppData.setAdExposurePvAdd(pv);
                updateAppData.setAdExposureUvAdd(req.getAppUv());
                break;
            case ADVERT_CLICK:
                updateSlotData.setAdClickPvAdd(pv);
                updateSlotData.setAdClickUvAdd(req.getSlotUv());
                updateSlotHourData.setAdClickPvAdd(pv);
                updateSlotHourData.setAdClickUvAdd(req.getHourSlotUv());
                updateAppData.setAdClickPvAdd(pv);
                updateAppData.setAdClickUvAdd(req.getAppUv());
                break;
            default:
                return;
        }

        // 执行更新
        slotActivityDataService.updateSlotActivityData(updateSlotData);
        slotActivityHourDataService.updateSlotActivityHourData(updateSlotHourData);
        appActivityDataService.updateAppActivityData(updateAppData);
    }

    /**
     * 通过缓存获取广告位数据ID
     */
    private Long getSlotDataId(DataUpdateReq req) {
        String key = EngineRedisKeyFactory.K022.join("SlotData", req.getDateStr(), req.getSlotId());
        Long dataId = redisCache.getCacheObject(key);
        if (null != dataId) {
            return dataId;
        }

        // 查询并初始数据
        SlotData data = slotDataService.selectBySlotIdAndDate(req.getSlotId(), req.getDate());
        if (null == data) {
            data = new SlotData();
            data.setCurDate(req.getDate());
            data.setAccountId(req.getAccountId());
            data.setAppId(req.getAppId());
            data.setSlotId(req.getSlotId());
            slotDataService.insertSlotData(data);
            data = slotDataService.selectBySlotIdAndDate(req.getSlotId(), req.getDate());
        }
        redisCache.setCacheObject(key, data.getId(), 1, TimeUnit.DAYS);
        return data.getId();
    }

    /**
     * 通过缓存获取广告位时段数据ID
     */
    private Long getSlotHourDataId(DataUpdateReq req) {
        String key = EngineRedisKeyFactory.K022.join("SlotHourData", req.getDateStr(), req.getHour(), req.getSlotId());
        Long dataId = redisCache.getCacheObject(key);
        if (null != dataId) {
            return dataId;
        }

        // 查询并初始数据
        SlotHourData data = slotHourDataService.selectBySlotIdAndDateHour(req.getSlotId(), req.getDate(), req.getHour());
        if (null == data) {
            data = new SlotHourData();
            data.setCurDate(req.getDate());
            data.setCurHour(req.getHour());
            data.setAccountId(req.getAccountId());
            data.setAppId(req.getAppId());
            data.setSlotId(req.getSlotId());
            slotHourDataService.insertSlotHourData(data);
            data = slotHourDataService.selectBySlotIdAndDateHour(req.getSlotId(), req.getDate(), req.getHour());
        }
        redisCache.setCacheObject(key, data.getId(), 1, TimeUnit.HOURS);
        return data.getId();
    }

    /**
     * 通过缓存获取广告位活动数据ID
     */
    private Long getSlotActivityDataId(DataUpdateReq req) {
        if (null == req.getActivityId()) {
            log.error("SlotActivityData 数据更新异常, activityId为null, req={}", JSON.toJSONString(req));
            return null;
        }

        String key = EngineRedisKeyFactory.K022.join("SlotActivityData", req.getDateStr(), req.getSlotId(), req.getActivityId());
        Long dataId = redisCache.getCacheObject(key);
        if (null != dataId) {
            return dataId;
        }

        // 查询并初始数据
        Date date = req.getDate();
        Long appId = req.getAppId();
        Long slotId = req.getSlotId();
        Long accountId = req.getAccountId();
        Long activityId = req.getActivityId();
        SlotActivityData data = slotActivityDataService.selectBy(slotId, activityId, date);
        if (null == data && null != accountId && null != activityId && null != slotId && null != appId) {
            data = new SlotActivityData();
            data.setCurDate(date);
            data.setAccountId(accountId);
            data.setAppId(appId);
            data.setSlotId(slotId);
            data.setActivityId(activityId);
            slotActivityDataService.insertSlotActivityData(data);
            data = slotActivityDataService.selectBy(slotId, activityId, date);
        }
        if (null == data) {
            return null;
        }
        redisCache.setCacheObject(key, data.getId(), 1, TimeUnit.DAYS);
        return data.getId();
    }

    /**
     * 通过缓存获取广告位活动时段数据ID
     */
    private Long getSlotActivityHourDataId(DataUpdateReq req) {
        if (null == req.getActivityId()) {
            log.error("SlotActivityHourData 数据更新异常, activityId为null, req={}", JSON.toJSONString(req));
            return null;
        }

        String key = EngineRedisKeyFactory.K022.join("SlotActivityHourData", req.getDateStr(), req.getHour(), req.getSlotId(), req.getActivityId());
        Long dataId = redisCache.getCacheObject(key);
        if (null != dataId) {
            return dataId;
        }

        // 查询并初始数据
        Date date = req.getDate();
        Integer hour = req.getHour();
        Long appId = req.getAppId();
        Long slotId = req.getSlotId();
        Long accountId = req.getAccountId();
        Long activityId = req.getActivityId();
        SlotActivityHourData data = slotActivityHourDataService.selectBy(slotId, activityId, date, hour);
        if (null == activityId) {
            log.error("SlotActivityHourData 数据更新异常, activityId为null, req={}", JSON.toJSONString(req));
        }
        if (null == data && null != accountId && null != activityId && null != slotId && null != appId) {
            data = new SlotActivityHourData();
            data.setCurDate(date);
            data.setCurHour(hour);
            data.setAccountId(accountId);
            data.setAppId(appId);
            data.setSlotId(slotId);
            data.setActivityId(activityId);
            slotActivityHourDataService.insertSlotActivityHourData(data);
            data = slotActivityHourDataService.selectBy(slotId, activityId, date, hour);
        }
        if (null == data) {
            return null;
        }
        redisCache.setCacheObject(key, data.getId(), 1, TimeUnit.HOURS);
        return data.getId();
    }

    /**
     * 通过缓存获取媒体活动数据ID
     */
    private Long getAppActivityDataId(DataUpdateReq req) {
        if (null == req.getActivityId()) {
            log.error("AppActivityData 数据更新异常, activityId为null, req={}", JSON.toJSONString(req));
            return null;
        }

        String key = EngineRedisKeyFactory.K022.join("AppActivityData", req.getDateStr(), req.getAppId(), req.getActivityId());
        Long dataId = redisCache.getCacheObject(key);
        if (null != dataId) {
            return dataId;
        }

        // 查询并初始数据
        Date date = req.getDate();
        Long appId = req.getAppId();
        Long accountId = req.getAccountId();
        Long activityId = req.getActivityId();
        AppActivityData data = appActivityDataService.selectBy(appId, activityId, date);
        if (null == data && null != accountId && null != activityId && null != appId) {
            data = new AppActivityData();
            data.setCurDate(date);
            data.setAccountId(accountId);
            data.setAppId(appId);
            data.setActivityId(activityId);
            appActivityDataService.insertAppActivityData(data);
            data = appActivityDataService.selectBy(appId, activityId, date);
        }
        if (null == data) {
            return null;
        }
        redisCache.setCacheObject(key, data.getId(), 1, TimeUnit.DAYS);
        return data.getId();
    }
}
