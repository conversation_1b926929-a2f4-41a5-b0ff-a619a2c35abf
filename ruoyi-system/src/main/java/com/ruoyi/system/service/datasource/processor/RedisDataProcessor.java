package com.ruoyi.system.service.datasource.processor;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.DataDimensionEnum;
import com.ruoyi.common.enums.InnerLogType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.datashow.DataStatContext;
import com.ruoyi.system.req.datashow.DataStatReq;
import com.ruoyi.system.service.datasource.DataStatService;
import com.ruoyi.system.service.engine.CallbackService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

import static com.ruoyi.common.enums.DataDimensionEnum.REDIS_DATA;

/**
 * Redis缓存数据处理器
 *
 * <AUTHOR>
 * @date 2023/07/25
 */
@Slf4j
@Service
public class RedisDataProcessor implements DataProcessor, InitializingBean {

    @Autowired
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private CallbackService callbackService;

    @Autowired
    private RedisCache redisCache;

    @Override
    public DataDimensionEnum getDimension() {
        return REDIS_DATA;
    }

    @Override
    public boolean validate(DataStatContext context) {
        DataStatReq req = context.getReq();
        return null != req.getDate() && null != req.getAdvertId() && null != req.getSlotId();
    }

    @Override
    public boolean process(DataStatContext context) {
        DataStatReq req = context.getReq();
        InnerLogType type = context.getType();
        String dateStr = DateUtil.formatDate(req.getDate());

        // 计算uv
        switch (type) {
            case ADVERT_BILLING:
                String hu = StringUtils.defaultString(callbackService.getParameterFromCache(req.getOrderId()).getString("hu"));
                if (null != req.getAdvertId() && null != req.getSlotId() && null != req.getUnitPrice()) {
                    redisAtomicClient.incrBy(EngineRedisKeyFactory.K085.join(dateStr, req.getSlotId(), req.getAdvertId(), hu), req.getUnitPrice(), 1, TimeUnit.DAYS);
                    redisAtomicClient.incrBy(EngineRedisKeyFactory.K087.join(dateStr, req.getSlotId(), hu), req.getUnitPrice(), 1, TimeUnit.DAYS);
                }

                // 关联信息流账户和广告位ID
                if (null != req.getSlotId() && StringUtils.isNotBlank(hu)) {
                    redisCache.setCacheObjectIfAbsent(EngineRedisKeyFactory.K088.join(hu), req.getSlotId(), 60, TimeUnit.DAYS);
                }

                redisAtomicClient.incrBy(EngineRedisKeyFactory.K108.join(dateStr, req.getOrientId(), type.getType()), req.getUnitPrice(), 1, TimeUnit.DAYS);
                redisAtomicClient.incrBy(EngineRedisKeyFactory.K086.join(dateStr, req.getAdvertId()), req.getUnitPrice(), 1,  TimeUnit.DAYS);
                break;
            case LANDPAGE_CLICK:
                redisAtomicClient.incrBy(EngineRedisKeyFactory.K108.join(dateStr, req.getOrientId(), type.getType()), 1, 1, TimeUnit.DAYS);
                break;
            case CONVERT_EVENT:
                redisAtomicClient.incrBy(EngineRedisKeyFactory.K108.join(dateStr, req.getOrientId(), type.getType(), req.getConvType()), 1, 1, TimeUnit.DAYS);
                break;
            default:
                break;
        }
        return true;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        DataStatService.register(this);
    }
}
