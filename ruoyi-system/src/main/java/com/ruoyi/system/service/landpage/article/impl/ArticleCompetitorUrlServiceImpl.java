package com.ruoyi.system.service.landpage.article.impl;

import org.springframework.stereotype.Service;
import com.ruoyi.system.service.landpage.article.ArticleCompetitorUrlService;
import com.ruoyi.system.entity.landpage.article.ArticleCompetitorUrlEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

import com.ruoyi.system.mapper.landpage.article.ArticleCompetitorUrlMapper;

/**
 * 公众号阅读竞品文章 Service
 *
 * <AUTHOR>
 * @date 2024-9-20 11:47:44
 */
@Service
public class ArticleCompetitorUrlServiceImpl implements ArticleCompetitorUrlService {

    @Autowired
    private ArticleCompetitorUrlMapper articleCompetitorUrlMapper;

    @Override
    public Boolean insert(ArticleCompetitorUrlEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return articleCompetitorUrlMapper.insert(entity) > 0;
    }

    @Override
    public Boolean updateById(ArticleCompetitorUrlEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return articleCompetitorUrlMapper.updateById(entity) > 0;
    }

    @Override
    public ArticleCompetitorUrlEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return articleCompetitorUrlMapper.selectById(id);
    }
}
