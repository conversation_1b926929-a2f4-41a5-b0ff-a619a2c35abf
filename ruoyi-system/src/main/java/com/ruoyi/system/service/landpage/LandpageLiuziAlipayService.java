package com.ruoyi.system.service.landpage;

import com.ruoyi.system.entity.landpage.LandpageLiuziAlipayEntity;

import java.util.List;
import java.util.Map;

/**
 * 支付宝留资落地页表 Service
 *
 * <AUTHOR>
 * @date 2022-12-5 10:08:00
 */
public interface LandpageLiuziAlipayService {
    /**
     * 新增记录
     */
    Boolean insert(LandpageLiuziAlipayEntity entity);

    /**
     * 根据id删除
     */
    Boolean deleteById(Long id);

    /**
     * 根据id更新
     */
    Boolean updateById(LandpageLiuziAlipayEntity entity);

    /**
     * 根据id获取
     */
    LandpageLiuziAlipayEntity selectById(Long id);

    /**
     * 根据落地页id列表查询
     * @param landpageIds
     * @return
     */
    List<LandpageLiuziAlipayEntity> selectListByLandpageIds(List<Long> landpageIds);

    /**
     * 新增更新支付宝推广页
     * @param entities
     * @return
     */
    Boolean batchInsertOrUpdate(List<LandpageLiuziAlipayEntity> entities);

    /**
     * 批量查询落地页名称
     *
     * @param landpageIds 落地页ID列表
     * @return 落地页ID-落地页名称映射
     */
    Map<Long, String> selectLandpageNameMap(List<Long> landpageIds);
}
