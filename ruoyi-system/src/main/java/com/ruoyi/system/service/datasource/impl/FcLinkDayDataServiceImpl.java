package com.ruoyi.system.service.datasource.impl;

import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisAtomicClient;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.InnerLogType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.UvCountUtils;
import com.ruoyi.system.entity.fc.FcLinkDayDataEntity;
import com.ruoyi.system.mapper.fc.FcLinkDayDataMapper;
import com.ruoyi.system.service.datasource.FcLinkDayDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class FcLinkDayDataServiceImpl implements FcLinkDayDataService {

    
    @Autowired
    private RedisAtomicClient redisAtomicClient;
    
    @Autowired
    private RedisCache redisCache;
    
    @Autowired
    private FcLinkDayDataMapper fcLinkDayDataMapper;

    @Override
    public FcLinkDayDataEntity getFcLinkDayData(String date, String fcLinkKey) {
        if ( StringUtils.isEmpty(date) || StringUtils.isEmpty(fcLinkKey) ) {
            return null;
        }
        FcLinkDayDataEntity fcLinkDayDataEntity = new FcLinkDayDataEntity();
        fcLinkDayDataEntity.setFcLinkKey(fcLinkKey);
        // 统计PV
        String pvKey = EngineRedisKeyFactory.k152.join(InnerLogType.FC_LINK_REQUEST.getType(), fcLinkKey, date, "pv");
        fcLinkDayDataEntity.setPv(redisAtomicClient.getLong(pvKey));

        // 统计UV
        String uvKey = EngineRedisKeyFactory.k152.join(InnerLogType.FC_LINK_REQUEST.getType(), fcLinkKey, date, "uv");
        fcLinkDayDataEntity.setUv(UvCountUtils.getUv(uvKey));
        return fcLinkDayDataEntity;
    }

    @Override
    public void stataDayDate(String date, String fcLinkKey, String userId) {
        if ( StringUtils.isEmpty(date) || StringUtils.isEmpty(fcLinkKey) ) {
            return ;
        }

        // 统计PV
        String pvKey = EngineRedisKeyFactory.k152.join(InnerLogType.FC_LINK_REQUEST.getType(), fcLinkKey, date, "pv");
        redisAtomicClient.incrBy(pvKey, 1, 28, TimeUnit.HOURS);

        // 统计UV
        String uvKey = EngineRedisKeyFactory.k152.join(InnerLogType.FC_LINK_REQUEST.getType(), fcLinkKey, date, "uv");
        UvCountUtils.recordUv(uvKey, userId, 28, TimeUnit.HOURS);

    }
    
    /**
     * 从Redis同步丰巢链接PV和UV数据到MySQL
     *
     * @param date 日期，格式：yyyy-MM-dd
     * @param fcLinkKey 丰巢链接唯一key
     * @return 同步是否成功
     */
    @Override
    public boolean syncRedisMetricsToDb(String date, String fcLinkKey) {
        if (StringUtils.isEmpty(date) || StringUtils.isEmpty(fcLinkKey)) {
            log.error("同步参数错误，日期或链接key为空");
            return false;
        }
        
        try {
            // 从Redis获取最新的PV和UV数据
            FcLinkDayDataEntity redisData = getFcLinkDayData(date, fcLinkKey);
            boolean hasRedisData = redisData != null && redisData.getPv() != null && redisData.getUv() != null;
            Long pv = 0L;
            Long uv = 0L;
            
            // 如果Redis有数据，则使用Redis数据
            if (hasRedisData) {
                pv = redisData.getPv();
                uv = redisData.getUv();
                log.info("获取到Redis中链接{}在{}的数据: PV={}, UV={}", fcLinkKey, date, pv, uv);
            } else {
                log.warn("链接{}在{}日期没有Redis统计数据，将使用默认值 PV=0, UV=0 进行新插入", fcLinkKey, date);
            }
            
            // 查询是否已存在该日期的统计数据
            FcLinkDayDataEntity existingData = fcLinkDayDataMapper.selectFcLinkDayData(date, fcLinkKey);
            
            if (Objects.isNull(existingData)) {

                Date curDate = DateUtils.parseDate(date, DateUtils.YYYY_MM_DD);
                FcLinkDayDataEntity newData = new FcLinkDayDataEntity();
                newData.setFcLinkKey(fcLinkKey);
                newData.setCurDate(curDate);
                newData.setPv(pv);
                newData.setUv(uv);
                
                fcLinkDayDataMapper.insertFcLinkDayData(newData);
                log.info("新增丰巢链接日数据统计，链接key: {}，日期: {}，PV: {}，UV: {}", 
                        fcLinkKey, date, pv, uv);
                return true;
            } else if (hasRedisData) {
                // 已存在且Redis有数据，才更新数据
                existingData.setPv(pv);
                existingData.setUv(uv);
                
                fcLinkDayDataMapper.updateFcLinkDayData(existingData);
                log.info("更新丰巢链接日数据统计，链接key: {}，日期: {}，PV: {}，UV: {}", 
                        fcLinkKey, date, pv, uv);
                return true;
            } else {
                // 已存在但Redis无数据，不更新
                log.info("链接{}在{}日期已有数据库记录但Redis无数据，不执行更新", fcLinkKey, date);
                return false;
            }
            
        } catch (Exception e) {
            log.error("同步Redis链接数据到MySQL失败，链接key: {}，日期: {}", fcLinkKey, date, e);
            return false;
        }
    }
    
    /**
     * 清理Redis中丰巢链接的PV和UV缓存数据
     *
     * @param date 日期，格式：yyyy-MM-dd
     * @param fcLinkKey 丰巢链接唯一key
     */
    @Override
    public void cleanRedisCache(String date, String fcLinkKey) {
        try {
            // 清理PV缓存
            String pvKey = EngineRedisKeyFactory.k152.join(InnerLogType.FC_LINK_REQUEST.getType(), fcLinkKey, date, "pv");
            redisCache.deleteObject(pvKey);
            
            // 清理UV缓存
            String uvKey = EngineRedisKeyFactory.k152.join(InnerLogType.FC_LINK_REQUEST.getType(), fcLinkKey, date, "uv");
            UvCountUtils.deleteUvData(uvKey);
            
            log.info("已清理丰巢链接{}在{}日期的Redis缓存数据", fcLinkKey, date);
        } catch (Exception e) {
            log.error("清理Redis缓存数据异常，链接key: {}，日期: {}", fcLinkKey, date, e);
        }
    }
}
