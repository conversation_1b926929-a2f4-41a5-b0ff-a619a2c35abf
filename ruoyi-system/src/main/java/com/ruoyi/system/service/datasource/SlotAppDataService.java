package com.ruoyi.system.service.datasource;

import com.ruoyi.system.bo.data.AppDataUpdateBO;
import com.ruoyi.system.req.slot.SlotDataUpdateReq;
import com.ruoyi.system.req.slot.SlotMonthDataUpdateReq;

import java.util.Date;
import java.util.List;

/**
 * 广告位媒体数据更新接口
 *
 * <AUTHOR>
 * @date 2022-03-08
 */
public interface SlotAppDataService {

    /**
     * 更新广告和媒体数据
     *
     * @param req 请求参数
     */
    void updateSlotAppData(SlotDataUpdateReq req);

    /**
     * 更新广告和媒体数据及月账单
     *
     * @param req 请求参数
     */
    void updateSlotAppMonthData(SlotMonthDataUpdateReq req);

    /**
     * 更新媒体数据
     *
     * @param appId 媒体ID
     * @param curDate 日期
     */
    void updateAppData(Long appId, Date curDate);

    /**
     * 批量更新媒体数据
     * @param bos
     */
    void batchUpdate(List<AppDataUpdateBO> bos);
}
