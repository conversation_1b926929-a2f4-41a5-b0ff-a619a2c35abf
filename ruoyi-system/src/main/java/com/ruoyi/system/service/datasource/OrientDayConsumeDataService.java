package com.ruoyi.system.service.datasource;

import com.ruoyi.system.entity.datashow.OrientDayConsumeData;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 广告配置日消耗数据Service接口
 *
 * <AUTHOR>
 * @date 2021-08-24
 */
public interface OrientDayConsumeDataService {

    /**
     * 查询广告配置消耗数据
     *
     * @param curDate 日期
     * @param orientId 广告配置ID
     * @return 广告配置消耗数据
     */
    OrientDayConsumeData selectByDateAndOrientId(Date curDate, Long orientId);

    /**
     * 修改广告配置日消耗数据
     *
     * @param orientDayConsumeData 广告配置日消耗数据
     * @return 结果
     */
    int addConsumeData(OrientDayConsumeData orientDayConsumeData);

    /**
     * 查询广告配置日消耗数据列表
     *
     * @param orientDayConsumeData 广告配置日消耗数据
     * @return 广告配置日消耗数据集合
     */
    List<OrientDayConsumeData> selectOrientDayConsumeDataList(OrientDayConsumeData orientDayConsumeData);

    /**
     * 新增广告配置日消耗数据
     *
     * @param orientDayConsumeData 广告配置日消耗数据
     * @return 结果
     */
    int insertOrientDayConsumeData(OrientDayConsumeData orientDayConsumeData);

    /**
     * 修改广告配置日消耗数据
     *
     * @param orientDayConsumeData 广告配置日消耗数据
     * @return 结果
     */
    int updateOrientDayConsumeData(OrientDayConsumeData orientDayConsumeData);

    /**
     * 查询广告配置日消耗映射
     *
     * @param curDate 日期
     * @param orientIds 配置ID列表
     * @return 配置ID-配置消耗映射
     */
    Map<Long, Integer> selectOrientConsumeMap(Date curDate, List<Long> orientIds);
}
