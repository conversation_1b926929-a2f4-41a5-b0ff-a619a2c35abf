package com.ruoyi.system.service.datasource.processor;

import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.DataDimensionEnum;
import com.ruoyi.system.domain.datashow.DataStatContext;
import com.ruoyi.system.entity.advert.Advert;
import com.ruoyi.system.entity.datashow.AdvertDayBudgetData;
import com.ruoyi.system.req.datashow.DataStatReq;
import com.ruoyi.system.service.datasource.AdvertDayBudgetDataService;
import com.ruoyi.system.service.datasource.DataStatService;
import com.ruoyi.system.service.advert.AdvertService;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.ruoyi.common.constant.BizConstants.OUTER_HD_ADVERT;
import static com.ruoyi.common.enums.DataDimensionEnum.ADVERT_DAY_BUDGET;

/**
 * 广告维度日预算处理器
 *
 * <AUTHOR>
 * @date 2021/10/19
 */
@Service
public class AdvertDayBudgetProcessor implements DataProcessor, InitializingBean {

    @Autowired
    private AdvertDayBudgetDataService advertDayBudgetDataService;

    @Autowired
    private AdvertService advertService;

    @Autowired
    private RedisCache redisCache;

    @Override
    public DataDimensionEnum getDimension() {
        return ADVERT_DAY_BUDGET;
    }

    @Override
    public boolean validate(DataStatContext context) {
        DataStatReq req = context.getReq();
        return null != req.getDate() && null != req.getAdvertId();
    }

    @Override
    public boolean process(DataStatContext context) {
        DataStatReq req = context.getReq();

        // 外部互动广告不处理
        if (Objects.equals(req.getAdvertId(), OUTER_HD_ADVERT)) {
            return true;
        }

        initAdvertDayBudgetData(req.getDateStr(), req.getDate(), req.getAdvertId());
        return true;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        DataStatService.register(this);
    }

    /**
     * 初始化广告日预算数据ID
     *
     * @param dateStr 日期字符串
     * @param date 日期
     * @param advertId 广告ID
     */
    private void initAdvertDayBudgetData(String dateStr, Date date, Long advertId) {
        String key = EngineRedisKeyFactory.K022.join("AdvertDayBudgetData", dateStr, advertId);
        Long advertDayBudgetDataId = redisCache.getCacheObject(key);
        if (null != advertDayBudgetDataId) {
            return;
        }

        AdvertDayBudgetData data = advertDayBudgetDataService.selectByDateAndAdvertId(date, advertId);
        if (null == data) {
            data = new AdvertDayBudgetData();
            data.setCurDate(date);
            data.setAdvertId(advertId);

            // 查询预算
            Advert advert = advertService.selectAdvertById(advertId);
            if (null != advert) {
                data.setBudget(advert.getDailyBudget());
                advertDayBudgetDataService.insertAdvertDayBudgetData(data);
                data = advertDayBudgetDataService.selectByDateAndAdvertId(date, advertId);
            }
        }
        redisCache.setCacheObject(key, data.getId(), 1, TimeUnit.DAYS);
    }
}
