package com.ruoyi.system.service.publisher.prepay;

import com.ruoyi.system.entity.account.finance.AccountPrepayApplyRecordEntity;
import com.ruoyi.system.req.publisher.prepay.PrepayRecordListParam;

import java.util.List;

/**
 * 媒体预付款申请记录 Service
 *
 * <AUTHOR>
 * @date 2022-7-29 14:44:56
 */
public interface AccountPrepayApplyRecordService {

    /**
     * 新增记录
     */
    Boolean insert(AccountPrepayApplyRecordEntity entity);

    /**
     * 根据id更新
     */
    Boolean updateById(AccountPrepayApplyRecordEntity entity);

    /**
     * 根据id获取
     */
    AccountPrepayApplyRecordEntity selectById(Long id);

    /**
     * 查询媒体预付款记录列表
     *
     * @param req 请求参数
     * @return 媒体预付款记录列表
     */
    List<AccountPrepayApplyRecordEntity> selectList(PrepayRecordListParam req);

    /**
     * 查询媒体预付款申请记录
     *
     * @param accountId 媒体账号ID
     * @param auditStatus 审核状态
     * @return 媒体预付款申请记录
     */
    List<AccountPrepayApplyRecordEntity> selectListByAccountIdAndAuditStatus(Long accountId, Integer auditStatus);

    /**
     * 查询媒体预付款申请记录数量
     *
     * @param accountId 媒体账号ID
     * @param auditStatus 审核状态
     * @return 媒体预付款申请记录数量
     */
    int countByAccountIdAndAuditStatus(Long accountId, Integer auditStatus);
}
