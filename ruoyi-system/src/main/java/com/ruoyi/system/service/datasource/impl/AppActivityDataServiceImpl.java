package com.ruoyi.system.service.datasource.impl;

import com.ruoyi.system.bo.appactivitydata.AppActivityDataSumBo;
import com.ruoyi.system.entity.datashow.AppActivityData;
import com.ruoyi.system.mapper.datashow.AppActivityDataMapper;
import com.ruoyi.system.service.datasource.AppActivityDataService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 媒体维度活动数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2021-07-21
 */
@Service
public class AppActivityDataServiceImpl implements AppActivityDataService {

    @Autowired
    private AppActivityDataMapper appActivityDataMapper;

    @Override
    public AppActivityData selectBy(Long appId, Long activityId, Date curDate) {
        return appActivityDataMapper.selectBy(appId, activityId, curDate);
    }

    /**
     * 查询媒体维度活动数据
     * 
     * @param id 媒体维度活动数据ID
     * @return 媒体维度活动数据
     */
    @Override
    public AppActivityData selectAppActivityDataById(String id) {
        return appActivityDataMapper.selectAppActivityDataById(id);
    }

    /**
     * 查询媒体维度活动数据列表
     * 
     * @param appActivityData 媒体维度活动数据
     * @return 媒体维度活动数据
     */
    @Override
    public List<AppActivityData> selectAppActivityDataList(AppActivityData appActivityData) {
        return appActivityDataMapper.selectAppActivityDataList(appActivityData);
    }

    /**
     * 新增媒体维度活动数据
     * 
     * @param appActivityData 媒体维度活动数据
     * @return 结果
     */
    @Override
    public int insertAppActivityData(AppActivityData appActivityData) {
        return appActivityDataMapper.insertAppActivityData(appActivityData);
    }

    /**
     * 修改媒体维度活动数据
     * 
     * @param appActivityData 媒体维度活动数据
     * @return 结果
     */
    @Override
    public int updateAppActivityData(AppActivityData appActivityData) {
        if (null == appActivityData.getId()) {
            return 0;
        }
        return appActivityDataMapper.updateAppActivityData(appActivityData);
    }

    @Override
    public List<AppActivityDataSumBo> selectAppActivityDataSum(List<Long> appIds, Date startDate, Date endDate) {
        if(CollectionUtils.isEmpty(appIds) || Objects.isNull(startDate) || Objects.isNull(endDate)){
            return Collections.emptyList();
        }
        return appActivityDataMapper.selectAppActivityDataSum(appIds, startDate, endDate);

    }

    @Override
    public List<AppActivityData> selectAppActivityData(List<Long> appIds, Date startDate, Date endDate) {
        if(CollectionUtils.isEmpty(appIds) || Objects.isNull(startDate) || Objects.isNull(endDate)){
            return Collections.emptyList();
        }
        return appActivityDataMapper.selectAppActivityData(appIds, startDate, endDate);
    }
}
