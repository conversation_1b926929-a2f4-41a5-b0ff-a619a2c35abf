package com.ruoyi.system.service.landpage;

import com.ruoyi.system.entity.landpage.ExternalLandpageDayData;

import java.util.Date;
import java.util.List;

/**
 * 外部落地页日数据Service接口
 *
 * <AUTHOR>
 * @date 2021-10-09
 */
public interface ExternalLandpageDayDataService {

    /**
     * 查询外部落地页日数据
     *
     * @param advertiserId 广告主ID
     * @param landpageId 落地页ID
     * @param curDate 日期
     * @return 外部落地页日数据
     */
    ExternalLandpageDayData selectBy(Long advertiserId, String landpageId, Date curDate);

    /**
     * 查询外部落地页日数据
     *
     * @param id 外部落地页日数据ID
     * @return 外部落地页日数据
     */
    ExternalLandpageDayData selectExternalLandpageDayDataById(Long id);

    /**
     * 查询外部落地页日数据列表
     *
     * @param externalLandpageDayData 外部落地页日数据
     * @return 外部落地页日数据集合
     */
    List<ExternalLandpageDayData> selectExternalLandpageDayDataList(ExternalLandpageDayData externalLandpageDayData);

    /**
     * 新增外部落地页日数据
     *
     * @param externalLandpageDayData 外部落地页日数据
     * @return 结果
     */
    int insertExternalLandpageDayData(ExternalLandpageDayData externalLandpageDayData);

    /**
     * 修改外部落地页日数据
     *
     * @param externalLandpageDayData 外部落地页日数据
     * @return 结果
     */
    int updateExternalLandpageDayData(ExternalLandpageDayData externalLandpageDayData);
}
