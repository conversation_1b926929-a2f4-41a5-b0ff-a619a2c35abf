package com.ruoyi.system.service.landpage.impl;

import org.springframework.stereotype.Service;
import com.ruoyi.system.service.landpage.ExternalLandpageFormSendRecordService;
import com.ruoyi.system.entity.landpage.ExternalLandpageFormSendRecordEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import com.ruoyi.system.mapper.landpage.ExternalLandpageFormSendRecordMapper;
import org.springframework.util.CollectionUtils;

/**
 * 外部落地页表单上报记录 Service
 *
 * <AUTHOR>
 * @date 2023-4-11 15:38:53
 */
@Service
public class ExternalLandpageFormSendRecordServiceImpl implements ExternalLandpageFormSendRecordService {

    @Autowired
    private ExternalLandpageFormSendRecordMapper externalLandpageFormSendRecordMapper;

    @Override
    public Boolean insert(ExternalLandpageFormSendRecordEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return externalLandpageFormSendRecordMapper.insert(entity) > 0;
    }

    @Override
    public Boolean deleteById(Long id) {
        if (Objects.isNull(id)) {
            return false;
        }
        return externalLandpageFormSendRecordMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean updateById(ExternalLandpageFormSendRecordEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return externalLandpageFormSendRecordMapper.updateById(entity) > 0;
    }

    @Override
    public ExternalLandpageFormSendRecordEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return externalLandpageFormSendRecordMapper.selectById(id);
    }

    @Override
    public Set<String> selectExternalNoSet(List<String> externalNoList) {
        if (CollectionUtils.isEmpty(externalNoList)) {
            return Collections.emptySet();
        }
        List<String> list = externalLandpageFormSendRecordMapper.selectExternalNo(externalNoList);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptySet();
        }
        return new HashSet<>(list);
    }

    @Override
    public List<Long> selectAdvertiserIds() {
        return externalLandpageFormSendRecordMapper.selectAdvertiserIds();
    }
}
