package com.ruoyi.system.service.accounttag.impl;

import com.ruoyi.common.utils.NumberUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.accounttag.AccountTagRelationService;
import com.ruoyi.system.entity.accounttag.AccountTagRelationEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.ruoyi.system.mapper.accounttag.AccountTagRelationMapper;

/**
 * 账号标签关联表 Service
 *
 * <AUTHOR>
 * @date 2023-3-6 10:53:35
 */
@Service
public class AccountTagRelationServiceImpl implements AccountTagRelationService {
    @Autowired
    private AccountTagRelationMapper accountTagRelationMapper;

    @Override
    public Boolean insert(AccountTagRelationEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return accountTagRelationMapper.insert(entity) > 0;
    }

    @Override
    public Boolean deleteById(Long id) {
        if (Objects.isNull(id)) {
            return false;
        }
        return accountTagRelationMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean updateById(AccountTagRelationEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return accountTagRelationMapper.updateById(entity) > 0;
    }

    @Override
    public AccountTagRelationEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return accountTagRelationMapper.selectById(id);
    }

    @Override
    public Boolean deleteByAccountIdAndType(Long accountId, Integer type) {
        if(NumberUtils.isNullOrLteZero(accountId) || NumberUtils.isNullOrLteZero(type)){
            return false;
        }
        return accountTagRelationMapper.deleteByAccountIdAndType(accountId, type) > 0;
    }

    @Override
    public Boolean batchInsert(List<AccountTagRelationEntity> entities) {
        if(CollectionUtils.isEmpty(entities)){
            return false;
        }
        return accountTagRelationMapper.batchInsert(entities) > 0;
    }

    @Override
    public List<AccountTagRelationEntity> selectListByAccountType(Long accountId, Integer tagType) {
        return accountTagRelationMapper.selectListByAccountType(accountId, tagType);
    }

    @Override
    public List<Long> selectTagIdsByAccountType(Long accountId, Integer tagType) {
        List<AccountTagRelationEntity> relationEntities = selectListByAccountType(accountId, tagType);
        return relationEntities.stream().map(AccountTagRelationEntity::getTagId).collect(Collectors.toList());
    }

    @Override
    public List<AccountTagRelationEntity> selectListByTagIds(List<Long> tagIds) {
        if(CollectionUtils.isEmpty(tagIds)){
            return Collections.emptyList();
        }
        return accountTagRelationMapper.selectListByTagIds(tagIds);
    }
}
