package com.ruoyi.system.service.advertiser;

import com.ruoyi.system.entity.account.Account;
import com.ruoyi.system.req.agent.client.AgentClientAddReq;

import java.util.List;
import java.util.Map;

/**
 * 广告主Service接口
 *
 * <AUTHOR>
 * @date 2021/8/9
 */
public interface AdvertiserService {

    /**
     * 查询广告主列表
     *
     * @param param 账号
     * @return 广告主列表
     */
    List<Account> selectList(Account param);

    /**
     * 查询广告主列表
     *
     * @return 广告主列表
     */
    List<Account> selectTotalAdvertiserList();

    /**
     * 查询广告主对应的广告主名称
     *
     * @param advertiserIds 广告主ID列表
     * @return 广告主ID-广告主名称映射
     */
    Map<Long, String> selectAdvertiserNameMap(List<Long> advertiserIds);

    /**
     * 查询广告主名称
     *
     * @param advertiserId 广告主ID
     * @return 广告主名称
     */
    String selectAdvertiserName(Long advertiserId);

    /**
     * 根据广告主ID/名称/邮箱模糊查询广告主ID列表
     *
     * @param advertiserSearch 广告主ID/名称/邮箱
     * @return 广告主ID列表
     */
    List<Long> selectAdvertiserIdsBySearch(String advertiserSearch);

    /**
     * 根据广告主名称模糊查询广告主ID列表
     *
     * @param advertiserName 广告主名称
     * @return 广告主ID列表
     */
    List<Long> selectAdvertiserIdsByName(String advertiserName);

    /**
     * 根据广告主邮箱模糊查询广告主ID列表
     *
     * @param email 广告主邮箱
     * @return 广告主ID列表
     */
    List<Long> selectAdvertiserIdsByEmail(String email);

    /**
     * 广告主回传媒体信息配置
     *
     * @param advertiserId 广告主ID
     * @param appRet 是否回传媒体信息
     */
    void advertiserAppRetConfig(Long advertiserId, Integer appRet);

    /**
     * 更新广告主落地页回传链接
     *
     * @param advertiserId 广告主ID
     * @param lpCallbackUrl 回传链接
     * @return 是否更新成功
     */
    boolean updateLpCallbackUrl(Long advertiserId, String lpCallbackUrl);

    /**
     * 更新广告主表单价格及计费类型
     *
     * @param advertiserId 广告主ID
     * @param formPrice 表单价格
     * @param priceType 计费类型
     * @param weight 权重
     * @return 是否更新成功
     */
    boolean updateFormPrice(Long advertiserId, Integer formPrice, Integer priceType, Double weight);

    /**
     * 查询广告主表单价格映射
     *
     * @param advertiserIds 广告主ID列表
     * @return 广告主ID-表单价格映射
     */
    Map<Long, Integer> selectFormPriceMap(List<Long> advertiserIds);

    /**
     * 查询广告主表单权重映射
     *
     * @param advertiserIds 广告主ID列表
     * @return 广告主ID-表单权重映射
     */
    Map<Long, Double> selectFormWeightMap(List<Long> advertiserIds);

    /**
     * 代理商新增广告主账号
     *
     * @param agentId 代理商ID
     * @param req 账号信息
     * @return 账号ID
     */
    Long insertByAgent(Long agentId, AgentClientAddReq req);

    /**
     * 更新广告主结算类型
     *
     * @param advertiserId 广告主ID
     * @param consumeType 结算类型
     * @return true.更新成功,false.更新失败
     */
    boolean updateConsumeType(Long advertiserId, Integer consumeType);

    /**
     * 判断是不是CPC结算广告主
     *
     * @param advertiserId 广告主ID
     * @return true.是,false.不是
     */
    boolean isCpcAdvertiser(Long advertiserId);
}
