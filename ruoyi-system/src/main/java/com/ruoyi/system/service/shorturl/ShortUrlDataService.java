package com.ruoyi.system.service.shorturl;

import com.ruoyi.system.entity.shorturl.ShortUrlDataEntity;
import com.ruoyi.system.param.shorturl.ShortUrlDataParam;

import java.util.List;

/**
 * 短链数据表 Service
 *
 * <AUTHOR>
 * @date 2022-10-8 14:16:45
 */
public interface ShortUrlDataService {

    /**
     * 新增记录
     */
    Boolean insert(ShortUrlDataEntity entity);

    /**
     * 根据id删除
     */
    Boolean deleteById(Long id);

    /**
     * 根据id更新
     */
    Boolean updateById(ShortUrlDataEntity entity);

    /**
     * 根据id获取
     */
    ShortUrlDataEntity selectById(Long id);

    /**
     * 查询短链日数据
     *
     * @param param 参数
     * @return 短链日数据
     */
    ShortUrlDataEntity selectBy(ShortUrlDataEntity param);

    /**
     * 短链数据统计(异步)
     *
     * @param idStr 编码后的短链ID
     */
    void statistics(String idStr);

    /**
     * 根据条件查询短链数据
     * @return
     */
    List<ShortUrlDataEntity> selectListByParam(ShortUrlDataParam param);
}
