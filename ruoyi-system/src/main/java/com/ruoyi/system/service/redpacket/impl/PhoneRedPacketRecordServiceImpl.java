package com.ruoyi.system.service.redpacket.impl;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.system.req.cashback.CashBackListReq;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.redpacket.PhoneRedPacketRecordService;
import com.ruoyi.system.entity.redpacket.PhoneRedPacketRecordEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;

import com.ruoyi.system.mapper.redpacket.PhoneRedPacketRecordMapper;

/**
 * 表单用户领红包记录表 Service
 *
 * <AUTHOR>
 * @date 2022-5-26 17:50:17
 */
@Service
public class PhoneRedPacketRecordServiceImpl implements PhoneRedPacketRecordService {

    @Autowired
    private PhoneRedPacketRecordMapper phoneRedPacketRecordMapper;

    @Override
    public int insert(PhoneRedPacketRecordEntity entity) {
        return phoneRedPacketRecordMapper.insert(entity);
    }

    @Override
    public int updateById(PhoneRedPacketRecordEntity entity) {
        if (null != entity.getIp() && entity.getIp().length() > 32) {
            entity.setIp(entity.getIp().substring(0, 32));
        }
        if (null != entity.getUserAgent() && entity.getUserAgent().length() > 255) {
            entity.setUserAgent(entity.getUserAgent().substring(0, 255));
        }
        return phoneRedPacketRecordMapper.updateById(entity);
    }

    @Override
    public PhoneRedPacketRecordEntity selectByPhone(String phone) {
        if (Objects.isNull(phone)) {
            return null;
        }
        return phoneRedPacketRecordMapper.selectByPhone(phone);
    }

    @Override
    public List<PhoneRedPacketRecordEntity> selectByReq(CashBackListReq req) {
        if(Objects.nonNull(req.getSubmitStartTime()) && Objects.nonNull(req.getSubmitEndTime())){
            req.setSubmitStartTime(DateUtil.beginOfDay(req.getSubmitStartTime()));
            req.setSubmitEndTime(DateUtil.endOfDay(req.getSubmitEndTime()));
        }
        if(Objects.nonNull(req.getOperatorEndTime()) && Objects.nonNull(req.getOperatorStartTime())){
            req.setOperatorStartTime(DateUtil.beginOfDay(req.getOperatorStartTime()));
            req.setOperatorEndTime(DateUtil.endOfDay(req.getOperatorEndTime()));
        }
        return phoneRedPacketRecordMapper.selectByReq(req);
    }
}
