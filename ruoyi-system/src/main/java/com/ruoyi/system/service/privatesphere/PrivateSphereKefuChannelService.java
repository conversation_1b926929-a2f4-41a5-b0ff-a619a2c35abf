package com.ruoyi.system.service.privatesphere;

import com.ruoyi.system.entity.privatesphere.PrivateSphereKefuChannelEntity;

import java.util.List;
import java.util.Map;

/**
 * 私域客服渠道表 Service
 *
 * <AUTHOR>
 * @date 2023-3-6 15:22:11
 */
public interface PrivateSphereKefuChannelService {
    /**
     * 新增记录
     */
    Boolean insert(PrivateSphereKefuChannelEntity entity);

    /**
     * 根据id删除
     */
    Boolean deleteById(Long id);

    /**
     * 根据id更新
     */
    Boolean updateById(PrivateSphereKefuChannelEntity entity);

    /**
     * 根据id获取
     */
    PrivateSphereKefuChannelEntity selectById(Long id);

    /**
     * 根据渠道简称查询
     * @return
     */
    PrivateSphereKefuChannelEntity selectByChannel(String channel);

    /**
     * 获取所有客服渠道列表
     * @return
     */
    List<PrivateSphereKefuChannelEntity> selectAllList();

    /**
     * 根据渠道名称模糊查询
     * @param channel
     * @return
     */
    List<Long> selectIdListByChannel(String channel);

    /**
     * 根据id列表查询渠道信息
     * @param ids
     * @return
     */
    List<PrivateSphereKefuChannelEntity> selectListByIds(List<Long> ids);

    /**
     * 根据id列表查询渠道名
     * @param ids
     * @return
     */
    Map<Long,String> selectChannelNameMapByIds(List<Long> ids);

}
