package com.ruoyi.system.service.slotdata.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Pair;
import com.ruoyi.common.constant.ErrorCode;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.system.entity.appdata.AppMonthDataEntity;
import com.ruoyi.system.entity.slotdata.SlotMonthDataEntity;
import com.ruoyi.system.mapper.slotdata.SlotMonthDataMapper;
import com.ruoyi.system.service.appdata.AppMonthDataService;
import com.ruoyi.system.service.manager.AppService;
import com.ruoyi.system.service.manager.SlotService;
import com.ruoyi.system.service.qualification.AccountQualificationService;
import com.ruoyi.system.service.slotdata.SlotMonthDataService;
import com.ruoyi.system.vo.datashow.CrmSlotMonthDataVO;
import com.ruoyi.system.vo.datashow.StatementInfoVO;
import com.ruoyi.system.vo.qualification.QualificationInfoVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 广告位月账单数据表 Service
 *
 * <AUTHOR>
 * @date 2021-9-9 16:56:55
 */
@Service
public class SlotMonthDataServiceImpl implements SlotMonthDataService{

    @Autowired
    private SlotMonthDataMapper slotMonthDataMapper;

    @Autowired
    private AppMonthDataService appMonthDataService;

    @Autowired
    private SlotService slotService;

    @Autowired
    private AppService appService;

    @Autowired
    private AccountQualificationService accountQualificationService;

    @Override
    public int batchInsertOrUpdate(List<SlotMonthDataEntity> entities) {
        if(CollectionUtils.isEmpty(entities)){
            return 0;
        }
        return slotMonthDataMapper.batchInsertOrUpdate(entities);
    }

    @Override
    public StatementInfoVO selectStatementInfoByAppMonthDataId(Long appMonthDataId) {
        if(NumberUtils.isNullOrLteZero(appMonthDataId)){
            throw new CustomException(ErrorCode.ARGS);
        }

        AppMonthDataEntity appMonthData = appMonthDataService.selectById(appMonthDataId);
        if(Objects.isNull(appMonthData)){
            throw new CustomException(ErrorCode.E106002);
        }
        //查询媒体指定日期下的所有广告位月数据
        List<SlotMonthDataEntity> slotMonthDataEntities = slotMonthDataMapper.selectByAppIdAndDate(appMonthData.getAppId(), appMonthData.getMonthDate());
        if(CollectionUtils.isEmpty(slotMonthDataEntities)){
            throw new CustomException(ErrorCode.E106002);
        }

        StatementInfoVO vo = new StatementInfoVO();
        vo.setAppId(appMonthData.getAppId());
        vo.setAppName(appService.selectAppNameById(appMonthData.getAppId()));
        vo.setAppRevenue(appMonthData.getAppRevenue());
        // 预付款信息
        vo.setPayType(appMonthData.getPayType());
        vo.setPrepayAmount(appMonthData.getPrepayAmount());
        // 广告位月账单
        Map<Long, String> slotNameMap = slotService.selectSlotNameMap(ListUtils.mapToList(slotMonthDataEntities, SlotMonthDataEntity::getSlotId));
        List<CrmSlotMonthDataVO> monthDataVOS = slotMonthDataEntities.stream().map(entity -> {
            CrmSlotMonthDataVO crmSlotMonthDataVO = BeanUtil.copyProperties(entity, CrmSlotMonthDataVO.class);
            crmSlotMonthDataVO.setSlotName(slotNameMap.get(entity.getSlotId()));
            return crmSlotMonthDataVO;
        }).collect(Collectors.toList());
        vo.setMonthDatas(monthDataVOS);
        // 媒体资质信息
        QualificationInfoVO qualificationInfoVO = accountQualificationService.selectQualificationInfoByAccountId(appMonthData.getAccountId());
        vo.setQualificationInfo(qualificationInfoVO);
        return vo;
    }

    @Override
    public int countBySlotIdAndMonth(Long slotId, Integer month) {
        if(NumberUtils.isNullOrLteZero(slotId) || NumberUtils.isNullOrLteZero(month)){
            return 0;
        }
        return slotMonthDataMapper.countBySlotIdAndMonth(slotId,month);
    }

    @Override
    public Set<Pair<Long, Integer>> existBySlotIdsAndMonths(List<Long> slotIds, List<Integer> months) {
        List<SlotMonthDataEntity> list = slotMonthDataMapper.selectBySlotIdsAndMonths(slotIds, months);
        return list.stream().map(s -> Pair.of(s.getSlotId(), s.getMonthDate())).collect(Collectors.toSet());
    }

    @Override
    public List<SlotMonthDataEntity> selectBySlotIdsAndMonth(List<Long> slotIds, Integer month) {
        if(CollectionUtils.isEmpty(slotIds) || NumberUtils.isNullOrLteZero(month)){
            return Collections.emptyList();
        }
        return slotMonthDataMapper.selectBySlotIdsAndMonth(slotIds,month);
    }

    @Override
    public SlotMonthDataEntity selectBySlotIdAndMonth(Long slotId, Integer month) {
        if (null == slotId || null == month) {
            return null;
        }
        return slotMonthDataMapper.selectBySlotIdAndMonth(slotId, month);
    }

    @Override
    public int insert(SlotMonthDataEntity entity) {
        if (null == entity) {
            return 0;
        }
        return slotMonthDataMapper.insert(entity);
    }

    @Override
    public int update(SlotMonthDataEntity entity) {
        if (null == entity || null == entity.getId()) {
            return 0;
        }
        return slotMonthDataMapper.update(entity);
    }
}
