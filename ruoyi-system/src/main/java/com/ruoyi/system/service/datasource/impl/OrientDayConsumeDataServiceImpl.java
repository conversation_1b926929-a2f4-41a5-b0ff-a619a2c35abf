package com.ruoyi.system.service.datasource.impl;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.datashow.OrientDayConsumeDataMapper;
import com.ruoyi.system.entity.datashow.OrientDayConsumeData;
import com.ruoyi.system.service.datasource.OrientDayConsumeDataService;

/**
 * 广告配置日消耗数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-08-24
 */
@Service
public class OrientDayConsumeDataServiceImpl implements OrientDayConsumeDataService {

    @Autowired
    private OrientDayConsumeDataMapper orientDayConsumeDataMapper;

    @Override
    public OrientDayConsumeData selectByDateAndOrientId(Date curDate, Long orientId) {
        return orientDayConsumeDataMapper.selectByDateAndOrientId(curDate, orientId);
    }

    @Override
    public int addConsumeData(OrientDayConsumeData orientDayConsumeData) {
        return orientDayConsumeDataMapper.addConsumeData(orientDayConsumeData);
    }

    /**
     * 查询广告配置日消耗数据列表
     *
     * @param orientDayConsumeData 广告配置日消耗数据
     * @return 广告配置日消耗数据
     */
    @Override
    public List<OrientDayConsumeData> selectOrientDayConsumeDataList(OrientDayConsumeData orientDayConsumeData) {
        return orientDayConsumeDataMapper.selectOrientDayConsumeDataList(orientDayConsumeData);
    }

    /**
     * 新增广告配置日消耗数据
     *
     * @param orientDayConsumeData 广告配置日消耗数据
     * @return 结果
     */
    @Override
    public int insertOrientDayConsumeData(OrientDayConsumeData orientDayConsumeData) {
        return orientDayConsumeDataMapper.insertOrientDayConsumeData(orientDayConsumeData);
    }

    /**
     * 修改广告配置日消耗数据
     *
     * @param orientDayConsumeData 广告配置日消耗数据
     * @return 结果
     */
    @Override
    public int updateOrientDayConsumeData(OrientDayConsumeData orientDayConsumeData) {
        return orientDayConsumeDataMapper.updateOrientDayConsumeData(orientDayConsumeData);
    }

    @Override
    public Map<Long, Integer> selectOrientConsumeMap(Date curDate, List<Long> orientIds) {
       if (null == curDate || CollectionUtils.isEmpty(orientIds)) {
           return Collections.emptyMap();
       }
        List<OrientDayConsumeData> list = orientDayConsumeDataMapper.selectByDateAndOrientIds(curDate, orientIds);
        return list.stream().collect(Collectors.toMap(OrientDayConsumeData::getOrientId, OrientDayConsumeData::getConsume, (v1, v2) -> v2));
    }
}
