package com.ruoyi.system.service.datasource.processor;

import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.DataDimensionEnum;
import com.ruoyi.common.enums.InnerLogType;
import com.ruoyi.common.utils.BizUtils;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.system.domain.datashow.DataStatContext;
import com.ruoyi.system.entity.datashow.AdvertHourData;
import com.ruoyi.system.req.datashow.DataStatReq;
import com.ruoyi.system.service.datasource.AdvertHourDataService;
import com.ruoyi.system.service.datasource.DataStatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

import static com.ruoyi.common.enums.DataDimensionEnum.ADVERT_HOUR;

/**
 * 广告维度时段数据处理器
 *
 * <AUTHOR>
 * @date 2021/10/14
 */
@Slf4j
@Service
public class AdvertHourProcessor implements DataProcessor, InitializingBean {

    @Autowired
    private AdvertHourDataService advertHourDataService;

    @Autowired
    private RedisCache redisCache;

    @Override
    public DataDimensionEnum getDimension() {
        return ADVERT_HOUR;
    }

    @Override
    public boolean validate(DataStatContext context) {
        DataStatReq req = context.getReq();
        return null != req.getDate() && null != req.getHour() && null != req.getAdvertId() && null != req.getConsumerId();
    }

    @Override
    public boolean process(DataStatContext context) {
        DataStatReq req = context.getReq();
        InnerLogType type = context.getType();
        int pv = 1;
        int uv = 0;

        // 计算uv
        switch (type) {
            case ADVERT_LAUNCH:
            case ADVERT_EXPOSURE:
            case ADVERT_CLICK:
            case LANDPAGE_EXPOSURE:
            case LANDPAGE_CLICK:
            case ADVERT_BILLING:
            case BLIND_BOX_POPUP_CLICK:
                String uvKey = EngineRedisKeyFactory.K002.join(type.getType(), req.getAdvertId(), req.getDateStr(), req.getHour());
                uv = BizUtils.countUv(uvKey, String.valueOf(req.getConsumerId()), 1, TimeUnit.HOURS);
                break;
            default:
                break;
        }

        // 数据
        AdvertHourData updateAdvertData = new AdvertHourData();
        updateAdvertData.setId(getDataId(req));

        switch (type) {
            case ADVERT_LAUNCH:
                updateAdvertData.setAdLaunchPvAdd(pv);
                updateAdvertData.setAdLaunchUvAdd(uv);
                break;
            case ADVERT_EXPOSURE:
                updateAdvertData.setExposurePvAdd(pv);
                updateAdvertData.setExposureUvAdd(uv);
                break;
            case ADVERT_CLICK:
                updateAdvertData.setClickPvAdd(pv);
                updateAdvertData.setClickUvAdd(uv);
                break;
            case LANDPAGE_EXPOSURE:
                updateAdvertData.setLpExposurePvAdd(pv);
                updateAdvertData.setLpExposureUvAdd(uv);
                break;
            case LANDPAGE_CLICK:
                updateAdvertData.setLpClickPvAdd(pv);
                updateAdvertData.setLpClickUvAdd(uv);
                break;
            case ADVERT_BILLING:
                updateAdvertData.setBillingClickPvAdd(pv);
                updateAdvertData.setBillingClickUvAdd(uv);
                updateAdvertData.setConsumeAdd(NumberUtils.defaultInt(req.getUnitPrice()));
                break;
            case BLIND_BOX_POPUP_CLICK:
                updateAdvertData.setTakePvAdd(pv);
                updateAdvertData.setTakeUvAdd(uv);
                break;
            default:
                break;
        }
        return advertHourDataService.updateAdvertHourData(updateAdvertData) > 0;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        DataStatService.register(this);
    }

    /**
     * 通过缓存获取数据ID
     */
    private Long getDataId(DataStatReq req) {
        String key = EngineRedisKeyFactory.K022.join("AdvertHourData", req.getDateStr(), req.getHour(), req.getAdvertId());
        Long dataId = redisCache.getCacheObject(key);
        if (null != dataId) {
            return dataId;
        }

        // 查询并初始数据
        AdvertHourData param = new AdvertHourData();
        param.setCurDate(req.getDate());
        param.setCurHour(req.getHour());
        param.setAdvertId(req.getAdvertId());
        AdvertHourData data = advertHourDataService.selectBy(param);
        if (null == data) {
            data = new AdvertHourData();
            data.setCurDate(req.getDate());
            data.setCurHour(req.getHour());
            data.setAdvertId(req.getAdvertId());
            advertHourDataService.insertAdvertHourData(data);
            data = advertHourDataService.selectBy(param);
        }
        redisCache.setCacheObject(key, data.getId(), 1, TimeUnit.HOURS);
        return data.getId();
    }
}
