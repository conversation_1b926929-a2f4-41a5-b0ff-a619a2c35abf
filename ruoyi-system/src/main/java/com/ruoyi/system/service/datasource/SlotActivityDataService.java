package com.ruoyi.system.service.datasource;

import com.ruoyi.system.bo.slotactivitydata.SlotActivityDataSumBo;
import com.ruoyi.system.entity.datashow.SlotActivityData;

import java.util.Date;
import java.util.List;

/**
 * 广告位维度活动数据Service接口
 *
 * <AUTHOR>
 * @date 2021-07-21
 */
public interface SlotActivityDataService {

    /**
     * 查询广告位数据
     *
     * @param slotId 广告位ID
     * @param curDate 日期
     * @return 广告位数据
     */
    SlotActivityData selectBy(Long slotId, Long activityId, Date curDate);

    /**
     * 查询广告位维度活动数据
     *
     * @param id 广告位维度活动数据ID
     * @return 广告位维度活动数据
     */
    SlotActivityData selectSlotActivityDataById(String id);

    /**
     * 查询广告位维度活动数据列表
     *
     * @param slotActivityData 广告位维度活动数据
     * @return 广告位维度活动数据集合
     */
    List<SlotActivityData> selectSlotActivityDataList(SlotActivityData slotActivityData);

    /**
     * 新增广告位维度活动数据
     *
     * @param slotActivityData 广告位维度活动数据
     * @return 结果
     */
    int insertSlotActivityData(SlotActivityData slotActivityData);

    /**
     * 修改广告位维度活动数据
     *
     * @param slotActivityData 广告位维度活动数据
     * @return 结果
     */
    int updateSlotActivityData(SlotActivityData slotActivityData);


    /**
     * 统计广告位活动数据 根据广告位分组总计
     * @param slotIds 广告位id列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 总计
     */
    List<SlotActivityDataSumBo> selectSumSlotActivityDataGroupBySlot(List<Long> slotIds,Date startDate,Date endDate);
    /**
     * 统计广告位活动数据 总计
     * @param slotIds 广告位id列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 总计
     */
    SlotActivityDataSumBo selectSlotActivityDataSum(List<Long> slotIds,Date startDate,Date endDate);

    /**
     * 查询广告位活动数据
     * @param slotIds 广告位id列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 结果
     */
    List<SlotActivityData> selectSlotActivityData(List<Long> slotIds, Date startDate, Date endDate);


    /**
     * 根据媒体id和日期查询广告位活动数据
     * @param appId
     * @param curDate
     * @return
     */
    List<SlotActivityData> selectDataByAppIdAndDate(Long appId, Date curDate);
}
