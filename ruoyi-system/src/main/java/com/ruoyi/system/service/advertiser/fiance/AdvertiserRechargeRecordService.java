package com.ruoyi.system.service.advertiser.fiance;

import com.ruoyi.system.entity.advertiser.finance.AdvertiserRechargeRecordEntity;
import com.ruoyi.system.req.advertiser.finance.AdvertiserRechargeListReq;

import java.util.List;
import java.util.Map;

/**
 * 广告主充值记录表 Service
 *
 * <AUTHOR>
 * @date 2022-3-18 17:57:43
 */
public interface AdvertiserRechargeRecordService {

    /**
     * 新增记录
     */
    int insert(AdvertiserRechargeRecordEntity entity);

    /**
     * 根据id更新
     */
    int updateById(AdvertiserRechargeRecordEntity entity);

    /**
     * 根据id获取
     */
    AdvertiserRechargeRecordEntity selectById(Long id);

    /**
     * 查询广告主充值记录
     *
     * @param req 参数
     * @return 广告主充值记录列表
     */
    List<AdvertiserRechargeRecordEntity> selectListByAccountIdAndDate(AdvertiserRechargeListReq req);

    /**
     * 查询广告主充值金额汇总
     *
     * @param req 参数
     * @return 广告主充值金额汇总
     */
    Long selectStatisticRecharge(AdvertiserRechargeListReq req);

    /**
     * 统计代理商ID的划账总金额
     *
     * @param sourceAccountId 代理商ID
     * @return 划账总金额
     */
    Integer sumBySourceAccountId(Long sourceAccountId);

    /**
     * 统计代理商下不同广告主的划账总金额
     *
     * @param sourceAccountId 代理商ID
     * @param accountIds 广告主ID列表
     * @return 广告主ID-划账总金额映射
     */
    Map<Long, Integer> sumAccountMapBySourceAccountId(Long sourceAccountId, List<Long> accountIds);
}
