package com.ruoyi.system.service.privatesphere;

import com.ruoyi.system.bo.privatesphere.PrivateSphereChannelListBO;
import com.ruoyi.system.entity.privatesphere.PrivateSphereChannelEntity;

import java.util.List;

/**
 * 私域渠道表 Service
 *
 * <AUTHOR>
 * @date 2023-2-8 15:51:57
 */
public interface PrivateSphereChannelService {
    /**
     * 新增记录
     */
    Boolean insert(PrivateSphereChannelEntity entity);

    /**
     * 根据id删除
     */
    Boolean deleteById(Long id);

    /**
     * 根据id更新
     */
    Boolean updateById(PrivateSphereChannelEntity entity);

    /**
     * 根据id获取
     */
    PrivateSphereChannelEntity selectById(Long id);

    /**
     * 根据产品id和渠道名获取
     * @param productId
     * @param channelName
     * @return
     */
    PrivateSphereChannelEntity selectByProductAndChannelName(Long productId,String channelName);

    /**
     * 根据条件查询渠道列表
     * @param bo
     * @return
     */
    List<PrivateSphereChannelEntity> selectListByParam(PrivateSphereChannelListBO bo);


}
