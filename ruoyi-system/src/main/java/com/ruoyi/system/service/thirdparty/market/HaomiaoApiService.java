package com.ruoyi.system.service.thirdparty.market;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.format.FastDateFormat;
import cn.hutool.core.map.MapUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.enums.CountMonitorTypeEnum;
import com.ruoyi.common.utils.SpringEnvironmentUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.UrlUtils;
import com.ruoyi.common.utils.sign.Base64;
import com.ruoyi.system.bo.thirdparty.IdCardAuditBo;
import com.ruoyi.system.util.CountMonitorUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.Key;
import java.security.NoSuchAlgorithmException;
import java.util.Date;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TimeZone;

import static com.ruoyi.common.enums.IdCardAuditApiType.HAOMIAO;

/**
 * 毫秒科技接口
 *
 * <AUTHOR>
 * @date 2022/11/2
 */
@Slf4j
@Service
public class HaomiaoApiService {

    // 来源
    private static final String SOURCE = "market";
    // 接口地址(实名认证)
    private static final String API_URL = "https://service-iksuqugz-1253454403.sh.apigw.tencentcs.com/release/idCardAudit";
    // 接口地址(IP解析)
    private static final String IP_API_URL = "https://service-g9qyj6ll-1253454403.sh.apigw.tencentcs.com/release/ipLocation";
    // 接口请求超时时间
    private static final int HTTP_TIMEOUT = 5000;
    // 时间格式
    private static final FastDateFormat DATE_TIME_FORMATTER = FastDateFormat.getInstance("EEE, dd MMM yyyy HH:mm:ss 'GMT'", TimeZone.getTimeZone("GMT"), Locale.US);
    // 运营商域名-名称映射
    public static final Map<String, String> ISP_MAP = MapUtil.ofEntries(
            MapUtil.entry("chinatelecom.com.cn", "电信"),
            MapUtil.entry("chinaunicom.com", "联通"),
            MapUtil.entry("chinamobile.com", "移动")
    );

    // 云市场分配的密钥Id(实名认证)
    @Value("${haomiao.idCardAuth.secretId}")
    private String secretId;

    // 云市场分配的密钥Key(实名认证)
    @Value("${haomiao.idCardAuth.secretKey}")
    private String secretKey;

    // 云市场分配的密钥Id(IP解析)
    @Value("${haomiao.ip.secretId}")
    private String ipSecretId;

    // 云市场分配的密钥Key(IP解析)
    @Value("${haomiao.ip.secretKey}")
    private String ipSecretKey;

    /**
     * 身份证实名认证
     *
     * @param name 姓名
     * @param idCard 身份证
     * @return 调用结果
     */
    public IdCardAuditBo idCardAuditWrap(String name, String idCard) {
        JSONObject authResult = idCardAudit(name, idCard);
        if (null == authResult) {
            return null;
        }

        if (!Objects.equals(authResult.getInteger("apiCode"), 0) || !authResult.containsKey("data")) {
            return null;
        }

        IdCardAuditBo result = new IdCardAuditBo();
        result.setApiType(HAOMIAO.getType());

        Optional.ofNullable(authResult.getJSONObject("data")).ifPresent(data -> {
            result.setCode(data.getInteger("code"));
            result.setMsg(data.getString("msg"));
        });
        return result;
    }

    /**
     * 身份证实名认证
     *
     * @param name 姓名
     * @param idCard 身份证
     * @return 调用结果
     */
    public JSONObject idCardAudit(String name, String idCard) {
        if (StringUtils.isBlank(name) || StringUtils.isBlank(idCard)) {
            return null;
        }

        // 非正式环境不调用
        if (!SpringEnvironmentUtils.isProd()) {
            return null;
        }

        try {
            // 时间
            String datetime = DateUtil.format(new Date(), DATE_TIME_FORMATTER);
            // 签名
            String auth = calcAuthorization(SOURCE, secretId, secretKey, datetime);

            // 查询参数
            Map<String, String> params = new HashMap<>();
            params.put("idCard", UrlUtils.urlEncode(idCard));
            params.put("name", UrlUtils.urlEncode(name));

            String url = UrlUtils.appendParams(API_URL, params);
            String resp = post(url, datetime, auth);
            log.info("毫秒科技-身份证实名认证, idCard={}, name={}, resp={}", idCard, name, resp);
            return JSON.parseObject(resp);
        } catch (Exception e) {
            log.error("毫秒科技-身份证实名认证异常, idCard={}, name={}, req={}", idCard, name, e);
            CountMonitorUtils.errorMonitor(CountMonitorTypeEnum.HAOMIAO_SFZ);
        }
        return null;
    }

    /**
     * IP解析
     *
     * @param ip IP地址
     * @return 调用结果
     */
    public JSONObject ipAnalysis(String ip) {
        if (StringUtils.isBlank(ip)) {
            return null;
        }

        try {
            // 时间
            String datetime = DateUtil.format(new Date(), DATE_TIME_FORMATTER);
            // 签名
            String auth = calcAuthorization(SOURCE, ipSecretId, ipSecretKey, datetime);
            // 调用
            String url = IP_API_URL + "?ip=" + ip;
            String resp = post(url, datetime, auth);
            log.info("毫秒科技-IP解析, ip={}, resp={}", ip, resp);
            return JSON.parseObject(resp);
        } catch (Exception e) {
            log.error("毫秒科技-IP解析异常, ip={}", ip, e);
            CountMonitorUtils.errorMonitor(CountMonitorTypeEnum.HAOMIAO_IP);
        }
        return null;
    }

    private String post(String url, String datetime, String auth) {
        return HttpUtil.createPost(url)
                .header("X-Source", SOURCE)
                .header("X-Date", datetime)
                .header("Authorization", auth)
                .header("Content-Type", "application/x-www-form-urlencoded")
                .setConnectionTimeout(HTTP_TIMEOUT)
                .setReadTimeout(HTTP_TIMEOUT)
                .execute().body();
    }

    private String calcAuthorization(String source, String secretId, String secretKey, String datetime) throws NoSuchAlgorithmException, InvalidKeyException {
        String signStr = "x-date: " + datetime + "\n" + "x-source: " + source;
        Mac mac = Mac.getInstance("HmacSHA1");
        Key sKey = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), mac.getAlgorithm());
        mac.init(sKey);
        byte[] hash = mac.doFinal(signStr.getBytes(StandardCharsets.UTF_8));
        String sig = Base64.encode(hash);
        return "hmac id=\"" + secretId + "\", algorithm=\"hmac-sha1\", headers=\"x-date x-source\", signature=\"" + sig + "\"";
    }
}
