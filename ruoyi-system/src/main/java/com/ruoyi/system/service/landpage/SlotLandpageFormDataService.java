package com.ruoyi.system.service.landpage;

import com.ruoyi.system.entity.landpage.SlotLandpageFormDataEntity;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 广告位日维度落地页表单数据表 Service
 *
 * <AUTHOR>
 * @date 2023-2-20 10:45:27
 */
public interface SlotLandpageFormDataService {

    /**
     * 更新广告位表单数据
     *
     * @param curDate 日期
     * @param slotId 广告位ID
     * @return 影响行数
     */
    int incr(Date curDate, Long slotId);

    /**
     * 更新广告位表单消耗
     *
     * @param curDate 日期
     * @param slotId 广告位ID
     * @param formPrice 表单价格(分)
     * @return 影响行数
     */
    int incrFormConsume(Date curDate, Long slotId, Integer formPrice);

    /**
     * 根据id更新
     */
    Boolean updateById(SlotLandpageFormDataEntity entity);

    /**
     * 根据id获取
     */
    SlotLandpageFormDataEntity selectById(Long id);

    /**
     * 查询广告位落地页表单数据映射
     *
     * @param startDate 起始日期
     * @param endDate 结束日期
     * @param slotIds 广告位ID列表
     * @return 广告位ID_日期-落地页表单数量
     */
    Map<String, Integer> selectMapByDateAndSlotIds(Date startDate, Date endDate, List<Long> slotIds);

    /**
     * 查询广告位落地页表单数据汇总
     *
     * @param startDate 起始日期
     * @param endDate 结束日期
     * @param slotIds 广告位ID列表
     * @return 落地页表单数据汇总
     */
    Integer sumFormDataByDateAndSlotIds(Date startDate, Date endDate, List<Long> slotIds);
}
