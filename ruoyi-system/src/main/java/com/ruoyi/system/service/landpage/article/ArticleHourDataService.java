package com.ruoyi.system.service.landpage.article;

import com.ruoyi.system.bo.landpage.article.ArticleDataBo;
import com.ruoyi.system.bo.landpage.article.ArticleDataParamBo;
import com.ruoyi.system.entity.landpage.article.ArticleHourDataEntity;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 文章时段数据表 Service
 *
 * <AUTHOR>
 * @date 2023-12-1 15:14:16
 */
public interface ArticleHourDataService {

    /**
     * 新增记录
     */
    Boolean insert(ArticleHourDataEntity entity);

    /**
     * 根据id更新
     */
    Boolean updateById(ArticleHourDataEntity entity);

    /**
     * 更新数据
     */
    Boolean update(Long id, Integer requestPv, Integer requestUv);

    /**
     * 根据id获取
     */
    ArticleHourDataEntity selectById(Long id);

    /**
     * 查询文章数据
     */
    ArticleHourDataEntity selectBy(Date curDate, Integer curHour, Long linkId, Long articleId);

    /**
     * 根据条件查询汇总数据
     */
    ArticleDataBo selectSumBy(ArticleDataParamBo param);

    /**
     * 查询文章数据
     */
    List<ArticleDataBo> selectDataBy(ArticleDataParamBo param);

    /**
     * 查询文章今日数据
     *
     * @param articleIds 文章ID列表
     * @return 文章ID-今日数据映射
     */
    Map<Long, ArticleDataBo> selectTodayDataByArticleIds(List<Long> articleIds);
}
