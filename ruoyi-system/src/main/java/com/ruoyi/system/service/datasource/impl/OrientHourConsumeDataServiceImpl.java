package com.ruoyi.system.service.datasource.impl;

import com.ruoyi.system.entity.datashow.OrientHourConsumeData;
import com.ruoyi.system.mapper.datashow.OrientHourConsumeDataMapper;
import com.ruoyi.system.service.datasource.OrientHourConsumeDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 广告配置分时段消耗数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2021-08-30
 */
@Service
public class OrientHourConsumeDataServiceImpl implements OrientHourConsumeDataService {

    @Autowired
    private OrientHourConsumeDataMapper orientHourConsumeDataMapper;

    /**
     * 查询广告配置分时段消耗数据
     * 
     * @param id 广告配置分时段消耗数据ID
     * @return 广告配置分时段消耗数据
     */
    @Override
    public OrientHourConsumeData selectOrientHourConsumeDataById(Long id) {
        return orientHourConsumeDataMapper.selectOrientHourConsumeDataById(id);
    }

    @Override
    public OrientHourConsumeData selectByDateHourAndOrientId(Date curDate, Integer curHour, Long orientId) {
        return orientHourConsumeDataMapper.selectByDateHourAndOrientId(curDate, curHour, orientId);
    }

    @Override
    public int addConsumeData(OrientHourConsumeData orientHourConsumeData) {
        return orientHourConsumeDataMapper.addConsumeData(orientHourConsumeData);
    }

    /**
     * 查询广告配置分时段消耗数据列表
     * 
     * @param orientHourConsumeData 广告配置分时段消耗数据
     * @return 广告配置分时段消耗数据
     */
    @Override
    public List<OrientHourConsumeData> selectOrientHourConsumeDataList(OrientHourConsumeData orientHourConsumeData) {
        return orientHourConsumeDataMapper.selectOrientHourConsumeDataList(orientHourConsumeData);
    }

    /**
     * 新增广告配置分时段消耗数据
     * 
     * @param orientHourConsumeData 广告配置分时段消耗数据
     * @return 结果
     */
    @Override
    public int insertOrientHourConsumeData(OrientHourConsumeData orientHourConsumeData) {
        return orientHourConsumeDataMapper.insertOrientHourConsumeData(orientHourConsumeData);
    }

    /**
     * 修改广告配置日消耗数据
     * 
     * @param orientHourConsumeData 广告配置分时段消耗数据
     * @return 结果
     */
    @Override
    public int updateOrientHourConsumeData(OrientHourConsumeData orientHourConsumeData) {
        return orientHourConsumeDataMapper.updateOrientHourConsumeData(orientHourConsumeData);
    }
}
