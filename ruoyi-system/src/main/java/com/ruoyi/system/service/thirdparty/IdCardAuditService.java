package com.ruoyi.system.service.thirdparty;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.common.BizConfigEnum;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.SpringEnvironmentUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.system.bo.thirdparty.IdCardAuditBo;
import com.ruoyi.system.service.system.ISysConfigService;
import com.ruoyi.system.service.thirdparty.market.HaomiaoApiService;
import com.ruoyi.system.service.thirdparty.market.JuheApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * 身份证实名认证接口
 *
 * <AUTHOR>
 * @date 2022/11/02
 */
@Slf4j
@Service
public class IdCardAuditService {

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private JuheApiService juheApiService;

    @Autowired
    private HaomiaoApiService haomiaoApiService;

    @Autowired
    private ISysConfigService sysConfigService;

    /**
     * 身份证实名认证(只读缓存)
     *
     * @param realName 姓名
     * @param idCard 身份证
     * @return 结果
     */
    public IdCardAuditBo idCardAuditCache(String realName, String idCard) {
        if (StringUtils.isBlank(realName) || StringUtils.isBlank(idCard)) {
            return null;
        }

        // 测试环境返回特定数据
        if (SpringEnvironmentUtils.isTest()) {
            IdCardAuditBo audit = new IdCardAuditBo();
            audit.setApiType(0);
            audit.setCode(0);
            return audit;
        }

        String key = EngineRedisKeyFactory.K046.join(Md5Utils.hash(realName + ":" + idCard));
        String value = redisCache.getCacheObject(key);
        return JSONUtil.isTypeJSONObject(value) ? JSON.parseObject(value, IdCardAuditBo.class) : null;
    }

    /**
     * 身份证实名认证
     *
     * @param realName 姓名
     * @param idCard 身份证
     * @return 结果
     */
    public IdCardAuditBo idCardAudit(String realName, String idCard) {
        if (StringUtils.isBlank(realName) || StringUtils.isBlank(idCard)) {
            return null;
        }
        // 查询缓存
        IdCardAuditBo cacheResult = idCardAuditCache(realName, idCard);
        if (null != cacheResult) {
            return cacheResult;
        }

        IdCardAuditBo result = null;

        // 灰度毫秒科技的接口
        boolean isHit = false;
        String value = sysConfigService.selectConfigByKey(BizConfigEnum.IDCARD_AUDIT_RATIO.getKey());
        if (StringUtils.isNumeric(value)) {
            int ratio = Integer.parseInt(value);
            if (ratio > 0) {
                int nonce = RandomUtil.randomInt(0, 100);
                if (nonce < ratio) {
                    isHit = true;
                    result = haomiaoApiService.idCardAuditWrap(realName, idCard);
                }
            }
        }

        // 兜底查询聚合
        if (null == result || NumberUtils.defaultInt(result.getCode()) > 1) {
            result = juheApiService.idCardAuditWrap(realName, idCard);
        }

        // 聚合查询失败，再查一次毫秒科技
        if (!isHit && null == result) {
            result = haomiaoApiService.idCardAuditWrap(realName, idCard);
        }

        // 缓存结果(匹配缓存6小时，不匹配缓存1小时)
        if (null != result) {
            String key = EngineRedisKeyFactory.K046.join(Md5Utils.hash(realName + ":" + idCard));
            int timeout = IdCardAuditBo.isMatch(result) ? 6 : 1;
            redisCache.setCacheObject(key, JSON.toJSONString(result), timeout, TimeUnit.HOURS);
        }
        return result;
    }
}
