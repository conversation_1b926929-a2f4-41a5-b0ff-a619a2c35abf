package com.ruoyi.system.service.datasource.processor;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.enums.DataDimensionEnum;
import com.ruoyi.common.enums.InnerLogType;
import com.ruoyi.common.utils.BizUtils;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.datashow.DataStatContext;
import com.ruoyi.system.entity.datashow.AdvertSlotConvDayDataEntity;
import com.ruoyi.system.req.datashow.DataStatReq;
import com.ruoyi.system.service.datasource.AdvertSlotConvDayDataService;
import com.ruoyi.system.service.datasource.DataStatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

import static com.ruoyi.common.enums.DataDimensionEnum.ADVERT_SLOT_CONV_DAY;

/**
 * 广告广告位维度后端转化日数据处理器
 *
 * <AUTHOR>
 * @date 2023/9/29
 */
@Slf4j
@Service
public class AdvertSlotConvDayProcessor implements DataProcessor, InitializingBean {

    @Autowired
    private AdvertSlotConvDayDataService advertSlotConvDayDataService;

    @Override
    public DataDimensionEnum getDimension() {
        return ADVERT_SLOT_CONV_DAY;
    }

    @Override
    public boolean validate(DataStatContext context) {
        DataStatReq req = context.getReq();
        return null != req.getDate() && null != req.getAdvertId() && null != req.getSlotId()
                && null != req.getConvType()  && null != req.getConsumerId();
    }

    @Override
    public boolean process(DataStatContext context) {
        DataStatReq req = context.getReq();
        InnerLogType type = context.getType();
        int pv = 1;

        // 查询并初始数据
        AdvertSlotConvDayDataEntity param = new AdvertSlotConvDayDataEntity();
        param.setCurDate(req.getDate());
        param.setAdvertId(req.getAdvertId());
        param.setSlotId(req.getSlotId());
        param.setConvType(req.getConvType());
        AdvertSlotConvDayDataEntity data = advertSlotConvDayDataService.selectBy(param);
        if (null == data) {
            data = new AdvertSlotConvDayDataEntity();
            data.setCurDate(req.getDate());
            data.setAdvertId(req.getAdvertId());
            data.setSlotId(req.getSlotId());
            data.setConvType(req.getConvType());
            advertSlotConvDayDataService.insert(data);
            data = advertSlotConvDayDataService.selectBy(param);
        }

        // 计算uv
        String datStr = DateUtils.dateTime(req.getDate());
        String uvKey = EngineRedisKeyFactory.K012.join(type.getType(), req.getConvType(), req.getAdvertId(), req.getSlotId(), datStr);
        int uv = BizUtils.countUv(uvKey, String.valueOf(req.getConsumerId()));

        // 数据
        AdvertSlotConvDayDataEntity updateData = new AdvertSlotConvDayDataEntity();
        updateData.setId(data.getId());
        updateData.setConvPvAdd(pv);
        updateData.setConvUvAdd(uv);
        updateData.setConvPriceAdd(parsePayPrice(req.getConvExt()));
        return advertSlotConvDayDataService.updateById(updateData) > 0;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        DataStatService.register(this);
    }

    /**
     * 从转化扩展参数中获取支付金额
     */
    private BigDecimal parsePayPrice(JSONObject convExt) {
        if (null != convExt) {
            try {
                return convExt.getBigDecimal("price");
            } catch (Exception e) {
                log.error("parsePayPrice error");
            }
        }
        return null;
    }
}
