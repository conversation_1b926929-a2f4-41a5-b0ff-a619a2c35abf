package com.ruoyi.system.service.quickapp;

import com.ruoyi.system.entity.quickapp.QuickappContentEntity;

import java.util.List;

/**
 * 快应用内容表 Service
 *
 * <AUTHOR>
 * @date 2022-4-1 17:07:39
 */
public interface QuickappContentService {

    /**
     * 新增记录
     */
    Boolean insert(QuickappContentEntity entity);

    /**
     * 根据id删除
     */
    Boolean deleteById(Long id);

    /**
     * 根据id更新
     */
    Boolean updateById(QuickappContentEntity entity);

    /**
     * 根据id获取
     */
    QuickappContentEntity selectById(Long id);

    /**
     * 模糊搜索花
     */
    QuickappContentEntity selectByTitle(String title);

    /**
     * 查询100条花
     * @return
     */
    List<QuickappContentEntity> selectList();
}
