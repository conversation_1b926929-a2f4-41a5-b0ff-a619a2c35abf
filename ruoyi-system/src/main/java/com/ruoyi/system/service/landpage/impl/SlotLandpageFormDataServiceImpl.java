package com.ruoyi.system.service.landpage.impl;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.system.entity.landpage.SlotLandpageFormDataEntity;
import com.ruoyi.system.mapper.landpage.SlotLandpageFormDataMapper;
import com.ruoyi.system.service.landpage.SlotLandpageFormDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 广告位日维度落地页表单数据表 Service
 *
 * <AUTHOR>
 * @date 2023-2-20 10:45:27
 */
@Slf4j
@Service
public class SlotLandpageFormDataServiceImpl implements SlotLandpageFormDataService {

    @Autowired
    private SlotLandpageFormDataMapper slotLandpageFormDataMapper;


    @Override
    public int incr(Date curDate, Long slotId) {
        if (null == curDate || null == slotId) {
            return 0;
        }
        try {
            SlotLandpageFormDataEntity data = slotLandpageFormDataMapper.selectBy(curDate, slotId);
            if (null == data) {
                data = new SlotLandpageFormDataEntity();
                data.setCurDate(curDate);
                data.setSlotId(slotId);
                slotLandpageFormDataMapper.insert(data);
                data = slotLandpageFormDataMapper.selectBy(curDate, slotId);
            }
            if (null != data) {
                SlotLandpageFormDataEntity updateData = new SlotLandpageFormDataEntity();
                updateData.setId(data.getId());
                updateData.setFormCountAdd(1);
                slotLandpageFormDataMapper.updateById(updateData);
            }
        } catch (Exception e) {
            log.error("广告位日维度落地页表单数据更新异常, curDate={}, slotId={}", curDate, slotId, e);
        }
        return 0;
    }

    @Override
    public int incrFormConsume(Date curDate, Long slotId, Integer formPrice) {
        if (null == curDate || null == slotId) {
            return 0;
        }
        try {
            SlotLandpageFormDataEntity data = slotLandpageFormDataMapper.selectBy(curDate, slotId);
            if (null == data) {
                data = new SlotLandpageFormDataEntity();
                data.setCurDate(curDate);
                data.setSlotId(slotId);
                slotLandpageFormDataMapper.insert(data);
                data = slotLandpageFormDataMapper.selectBy(curDate, slotId);
            }
            if (null != data) {
                SlotLandpageFormDataEntity updateData = new SlotLandpageFormDataEntity();
                updateData.setId(data.getId());
                updateData.setFormConsumeAdd(formPrice);
                slotLandpageFormDataMapper.updateById(updateData);
            }
        } catch (Exception e) {
            log.error("广告位日维度落地页表单消费数据更新异常, curDate={}, slotId={}", curDate, slotId, e);
        }
        return 0;
    }

    @Override
    public Boolean updateById(SlotLandpageFormDataEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return slotLandpageFormDataMapper.updateById(entity) > 0;
    }

    @Override
    public SlotLandpageFormDataEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return slotLandpageFormDataMapper.selectById(id);
    }
    @Override
    public Map<String, Integer> selectMapByDateAndSlotIds(Date startDate, Date endDate, List<Long> slotIds) {
        List<SlotLandpageFormDataEntity> list = slotLandpageFormDataMapper.selectListByDateAndSlotIds(startDate, endDate, slotIds);
        return list.stream().collect(Collectors.toMap(s -> s.getSlotId() + "_" + DateUtil.formatDate(s.getCurDate()), SlotLandpageFormDataEntity::getFormCount, (v1, v2) -> v2));
    }

    @Override
    public Integer sumFormDataByDateAndSlotIds(Date startDate, Date endDate, List<Long> slotIds) {
        return slotLandpageFormDataMapper.sumFormDataByDateAndSlotIds(startDate, endDate, slotIds);
    }
}
