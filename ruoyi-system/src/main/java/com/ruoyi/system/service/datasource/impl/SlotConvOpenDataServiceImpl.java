package com.ruoyi.system.service.datasource.impl;

import com.ruoyi.system.entity.datashow.SlotConvOpenDataEntity;
import com.ruoyi.system.mapper.datashow.SlotConvOpenDataMapper;
import com.ruoyi.system.service.datasource.SlotConvOpenDataService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 媒体可见的转化数据表 Service
 *
 * <AUTHOR>
 * @date 2023-3-10 11:10:04
 */
@Service
public class SlotConvOpenDataServiceImpl implements SlotConvOpenDataService {

    @Autowired
    private SlotConvOpenDataMapper slotConvOpenDataMapper;

    @Override
    public int incr(Long slotId, Date curDate) {
        SlotConvOpenDataEntity data = slotConvOpenDataMapper.selectBy(slotId, curDate);
        if (null == data) {
            data = new SlotConvOpenDataEntity();
            data.setCurDate(curDate);
            data.setSlotId(slotId);
            slotConvOpenDataMapper.insert(data);
            data = slotConvOpenDataMapper.selectBy(slotId, curDate);
        }
        return slotConvOpenDataMapper.incr(data.getId());
    }

    @Override
    public Map<Long, Integer> selectMap(List<Long> slotIds, Date curDate) {
        if (CollectionUtils.isEmpty(slotIds)) {
            return Collections.emptyMap();
        }
        List<SlotConvOpenDataEntity> list = slotConvOpenDataMapper.selectListBySlotIdsAndDate(slotIds, curDate);
        return list.stream().collect(Collectors.toMap(SlotConvOpenDataEntity::getSlotId, SlotConvOpenDataEntity::getConv, (v1, v2) -> v2));

    }
}
