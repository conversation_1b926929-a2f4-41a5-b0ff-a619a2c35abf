package com.ruoyi.system.service.advertiser.impl;

import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.system.bo.advertiser.finance.AdvertiserBalanceStatisticBo;
import com.ruoyi.system.entity.advertiser.finance.AdvertiserBalanceEntity;
import com.ruoyi.system.mapper.advertiser.finance.AdvertiserBalanceMapper;
import com.ruoyi.system.req.advertiser.finance.AdvertiserBalanceUpdateReq;
import com.ruoyi.system.service.advertiser.DspAdvertiserConsumeRecordService;
import com.ruoyi.system.service.advertiser.fiance.AdvertiserBalanceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 广告主账户余额接口实现
 *
 * <AUTHOR>
 * @date 2022-3-18 17:57:16
 */
@Slf4j
@Service
public class AdvertiserBalanceServiceImpl implements AdvertiserBalanceService {

    @Autowired
    private AdvertiserBalanceMapper advertiserBalanceMapper;

    @Autowired
    private DspAdvertiserConsumeRecordService dspAdvertiserConsumeRecordService;

    @Override
    public int create(Long accountId) {
        if (Objects.isNull(accountId)) {
            return 0;
        }
        try {
            AdvertiserBalanceEntity entity = new AdvertiserBalanceEntity();
            entity.setAccountId(accountId);
            return advertiserBalanceMapper.insert(entity);
        } catch (DuplicateKeyException e) {
            log.error("AdvertiserBalanceEntity 插入冲突");
        }
        return 0;
    }

    @Override
    public int updateBalance(AdvertiserBalanceUpdateReq req) {
        if (Objects.isNull(req)) {
            return 0;
        }
        return advertiserBalanceMapper.updateBalance(req);
    }

    @Override
    public AdvertiserBalanceEntity selectOrCreate(Long accountId) {
        if (Objects.isNull(accountId)) {
            return null;
        }
        AdvertiserBalanceEntity entity = selectByAccountId(accountId);
        if (null != entity) {
            return entity;
        }
        create(accountId);
        return selectByAccountId(accountId);
    }

    @Override
    public List<AdvertiserBalanceEntity> selectList(List<Long> accountIds) {
        return advertiserBalanceMapper.selectList(accountIds);
    }

    @Override
    public AdvertiserBalanceStatisticBo selectStatisticBalance(List<Long> accountIds, List<Long> excludeAccountIds) {
        return advertiserBalanceMapper.selectStatisticBalance(accountIds, excludeAccountIds);
    }

    @Override
    public AdvertiserBalanceEntity selectByAccountId(Long accountId) {
        if (Objects.isNull(accountId)) {
            return null;
        }
        return advertiserBalanceMapper.selectByAccountId(accountId);
    }

    @Override
    public Integer selectTotalAmountByAccountId(Long accountId) {
        if (Objects.isNull(accountId)) {
            return 0;
        }
        AdvertiserBalanceEntity balance = advertiserBalanceMapper.selectByAccountId(accountId);
        return null != balance ? NumberUtils.defaultInt(balance.getTotalAmount()) : 0;
    }

    @Override
    public Integer sumTotalAmountByAccountIds(List<Long> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return 0;
        }
        return advertiserBalanceMapper.sumByAccountIds(accountIds);
    }

    @Override
    public Integer selectAdjustTotalAmountByAccountId(Long accountId) {
        // 广告主原余额
        Integer balance = selectTotalAmountByAccountId(accountId);
        // 离线数据广告主特殊处理, 账户余额的差额计算.offset=订正后的值-原值
        Integer consumeOffset = dspAdvertiserConsumeRecordService.consumeOffsetForCrm(accountId);
        return balance - consumeOffset;
    }

    @Override
    public Map<Long, Integer> selectAdvertiserBalanceMap(List<Long> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return Collections.emptyMap();
        }

        List<AdvertiserBalanceEntity> list = advertiserBalanceMapper.selectList(accountIds);
        return list.stream().collect(Collectors.toMap(AdvertiserBalanceEntity::getAccountId, AdvertiserBalanceEntity::getTotalAmount, (o, n) -> n));
    }
}
