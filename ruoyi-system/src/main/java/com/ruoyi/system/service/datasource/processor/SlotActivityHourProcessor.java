package com.ruoyi.system.service.datasource.processor;

import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.DataDimensionEnum;
import com.ruoyi.common.enums.InnerLogType;
import com.ruoyi.common.utils.BizUtils;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.adengine.SlotCacheDto;
import com.ruoyi.system.domain.datashow.DataStatContext;
import com.ruoyi.system.entity.datashow.SlotActivityHourData;
import com.ruoyi.system.req.datashow.DataStatReq;
import com.ruoyi.system.service.datasource.DataStatService;
import com.ruoyi.system.service.datasource.SlotActivityHourDataService;
import com.ruoyi.system.service.engine.cache.SlotCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

import static com.ruoyi.common.enums.DataDimensionEnum.SLOT_ACTIVITY_HOUR;

/**
 * 广告位活动维度时段数据处理器
 *
 * <AUTHOR>
 * @date 2023/10/19
 */
@Slf4j
@Service
public class SlotActivityHourProcessor implements DataProcessor, InitializingBean {

    @Autowired
    private SlotActivityHourDataService slotActivityHourDataService;

    @Autowired
    private SlotCacheService slotCacheService;

    @Autowired
    private RedisCache redisCache;

    @Override
    public DataDimensionEnum getDimension() {
        return SLOT_ACTIVITY_HOUR;
    }

    @Override
    public boolean validate(DataStatContext context) {
        DataStatReq req = context.getReq();
        return null != req.getDate() && null != req.getHour() && null != req.getSlotId() && null != req.getActivityId() && null != req.getConsumerId();
    }

    @Override
    public boolean process(DataStatContext context) {
        DataStatReq req = context.getReq();
        InnerLogType type = context.getType();
        int pv = 1;
        int uv = 0;

        // 计算uv
        switch (type) {
            case LANDPAGE_EXPOSURE:
            case LANDPAGE_CLICK:
                String datStr = DateUtils.dateTime(req.getDate());
                String uvKey = EngineRedisKeyFactory.K107.join(type.getType(), req.getAdvertId(), datStr, req.getHour());
                uv = BizUtils.countUv(uvKey, String.valueOf(req.getConsumerId()), 1, TimeUnit.HOURS);
                break;
            default:
                break;
        }

        // 数据
        SlotActivityHourData updateData = new SlotActivityHourData();
        updateData.setId(getDataId(req));

        switch (type) {
            case ADVERT_BILLING:
                updateData.setConsumeAdd(req.getUnitPrice());
                break;
            case LANDPAGE_EXPOSURE:
                updateData.setLpExposurePvAdd(pv);
                updateData.setLpExposureUvAdd(uv);
                break;
            case LANDPAGE_CLICK:
                updateData.setLpClickPvAdd(pv);
                updateData.setLpClickUvAdd(uv);
                updateData.setTheoryCostAdd(req.getAssessCost());
                break;
            default:
                break;
        }
        return slotActivityHourDataService.updateSlotActivityHourData(updateData) > 0;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        DataStatService.register(this);
    }

    /**
     * 通过缓存获取数据ID
     */
    private Long getDataId(DataStatReq req) {
        String key = EngineRedisKeyFactory.K022.join("SlotActivityHourData", req.getDateStr(), req.getHour(), req.getSlotId(), req.getActivityId());
        Long dataId = redisCache.getCacheObject(key);
        if (null != dataId) {
            return dataId;
        }

        // 查询并初始数据
        SlotActivityHourData data = slotActivityHourDataService.selectBy(req.getSlotId(), req.getActivityId(), req.getDate(), req.getHour());
        if (null == data) {
            SlotCacheDto slot = slotCacheService.getSlotCache(req.getSlotId());
            if (null != slot && null != slot.getAccountId() && null != req.getAppId()) {
                data = new SlotActivityHourData();
                data.setCurDate(req.getDate());
                data.setCurHour(req.getHour());
                data.setAccountId(slot.getAccountId());
                data.setAppId(req.getAppId());
                data.setSlotId(req.getSlotId());
                data.setActivityId(req.getActivityId());
                slotActivityHourDataService.insertSlotActivityHourData(data);
                data = slotActivityHourDataService.selectBy(req.getSlotId(), req.getActivityId(), req.getDate(), req.getHour());
            }
        }
        if (null == data) {
            return null;
        }
        redisCache.setCacheObject(key, data.getId(), 1, TimeUnit.HOURS);
        return data.getId();
    }
}
