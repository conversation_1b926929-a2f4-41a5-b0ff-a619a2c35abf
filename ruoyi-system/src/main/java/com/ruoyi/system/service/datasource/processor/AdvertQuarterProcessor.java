package com.ruoyi.system.service.datasource.processor;

import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.DataDimensionEnum;
import com.ruoyi.common.enums.InnerLogType;
import com.ruoyi.common.enums.advert.ConvType;
import com.ruoyi.common.utils.BizUtils;
import com.ruoyi.system.domain.datashow.DataStatContext;
import com.ruoyi.system.entity.datashow.AdvertQuarterDataEntity;
import com.ruoyi.system.req.datashow.DataStatReq;
import com.ruoyi.system.service.datasource.AdvertQuarterDataService;
import com.ruoyi.system.service.datasource.DataStatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

import static com.ruoyi.common.enums.DataDimensionEnum.ADVERT_QUARTER;

/**
 * 广告维度时刻数据处理器
 *
 * <AUTHOR>
 * @date 2022/10/21
 */
@Slf4j
@Service
public class AdvertQuarterProcessor implements DataProcessor, InitializingBean {

    @Autowired
    private AdvertQuarterDataService advertQuarterDataService;

    @Autowired
    private RedisCache redisCache;

    @Override
    public DataDimensionEnum getDimension() {
        return ADVERT_QUARTER;
    }

    @Override
    public boolean validate(DataStatContext context) {
        DataStatReq req = context.getReq();
        return null != req.getDate() && null != req.getQuarter() && null != req.getAdvertId() && null != req.getConsumerId();
    }

    @Override
    public boolean process(DataStatContext context) {
        DataStatReq req = context.getReq();
        InnerLogType type = context.getType();
        int pv = 1;
        int uv = 0;

        // 计算uv
        switch (type) {
            case ADVERT_LAUNCH:
            case ADVERT_EXPOSURE:
            case ADVERT_CLICK:
            case ADVERT_BILLING:
            case LANDPAGE_EXPOSURE:
            case LANDPAGE_CLICK:
            case CONVERT_EVENT:
                String uvKey = EngineRedisKeyFactory.K012.join(type.getType(), req.getAdvertId(), req.getDateStr(), req.getQuarter());
                uv = BizUtils.countUv(uvKey, String.valueOf(req.getConsumerId()), 15, TimeUnit.MINUTES);
                break;
            default:
                break;
        }

        // 数据
        AdvertQuarterDataEntity updateData = new AdvertQuarterDataEntity();
        updateData.setId(getDataId(req));

        switch (type) {
            case ADVERT_LAUNCH:
                updateData.setAdLaunchPvAdd(pv);
                updateData.setAdLaunchUvAdd(uv);
                break;
            case ADVERT_EXPOSURE:
                updateData.setExposurePvAdd(pv);
                updateData.setExposureUvAdd(uv);
                break;
            case ADVERT_CLICK:
                updateData.setClickPvAdd(pv);
                updateData.setClickUvAdd(uv);
                break;
            case ADVERT_BILLING:
                updateData.setBillingClickPvAdd(pv);
                updateData.setBillingClickUvAdd(uv);
                updateData.setConsumeAdd(req.getUnitPrice());
                break;
            case LANDPAGE_EXPOSURE:
                updateData.setLpExposurePvAdd(pv);
                updateData.setLpExposureUvAdd(uv);
                break;
            case LANDPAGE_CLICK:
                updateData.setLpClickPvAdd(pv);
                updateData.setLpClickUvAdd(uv);
                break;
            case CONVERT_EVENT:
                if (ConvType.isPay(req.getConvType())) {
                    updateData.setPayAdd(pv);
                } else if (ConvType.isRegister(req.getConvType())) {
                    updateData.setRegisterAdd(pv);
                }
                break;
            default:
                break;
        }
        return advertQuarterDataService.updateById(updateData) > 0;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        DataStatService.register(this);
    }

    /**
     * 通过缓存获取数据ID
     */
    private Long getDataId(DataStatReq req) {
        String key = EngineRedisKeyFactory.K022.join("AdvertQuarterDataEntity", req.getDateStr(), req.getQuarter(), req.getAdvertId());
        Long dataId = redisCache.getCacheObject(key);
        if (null != dataId) {
            return dataId;
        }

        // 查询并初始数据
        AdvertQuarterDataEntity param = new AdvertQuarterDataEntity();
        param.setCurDate(req.getDate());
        param.setCurQuarter(req.getQuarter());
        param.setAdvertId(req.getAdvertId());
        AdvertQuarterDataEntity data = advertQuarterDataService.selectBy(param);
        if (null == data) {
            data = new AdvertQuarterDataEntity();
            data.setCurDate(req.getDate());
            data.setCurQuarter(req.getQuarter());
            data.setAdvertId(req.getAdvertId());
            advertQuarterDataService.insert(data);
            data = advertQuarterDataService.selectBy(param);
        }
        redisCache.setCacheObject(key, data.getId(), 16, TimeUnit.MINUTES);
        return data.getId();
    }
}
