package com.ruoyi.system.service.landpage.impl;

import com.ruoyi.system.entity.landpage.ExternalLandpageDayData;
import com.ruoyi.system.mapper.landpage.ExternalLandpageDayDataMapper;
import com.ruoyi.system.service.landpage.ExternalLandpageDayDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 外部落地页日数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-10-09
 */
@Service
public class ExternalLandpageDayDataServiceImpl implements ExternalLandpageDayDataService {

    @Autowired
    private ExternalLandpageDayDataMapper externalLandpageDayDataMapper;

    @Override
    public ExternalLandpageDayData selectBy(Long advertiserId, String landpageId, Date curDate) {
        return externalLandpageDayDataMapper.selectBy(advertiserId, landpageId, curDate);
    }

    /**
     * 查询外部落地页日数据
     *
     * @param id 外部落地页日数据ID
     * @return 外部落地页日数据
     */
    @Override
    public ExternalLandpageDayData selectExternalLandpageDayDataById(Long id)
    {
        return externalLandpageDayDataMapper.selectExternalLandpageDayDataById(id);
    }

    /**
     * 查询外部落地页日数据列表
     *
     * @param externalLandpageDayData 外部落地页日数据
     * @return 外部落地页日数据
     */
    @Override
    public List<ExternalLandpageDayData> selectExternalLandpageDayDataList(ExternalLandpageDayData externalLandpageDayData)
    {
        return externalLandpageDayDataMapper.selectExternalLandpageDayDataList(externalLandpageDayData);
    }

    /**
     * 新增外部落地页日数据
     *
     * @param externalLandpageDayData 外部落地页日数据
     * @return 结果
     */
    @Override
    public int insertExternalLandpageDayData(ExternalLandpageDayData externalLandpageDayData)
    {
        return externalLandpageDayDataMapper.insertExternalLandpageDayData(externalLandpageDayData);
    }

    /**
     * 修改外部落地页日数据
     *
     * @param externalLandpageDayData 外部落地页日数据
     * @return 结果
     */
    @Override
    public int updateExternalLandpageDayData(ExternalLandpageDayData externalLandpageDayData)
    {
        return externalLandpageDayDataMapper.updateExternalLandpageDayData(externalLandpageDayData);
    }
}
