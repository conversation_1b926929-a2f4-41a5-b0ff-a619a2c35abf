package com.ruoyi.system.service.datasource.impl;

import java.util.List;

import com.ruoyi.system.entity.datashow.MaterialSlotDayData;
import com.ruoyi.system.service.datasource.MaterialSlotDayDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.datashow.MaterialSlotDayDataMapper;

/**
 * 素材广告位日数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-08-25
 */
@Service
public class MaterialSlotDayDataServiceImpl implements MaterialSlotDayDataService {

    @Autowired
    private MaterialSlotDayDataMapper materialSlotDayDataMapper;

    @Override
    public MaterialSlotDayData selectBy(MaterialSlotDayData param) {
        return materialSlotDayDataMapper.selectBy(param);
    }

    /**
     * 查询素材广告位日数据列表
     *
     * @param param 查询条件
     * @return 素材广告位日数据
     */
    @Override
    public List<MaterialSlotDayData> selectMaterialSlotDayDataList(MaterialSlotDayData param) {
        return materialSlotDayDataMapper.selectMaterialSlotDayDataList(param);
    }

    /**
     * 新增素材广告位日数据
     *
     * @param param 素材广告位日数据
     * @return 结果
     */
    @Override
    public int insertMaterialSlotDayData(MaterialSlotDayData param) {
        return materialSlotDayDataMapper.insertMaterialSlotDayData(param);
    }

    /**
     * 修改素材广告位日数据
     *
     * @param param 素材广告位日数据
     * @return 结果
     */
    @Override
    public int updateMaterialSlotDayData(MaterialSlotDayData param) {
        if (null == param.getId()) {
            return 0;
        }
        return materialSlotDayDataMapper.updateMaterialSlotDayData(param);
    }
}
