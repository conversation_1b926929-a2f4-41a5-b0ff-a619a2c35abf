package com.ruoyi.system.service.landpage.impl;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.system.bo.landpage.ExternalLandpageRecordBo;
import com.ruoyi.system.bo.landpage.ExternalLandpageRecordSelectBo;
import com.ruoyi.system.entity.landpage.ExternalLandpageRecord;
import com.ruoyi.system.mapper.landpage.ExternalLandpageRecordMapper;
import com.ruoyi.system.service.landpage.ExternalLandpageRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 外部落地页单记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-10-09
 */
@Service
public class ExternalLandpageRecordServiceImpl implements ExternalLandpageRecordService {

    @Autowired
    private ExternalLandpageRecordMapper externalLandpageRecordMapper;

    /**
     * 查询外部落地页单记录
     *
     * @param id 外部落地页单记录ID
     * @return 外部落地页单记录
     */
    @Override
    public ExternalLandpageRecord selectExternalLandpageRecordById(Long id) {
        return externalLandpageRecordMapper.selectExternalLandpageRecordById(id);
    }

    @Override
    public List<ExternalLandpageRecordBo> selectList(ExternalLandpageRecordSelectBo param) {
        if (null != param.getSubmitDateEnd()) {
            param.setSubmitDateEnd(DateUtil.endOfDay(param.getSubmitDateEnd()));
        }
        if (null != param.getSendDateEnd()) {
            param.setSendDateEnd(DateUtil.endOfDay(param.getSendDateEnd()));
        }
        return externalLandpageRecordMapper.selectList(param);
    }

    @Override
    public List<Long> selectAdvertiserIds() {
        return externalLandpageRecordMapper.selectAdvertiserIds();
    }

    @Override
    public List<Long> selectAgentIds() {
        return externalLandpageRecordMapper.selectAgentIds();
    }

    /**
     * 新增外部落地页单记录
     *
     * @param externalLandpageRecord 外部落地页单记录
     * @return 结果
     */
    @Override
    public int insertExternalLandpageRecord(ExternalLandpageRecord externalLandpageRecord) {
        return externalLandpageRecordMapper.insertExternalLandpageRecord(externalLandpageRecord);
    }

    /**
     * 修改外部落地页单记录
     *
     * @param externalLandpageRecord 外部落地页单记录
     * @return 结果
     */
    @Override
    public int updateExternalLandpageRecord(ExternalLandpageRecord externalLandpageRecord) {
        return externalLandpageRecordMapper.updateExternalLandpageRecord(externalLandpageRecord);
    }

    @Override
    public int batchInsert(List<ExternalLandpageRecord> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return 0;
        }
        return externalLandpageRecordMapper.batchInsert(entities);
    }

    @Override
    public boolean batchUpdateExportStatus(List<Long> ids) {
        if(CollectionUtils.isEmpty(ids)){
            return false;
        }
        return externalLandpageRecordMapper.batchUpdateExportStatus(ids) > 0;
    }

    @Override
    public Set<String> selectExternalNoSet(List<String> externalNoList) {
        List<String> list = externalLandpageRecordMapper.selectExternalNo(externalNoList);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptySet();
        }
        return new HashSet<>(list);
    }

    @Override
    public Map<String, ExternalLandpageRecord> selectMapByExternalNo(List<String> externalNoList) {
        if (CollectionUtils.isEmpty(externalNoList)) {
            return Collections.emptyMap();
        }
        List<ExternalLandpageRecord> list = externalLandpageRecordMapper.selectListByExternalNo(externalNoList);
        return list.stream().collect(Collectors.toMap(ExternalLandpageRecord::getExternalNo, Function.identity(), (v1, v2) -> v2));
    }
}
