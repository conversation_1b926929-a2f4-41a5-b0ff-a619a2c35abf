package com.ruoyi.system.service.landpage.article;

import com.ruoyi.system.entity.landpage.article.ArticleCompetitorUrlEntity;

/**
 * 公众号阅读竞品文章 Service
 *
 * <AUTHOR>
 * @date 2024-9-20 11:47:44
 */
public interface ArticleCompetitorUrlService {

    /**
     * 新增记录
     */
    Boolean insert(ArticleCompetitorUrlEntity entity);

    /**
     * 根据id更新
     */
    Boolean updateById(ArticleCompetitorUrlEntity entity);

    /**
     * 根据id获取
     */
    ArticleCompetitorUrlEntity selectById(Long id);
}
