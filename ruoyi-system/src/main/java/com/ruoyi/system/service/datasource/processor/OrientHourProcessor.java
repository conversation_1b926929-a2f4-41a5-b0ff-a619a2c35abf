package com.ruoyi.system.service.datasource.processor;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.DataDimensionEnum;
import com.ruoyi.common.enums.InnerLogType;
import com.ruoyi.common.enums.advert.ConvType;
import com.ruoyi.common.utils.BizUtils;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.system.bo.advert.OrientHourDataUpdateParam;
import com.ruoyi.system.domain.datashow.DataStatContext;
import com.ruoyi.system.entity.datashow.OrientHourDataEntity;
import com.ruoyi.system.req.datashow.DataStatReq;
import com.ruoyi.system.service.datasource.DataStatService;
import com.ruoyi.system.service.datasource.OrientHourDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.concurrent.TimeUnit;

import static com.ruoyi.common.enums.DataDimensionEnum.ORIENT_HOUR;

/**
 * 配置维度时段数据处理器
 *
 * <AUTHOR>
 * @date 2023/7/11
 */
@Slf4j
@Service
public class OrientHourProcessor implements DataProcessor, InitializingBean {

    @Autowired
    private OrientHourDataService orientHourDataService;

    @Autowired
    private RedisCache redisCache;

    @Override
    public DataDimensionEnum getDimension() {
        return ORIENT_HOUR;
    }

    @Override
    public boolean validate(DataStatContext context) {
        DataStatReq req = context.getReq();
        return null != req.getDate() && null != req.getHour() && null != req.getOrientId()
                && null != req.getConsumerId() && null != req.getSlotId() && null != req.getActivityId();
    }

    @Override
    public boolean process(DataStatContext context) {
        DataStatReq req = context.getReq();
        InnerLogType type = context.getType();
        int pv = 1;
        int uv = 0;

        // 计算uv
        switch (type) {
            case ADVERT_LAUNCH:
            case ADVERT_EXPOSURE:
            case ADVERT_CLICK:
            case LANDPAGE_EXPOSURE:
            case LANDPAGE_CLICK:
            case ADVERT_BILLING:
            case BLIND_BOX_POPUP_CLICK:
                String datStr = DateUtils.dateTime(req.getDate());
                String uvKey = EngineRedisKeyFactory.K083.join(type.getType(), req.getOrientId(), datStr, req.getHour(), req.getSlotId(), req.getActivityId());
                uv = BizUtils.countUv(uvKey, String.valueOf(req.getConsumerId()), 1, TimeUnit.HOURS);
                break;
            default:
                break;
        }

        // 数据
        OrientHourDataUpdateParam updateOrientData = new OrientHourDataUpdateParam();
        updateOrientData.setId(getDataId(req));

        switch (type) {
            case ADVERT_LAUNCH:
                updateOrientData.setAdLaunchPvAdd(pv);
                updateOrientData.setAdLaunchUvAdd(uv);
                break;
            case ADVERT_EXPOSURE:
                updateOrientData.setExposurePvAdd(pv);
                updateOrientData.setExposureUvAdd(uv);
                break;
            case ADVERT_CLICK:
                updateOrientData.setClickPvAdd(pv);
                updateOrientData.setClickUvAdd(uv);
                break;
            case LANDPAGE_EXPOSURE:
                updateOrientData.setLpExposurePvAdd(pv);
                updateOrientData.setLpExposureUvAdd(uv);
                break;
            case LANDPAGE_CLICK:
                updateOrientData.setLpClickPvAdd(pv);
                updateOrientData.setLpClickUvAdd(uv);
                break;
            case ADVERT_BILLING:
                updateOrientData.setBillingClickPvAdd(pv);
                updateOrientData.setBillingClickUvAdd(uv);
                updateOrientData.setConsumeAdd(NumberUtils.defaultInt(req.getUnitPrice()));
                break;
            case BLIND_BOX_POPUP_CLICK:
                updateOrientData.setTakePvAdd(pv);
                updateOrientData.setTakeUvAdd(uv);
                break;
            case CONVERT_EVENT:
                ConvType convType = ConvType.getByType(req.getConvType());
                switch (convType) {
                    case SUBMIT:
                        updateOrientData.setFormAdd(pv);
                        break;
                    case PAY:
                        updateOrientData.setPayAdd(pv);
                        updateOrientData.setPayPriceAdd(parsePayPrice(req.getConvExt()));
                        break;
                    case REFUND:
                        updateOrientData.setRefundAdd(pv);
                        break;
                    case REGISTER:
                        updateOrientData.setRegisterAdd(pv);
                        break;
                    case DELIVERY:
                        updateOrientData.setDeliveryAdd(pv);
                        break;
                    case COMPLAIN:
                        updateOrientData.setComplainAdd(pv);
                        break;
                    case APP_DOWNLOAD:
                        updateOrientData.setAppDownloadAdd(pv);
                        break;
                    case APP_ACTIVE:
                        updateOrientData.setAppActiveAdd(pv);
                        break;
                    case APP_REGISTER:
                        updateOrientData.setAppRegisterAdd(pv);
                        break;
                    default:
                        break;
                }
                break;
            default:
                break;
        }
        return orientHourDataService.updateById(updateOrientData) > 0;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        DataStatService.register(this);
    }

    /**
     * 从转化扩展参数中获取支付金额
     */
    private BigDecimal parsePayPrice(JSONObject convExt) {
        if (null != convExt) {
            try {
                return convExt.getBigDecimal("price");
            } catch (Exception e) {
                log.error("parsePayPrice error");
            }
        }
        return null;
    }

    /**
     * 通过缓存获取数据ID
     */
    private Long getDataId(DataStatReq req) {
        String key = EngineRedisKeyFactory.K022.join("OrientHourDataEntity", req.getDateStr(), req.getHour(), req.getOrientId(), req.getSlotId(), req.getActivityId());
        Long dataId = redisCache.getCacheObject(key);
        if (null != dataId) {
            return dataId;
        }

        // 查询并初始数据
        OrientHourDataEntity orientParam = new OrientHourDataEntity();
        orientParam.setCurDate(req.getDate());
        orientParam.setCurHour(req.getHour());
        orientParam.setOrientId(req.getOrientId());
        orientParam.setSlotId(req.getSlotId());
        orientParam.setActivityId(req.getActivityId());
        OrientHourDataEntity data = orientHourDataService.selectBy(orientParam);
        if (null == data) {
            data = new OrientHourDataEntity();
            data.setCurDate(req.getDate());
            data.setCurHour(req.getHour());
            data.setAdvertId(req.getAdvertId());
            data.setOrientId(req.getOrientId());
            data.setAppId(req.getAppId());
            data.setSlotId(req.getSlotId());
            data.setActivityId(req.getActivityId());
            orientHourDataService.insert(data);
            data = orientHourDataService.selectBy(orientParam);
        }
        redisCache.setCacheObject(key, data.getId(), 1, TimeUnit.HOURS);
        return data.getId();
    }
}
