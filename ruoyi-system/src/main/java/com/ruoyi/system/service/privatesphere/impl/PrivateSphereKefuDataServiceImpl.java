package com.ruoyi.system.service.privatesphere.impl;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.system.bo.privatesphere.PrivateSphereKefuDataListBO;
import com.ruoyi.system.entity.privatesphere.PrivateSphereKefuDataEntity;
import com.ruoyi.system.mapper.privatesphere.PrivateSphereKefuDataMapper;
import com.ruoyi.system.service.privatesphere.PrivateSphereKefuDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 私域客服数据表 Service
 *
 * <AUTHOR>
 * @date 2023-3-6 18:51:08
 */
@Service
public class PrivateSphereKefuDataServiceImpl implements PrivateSphereKefuDataService {
    @Autowired
    private PrivateSphereKefuDataMapper privateSphereKefuDataMapper;

    @Override
    public Boolean insert(PrivateSphereKefuDataEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return privateSphereKefuDataMapper.insert(entity) > 0;
    }

    @Override
    public Boolean deleteById(Long id) {
        if (Objects.isNull(id)) {
            return false;
        }
        return privateSphereKefuDataMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean updateById(PrivateSphereKefuDataEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return privateSphereKefuDataMapper.updateById(entity) > 0;
    }

    @Override
    public PrivateSphereKefuDataEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return privateSphereKefuDataMapper.selectById(id);
    }

    @Override
    public PrivateSphereKefuDataEntity selectByProductAndKefuChannelId(Date curDate, Long productId, Long kefuChannelId) {
        if(Objects.isNull(curDate) || NumberUtils.isNullOrLteZero(kefuChannelId) || NumberUtils.isNullOrLteZero(productId)){
            return null;
        }
        curDate = DateUtil.beginOfDay(curDate);
        return privateSphereKefuDataMapper.selectByProductAndKefuChannelId(curDate, productId, kefuChannelId);
    }

    @Override
    public List<PrivateSphereKefuDataEntity> selectListByParam(PrivateSphereKefuDataListBO bo) {
        if(Objects.isNull(bo)){
            bo = new PrivateSphereKefuDataListBO();
        }
        return privateSphereKefuDataMapper.selectListByParam(bo);
    }

    @Override
    public PrivateSphereKefuDataEntity statisticsByParam(PrivateSphereKefuDataListBO bo) {
        if(Objects.isNull(bo)){
            bo = new PrivateSphereKefuDataListBO();
        }
        return privateSphereKefuDataMapper.statisticsByParam(bo);
    }

    @Override
    public List<Long> selectIdsByParam(PrivateSphereKefuDataListBO bo) {
        if(Objects.isNull(bo)){
            bo = new PrivateSphereKefuDataListBO();
        }
        return privateSphereKefuDataMapper.selectIdsByParam(bo);
    }

    @Override
    public int countByKefuChannelId(Long kefuChannelId) {
        if(NumberUtils.isNullOrLteZero(kefuChannelId)){
            return 0;
        }
        return privateSphereKefuDataMapper.countByKefuChannelId(kefuChannelId);
    }
}
