package com.ruoyi.system.service.datasource.impl;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.system.bo.landpage.ConvDataBo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.datasource.AdvertSlotConvHourDataService;
import com.ruoyi.system.entity.datashow.AdvertSlotConvHourDataEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.ruoyi.system.mapper.datashow.AdvertSlotConvHourDataMapper;

/**
 * 广告广告位维度后端转化时段数据表 Service
 *
 * <AUTHOR>
 * @date 2023-2-9 10:55:35
 */
@Service
public class AdvertSlotConvHourDataServiceImpl implements AdvertSlotConvHourDataService {

    @Autowired
    private AdvertSlotConvHourDataMapper advertSlotConvHourDataMapper;

    @Override
    public int insert(AdvertSlotConvHourDataEntity entity) {
        if (Objects.isNull(entity)) {
            return 0;
        }
        return advertSlotConvHourDataMapper.insert(entity);
    }

    @Override
    public int updateById(AdvertSlotConvHourDataEntity entity) {
        if (Objects.isNull(entity)) {
            return 0;
        }
        return advertSlotConvHourDataMapper.updateById(entity);
    }

    @Override
    public AdvertSlotConvHourDataEntity selectBy(AdvertSlotConvHourDataEntity param) {
        return advertSlotConvHourDataMapper.selectBy(param);
    }

    @Override
    public Map<Integer, ConvDataBo> countByDateAndAdvertId(Date curDate, Long advertId) {
        if (null == curDate || null == advertId) {
            return Collections.emptyMap();
        }
        List<ConvDataBo> list = advertSlotConvHourDataMapper.countByDateAndAdvertId(curDate, advertId);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(ConvDataBo::getCurHour, Function.identity(), (v1, v2) -> v2));
    }

    @Override
    public Map<String, ConvDataBo> countByDateListAndAdvertIds(List<Date> dateList, List<Long> advertIds) {
        if (CollectionUtils.isEmpty(dateList) && CollectionUtils.isEmpty(advertIds)) {
            return Collections.emptyMap();
        }
        List<ConvDataBo> list = advertSlotConvHourDataMapper.countByDateListAndAdvertIds(dateList, advertIds);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(s -> DateUtil.formatDate(s.getCurDate()) + "_" + s.getCurHour() + "_" + s.getAdvertId(), Function.identity(), (v1, v2) -> v2));
    }

    @Override
    public Map<String, ConvDataBo> countByDateListAndAdvertIdsGroupByDateHour(List<Date> dateList, List<Long> advertIds) {
        if (CollectionUtils.isEmpty(dateList) && CollectionUtils.isEmpty(advertIds)) {
            return Collections.emptyMap();
        }
        List<ConvDataBo> list = advertSlotConvHourDataMapper.countByDateListAndAdvertIdsGroupByDateHour(dateList, advertIds);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(s -> DateUtil.formatDate(s.getCurDate()) + "_" + s.getCurHour(), Function.identity(), (v1, v2) -> v2));
    }
}
