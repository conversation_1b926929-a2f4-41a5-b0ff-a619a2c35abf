package com.ruoyi.system.service.permission.impl;

import com.alibaba.fastjson.JSON;
import com.ruoyi.common.utils.NumberUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.permission.PostPermissionRelationService;
import com.ruoyi.system.entity.permission.PostPermissionRelationEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import com.ruoyi.system.mapper.permission.PostPermissionRelationMapper;

/**
 * 职位权限关联表 Service
 *
 * <AUTHOR>
 * @date 2022-6-23 16:23:19
 */
@Service
public class PostPermissionRelationServiceImpl implements PostPermissionRelationService {

    @Autowired
    private PostPermissionRelationMapper postPermissionRelationMapper;

    @Override
    public Boolean insert(PostPermissionRelationEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return postPermissionRelationMapper.insert(entity) > 0;
    }

    @Override
    public Boolean deleteById(Long id) {
        if (Objects.isNull(id)) {
            return false;
        }
        return postPermissionRelationMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean updateById(PostPermissionRelationEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return postPermissionRelationMapper.updateById(entity) > 0;
    }

    @Override
    public PostPermissionRelationEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return postPermissionRelationMapper.selectById(id);
    }

    @Override
    public PostPermissionRelationEntity selectByPostId(Long postId) {
        if(NumberUtils.isNullOrLteZero(postId)){
            return null;
        }
        return postPermissionRelationMapper.selectByPostId(postId);
    }

    @Override
    public List<Long> selectPermissionIdsByPostId(Long postId) {
        PostPermissionRelationEntity relation = selectByPostId(postId);
        if (null == relation) {
            return Collections.emptyList();
        }
        return JSON.parseArray(relation.getPermissionIds(), Long.class);
    }

    @Override
    public Boolean updateOrInsertPermissionByPostId(PostPermissionRelationEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        PostPermissionRelationEntity relation = selectByPostId(entity.getOaPostId());
        if (null == relation) {
            return postPermissionRelationMapper.insert(entity) > 0;
        } else {
            return postPermissionRelationMapper.updatePermissionByPostId(entity);
        }
    }
}
