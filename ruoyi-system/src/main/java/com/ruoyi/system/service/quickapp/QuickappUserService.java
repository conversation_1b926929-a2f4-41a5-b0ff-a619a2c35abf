package com.ruoyi.system.service.quickapp;

import com.ruoyi.system.entity.quickapp.QuickappUserEntity;

import java.util.List;

/**
* 快应用用户表 Service
* <AUTHOR>
* @date 2022-8-8 10:57:00
*/
public interface QuickappUserService {
    /**
    * 新增记录
    */
    Boolean insert(QuickappUserEntity entity);

    /**
    * 根据id删除
    */
    Boolean deleteById(Long id);
    /**
    * 根据id更新
    */
    Boolean updateById(QuickappUserEntity entity);
    /**
    * 根据id获取
    */
    QuickappUserEntity selectById(Long id);

    /**
     * 根据邮箱获取用户
     * @param email
     * @return
     */
    QuickappUserEntity selectByEmail(String email);

    /**
     * 根据用户id列表查询用户信息
     * @param userIds
     * @return
     */
    List<QuickappUserEntity> selectByUserIds(List<Long> userIds);


}
