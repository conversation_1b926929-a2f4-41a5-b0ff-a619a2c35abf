package com.ruoyi.system.service.landpage.impl;

import com.ruoyi.common.utils.StringUtils;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.landpage.IspVipLandpageFormRecordService;
import com.ruoyi.system.entity.landpage.IspVipLandpageFormRecordEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

import com.ruoyi.system.mapper.landpage.IspVipLandpageFormRecordMapper;

/**
 * 运营商会员落地页记录 Service
 *
 * <AUTHOR>
 * @date 2023-11-7 11:41:45
 */
@Service
public class IspVipLandpageFormRecordServiceImpl implements IspVipLandpageFormRecordService {

    @Autowired
    private IspVipLandpageFormRecordMapper ispVipLandpageFormRecordMapper;

    @Override
    public Boolean insert(IspVipLandpageFormRecordEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return ispVipLandpageFormRecordMapper.insert(entity) > 0;
    }

    @Override
    public Boolean updateById(IspVipLandpageFormRecordEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return ispVipLandpageFormRecordMapper.updateById(entity) > 0;
    }

    @Override
    public IspVipLandpageFormRecordEntity selectById(Long id) {
        if (null == id) {
            return null;
        }
        return ispVipLandpageFormRecordMapper.selectById(id);
    }

    @Override
    public IspVipLandpageFormRecordEntity selectBy(String orderId, String phone) {
        if (StringUtils.isBlank(orderId) || StringUtils.isBlank(phone)) {
            return null;
        }
        return ispVipLandpageFormRecordMapper.selectBy(orderId, phone);
    }

    @Override
    public IspVipLandpageFormRecordEntity selectByBizOrderNoAndOrderId(String bizOrderNo, String orderId) {
        if (StringUtils.isBlank(bizOrderNo) || StringUtils.isBlank(orderId)) {
            return null;
        }
        return ispVipLandpageFormRecordMapper.selectByBizOrderNoAndOrderId(bizOrderNo, orderId);
    }

    @Override
    public IspVipLandpageFormRecordEntity selectByOrderId(String orderId) {
        if (StringUtils.isBlank(orderId)) {
            return null;
        }
        return ispVipLandpageFormRecordMapper.selectByOrderId(orderId);
    }
}
