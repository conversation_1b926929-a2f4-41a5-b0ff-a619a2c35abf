package com.ruoyi.system.service.landpage;

import com.ruoyi.system.domain.advertiser.LpCallbackAdvertiser;
import com.ruoyi.system.entity.datashow.LandpageFormSendRuleEntity;

import java.util.List;

/**
 * 落地页表单上报规则 Service
 *
 * <AUTHOR>
 * @date 2021-12-23
 */
public interface LandpageFormSendRuleService {

    /**
     * 根据标签和地域获取广告主列表, 并按照表单价格*权重逆序
     *
     * @param tag 标签
     * @param city 市
     * @param province 省
     * @return 广告主列表
     */
    List<LpCallbackAdvertiser> selectAdvertiserList(String tag, String city, String province);

    /**
     * 根据标签和地域获取广告主列表
     *
     * @param tag 标签
     * @param area 地域(省或者市)
     * @return 广告主列表
     */
    List<LpCallbackAdvertiser> selectAdvertiserList(String tag, String area);

    /**
     * 新增/更新规则
     *
     * @param entity 新增对象
     * @return 结果
     */
    int update(LandpageFormSendRuleEntity entity);

    /**
     * 获取有效的规则
     *
     * @return 结果
     */
    List<LandpageFormSendRuleEntity> selectList();

    /**
     * 根据广告主ID获取
     *
     * @param advertiserId 广告主ID
     * @return 结果
     */
    LandpageFormSendRuleEntity selectByAdvertiserId(Long advertiserId);
}
