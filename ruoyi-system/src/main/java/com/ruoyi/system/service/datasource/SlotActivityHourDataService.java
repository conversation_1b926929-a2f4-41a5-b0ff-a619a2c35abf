package com.ruoyi.system.service.datasource;

import com.ruoyi.system.entity.datashow.SlotActivityHourData;

import java.util.Date;
import java.util.List;

/**
 * 广告位维度分时段活动数据Service接口
 * 
 * <AUTHOR>
 * @date 2021-07-21
 */
public interface SlotActivityHourDataService {

    /**
     * 查询广告位维度活动数据列表
     * 
     * @param slotActivityHourData 广告位维度活动数据
     * @return 广告位维度活动数据集合
     */
    List<SlotActivityHourData> selectSlotActivityHourDataList(SlotActivityHourData slotActivityHourData);

    /**
     * 查询广告位数据
     *
     * @param slotId 广告位ID
     * @param curDate 日期
     * @param curHour 小时
     * @return 广告位数据
     */
    SlotActivityHourData selectBy(Long slotId, Long activityId, Date curDate, Integer curHour);

    /**
     * 新增广告位维度活动数据
     * 
     * @param slotActivityHourData 广告位维度活动数据
     * @return 结果
     */
    int insertSlotActivityHourData(SlotActivityHourData slotActivityHourData);

    /**
     * 修改广告位维度活动数据
     * 
     * @param slotActivityHourData 广告位维度活动数据
     * @return 结果
     */
    int updateSlotActivityHourData(SlotActivityHourData slotActivityHourData);
}
