package com.ruoyi.system.service.permission;

import com.ruoyi.system.entity.permission.PostPermissionRelationEntity;

import java.util.List;

/**
 * 职位权限关联表 Service
 *
 * <AUTHOR>
 * @date 2022-6-23 16:23:19
 */
public interface PostPermissionRelationService {

    /**
     * 新增记录
     */
    Boolean insert(PostPermissionRelationEntity entity);

    /**
     * 根据id删除
     */
    Boolean deleteById(Long id);

    /**
     * 根据id更新
     */
    Boolean updateById(PostPermissionRelationEntity entity);

    /**
     * 根据id获取
     */
    PostPermissionRelationEntity selectById(Long id);

    /**
     * 根据职位id查询权限id列表
     *
     * @param postId 职位id
     * @return 职位权限关联
     */
    PostPermissionRelationEntity selectByPostId(Long postId);

    /**
     * 查询职位对应的权限ID列表
     *
     * @param postId 职位ID
     * @return 权限ID列表
     */
    List<Long> selectPermissionIdsByPostId(Long postId);

    /**
     * 更新职位权限
     *
     * @param entity 参数
     * @return 是否更新成功
     */
    Boolean updateOrInsertPermissionByPostId(PostPermissionRelationEntity entity);
}
