package com.ruoyi.system.service.privatesphere.impl;

import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.bo.privatesphere.PrivateSphereChannelListBO;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.privatesphere.PrivateSphereChannelService;
import com.ruoyi.system.entity.privatesphere.PrivateSphereChannelEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import com.ruoyi.system.mapper.privatesphere.PrivateSphereChannelMapper;

/**
 * 私域渠道表 Service
 *
 * <AUTHOR>
 * @date 2023-2-8 15:51:57
 */
@Service
public class PrivateSphereChannelServiceImpl implements PrivateSphereChannelService {
    @Autowired
    private PrivateSphereChannelMapper privateSphereChannelMapper;

    @Override
    public Boolean insert(PrivateSphereChannelEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return privateSphereChannelMapper.insert(entity) > 0;
    }

    @Override
    public Boolean deleteById(Long id) {
        if (Objects.isNull(id)) {
            return false;
        }
        return privateSphereChannelMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean updateById(PrivateSphereChannelEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return privateSphereChannelMapper.updateById(entity) > 0;
    }

    @Override
    public PrivateSphereChannelEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return privateSphereChannelMapper.selectById(id);
    }

    @Override
    public PrivateSphereChannelEntity selectByProductAndChannelName(Long productId, String channelName) {
        if(NumberUtils.isNullOrLteZero(productId) || StringUtils.isBlank(channelName)){
            return null;
        }
        return privateSphereChannelMapper.selectByProductAndChannelName(productId, channelName);
    }

    @Override
    public List<PrivateSphereChannelEntity> selectListByParam(PrivateSphereChannelListBO bo) {
        if(Objects.isNull(bo)){
            bo = new PrivateSphereChannelListBO();
        }
        return privateSphereChannelMapper.selectListByParam(bo);
    }
}
