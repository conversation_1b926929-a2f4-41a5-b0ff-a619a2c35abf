package com.ruoyi.system.service.landpage.article.impl;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.system.entity.landpage.article.ArticleEntity;
import com.ruoyi.system.mapper.landpage.article.ArticleMapper;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.landpage.article.ArticleEditHistoryService;
import com.ruoyi.system.entity.landpage.article.ArticleEditHistoryEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

import com.ruoyi.system.mapper.landpage.article.ArticleEditHistoryMapper;

/**
 * 文章修改记录表 Service
 *
 * <AUTHOR>
 * @date 2023-12-1 15:13:41
 */
@Service
public class ArticleEditHistoryServiceImpl implements ArticleEditHistoryService {

    @Autowired
    private ArticleEditHistoryMapper articleEditHistoryMapper;

    @Autowired
    private ArticleMapper articleMapper;

    @Override
    public Boolean add(ArticleEntity article) {
        if (null == articleEditHistoryMapper.existByArticleId(article.getId())) {
            articleEditHistoryMapper.insert(convertTo(articleMapper.selectById(article.getId())));
        }
        return articleEditHistoryMapper.insert(convertTo(article)) > 0;
    }

    @Override
    public Boolean insert(ArticleEditHistoryEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return articleEditHistoryMapper.insert(entity) > 0;
    }

    @Override
    public Boolean deleteById(Long id) {
        return null;
    }

    @Override
    public Boolean updateById(ArticleEditHistoryEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return articleEditHistoryMapper.updateById(entity) > 0;
    }

    @Override
    public ArticleEditHistoryEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return articleEditHistoryMapper.selectById(id);
    }

    private ArticleEditHistoryEntity convertTo(ArticleEntity article) {
        ArticleEditHistoryEntity history = new ArticleEditHistoryEntity();
        history.setArticleId(article.getId());
        history.setName(article.getName());
        history.setWeight(article.getWeight());
        history.setTargetRequestPv(article.getTargetRequestPv());
        history.setOperatorId(article.getOperatorId());
        history.setOperatorName(article.getOperatorName());
        return history;
    }
}
