package com.ruoyi.system.service.datasource.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.BooleanUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.constant.ErrorCode;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.PageDomain;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.account.AccountMainType;
import com.ruoyi.common.enums.common.SwitchStatusEnum;
import com.ruoyi.common.enums.common.WhitelistType;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.ListUtils;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.PageInfoUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.sql.SqlUtil;
import com.ruoyi.system.bo.account.DataPermissionBo;
import com.ruoyi.system.bo.landpage.AdvertConvDataParamBo;
import com.ruoyi.system.bo.landpage.ConvDataBo;
import com.ruoyi.system.bo.slot.CrmSlotDataBo;
import com.ruoyi.system.bo.slotactivitydata.SlotActivityDataSumBo;
import com.ruoyi.system.entity.app.App;
import com.ruoyi.system.entity.datashow.AdvertSlotDayData;
import com.ruoyi.system.entity.datashow.SlotActivityData;
import com.ruoyi.system.entity.datashow.SlotData;
import com.ruoyi.system.entity.slot.BiddingDayDataEntity;
import com.ruoyi.system.entity.slot.Slot;
import com.ruoyi.system.entity.slot.SlotSwitchConfig;
import com.ruoyi.system.manager.account.DataPermissionManager;
import com.ruoyi.system.mapper.datashow.AdvertSlotDayDataMapper;
import com.ruoyi.system.mapper.datashow.SlotActivityDataMapper;
import com.ruoyi.system.mapper.datashow.SlotDataMapper;
import com.ruoyi.system.mapper.manager.AppMapper;
import com.ruoyi.system.mapper.manager.SlotMapper;
import com.ruoyi.system.req.datashow.SlotDataReq;
import com.ruoyi.system.service.common.WhitelistService;
import com.ruoyi.system.service.datasource.AdvertSlotConvDayDataService;
import com.ruoyi.system.service.datasource.AdvertSlotDayDataService;
import com.ruoyi.system.service.datasource.SlotActivityDataService;
import com.ruoyi.system.service.datasource.SlotDataService;
import com.ruoyi.system.service.landpage.BaijiuLandpageFormRecordService;
import com.ruoyi.system.service.landpage.SlotLandpageFormDataService;
import com.ruoyi.system.service.manager.AccountRelationService;
import com.ruoyi.system.service.manager.AccountService;
import com.ruoyi.system.service.manager.SlotConfigService;
import com.ruoyi.system.service.slot.BiddingDayDataService;
import com.ruoyi.system.service.slotdata.SlotMonthDataService;
import com.ruoyi.system.util.SlotReqUvCalculateUtil;
import com.ruoyi.system.vo.datashow.CrmSlotDataStatisticsVO;
import com.ruoyi.system.vo.datashow.CrmSlotDataVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ruoyi.common.enums.account.AccountMainType.isCrmUser;
import static com.ruoyi.common.enums.account.DataPermissionType.hasPartialPermission;
import static com.ruoyi.common.enums.slot.SlotDataVisibleEnum.INVISIBLE;
import static com.ruoyi.common.enums.slot.SlotDataVisibleEnum.VISIBLE;

/**
 * 广告位数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-07-04
 */
@Service
public class SlotDataServiceImpl implements SlotDataService {

    private static final CrmSlotDataVO EMPTY_CRM_SLOT_DATA = new CrmSlotDataVO();

    @Autowired
    private SlotDataMapper slotDataMapper;
    @Autowired
    private AppMapper appMapper;
    @Autowired
    private SlotMapper slotMapper;
    @Autowired
    private SlotActivityDataMapper slotActivityDataMapper;
    @Autowired
    private AccountService accountService;
    @Autowired
    private SlotActivityDataService slotActivityDataService;
    @Autowired
    private SlotMonthDataService slotMonthDataService;
    @Autowired
    private BaijiuLandpageFormRecordService baijiuLandpageFormRecordService;
    @Autowired
    private AdvertSlotDayDataMapper advertSlotDayDataMapper;
    @Autowired
    private WhitelistService whitelistService;
    @Autowired
    private DataPermissionManager dataPermissionManager;
    @Autowired
    private SlotConfigService slotConfigService;
    @Autowired
    private AccountRelationService accountRelationService;
    @Autowired
    private AdvertSlotDayDataService advertSlotDayDataService;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private SlotLandpageFormDataService slotLandpageFormDataService;
    @Autowired
    private BiddingDayDataService biddingDayDataService;
    @Autowired
    private AdvertSlotConvDayDataService advertSlotConvDayDataService;

    /**
     * 查询广告位数据
     *
     * @param id 广告位数据ID
     * @return 广告位数据
     */
    @Override
    public SlotData selectSlotDataById(Long id) {
        return slotDataMapper.selectSlotDataById(id);
    }

    /**
     * 查询广告位数据列表
     *
     * @param slotData 广告位数据
     * @return 广告位数据
     */
    @Override
    public List<SlotData> selectSlotDataList(SlotData slotData, boolean isExport) {
        // 获取当前账户信息
        LoginUser user = SecurityUtils.getLoginUser();
        boolean isCrm = isCrmUser(user.getMainType());
        // 非CRM用户限制查询账号以及时间
        if (!isCrm) {
            slotData.setAccountId(user.getCrmAccountId());
            Date yesterday = DateUtil.beginOfDay(DateUtil.yesterday());
            if (null == slotData.getEndDate() || !slotData.getEndDate().before(yesterday)) {
                slotData.setEndDate(yesterday);
            }
        }

        // 媒体模糊查询
        if (StringUtils.isNotBlank(slotData.getAppSearch())) {
            App app = new App();
            if(!isCrm){
                app.setAccountId(user.getCrmAccountId());
            }

            app.setSearchValue(slotData.getAppSearch());
            List<Long> appIds = appMapper.selectAppIdList(app);
            if (CollectionUtils.isEmpty(appIds)) {
                return Collections.emptyList();
            }
            slotData.setAppIds(appIds);
        }

        // 广告位模糊查询
        if (StringUtils.isNotBlank(slotData.getSlotSearch())) {
            Slot slot = new Slot();
            if(!isCrm){
                slot.setAccountId(user.getCrmAccountId());
            }

            slot.setSearchValue(slotData.getSlotSearch());
            List<Long> slotIds = slotMapper.selectSlotIdList(slot);
            if (CollectionUtils.isEmpty(slotIds)) {
                return Collections.emptyList();
            }
            slotData.setSlotIds(slotIds);
        }

        //账号模糊搜索
        if(StringUtils.isNotBlank(slotData.getAccountSearch())){
            List<Long> accountIds = accountService.selectIdsByIdOrEmail(slotData.getAccountSearch());
            if(CollectionUtils.isEmpty(accountIds)){
                return Collections.emptyList();
            }
            slotData.setAccountIds(accountIds);
        }
        // 负责人查询
        if (CollectionUtils.isNotEmpty(slotData.getManagerIds())) {
            List<Long> accountIds = accountRelationService.selectBySrcAccountIds(slotData.getManagerIds());
            if (CollectionUtils.isEmpty(accountIds)) {
                return Collections.emptyList();
            }
            slotData.setAccountIds(accountIds);
        }
        // 数据权限控制
        DataPermissionBo permission = dataPermissionManager.selectSlot();
        if (hasPartialPermission(permission.getType())) {
            if (CollectionUtils.isEmpty(permission.getValues())) {
                return Collections.emptyList();
            }
            if (null == slotData.getSlotIds()) {
                slotData.setSlotIds(permission.getValues());
            } else {
                slotData.getSlotIds().retainAll(permission.getValues());
            }
            if (CollectionUtils.isEmpty(slotData.getSlotIds())) {
                return Collections.emptyList();
            }
        }

        if (!isExport) {
            startPage();
        }

        List<SlotData> dataList = selectSlotDataList(slotData);
        if(CollectionUtils.isEmpty(dataList)){
            return dataList;
        }

        // 媒体名称
        List<Long> appIds = dataList.stream().map(SlotData::getAppId).collect(Collectors.toList());
        List<App> apps = appMapper.selectSimpleInfoByIds(appIds);
        Map<Long, String> appNameMap = apps.stream().collect(Collectors.toMap(App::getId, App::getAppName, (v1, v2) -> v1));
        // 广告位名称
        List<Long> slotIds = dataList.stream().map(SlotData::getSlotId).collect(Collectors.toList());
        List<Slot> slots = slotMapper.selectSimpleSlotByIds(slotIds);
        Map<Long, String> slotNameMap = slots.stream().collect(Collectors.toMap(Slot::getId, Slot::getSlotName, (v1, v2) -> v1));

        if (CollectionUtils.isNotEmpty(dataList)) {
            dataList.forEach(data -> {
                data.setAppName(appNameMap.get(data.getAppId()));
                data.setSlotName(slotNameMap.get(data.getSlotId()));
            });
        }
        return dataList;
    }

    @Override
    public List<CrmSlotDataBo> selectCrmSlotDataList(SlotData slotData, boolean isExport) {
        // 获取当前账户信息
        LoginUser user = SecurityUtils.getLoginUser();
        boolean isCrm = isCrmUser(user.getMainType());
        // 非CRM用户限制查询账号以及时间
        if (!isCrm) {
            slotData.setAccountId(user.getCrmAccountId());
            Date yesterday = DateUtil.beginOfDay(DateUtil.yesterday());
            if (null == slotData.getEndDate() || !slotData.getEndDate().before(yesterday)) {
                slotData.setEndDate(yesterday);
            }
        }

        // 媒体模糊查询
        if (StringUtils.isNotBlank(slotData.getAppSearch())) {
            App app = new App();
            if (!isCrm) {
                app.setAccountId(user.getCrmAccountId());
            }

            app.setSearchValue(slotData.getAppSearch());
            List<Long> appIds = appMapper.selectAppIdList(app);
            if (CollectionUtils.isEmpty(appIds)) {
                return Collections.emptyList();
            }
            slotData.setAppIds(appIds);
        }

        // 广告位模糊查询
        if (StringUtils.isNotBlank(slotData.getSlotSearch())) {
            Slot slot = new Slot();
            if (!isCrm) {
                slot.setAccountId(user.getCrmAccountId());
            }

            slot.setSearchValue(slotData.getSlotSearch());
            List<Long> slotIds = slotMapper.selectSlotIdList(slot);
            if (CollectionUtils.isEmpty(slotIds)) {
                return Collections.emptyList();
            }
            slotData.setSlotIds(slotIds);
        }

        //账号模糊搜索
        if (StringUtils.isNotBlank(slotData.getAccountSearch())) {
            List<Long> accountIds = accountService.selectIdsByIdOrEmail(slotData.getAccountSearch());
            if (CollectionUtils.isEmpty(accountIds)) {
                return Collections.emptyList();
            }
            slotData.setAccountIds(accountIds);
        }
        // 负责人查询
        if (CollectionUtils.isNotEmpty(slotData.getManagerIds())) {
            List<Long> accountIds = accountRelationService.selectBySrcAccountIds(slotData.getManagerIds());
            if (CollectionUtils.isEmpty(accountIds)) {
                return Collections.emptyList();
            }
            slotData.setAccountIds(accountIds);
        }
        // 数据权限控制
        DataPermissionBo permission = dataPermissionManager.selectSlot();
        if (hasPartialPermission(permission.getType())) {
            if (CollectionUtils.isEmpty(permission.getValues())) {
                return Collections.emptyList();
            }
            if (null == slotData.getSlotIds()) {
                slotData.setSlotIds(permission.getValues());
            } else {
                slotData.getSlotIds().retainAll(permission.getValues());
            }
            if (CollectionUtils.isEmpty(slotData.getSlotIds())) {
                return Collections.emptyList();
            }
        }

        if (!isExport) {
            startPage();
        }
        slotData.setOrderType(BooleanUtil.isTrue(slotData.getIsAsc()) ? "asc" : "desc");
        return selectCrmSlotDataList(slotData);
    }

    @Override
    public List<SlotData> selectAllSlotDataList(SlotData slotData) {
        List<SlotData> dataList = selectSlotDataList(slotData);
        // 媒体名称
        List<Long> appIds = dataList.stream().map(SlotData::getAppId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(appIds)){
            return Collections.emptyList();
        }
        List<App> apps = appMapper.selectSimpleInfoByIds(appIds);
        Map<Long, String> appNameMap = apps.stream().collect(Collectors.toMap(App::getId, App::getAppName, (v1, v2) -> v1));
        // 广告位名称
        List<Long> slotIds = dataList.stream().map(SlotData::getSlotId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(slotIds)){
            return Collections.emptyList();
        }
        List<Slot> slots = slotMapper.selectSimpleSlotByIds(slotIds);
        Map<Long, String> slotNameMap = slots.stream().collect(Collectors.toMap(Slot::getId, Slot::getSlotName, (v1, v2) -> v1));

        if (CollectionUtils.isNotEmpty(dataList)) {
            dataList.forEach(data -> {
                data.setAppName(appNameMap.get(data.getAppId()));
                data.setSlotName(slotNameMap.get(data.getSlotId()));
            });
        }
        return dataList;
    }

    @Override
    public SlotData selectBySlotIdAndDate(Long slotId, Date curDate) {
        return slotDataMapper.selectBySlotIdAndDate(slotId, curDate);
    }

    /**
     * 新增广告位数据
     *
     * @param slotData 广告位数据
     * @return 结果
     */
    @Override
    public int insertSlotData(SlotData slotData) {
        boolean isAdjustOpen = whitelistService.contains(WhitelistType.ADJUST_DATA_SLOT, slotData.getSlotId());
        if (!isAdjustOpen) {
            SlotSwitchConfig switchConfig = slotConfigService.selectSwitchConfigBySlotId(slotData.getSlotId());
            if (SwitchStatusEnum.isSwitchOn(switchConfig.getAdjust())) {
                isAdjustOpen = true;
            }
        }

        // 默认媒体可见，如果广告位在白名单，则需要修改后才可见
        if (isAdjustOpen) {
            slotData.setIsVisible(INVISIBLE.getStatus());
            slotData.setIsEditable(1);
        } else {
            slotData.setIsVisible(VISIBLE.getStatus());
            slotData.setIsEditable(0);
        }
        return slotDataMapper.insertSlotData(slotData);
    }

    /**
     * 修改广告位数据
     *
     * @param slotData 广告位数据
     * @return 结果
     */
    @Override
    public int updateSlotData(SlotData slotData) {
        return slotDataMapper.updateSlotData(slotData);
    }

    @Override
    public int updateNhCost(SlotData slotData) {
        return slotDataMapper.updateNhCost(slotData);
    }

    @Override
    public Map<Long, SlotData> groupBySlotId(List<Long> slotIds, Date date) {
        SlotData req = new SlotData();
        req.setCurDate(date);
        req.setSlotIds(slotIds);
        List<SlotData> list = selectSlotDataList(req);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        return list.stream().collect(Collectors.toMap(SlotData::getSlotId, Function.identity(), (oldVal, newVal) -> newVal));
    }

    @Override
    public PageInfo<CrmSlotDataVO> selectCrmSlotDataList(SlotDataReq req, boolean isExport) {
        List<CrmSlotDataBo> slotData = selectCrmSlotDataList(BeanUtil.toBean(req, SlotData.class), isExport);
        if(CollectionUtils.isEmpty(slotData)){
            return PageInfoUtils.buildReturnList(Collections.emptyList());
        }
        Date today = DateUtil.beginOfDay(new Date());
        List<Long> slotIds = ListUtils.mapToList(slotData, CrmSlotDataBo::getSlotId);
        List<SlotActivityData> slotActivityData = slotActivityDataMapper.selectSumSlotActivityDataBySlotIdsAndDate(slotIds, req.getStartDate(), req.getEndDate());
        List<AdvertSlotDayData> landpageDataList = advertSlotDayDataMapper.selectSumLpDataBySlotIdsAndDate(slotIds, req.getStartDate(), req.getEndDate());
        Map<String, Integer> formDataMap = slotLandpageFormDataService.selectMapByDateAndSlotIds(req.getStartDate(), req.getEndDate(), slotIds);
        Map<String, Integer> baijiuFormCountMap = baijiuLandpageFormRecordService.countByDateAndSlotId(req.getStartDate(), req.getEndDate(), slotIds);
        Map<String, ConvDataBo> convCountMap = advertSlotConvDayDataService.countByDateAndSlotIds(req.getStartDate(), req.getEndDate(), slotIds);
        Set<Pair<Long, Integer>> monthDataSet = slotMonthDataService.existBySlotIdsAndMonths(slotIds, ListUtils.mapToList(slotData, s -> DateUtils.dateTimeMonth(s.getCurDate())));
        Map<String, SlotActivityData> activityDataMap = slotActivityData.stream().collect(Collectors.toMap(data -> data.getSlotId() + "_" + DateUtils.dateTime(data.getCurDate()), Function.identity(), (v1, v2) -> v2));
        Map<String, AdvertSlotDayData> landpageDataMap = landpageDataList.stream().collect(Collectors.toMap(data -> data.getSlotId() + "_" + DateUtils.dateTime(data.getCurDate()), Function.identity(), (v1, v2) -> v2));
        Map<Long, String> managerNameMap = accountService.selectManagerMap(ListUtils.mapToList(slotData, CrmSlotDataBo::getAccountId));
        Map<Long, Long> todayNhCostMap = DateUtils.containsDate(req.getStartDate(), req.getEndDate(), today) ? selectTodayNhCostMap(slotIds) : Collections.emptyMap();
        Map<String, BiddingDayDataEntity> biddingDataMap = biddingDayDataService.countByDateAndSlotIds(req.getStartDate(), req.getEndDate(), slotIds);

        return PageInfoUtils.dto2Vo(slotData,data ->{
            String dataKey = data.getSlotId() + "_" + DateUtils.dateTime(data.getCurDate());
            CrmSlotDataVO vo = BeanUtil.copyProperties(data, CrmSlotDataVO.class);
            if (DateUtil.isSameDay(data.getCurDate(), today)) {
                Optional.ofNullable(todayNhCostMap.get(data.getSlotId())).ifPresent(vo::setNhCost);
                vo.setTotalCost(vo.getNhCost() + vo.getOuterCost());
            }
            vo.setManagerName(managerNameMap.getOrDefault(data.getAccountId(), ""));
            vo.setSlotRequestPerUv(NumberUtils.calculateRate(data.getSlotRequestPv(), data.getSlotRequestUv()));
            Optional.ofNullable(activityDataMap.get(dataKey)).ifPresent(activityData -> fillActivityData(vo, activityData));
            Optional.ofNullable(landpageDataMap.get(dataKey)).ifPresent(landpageData -> fillLandpageData(vo, landpageData));
            vo.setExistMonthData(monthDataSet.contains(Pair.of(data.getSlotId(), DateUtils.dateTimeMonth(data.getCurDate()))));
            vo.setFormCount(formDataMap.getOrDefault(dataKey, 0) + baijiuFormCountMap.getOrDefault(dataKey, 0));
            vo.setEntryCpc(NumberUtils.calculateRate(NumberUtils.defaultLong(vo.getNhCost(), 0L).intValue(), data.getSlotRequestPv() * 100));
            Optional.ofNullable(vo.getAdLaunch()).ifPresent(adLaunch -> vo.setArpu(NumberUtils.calculateRate(NumberUtils.defaultLong(vo.getTotalCost(), 0L).intValue(), adLaunch * 100)));
            Optional.ofNullable(biddingDataMap.get(dataKey)).ifPresent(biddingData -> {
                vo.setBiddingConsume(biddingData.getConsume());
                vo.setBiddingConv(biddingData.getConv());
                vo.setBiddingCost(NumberUtils.calculateRate(biddingData.getConsume(), biddingData.getConv() * 100));
            });
            Optional.ofNullable(convCountMap.get(dataKey)).ifPresent(conv -> {
                vo.setRegister(conv.getRegister());
                vo.setPay(conv.getPay());
                vo.setRefund(conv.getRefund());
                vo.setAppActive(conv.getAppActive());
                vo.setExtPrice(conv.getPayPrice());
            });
            vo.setDivideRate(NumberUtils.calculatePercent(data.getAppRevenue() + NumberUtils.defaultLong(vo.getBiddingConsume()), vo.getTotalCost()));
            vo.setAppCpm(NumberUtils.calculateRate(data.getAppRevenue() * 10,data.getAppSlotExposurePv()));
            vo.setNhCpm(NumberUtils.calculateRate(vo.getTotalCost() * 10,data.getAppSlotExposurePv()));
            vo.setAppCpc(NumberUtils.calculateRate(data.getAppRevenue()/ 100,data.getAppSlotClickPv()));
            vo.setNhRevenuePerUv(NumberUtils.calculateRate(vo.getTotalCost()/ 100,data.getSlotRequestUv()));
            vo.setAppRevenuePerUv(NumberUtils.calculateRate(data.getAppRevenue()/ 100,data.getSlotRequestUv()));
            vo.setSlotClickRate(NumberUtils.calculatePercent(data.getAppSlotClickPv(),data.getAppSlotExposurePv()));
            return vo;
        });
    }

    @Override
    public CrmSlotDataVO selectStatisticCrmSlotData(SlotDataReq req) {
        SlotData param = new SlotData();
        param.setStartDate(req.getStartDate());
        param.setEndDate(req.getEndDate());

        // 媒体模糊查询
        if (StringUtils.isNotBlank(req.getAppSearch())) {
            App app = new App();
            app.setSearchValue(req.getAppSearch());
            List<Long> appIds = appMapper.selectAppIdList(app);
            if (CollectionUtils.isEmpty(appIds)) {
                return EMPTY_CRM_SLOT_DATA;
            }
            param.setAppIds(appIds);
        }
        // 广告位模糊查询
        if (StringUtils.isNotBlank(req.getSlotSearch())) {
            Slot slot = new Slot();
            slot.setSearchValue(req.getSlotSearch());
            List<Long> slotIds = slotMapper.selectSlotIdList(slot);
            if (CollectionUtils.isEmpty(slotIds)) {
                return EMPTY_CRM_SLOT_DATA;
            }
            param.setSlotIds(slotIds);
        }
        //账号模糊搜索
        if (StringUtils.isNotBlank(req.getAccountSearch())) {
            List<Long> accountIds = accountService.selectIdsByIdOrEmail(req.getAccountSearch());
            if (CollectionUtils.isEmpty(accountIds)) {
                return EMPTY_CRM_SLOT_DATA;
            }
            param.setAccountIds(accountIds);
        }
        // 负责人查询
        if (CollectionUtils.isNotEmpty(req.getManagerIds())) {
            List<Long> accountIds = accountRelationService.selectBySrcAccountIds(req.getManagerIds());
            if (CollectionUtils.isEmpty(accountIds)) {
                return EMPTY_CRM_SLOT_DATA;
            }
            if (null == param.getAccountIds()) {
                param.setAccountIds(accountIds);
            } else {
                param.getAccountIds().retainAll(accountIds);
            }
            if (CollectionUtils.isEmpty(param.getAccountIds())) {
                return EMPTY_CRM_SLOT_DATA;
            }
        }
        // 数据权限控制
        DataPermissionBo permission = dataPermissionManager.selectSlot();
        if (hasPartialPermission(permission.getType())) {
            if (CollectionUtils.isEmpty(permission.getValues())) {
                return EMPTY_CRM_SLOT_DATA;
            }
            if (null == param.getSlotIds()) {
                param.setSlotIds(permission.getValues());
            } else {
                param.getSlotIds().retainAll(permission.getValues());
            }
            if (CollectionUtils.isEmpty(param.getSlotIds())) {
                return EMPTY_CRM_SLOT_DATA;
            }
        }

        SlotData data = slotDataMapper.selectStatisticSlotData(param);
        if (null == data) {
            return EMPTY_CRM_SLOT_DATA;
        }

        List<Long> slotIds = slotDataMapper.selectSlotIds(param);
        SlotActivityData slotActivityData = slotActivityDataMapper.sumSlotActivityDataBySlotIdsAndDate(slotIds, req.getStartDate(), req.getEndDate());
        AdvertSlotDayData landpageData = advertSlotDayDataMapper.sumLpDataBySlotIdsAndDate(slotIds, req.getStartDate(), req.getEndDate());
        Integer formCount = slotLandpageFormDataService.sumFormDataByDateAndSlotIds(req.getStartDate(), req.getEndDate(), slotIds);
        Integer baijiuFormCount = baijiuLandpageFormRecordService.sumByDateAndSlotId(req.getStartDate(), req.getEndDate(), slotIds);
        ConvDataBo convData = advertSlotConvDayDataService.selectStatisticAdvertConvData(new AdvertConvDataParamBo().setStartDate(req.getStartDate()).setEndDate(req.getEndDate()).setSlotIds(slotIds));
        BiddingDayDataEntity biddingData = biddingDayDataService.sumByDateAndSlotIds(req.getStartDate(), req.getEndDate(), slotIds);

        CrmSlotDataVO vo = BeanUtil.copyProperties(data, CrmSlotDataVO.class);
        if (DateUtils.containsDate(req.getStartDate(), req.getEndDate(), DateUtil.beginOfDay(new Date()))) {
            vo.setNhCost(vo.getNhCost() + sumTodayNhCostMap(slotIds));
        }
        vo.setTotalCost(vo.getNhCost() + data.getOuterCost());
        vo.setSlotRequestPerUv(NumberUtils.calculateRate(data.getSlotRequestPv(), data.getSlotRequestUv()));
        Optional.ofNullable(slotActivityData).ifPresent(activityData -> fillActivityData(vo, activityData));
        Optional.ofNullable(landpageData).ifPresent(lpData -> fillLandpageData(vo, lpData));
        vo.setFormCount(formCount + baijiuFormCount);
        vo.setEntryCpc(NumberUtils.calculateRate(NumberUtils.defaultLong(vo.getNhCost(), 0L).intValue(), data.getSlotRequestPv() * 100));
        Optional.ofNullable(vo.getAdLaunch()).ifPresent(adLaunch -> vo.setArpu(NumberUtils.calculateRate(NumberUtils.defaultLong(vo.getTotalCost(), 0L).intValue(), adLaunch * 100)));
        vo.setRegister(convData.getRegister());
        vo.setPay(convData.getPay());
        vo.setRefund(convData.getRefund());
        vo.setAppActive(convData.getAppActive());
        vo.setExtPrice(convData.getPayPrice());
        vo.setAppCpm(NumberUtils.calculateRate(data.getAppRevenue() * 10,data.getAppSlotExposurePv()));//分转圆，要除100, 1000 /100 = 10
        vo.setNhCpm(NumberUtils.calculateRate(vo.getTotalCost() * 10,data.getAppSlotExposurePv()));
        vo.setAppCpc(NumberUtils.calculateRate(data.getAppRevenue() / 100 ,data.getAppSlotClickPv()));
        vo.setNhRevenuePerUv(NumberUtils.calculateRate(vo.getTotalCost() / 100,data.getSlotRequestUv()));
        vo.setAppRevenuePerUv(NumberUtils.calculateRate(data.getAppRevenue() / 100,data.getSlotRequestUv()));
        vo.setSlotClickRate(NumberUtils.calculatePercent(data.getAppSlotClickPv(),data.getAppSlotExposurePv()));
        vo.setBiddingConsume(biddingData.getConsume());
        vo.setBiddingConv(biddingData.getConv());
        vo.setBiddingCost(NumberUtils.calculateRate(biddingData.getConsume(), NumberUtils.defaultInt(biddingData.getConv()) * 100));
        vo.setDivideRate(NumberUtils.calculatePercent(data.getAppRevenue() + NumberUtils.defaultLong(vo.getBiddingConsume()), vo.getTotalCost()));
        return vo;
    }

    @Override
    public List<SlotData> selectSlotDataList(SlotData slotData) {
        if(Objects.isNull(slotData)){
            return Collections.emptyList();
        }
        List<SlotData> dataList = slotDataMapper.selectSlotDataList(slotData);
        //重新计算广告位访问uv
        calculateSlotReqUv(dataList);

        return dataList;
    }

    @Override
    public List<CrmSlotDataBo> selectCrmSlotDataList(SlotData slotData) {
        if (Objects.isNull(slotData)) {
            return Collections.emptyList();
        }
        List<CrmSlotDataBo> dataList = slotDataMapper.selectCrmSlotDataList(slotData);
        List<Long> slotLists = Lists.newArrayList(854556L, 854706L);
        dataList = dataList.stream().filter(data -> !slotLists.contains(data.getSlotId())).collect(Collectors.toList());
        //重新计算广告位访问uv
        calculateCrmSlotReqUv(dataList);

        return dataList;
    }

    @Override
    public List<Long> selectDistinctSlotIdByDate(Date startDate, Date endDate) {
        if(Objects.isNull(startDate) || Objects.isNull(endDate)){
            return Collections.emptyList();
        }
        return slotDataMapper.selectDistinctSlotIdByDate(startDate,endDate);
    }

    @Override
    public Long sumAppRevenueByAccountId(Long accountId) {
        Map<Long, Long> map = sumAppRevenueByAccountId(Collections.singletonList(accountId));
        return map.getOrDefault(accountId, 0L);
    }

    @Override
    public Map<Long, Long> sumAppRevenueByAccountId(List<Long> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return Collections.emptyMap();
        }
        List<SlotData> list = slotDataMapper.sumAppRevenueByAccountId(accountIds);
        return list.stream().collect(Collectors.toMap(SlotData::getAccountId, SlotData::getAppRevenue, (v1, v2) -> v2));
    }

    @Override
    public CrmSlotDataStatisticsVO statisticsCrmSlotData(SlotDataReq req) {
        // 获取当前账户信息
        LoginUser user = SecurityUtils.getLoginUser();
        //非crm账号不能查询
        if(!Objects.equals(user.getMainType(), AccountMainType.CRM.getType())){
            throw new CustomException(ErrorCode.E105001);
        }

        CrmSlotDataStatisticsVO vo = new CrmSlotDataStatisticsVO();

        SlotData data = new SlotData();
        List<Long> slotIds = null;
        // 媒体模糊查询
        if (StringUtils.isNotBlank(req.getAppSearch())) {
            App app = new App();
            app.setSearchValue(req.getAppSearch());
            List<Long> appIds = appMapper.selectAppIdList(app);
            if (CollectionUtils.isEmpty(appIds)) {
                return vo;
            }
            data.setAppIds(appIds);
        }

        // 广告位模糊查询
        if (StringUtils.isNotBlank(req.getSlotSearch())) {
            Slot slot = new Slot();
            slot.setSearchValue(req.getSlotSearch());
            slotIds = slotMapper.selectSlotIdList(slot);
            if (CollectionUtils.isEmpty(slotIds)) {
                return vo;
            }
            data.setSlotIds(slotIds);
        }

        //账号模糊搜索
        if(StringUtils.isNotBlank(req.getAccountSearch())){
            List<Long> accountIds = accountService.selectIdsByIdOrEmail(req.getAccountSearch());
            if(CollectionUtils.isEmpty(accountIds)){
                return vo;
            }
            data.setAccountIds(accountIds);
        }

        //查询所有广告位数据
        data.setStartDate(req.getStartDate());
        data.setEndDate(req.getEndDate());
        List<SlotData> slotDataList = selectSlotDataList(data);
        //<广告位id，广告位每日数据>
        Map<Long, List<SlotData>> slotDataMap = slotDataList.stream().collect(Collectors.groupingBy(SlotData::getSlotId));
        SlotActivityDataSumBo activityDataSums = slotActivityDataService.selectSlotActivityDataSum(new ArrayList<>(slotDataMap.keySet()), req.getStartDate(), req.getEndDate());

        //统计活动总数据
        slotDataMap.values().forEach(slotDatas ->{
            statisticsSlotData(slotDatas,vo);
        });
        if(Objects.nonNull(activityDataSums)){
            vo.setJoinPv(activityDataSums.getJoinPv());
            vo.setJoinUv(activityDataSums.getJoinUv());
        }
        // 今日结算款特殊处理
        if (DateUtils.containsDate(req.getStartDate(), req.getEndDate(), DateUtil.beginOfDay(new Date()))) {
            vo.setNhCost(vo.getNhCost() + sumTodayNhCostMap(new ArrayList<>(slotDataMap.keySet())));
            vo.setTotalCost(vo.getNhCost() + vo.getOuterCost());
        }
        return vo;
    }

    @Override
    public List<SlotData> selectDataByAppIdAndDate(Long appId, Date curDate) {
        return slotDataMapper.selectDataByAppIdAndDate(appId, curDate);
    }

    @Override
    public int batchUpdateSlotRequestPvAndUv(List<SlotData> slotDataList) {
        if(CollectionUtils.isEmpty(slotDataList)){
            return 0;
        }
        return slotDataMapper.batchUpdateSlotRequestPvAndUv(slotDataList);
    }

    @Override
    public int batchUpdateSlotAppRevenue(List<SlotData> slotDataList) {
        if(CollectionUtils.isEmpty(slotDataList)){
            return 0;
        }
        return slotDataMapper.batchUpdateSlotAppRevenue(slotDataList);
    }

    @Override
    public Map<Long, Long> selectTodayNhCostMap(List<Long> slotIds) {
        if (CollectionUtils.isEmpty(slotIds)) {
            return Collections.emptyMap();
        }

        // 查询广告位CPC消耗
        Map<Long, Integer> advertConsumeMap = advertSlotDayDataService.selectTodaySlotAdvertCpcConsumeMap(slotIds);
        // 查询表单消耗
        Map<String, Integer> formConsumeMap  = redisCache.getCacheMap(EngineRedisKeyFactory.K051.join(DateUtil.today()));
        // 汇总数据
        Map<Long, Long> consumeMap = new HashMap<>();
        advertConsumeMap.forEach((slotId, consume) -> consumeMap.put(slotId, consumeMap.getOrDefault(slotId, 0L) + consume));
        formConsumeMap.forEach((slotIdStr, consume) -> {
            Long slotId = Long.valueOf(slotIdStr);
            if (slotIds.contains(slotId)) {
                consumeMap.put(slotId, consumeMap.getOrDefault(slotId, 0L) + consume);
            }
        });
        return consumeMap;
    }

    @Override
    public Long sumTodayNhCostMap(List<Long> slotIds) {
        if (CollectionUtils.isEmpty(slotIds)) {
            return 0L;
        }
        // 查询广告位CPC消耗
        Long advertConsume = advertSlotDayDataService.sumTodaySlotAdvertCpcConsumeMap(slotIds);

        // 查询表单消耗
        List<Integer> formConsumeList = redisCache.getMultiCacheMapValue(EngineRedisKeyFactory.K051.join(DateUtil.today()), ListUtils.toStringList(slotIds));
        if (CollectionUtils.isEmpty(formConsumeList)) {
            return advertConsume;
        }
        return advertConsume + formConsumeList.stream().filter(Objects::nonNull).mapToInt(s -> s).sum();
    }

    @Override
    public int batchInsertOrUpdateAppSlotRequestData(List<SlotData> datas) {
        if(CollectionUtils.isEmpty(datas)){
            return 0;
        }
        return slotDataMapper.batchInsertOrUpdateAppSlotRequestData(datas);
    }

    @Override
    public List<SlotData> selectListByAppIdsAndDates(Set<Long> appIds, Set<String> curDate) {
        if(CollectionUtils.isEmpty(appIds) || CollectionUtils.isEmpty(curDate)){
            return Collections.emptyList();
        }
        return slotDataMapper.selectListByAppIdsAndDates(appIds,curDate);
    }

    /**
     * 构建广告位月统计数据
     *
     * @param slotDatas 广告位日数据列表
     */
    private void statisticsSlotData(List<SlotData> slotDatas,CrmSlotDataStatisticsVO vo) {

        slotDatas.forEach(slotData -> {
            vo.setSlotRequestUv(slotData.getSlotRequestUvOriginal() + NumberUtils.defaultInt(vo.getSlotRequestUv()));
            vo.setSlotRequestPv(slotData.getSlotRequestPv() + NumberUtils.defaultInt(vo.getSlotRequestPv()));
            vo.setTotalConsume(slotData.getTotalConsume().intValue() + NumberUtils.defaultInt(vo.getTotalConsume()));
            vo.setNhConsume(slotData.getNhConsume().intValue() + NumberUtils.defaultInt(vo.getNhConsume()));
            vo.setOuterConsume(slotData.getOuterConsume().intValue()+NumberUtils.defaultInt(vo.getOuterConsume()));
            vo.setNhCost(slotData.getNhCost() + NumberUtils.defaultLong(vo.getNhCost(),0L));
            vo.setOuterCost(slotData.getOuterCost() + NumberUtils.defaultLong(vo.getOuterCost(),0L));
            vo.setTotalCost(slotData.getNhCost() + slotData.getOuterCost() + NumberUtils.defaultLong(vo.getTotalCost(),0L));
            vo.setAppRevenue(slotData.getAppRevenue().intValue() +  NumberUtils.defaultInt(vo.getAppRevenue()));
        });
    }

    /**
     * 填充广告位活动数据
     *
     * @param data         结果对象
     * @param activityData 活动数据
     */
    private void fillActivityData(CrmSlotDataVO data, SlotActivityData activityData) {
        data.setAdClickUv(activityData.getAdClickUv());
        data.setAdClickPv(activityData.getAdClickPv());
        data.setAdExposurePv(activityData.getAdExposurePv());
        data.setAdExposureUv(activityData.getAdExposureUv());
        data.setAdLaunch(activityData.getAdLaunch());
        data.setAdRequest(activityData.getAdRequest());
        data.setLaunchRate(NumberUtils.calculatePercent(data.getAdLaunch(), data.getAdRequest()));
        data.setJoinUv(activityData.getJoinUv());
        data.setJoinPv(activityData.getJoinPv());
        data.setActivityRequestPv(activityData.getActivityRequestPv());
        data.setActivityRequestUv(activityData.getActivityRequestUv());
        data.setActArriveRate(NumberUtils.calculatePercent(data.getActivityRequestUv(), data.getSlotRequestUv()));
        data.setJoinRate(NumberUtils.calculatePercent(data.getJoinUv(), data.getActivityRequestUv()));
        data.setRejoin(NumberUtils.calculateRate(data.getJoinPv(), data.getJoinUv()));
        data.setCtr(NumberUtils.calculatePercent(data.getAdClickPv(), data.getAdExposurePv()));
        data.setAdLaunchPerUv(NumberUtils.calculateRate(data.getAdLaunch(), data.getActivityRequestUv()));
        data.setAdClickPerUv(NumberUtils.calculateRate(data.getAdClickUv(), data.getActivityRequestUv()));
    }

    /**
     * 填充落地页数据
     *
     * @param data         结果对象
     * @param landpageData 落地页数据
     */
    private void fillLandpageData(CrmSlotDataVO data, AdvertSlotDayData landpageData) {
        data.setLpExposurePv(landpageData.getLpExposurePv());
        data.setLpExposureUv(landpageData.getLpExposureUv());
        data.setLpClickPv(landpageData.getLpClickPv());
        data.setLpClickUv(landpageData.getLpClickUv());
        data.setCvrPv(NumberUtils.calculatePercent(data.getLpClickPv(), data.getLpExposurePv()));
        data.setCvrUv(NumberUtils.calculatePercent(data.getLpClickUv(), data.getLpExposureUv()));
    }

    /**
     * 设置请求分页数据
     */
    protected void startPage() {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        if (StringUtils.isNotNull(pageNum) && StringUtils.isNotNull(pageSize)) {
            String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
            PageHelper.startPage(pageNum, pageSize, orderBy);
        }
    }

    /**
     * 重新统计广告位访问uv
     *
     * @param dataList 广告位数据
     */
    private void calculateSlotReqUv(List<SlotData> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        dataList.forEach(slotData -> {
            slotData.setSlotRequestUvOriginal(slotData.getSlotRequestUv());
            Integer slotReqUv = SlotReqUvCalculateUtil.calculateSlotReqUv(slotData.getCurDate(), slotData.getAppId(), slotData.getSlotRequestUv());
            slotData.setSlotRequestUv(slotReqUv);
        });
    }

    /**
     * 重新统计广告位访问uv
     *
     * @param dataList 广告位数据
     */
    private void calculateCrmSlotReqUv(List<CrmSlotDataBo> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        dataList.forEach(slotData -> {
            slotData.setSlotRequestUvOriginal(slotData.getSlotRequestUv());
            Integer slotReqUv = SlotReqUvCalculateUtil.calculateSlotReqUv(slotData.getCurDate(), slotData.getAppId(), slotData.getSlotRequestUv());
            slotData.setSlotRequestUv(slotReqUv);
        });
    }
}
