package com.ruoyi.system.service.advertiser;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 广告主标签Service接口
 *
 * <AUTHOR>
 * @date 2022/2/21
 */
public interface AdvertiserTagService {

    /**
     * 添加广告主标签
     *
     * @param advertiserId 广告主ID
     * @param tag 标签
     * @return true.添加成功,false.添加失败
     */
    boolean add(Long advertiserId, String tag);

    /**
     * 更新广告主标签
     *
     * @param advertiserId 广告主ID
     * @param tags 标签集合
     * @return true.更新成功,false.更新失败
     */
    boolean update(Long advertiserId, Set<String> tags);

    /**
     * 删除广告主标签
     *
     * @param advertiserId 广告主ID
     * @param tag 标签
     * @return true.删除成功,false.删除失败
     */
    boolean remove(Long advertiserId, String tag);

    /**
     * 获取所有广告主及标签
     *
     * @return 广告主-标签列表映射
     */
    Map<Long, List<String>> getMap();

    /**
     * 根据标签获取广告主
     *
     * @param tag 标签
     * @return 广告主ID列表
     */
    List<Long> getAdvertiserByTag(String tag);

    /**
     * 获取广告主所有标签
     *
     * @param advertiserId 广告主ID
     * @return 标签集合
     */
    Set<String> get(Long advertiserId);

    /**
     * 判断广告主是否已有标签
     *
     * @param advertiserId 广告主ID
     * @param tags 标签列表
     * @return true.存在,false.不存在
     */
    boolean isExist(Long advertiserId, String... tags);
}
