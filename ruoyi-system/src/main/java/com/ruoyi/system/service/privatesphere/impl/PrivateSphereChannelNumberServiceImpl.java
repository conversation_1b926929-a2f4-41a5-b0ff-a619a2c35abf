package com.ruoyi.system.service.privatesphere.impl;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.privatesphere.PrivateSphereChannelNumberService;
import com.ruoyi.system.entity.privatesphere.PrivateSphereChannelNumberEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import com.ruoyi.system.mapper.privatesphere.PrivateSphereChannelNumberMapper;

/**
 * 私域渠道号表 Service
 *
 * <AUTHOR>
 * @date 2023-2-8 17:03:55
 */
@Service
public class PrivateSphereChannelNumberServiceImpl implements PrivateSphereChannelNumberService {
    @Autowired
    private PrivateSphereChannelNumberMapper privateSphereChannelNumberMapper;

    @Override
    public Boolean insert(PrivateSphereChannelNumberEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return privateSphereChannelNumberMapper.insert(entity) > 0;
    }

    @Override
    public Boolean deleteById(Long id) {
        if (Objects.isNull(id)) {
            return false;
        }
        return privateSphereChannelNumberMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean updateById(PrivateSphereChannelNumberEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return privateSphereChannelNumberMapper.updateById(entity) > 0;
    }

    @Override
    public PrivateSphereChannelNumberEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return privateSphereChannelNumberMapper.selectById(id);
    }

    @Override
    public int batchInsertOrUpdate(List<PrivateSphereChannelNumberEntity> entities) {
        if(CollectionUtils.isEmpty(entities)){
            return 0;
        }
        return privateSphereChannelNumberMapper.batchInsertOrUpdate(entities);
    }

    @Override
    public List<PrivateSphereChannelNumberEntity> selectByChannelIds(List<Long> channelIds) {
        if(CollectionUtils.isEmpty(channelIds)){
            return Collections.emptyList();
        }
        return privateSphereChannelNumberMapper.selectByChannelIds(channelIds);
    }
}
