package com.ruoyi.system.service.datasource.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.ruoyi.common.utils.DateUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.datashow.AdvertDayConsumeDataMapper;
import com.ruoyi.system.entity.datashow.AdvertDayConsumeData;
import com.ruoyi.system.service.datasource.AdvertDayConsumeDataService;

/**
 * 广告日消耗数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-08-24
 */
@Service
public class AdvertDayConsumeDataServiceImpl implements AdvertDayConsumeDataService {

    @Autowired
    private AdvertDayConsumeDataMapper advertDayConsumeDataMapper;

    /**
     * 查询广告日消耗数据
     *
     * @param id 广告日消耗数据ID
     * @return 广告日消耗数据
     */
    @Override
    public AdvertDayConsumeData selectAdvertDayConsumeDataById(Long id) {
        return advertDayConsumeDataMapper.selectAdvertDayConsumeDataById(id);
    }

    @Override
    public AdvertDayConsumeData selectByDateAndAdvertId(Date curDate, Long advertId) {
        return advertDayConsumeDataMapper.selectByDateAndAdvertId(curDate, advertId);
    }

    @Override
    public Map<String, Integer> selectConsumeByDateAndAdvertIds(Date startDate, Date endDate, List<Long> advertIds) {
        if (CollectionUtils.isEmpty(advertIds)) {
            return Collections.emptyMap();
        }

        AdvertDayConsumeData param = new AdvertDayConsumeData();
        param.setStartDate(startDate);
        param.setEndDate(endDate);
        param.setAdvertIds(advertIds);
        List<AdvertDayConsumeData> list = selectAdvertDayConsumeDataList(param);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        return list.stream().collect(Collectors.toMap(
                data -> data.getAdvertId() + "-" + DateUtils.dateTime(data.getCurDate()), AdvertDayConsumeData::getConsume,
                (oldVal, newVal) -> newVal));
    }

    @Override
    public int addConsumeData(AdvertDayConsumeData advertDayConsumeData) {
        if (null == advertDayConsumeData.getId()) {
            return 0;
        }
        return advertDayConsumeDataMapper.addConsumeData(advertDayConsumeData);
    }

    /**
     * 查询广告日消耗数据列表
     *
     * @param advertDayConsumeData 广告日消耗数据
     * @return 广告日消耗数据
     */
    @Override
    public List<AdvertDayConsumeData> selectAdvertDayConsumeDataList(AdvertDayConsumeData advertDayConsumeData) {
        return advertDayConsumeDataMapper.selectAdvertDayConsumeDataList(advertDayConsumeData);
    }

    /**
     * 新增广告日消耗数据
     *
     * @param advertDayConsumeData 广告日消耗数据
     * @return 结果
     */
    @Override
    public int insertAdvertDayConsumeData(AdvertDayConsumeData advertDayConsumeData) {
        return advertDayConsumeDataMapper.insertAdvertDayConsumeData(advertDayConsumeData);
    }

    /**
     * 修改广告日消耗数据
     *
     * @param advertDayConsumeData 广告日消耗数据
     * @return 结果
     */
    @Override
    public int updateAdvertDayConsumeData(AdvertDayConsumeData advertDayConsumeData) {
        return advertDayConsumeDataMapper.updateAdvertDayConsumeData(advertDayConsumeData);
    }
}
