package com.ruoyi.system.service.publisher.prepay.impl;

import com.ruoyi.system.entity.account.finance.AccountPrepayApplyRecordEntity;
import com.ruoyi.system.mapper.account.AccountPrepayApplyRecordMapper;
import com.ruoyi.system.req.publisher.prepay.PrepayRecordListParam;
import com.ruoyi.system.service.publisher.prepay.AccountPrepayApplyRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 媒体预付款申请记录 Service
 *
 * <AUTHOR>
 * @date 2022-7-29 14:44:56
 */
@Service
public class AccountPrepayApplyRecordServiceImpl implements AccountPrepayApplyRecordService {

    @Autowired
    private AccountPrepayApplyRecordMapper accountPrepayApplyRecordMapper;

    @Override
    public Boolean insert(AccountPrepayApplyRecordEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return accountPrepayApplyRecordMapper.insert(entity) > 0;
    }

    @Override
    public Boolean updateById(AccountPrepayApplyRecordEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return accountPrepayApplyRecordMapper.updateById(entity) > 0;
    }

    @Override
    public AccountPrepayApplyRecordEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return accountPrepayApplyRecordMapper.selectById(id);
    }

    @Override
    public List<AccountPrepayApplyRecordEntity> selectList(PrepayRecordListParam param) {
        return accountPrepayApplyRecordMapper.selectList(param);
    }

    @Override
    public List<AccountPrepayApplyRecordEntity> selectListByAccountIdAndAuditStatus(Long accountId, Integer auditStatus) {
        return accountPrepayApplyRecordMapper.selectListByAccountIdAndAuditStatus(accountId, auditStatus);
    }

    @Override
    public int countByAccountIdAndAuditStatus(Long accountId, Integer auditStatus) {
        return accountPrepayApplyRecordMapper.countByAccountIdAndAuditStatus(accountId, auditStatus);
    }
}
