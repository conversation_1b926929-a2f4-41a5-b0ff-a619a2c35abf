package com.ruoyi.system.service.shorturl.impl;

import cn.hutool.core.codec.Base62;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.utils.BizUtils;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.InnerLogUtils;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.system.entity.shorturl.ShortUrlDataEntity;
import com.ruoyi.system.mapper.shorturl.ShortUrlDataMapper;
import com.ruoyi.system.message.rocketmq.producer.LogMqProducer;
import com.ruoyi.system.param.shorturl.ShortUrlDataParam;
import com.ruoyi.system.service.shorturl.ShortUrlDataService;
import com.ruoyi.system.util.ShortUrlUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.ruoyi.common.enums.InnerLogType.SHORT_URL_REQUEST;

/**
 * 短链数据表 Service
 *
 * <AUTHOR>
 * @date 2022-10-8 14:16:45
 */
@Slf4j
@Service
public class ShortUrlDataServiceImpl implements ShortUrlDataService {

    @Autowired
    private ShortUrlDataMapper shortUrlDataMapper;

    @Autowired
    private LogMqProducer logMqProducer;

    @Override
    public Boolean insert(ShortUrlDataEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return shortUrlDataMapper.insert(entity) > 0;
    }

    @Override
    public Boolean deleteById(Long id) {
        if (Objects.isNull(id)) {
            return false;
        }
        return shortUrlDataMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean updateById(ShortUrlDataEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return shortUrlDataMapper.updateById(entity) > 0;
    }

    @Override
    public ShortUrlDataEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return shortUrlDataMapper.selectById(id);
    }

    @Override
    public ShortUrlDataEntity selectBy(ShortUrlDataEntity param) {
        return shortUrlDataMapper.selectBy(param);
    }

    @Override
    public void statistics(String idStr) {
        Date curDate = DateUtil.beginOfDay(new Date());
        HttpServletRequest request = ServletUtils.getRequest();
        String ip = IpUtils.getIpAddr(request);
        String userAgent = request.getHeader("User-Agent");
        // 如果 ua 存在 antispam，不做统计
        if (userAgent.contains("antispam")) {
            return;
        }
        String referer = request.getHeader("referer");
        GlobalThreadPool.statExecutorService.submit(() -> {
            long shortUrlId = ShortUrlUtils.shortUrlUrlToId(idStr);
            String userId = Md5Utils.hash(ip + userAgent);

            // 打印业务日志
            try {
                JSONObject logJson = InnerLogUtils.buildEmptyJSON();
                // 没有相关字段，借用广告位ID保存短链ID
                logJson.put("slotId", shortUrlId);
                logJson.put("deviceId", userId);
                logJson.put("ip", ip);
                logJson.put("userAgent", userAgent);
                logJson.put("referer", referer);
                InnerLogUtils.log(SHORT_URL_REQUEST, logJson);
                logMqProducer.sendMsg(SHORT_URL_REQUEST, logJson);
            } catch (Exception e) {
                log.error("短链访问打印业务日志异常, shortUrlId={}", shortUrlId, e);
            }

            // 更新数据
            int uv = BizUtils.countUv(EngineRedisKeyFactory.K043.join(DateUtils.dateTime(curDate), idStr), userId);
            ShortUrlDataEntity param = new ShortUrlDataEntity();
            param.setCurDate(curDate);
            param.setShortUrlId(shortUrlId);
            ShortUrlDataEntity shortUrlData = selectBy(param);
            if (null == shortUrlData) {
                shortUrlData = new ShortUrlDataEntity();
                shortUrlData.setCurDate(curDate);
                shortUrlData.setShortUrlId(shortUrlId);
                try {
                    insert(shortUrlData);
                } catch (DuplicateKeyException e) {
                    log.error("ShortUrlDataEntity 插入冲突");
                }
                shortUrlData = selectBy(param);
            }
            shortUrlDataMapper.update(shortUrlData.getId(), 1, uv);
        });
    }

    @Override
    public List<ShortUrlDataEntity> selectListByParam(ShortUrlDataParam param) {
        if(Objects.nonNull(param.getStartDate()) && Objects.nonNull(param.getEndDate())){
            param.setStartDate(DateUtil.beginOfDay(param.getStartDate()));
            param.setEndDate(DateUtil.endOfDay(param.getEndDate()));
        }
        return shortUrlDataMapper.selectListByParam(param);
    }
}
