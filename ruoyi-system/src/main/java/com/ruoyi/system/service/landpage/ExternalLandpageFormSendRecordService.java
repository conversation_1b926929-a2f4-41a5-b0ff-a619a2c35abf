package com.ruoyi.system.service.landpage;

import com.ruoyi.system.entity.landpage.ExternalLandpageFormSendRecordEntity;

import java.util.List;
import java.util.Set;

/**
 * 外部落地页表单上报记录 Service
 *
 * <AUTHOR>
 * @date 2023-4-11 15:38:53
 */
public interface ExternalLandpageFormSendRecordService {

    /**
     * 新增记录
     */
    Boolean insert(ExternalLandpageFormSendRecordEntity entity);

    /**
     * 根据id删除
     */
    Boolean deleteById(Long id);

    /**
     * 根据id更新
     */
    Boolean updateById(ExternalLandpageFormSendRecordEntity entity);

    /**
     * 根据id获取
     */
    ExternalLandpageFormSendRecordEntity selectById(Long id);

    /**
     * 查询已回传的外部表单编号
     *
     * @param externalNoList 外部表单编号列表
     * @return 已回传的外部表单编号列表
     */
    Set<String> selectExternalNoSet(List<String> externalNoList);

    /**
     * 查询广告主ID列表
     *
     * @return 广告主ID列表
     */
    List<Long> selectAdvertiserIds();
}
