package com.ruoyi.system.service.datasource;

import com.ruoyi.system.entity.datashow.AdvertDayConsumeData;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 广告日消耗数据Service接口
 *
 * <AUTHOR>
 * @date 2021-08-24
 */
public interface AdvertDayConsumeDataService {

    /**
     * 查询广告日消耗数据
     *
     * @param id 广告日消耗数据ID
     * @return 广告日消耗数据
     */
    AdvertDayConsumeData selectAdvertDayConsumeDataById(Long id);

    /**
     * 查询广告消耗数据
     *
     * @param curDate 日期
     * @param advertId 广告ID
     * @return 广告消耗数据
     */
    AdvertDayConsumeData selectByDateAndAdvertId(Date curDate, Long advertId);

    /**
     * 查询广告消耗数据
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param advertIds 广告ID列表
     * @return 广告消耗数据
     */
    Map<String, Integer> selectConsumeByDateAndAdvertIds(Date startDate, Date endDate, List<Long> advertIds);

    /**
     * 修改广告日消耗数据
     *
     * @param advertDayConsumeData 广告日消耗数据
     * @return 结果
     */
    int addConsumeData(AdvertDayConsumeData advertDayConsumeData);

    /**
     * 查询广告日消耗数据列表
     *
     * @param advertDayConsumeData 广告日消耗数据
     * @return 广告日消耗数据集合
     */
    List<AdvertDayConsumeData> selectAdvertDayConsumeDataList(AdvertDayConsumeData advertDayConsumeData);

    /**
     * 新增广告日消耗数据
     *
     * @param advertDayConsumeData 广告日消耗数据
     * @return 结果
     */
    int insertAdvertDayConsumeData(AdvertDayConsumeData advertDayConsumeData);

    /**
     * 修改广告日消耗数据
     *
     * @param advertDayConsumeData 广告日消耗数据
     * @return 结果
     */
    int updateAdvertDayConsumeData(AdvertDayConsumeData advertDayConsumeData);
}
