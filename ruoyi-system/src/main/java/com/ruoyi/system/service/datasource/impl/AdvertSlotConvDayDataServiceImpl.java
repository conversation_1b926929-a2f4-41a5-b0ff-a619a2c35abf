package com.ruoyi.system.service.datasource.impl;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.system.bo.landpage.AdvertConvDataParamBo;
import com.ruoyi.system.bo.landpage.ConvDataBo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.datasource.AdvertSlotConvDayDataService;
import com.ruoyi.system.entity.datashow.AdvertSlotConvDayDataEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.ruoyi.system.mapper.datashow.AdvertSlotConvDayDataMapper;

/**
 * 广告广告位维度后端转化日数据表 Service
 *
 * <AUTHOR>
 * @date 2023-2-9 10:54:16
 */
@Service
public class AdvertSlotConvDayDataServiceImpl implements AdvertSlotConvDayDataService {

    @Autowired
    private AdvertSlotConvDayDataMapper advertSlotConvDayDataMapper;

    @Override
    public int insert(AdvertSlotConvDayDataEntity entity) {
        if (Objects.isNull(entity)) {
            return 0;
        }
        return advertSlotConvDayDataMapper.insert(entity);
    }

    @Override
    public int updateById(AdvertSlotConvDayDataEntity entity) {
        if (Objects.isNull(entity)) {
            return 0;
        }
        return advertSlotConvDayDataMapper.updateById(entity);
    }

    @Override
    public AdvertSlotConvDayDataEntity selectBy(AdvertSlotConvDayDataEntity param) {
        return advertSlotConvDayDataMapper.selectBy(param);
    }

    @Override
    public Map<String, ConvDataBo> countByDateAndSlotIds(Date startDate, Date endDate, List<Long> slotIds) {
        if (CollectionUtils.isEmpty(slotIds)) {
            return Collections.emptyMap();
        }
        List<ConvDataBo> list = advertSlotConvDayDataMapper.countByDateAndSlotIds(startDate, endDate, slotIds);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(s ->  s.getSlotId() + "_" + DateUtil.formatDate(s.getCurDate()), Function.identity(), (v1, v2) -> v2));
    }

    @Override
    public Map<String, ConvDataBo> countByDateAndAdvertIds(Date startDate, Date endDate, List<Long> advertIds) {
        if (CollectionUtils.isEmpty(advertIds)) {
            return Collections.emptyMap();
        }
        List<ConvDataBo> list = advertSlotConvDayDataMapper.countByDateAndAdvertIds(startDate, endDate, advertIds);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(s ->  s.getAdvertId() + "-" + DateUtil.formatDate(s.getCurDate()), Function.identity(), (v1, v2) -> v2));
    }

    @Override
    public Map<Date, ConvDataBo> countByDateAndAdvertIds2(Date startDate, Date endDate, List<Long> advertIds) {
        if (CollectionUtils.isEmpty(advertIds)) {
            return Collections.emptyMap();
        }
        List<ConvDataBo> list = advertSlotConvDayDataMapper.countByDateAndAdvertIds2(startDate, endDate, advertIds);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(ConvDataBo::getCurDate, Function.identity(), (v1, v2) -> v2));
    }

    @Override
    public Map<String, ConvDataBo> countByDateAndAdvertIdAndAppIds(Date startDate, Date endDate, Long advertId, List<Long> appIds) {
        if (null == advertId) {
            return Collections.emptyMap();
        }
        List<ConvDataBo> list = advertSlotConvDayDataMapper.countByDateAndAdvertIdAndAppIds(startDate, endDate, advertId, appIds);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(s ->  s.getAppId() + "_" + DateUtil.formatDate(s.getCurDate()), Function.identity(), (v1, v2) -> v2));
    }

    @Override
    public Map<String, ConvDataBo> countByDateAndAdvertIdAndSlotIds(Date startDate, Date endDate, Long advertId, List<Long> slotIds) {
        if (null == advertId) {
            return Collections.emptyMap();
        }
        List<ConvDataBo> list = advertSlotConvDayDataMapper.countByDateAndAdvertIdAndSlotIds(startDate, endDate, advertId, slotIds,null);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(s -> s.getSlotId() + "_" + DateUtil.formatDate(s.getCurDate()), Function.identity(), (v1, v2) -> v2));
    }

    @Override
    public Map<String, ConvDataBo> countByDateAndAdvertIdAndSlotIds(Date startDate, Date endDate, List<Long> advertIds, List<Long> slotIds) {
        List<ConvDataBo> list = advertSlotConvDayDataMapper.countByDateAndAdvertIdAndSlotIds(startDate, endDate, null, slotIds,advertIds);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(s -> s.getSlotId() + "_" +s.getAdvertId()+ "_" + DateUtil.formatDate(s.getCurDate()), Function.identity(), (v1, v2) -> v2));
    }

    @Override
    public ConvDataBo selectStatisticAdvertConvData(AdvertConvDataParamBo param) {
        ConvDataBo convData = advertSlotConvDayDataMapper.selectStatisticAdvertConvData(param);
        return null != convData ? convData : new ConvDataBo();
    }
}
