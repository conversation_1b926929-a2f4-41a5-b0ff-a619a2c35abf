package com.ruoyi.system.service.quickapp;

import com.ruoyi.system.entity.quickapp.QuickappGoodsEntity;

import java.util.List;

/**
 * 快应用商品表 Service
 *
 * <AUTHOR>
 * @date 2022-8-15 14:15:35
 */
public interface QuickappGoodsService {
    /**
     * 新增记录
     */
    Boolean insert(QuickappGoodsEntity entity);

    /**
     * 根据id删除
     */
    Boolean deleteById(Long id);

    /**
     * 根据id更新
     */
    Boolean updateById(QuickappGoodsEntity entity);

    /**
     * 根据id获取
     */
    QuickappGoodsEntity selectById(Long id);

    /**
     * 分页查询列表
     * @return
     */
    List<QuickappGoodsEntity> selectList();

}
