package com.ruoyi.system.service.publisher.prepay;

import com.ruoyi.system.entity.account.finance.AccountPrepayStatementRecordEntity;

import java.util.List;
import java.util.Map;

/**
 * 媒体预付款结算记录 Service
 *
 * <AUTHOR>
 * @date 2022-7-29 14:45:13
 */
public interface AccountPrepayStatementRecordService {

    /**
     * 新增记录
     */
    boolean insert(AccountPrepayStatementRecordEntity entity);

    /**
     * 根据id更新
     */
    Boolean updateById(AccountPrepayStatementRecordEntity entity);

    /**
     * 根据id获取
     */
    AccountPrepayStatementRecordEntity selectById(Long id);

    /**
     * 查询媒体的历史结算总金额
     *
     * @param accountId 媒体账号ID
     * @return 历史结算总金额
     */
    Integer sumAmountByAccountId(Long accountId);

    /**
     * 批量查询媒体的历史结算总金额
     *
     * @param accountIds 媒体账号ID列表
     * @return 媒体账号ID-历史结算总金额映射
     */
    Map<Long, Integer> sumAmountByAccountIds(List<Long> accountIds);

    /**
     * 查询媒体已结算的预付款记录
     *
     * @param accountId 媒体账号ID
     * @return 预付款记录ID-结算金额总和映射
     */
    Map<Long, Integer> sumAmountGroupByPrepayRecordId(Long accountId);
}
