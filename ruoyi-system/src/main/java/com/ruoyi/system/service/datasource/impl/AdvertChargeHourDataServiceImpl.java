package com.ruoyi.system.service.datasource.impl;

import org.springframework.stereotype.Service;
import com.ruoyi.system.service.datasource.AdvertChargeHourDataService;
import com.ruoyi.system.entity.datashow.AdvertChargeHourDataEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

import com.ruoyi.system.mapper.datashow.AdvertChargeHourDataMapper;

/**
 * 广告计费类型维度时段数据表 Service
 *
 * <AUTHOR>
 * @date 2022-10-21 14:03:32
 */
@Service
public class AdvertChargeHourDataServiceImpl implements AdvertChargeHourDataService {

    @Autowired
    private AdvertChargeHourDataMapper advertChargeHourDataMapper;

    @Override
    public AdvertChargeHourDataEntity selectBy(AdvertChargeHourDataEntity param) {
        return advertChargeHourDataMapper.selectBy(param);
    }

    @Override
    public int insert(AdvertChargeHourDataEntity entity) {
        return advertChargeHourDataMapper.insert(entity);
    }

    @Override
    public int updateById(AdvertChargeHourDataEntity entity) {
        return advertChargeHourDataMapper.updateById(entity);
    }

    @Override
    public AdvertChargeHourDataEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return advertChargeHourDataMapper.selectById(id);
    }
}
