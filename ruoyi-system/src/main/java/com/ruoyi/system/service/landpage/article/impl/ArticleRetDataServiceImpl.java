package com.ruoyi.system.service.landpage.article.impl;

import com.ruoyi.system.bo.landpage.article.ArticleRetDataUpdateParamBo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.landpage.article.ArticleRetDataService;
import com.ruoyi.system.entity.landpage.article.ArticleRetDataEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.Objects;

import com.ruoyi.system.mapper.landpage.article.ArticleRetDataMapper;

/**
 * 文章返回拦截数据表 Service
 *
 * <AUTHOR>
 * @date 2024-2-29 17:39:13
 */
@Slf4j
@Service
public class ArticleRetDataServiceImpl implements ArticleRetDataService {

    @Autowired
    private ArticleRetDataMapper articleRetDataMapper;

    @Override
    public Boolean insert(ArticleRetDataEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return articleRetDataMapper.insert(entity) > 0;
    }

    @Override
    public Boolean updateById(ArticleRetDataEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return articleRetDataMapper.updateById(entity) > 0;
    }

    @Override
    public ArticleRetDataEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return articleRetDataMapper.selectById(id);
    }

    @Override
    public ArticleRetDataEntity selectBy(Date curDate, Long linkId, String retUrlMd5) {
        return articleRetDataMapper.selectBy(curDate, linkId, retUrlMd5);
    }

    @Override
    public Boolean update(Long id, Integer requestPv, Integer requestUv) {
        if (null == id) {
            return false;
        }
        try {
            ArticleRetDataUpdateParamBo param = new ArticleRetDataUpdateParamBo();
            param.setId(id);
            param.setRequestPvAdd(requestPv);
            param.setRequestUvAdd(requestUv);
            return articleRetDataMapper.update(param) > 0;
        } catch (Exception e) {
            log.error("文章返回拦截数据更新异常, id={}", id, e);
        }
        return false;
    }
}
