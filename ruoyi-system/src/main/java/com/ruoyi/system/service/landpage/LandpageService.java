package com.ruoyi.system.service.landpage;

import com.ruoyi.system.entity.datashow.LandpageFormRecord;
import com.ruoyi.system.req.engine.LandPageFormReq;

import java.util.List;

/**
 * 落地页接口
 *
 * <AUTHOR>
 * @date 2021/9/1
 */
public interface LandpageService {

    /**
     * 落地页表单
     */
    void formSubmit(LandPageFormReq req);

    /**
     * 落地页数据回传（临时写的，以后再优化）
     *
     * @param lpTag 落地页标签
     * @param record 落地页数据
     * @param age 年龄
     */
    void callback(String lpTag, LandpageFormRecord record, Integer age);

    /**
     * 回传表单
     */
    String callback(String url, String accessKey, String secretKey, LandpageFormRecord record, Integer channel, Long advertiserId);

    /**
     * 检查身份证是否已发送过
     *
     * @param idCardMd5 身份证号
     * @param lpTag 落地页标签
     * @return 是否已发送过
     */
     boolean isIdCardSent(String idCardMd5, String lpTag);

    /**
     * 发送钉钉提醒(频次限制)
     *
     * @param content 内容
     */
     void sendLpNoticeToDing(String content);

    /**
     * 重传失败表单
     *
     * @param recordId 表单Id
     */
    void reCallbackForm(Long recordId);

    /**
     * 身份证校验重试
     *
     * @param limit 数量限制
     * @return 重试的表单ID列表
     */
    List<Long> retryIdCardAudit(Integer limit);

    /**
     * 累加广告位维度表单消耗
     *
     * @param slotId 广告位ID
     * @param formPrice 表单价格
     */
    void incrSlotFormConsume(Long slotId, Integer formPrice);
}
