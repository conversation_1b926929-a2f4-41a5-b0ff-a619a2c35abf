package com.ruoyi.system.service.antiblock;

/**
 * 防风控跳链服务接口
 *
 * <AUTHOR>
 * @date 2025/07/29
 */
public interface AntiBlockService {

    /**
     * 加密URL前缀标识
     */
    String ENCRYPTION_PREFIX = "ENC:";

    /**
     * 选择最佳的中间跳转域名
     *
     * @param currentDomain 当前域名
     * @return 推荐的跳转域名
     */
    String selectBestStepDomain(String currentDomain);


    /**
     * 加密目标URL
     *
     * @param targetUrl 原始目标URL
     * @return 加密后的短字符串
     */
    String encryptTargetUrl(String targetUrl);

    /**
     * 解密目标URL
     *
     * @param encryptedUrl 加密后的URL字符串
     * @return 原始目标URL
     */
    String decryptTargetUrl(String encryptedUrl);

    /**
     * 获取当前有效的加密密钥
     *
     * @return 加密密钥
     */
    String getCurrentEncryptionKey();
}
