package com.ruoyi.system.service.landpage.impl;

import com.alibaba.fastjson.JSON;
import com.ruoyi.common.constant.EngineRedisKeyFactory;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.advertiser.AdvertiserConsumeType;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.advertiser.LpCallbackAdvertiser;
import com.ruoyi.system.entity.account.AccountExtInfo;
import com.ruoyi.system.entity.datashow.LandpageFormSendRuleEntity;
import com.ruoyi.system.mapper.landpage.LandpageFormSendRuleMapper;
import com.ruoyi.system.service.advertiser.AdvertiserService;
import com.ruoyi.system.service.common.MapConfigService;
import com.ruoyi.system.service.landpage.LandpageFormSendRuleService;
import com.ruoyi.system.service.advertiser.AdvertiserTagService;
import com.ruoyi.system.service.manager.AccountService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.ruoyi.system.util.LandpageUtil.fixProvinceName;

/**
 * 落地页表单上报规则 Service
 *
 * <AUTHOR>
 * @date 2021-12-23
 */
@Service
public class LandpageFormSendRuleServiceImpl implements LandpageFormSendRuleService {

    @Autowired
    private LandpageFormSendRuleMapper landpageFormSendRecordMapper;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private AdvertiserTagService advertiserTagService;

    @Autowired
    private AdvertiserService advertiserService;

    @Autowired
    private MapConfigService mapConfigService;

    @Autowired
    private AccountService accountService;

    @Override
    public List<LpCallbackAdvertiser> selectAdvertiserList(String tag, String city, String province) {
        // 查询符合标签和地域规则的广告主列表
        List<LpCallbackAdvertiser> advertiserList = new ArrayList<>();
        advertiserList.addAll(selectAdvertiserList(tag, city));
        advertiserList.addAll(selectAdvertiserList(tag, province));
        if (null != province && !province.contains("省") && !province.contains("自治区") && !province.contains("市")) {
            advertiserList.addAll(selectAdvertiserList(tag, fixProvinceName(province)));
        }

        // 广告主按规则排序(表单价格*权重逆序)
        advertiserList.sort((o1, o2) -> o2.getOrderFactor().compareTo(o1.getOrderFactor()));
        return advertiserList;
    }

    @Override
    public List<LpCallbackAdvertiser> selectAdvertiserList(String tag, String area) {
        if (StringUtils.isBlank(tag) || StringUtils.isBlank(area)) {
            return Collections.emptyList();
        }

        // 查询地域映射缓存
        String redisKey = EngineRedisKeyFactory.K028.toString();
        if (!redisCache.hasKey(redisKey)) {
            // 重新构建缓存
            initCache();
        }
        try {
            // 筛选符合条件的广告主
            String value = redisCache.getCacheMapValue(redisKey, tag + "-" + area).toString();
            if (StringUtils.isBlank(value)) {
                return Collections.emptyList();
            }

            List<LpCallbackAdvertiser> advertiserList = JSON.parseArray(value, LpCallbackAdvertiser.class);
            return null == advertiserList ? Collections.emptyList() : advertiserList;
        } catch (Exception ignored) {}
        return Collections.emptyList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(LandpageFormSendRuleEntity entity) {
        deleteByAdvertiserId(entity.getAdvertiserId());
        return insert(entity);
    }

    @Override
    public List<LandpageFormSendRuleEntity> selectList() {
        return landpageFormSendRecordMapper.selectList();
    }

    @Override
    public LandpageFormSendRuleEntity selectByAdvertiserId(Long advertiserId) {
        return landpageFormSendRecordMapper.selectByAdvertiserId(advertiserId);
    }

    /**
     * 新增规则
     *
     * @param entity 规则
     * @return 影响行数
     */
    private int insert(LandpageFormSendRuleEntity entity) {
        int result = landpageFormSendRecordMapper.insert(entity);
        if (result > 0) {
            clearCache();
        }
        return result;
    }

    /**
     * 删除规则
     *
     * @param advertiserId 广告主ID
     * @return 影响行数
     */
    public int deleteByAdvertiserId(Long advertiserId) {
        return landpageFormSendRecordMapper.deleteByAdvertiserId(advertiserId);
    }

    /**
     * 构建缓存
     */
    private void initCache() {
        List<LandpageFormSendRuleEntity> rules = selectList();
        if (CollectionUtils.isEmpty(rules)) {
            return;
        }

        // 查询广告主表单价格
        List<Long> advertiserIds = rules.stream().map(LandpageFormSendRuleEntity::getAdvertiserId).collect(Collectors.toList());
        Map<Long, Integer> formPriceMap = advertiserService.selectFormPriceMap(advertiserIds);
        Map<Long, Double> weightMap = advertiserService.selectFormWeightMap(advertiserIds);
        Map<Long, AccountExtInfo> extInfoMap = accountService.selectExtInfoMapByIds(advertiserIds);

        // <标签-地域>-广告主列表映射
        Map<String, List<LpCallbackAdvertiser>> map = new HashMap<>();
        rules.forEach(rule -> {
            if (StringUtils.isBlank(rule.getArea())) {
                return;
            }
            List<String> areaList = JSON.parseArray(rule.getArea(), String.class);
            if (CollectionUtils.isEmpty(areaList)) {
                return;
            }
            // 广告主标签
            Set<String> tags = advertiserTagService.get(rule.getAdvertiserId());
            if (CollectionUtils.isEmpty(tags)) {
                return;
            }
            // 广告主
            LpCallbackAdvertiser advertiser = new LpCallbackAdvertiser();
            advertiser.setAdvertiserId(rule.getAdvertiserId());
            advertiser.setAgeMin(rule.getAgeMin());
            advertiser.setAgeMax(rule.getAgeMax());
            advertiser.setDailyLimit(rule.getDailyLimit());
            advertiser.setFormPrice(NumberUtils.defaultInt(formPriceMap.get(rule.getAdvertiserId())));
            advertiser.setGmtCreate(rule.getGmtCreate());
            advertiser.setOrderFactor((int) (advertiser.getFormPrice() * weightMap.getOrDefault(rule.getAdvertiserId(), 1.0) + 0.5));
            Optional.ofNullable(extInfoMap.get(rule.getAdvertiserId())).ifPresent(extInfo -> advertiser.setConsumeType(extInfo.getConsumeType()));

            // 地域列表
            areaList.forEach(area -> {
                tags.forEach(tag -> {
                    List<LpCallbackAdvertiser> advertiserList = map.getOrDefault(tag + "-" + area, new ArrayList<>());
                    advertiserList.add(advertiser);
                    map.put(tag + "-" + area, advertiserList);
                });
            });
        });

        String redisKey = EngineRedisKeyFactory.K028.toString();
        redisCache.setCacheMap(redisKey, map);
        redisCache.expire(redisKey, 2, TimeUnit.HOURS);
    }

    /**
     * 清除缓存
     */
    private void clearCache() {
        redisCache.deleteObject(EngineRedisKeyFactory.K028.toString());
    }
}
