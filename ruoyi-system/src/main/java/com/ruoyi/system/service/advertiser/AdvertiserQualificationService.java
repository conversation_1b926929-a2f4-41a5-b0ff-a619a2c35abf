package com.ruoyi.system.service.advertiser;

import com.ruoyi.system.bo.advertiser.AdvertiserIndustryBo;
import com.ruoyi.system.entity.advertiser.AdvertiserQualificationEntity;
import com.ruoyi.system.req.advertiser.qualification.WisQualificationInfoReq;

import java.util.List;
import java.util.Map;

/**
 * 广告主资质信息 Service
 *
 * <AUTHOR>
 * @date 2022-4-28 17:11:16
 */
public interface AdvertiserQualificationService {

    /**
     * 根据ID查询资质
     *
     * @param id 资质ID
     * @return 资质信息
     */
    AdvertiserQualificationEntity selectById(Long id);

    /**
     * 查询资质
     *
     * @param accountId 账号ID
     * @param industryId 行业ID
     * @param qualificationName 资质名称
     * @return 资质信息
     */
    AdvertiserQualificationEntity selectBy(Long accountId, Long industryId, String qualificationName);

    /**
     * 更新资质信息
     */
    int updateById(AdvertiserQualificationEntity entity);

    /**
     * 编辑更新资质信息
     *
     * @param accountId 账号ID
     * @param industryId 行业ID
     * @param qualificationList 待更新资质列表
     * @return 是否编辑更新成功
     */
    boolean edit(Long accountId, Long industryId, List<WisQualificationInfoReq> qualificationList);

    /**
     * 批量新增
     *
     * @param list 资质列表
     * @return 是否新增成功
     */
    boolean batchInsert(List<AdvertiserQualificationEntity> list);

    /**
     * 批量更新
     *
     * @param list 资质列表
     * @return 是否更新成功
     */
    boolean batchUpdate(List<AdvertiserQualificationEntity> list);

    /**
     * 批量删除
     *
     * @param accountId 广告主ID
     * @param ids 资质ID列表
     * @return 是否删除成功
     */
    boolean batchDelete(Long accountId, List<Long> ids);

    /**
     * 根据账号ID查询资质ID列表
     *
     * @param accountId 账号id
     * @return 资质ID列表
     */
    List<Long> selectIdsByAccountId(Long accountId);

    /**
     * 根据账号id查询资质列表
     *
     * @param accountId 账号id
     * @return 资质列表
     */
    List<AdvertiserQualificationEntity> selectListByAccountId(Long accountId);

    /**
     * 根据账号ID和行业ID查询资质列表
     *
     * @param accountId 账号id
     * @return 资质列表
     */
    List<AdvertiserQualificationEntity> selectListByAccountIdAndIndustryId(Long accountId, Long industryId);

    /**
     * 根据账号id列表查询资质列表
     *
     * @param accountIds 账号id列表
     * @return 资质列表
     */
    List<AdvertiserQualificationEntity> selectListByAccountIds(List<Long> accountIds);

    /**
     * 根据账号ID列表查询资质信息
     *
     * @param accountIds 账号id列表
     * @return 账号ID-资质列表映射
     */
    Map<Long, List<AdvertiserQualificationEntity>> selectMapByAccountIds(List<Long> accountIds);

    /**
     * 查询广告主的行业
     *
     * @param accountId 账号ID
     * @return 行业名称列表映射
     */
    List<String> selectIndustryListByAccountId(Long accountId);

    /**
     * 批量查询广告主的行业
     *
     * @param accountIds 账号id列表
     * @return 账号ID-行业列表映射
     */
    Map<Long, List<String>> selectIndustryMapByAccountIds(List<Long> accountIds);

    /**
     * 查询广告主的行业
     *
     * @param accountId 账号ID
     * @return 广告主行业列表
     */
    List<AdvertiserIndustryBo> selectIndustryByAccountId(Long accountId);

    /**
     * 查询广告主的行业
     *
     * @param accountId 账号ID
     * @return 行业ID列表
     */
    List<Long> selectIndustryIdsByAccountId(Long accountId);

    /**
     * 批量校验广告主行业及资质是否齐全
     *
     * @param accountIds 账号ID列表
     * @return 账号ID-是否齐全
     */
    Map<Long, Boolean> industryQualificationRequireCheck(List<Long> accountIds);

    /**
     * 批量校验广告主资质是否审核通过
     *
     * @param accountIds 账号ID列表
     * @return 账号ID-是否审核通过映射
     */
    Map<Long, Boolean> industryQualificationAuditCheck(List<Long> accountIds);

    /**
     * 批量查询广告主资质审核状态
     *
     * @param accountIds 账号ID列表
     * @return 账号ID-审核状态映射
     */
    Map<Long, Integer> selectAdvertiserAuditStatusMap(List<Long> accountIds);

    /**
     * 广告主行业资质齐全校验是否通过
     *
     * @param advertiserId 广告主ID
     * @return true.通过,false.未通过
     */
    boolean isIndustryCheckPass(Long advertiserId);

    /**
     * 广告主资质审核校验是否通过
     *
     * @param advertiserId 广告主ID
     * @return true.通过,false.未通过
     */
    boolean isQualificationAuditPass(Long advertiserId);
}
