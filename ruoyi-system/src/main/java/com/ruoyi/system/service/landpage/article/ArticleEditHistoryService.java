package com.ruoyi.system.service.landpage.article;

import com.ruoyi.system.entity.landpage.article.ArticleEditHistoryEntity;
import com.ruoyi.system.entity.landpage.article.ArticleEntity;

/**
 * 文章修改记录表 Service
 *
 * <AUTHOR>
 * @date 2023-12-1 15:13:41
 */
public interface ArticleEditHistoryService {

    /**
     * 新增记录
     */
    Boolean add(ArticleEntity article);

    /**
     * 新增记录
     */
    Boolean insert(ArticleEditHistoryEntity entity);

    /**
     * 根据id删除
     */
    Boolean deleteById(Long id);

    /**
     * 根据id更新
     */
    Boolean updateById(ArticleEditHistoryEntity entity);

    /**
     * 根据id获取
     */
    ArticleEditHistoryEntity selectById(Long id);
}
