package com.ruoyi.system.service.advertiser.fiance;

import com.ruoyi.system.bo.advertiser.finance.AdvertiserConsumeCategorySumBo;
import com.ruoyi.system.entity.advertiser.finance.AdvertiserConsumeCategoryRecordEntity;

import java.util.Date;
import java.util.List;

/**
 * 广告主结算分类消费记录表 Service
 *
 * <AUTHOR>
 * @date 2022-04-06
 */
public interface AdvertiserConsumeCategoryRecordService {

    /**
     * 更新广告主消费记录
     *
     * @param accountId 广告主ID
     * @param curDate 日期
     * @param billingType 结算指标类型
     * @param consumeAmount 消费金额(分)
     * @return 影响行数
     */
    int update(Long accountId, Date curDate, Integer billingType, Integer consumeAmount);

    /**
     * 根据广告主ID和日期查询消费记录
     *
     * @param accountId 广告主ID
     * @param curDate 日期
     * @param billingType 结算指标类型
     * @return 广告主消费记录
     */
    AdvertiserConsumeCategoryRecordEntity selectBy(Long accountId, Date curDate, Integer billingType);

    /**
     * 根据账号和日期统计消费记录
     *
     * @param accountId 账号id
     * @param startDate 起始日期
     * @param endDate 结束日期
     * @param billingType 计费类型
     * @return 消费记录
     */
    List<AdvertiserConsumeCategorySumBo> selectSumByDateAndAccountId(Long accountId,Date startDate,Date endDate,Integer billingType);
}
