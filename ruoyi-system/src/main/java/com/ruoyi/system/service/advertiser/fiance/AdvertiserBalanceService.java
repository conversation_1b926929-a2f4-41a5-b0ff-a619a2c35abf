package com.ruoyi.system.service.advertiser.fiance;

import com.ruoyi.system.bo.advertiser.finance.AdvertiserBalanceStatisticBo;
import com.ruoyi.system.entity.advertiser.finance.AdvertiserBalanceEntity;
import com.ruoyi.system.req.advertiser.finance.AdvertiserBalanceUpdateReq;

import java.util.List;
import java.util.Map;

/**
 * 广告主账户余额接口
 *
 * <AUTHOR>
 * @date 2022-3-18 17:57:16
 */
public interface AdvertiserBalanceService {

    /**
     * 新增广告主账户余额
     *
     * @param accountId 广告主ID
     * @return 影响行数
     */
    int create(Long accountId);

    /**
     * 更新广告主账户余额
     *
     * @param req 广告主账户余额更新参数
     * @return 影响行数
     */
    int updateBalance(AdvertiserBalanceUpdateReq req);

    /**
     * 查询广告主的账户余额，不存在则创建
     *
     * @param accountId 广告主ID
     * @return 账户余额
     */
    AdvertiserBalanceEntity selectOrCreate(Long accountId);

    /**
     * 查询广告主余额列表
     *
     * @param accountIds 广告主ID列表
     * @return 余额列表
     */
    List<AdvertiserBalanceEntity> selectList(List<Long> accountIds);

    /**
     * 查询广告主余额汇总
     *
     * @param accountIds 广告主ID列表
     * @param excludeAccountIds 排除的广告主ID列表
     * @return 余额汇总
     */
    AdvertiserBalanceStatisticBo selectStatisticBalance(List<Long> accountIds, List<Long> excludeAccountIds);

    /**
     * 查询广告主的账户余额
     *
     * @param accountId 广告主ID
     * @return 账户余额
     */
    AdvertiserBalanceEntity selectByAccountId(Long accountId);

    /**
     * 查询广告主的账户余额
     *
     * @param accountId 广告主ID
     * @return 账户余额
     */
    Integer selectTotalAmountByAccountId(Long accountId);

    /**
     * 查询多个广告主的账户余额总和
     *
     * @param accountIds 广告主ID列表
     * @return 账户余额总和
     */
    Integer sumTotalAmountByAccountIds(List<Long> accountIds);

    /**
     * 查询广告主的账户余额(兼容离线广告主)(仅限CRM使用)
     *
     * @param accountId 广告主ID
     * @return 账户余额
     */
    Integer selectAdjustTotalAmountByAccountId(Long accountId);

    /**
     * 查询广告主账户余额映射
     *
     * @param accountIds 广告主ID列表
     * @return 广告主ID-账户余额映射
     */
    Map<Long, Integer> selectAdvertiserBalanceMap(List<Long> accountIds);
}
