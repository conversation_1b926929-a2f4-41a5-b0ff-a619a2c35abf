package com.ruoyi.system.service.datasource.impl;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.system.bo.advert.AdvertDayDataBo;
import com.ruoyi.system.bo.advert.AdvertDaySumDataBo;
import com.ruoyi.system.bo.advertiser.finance.AdvertiserDaySumDataBo;
import com.ruoyi.system.entity.datashow.AdvertDayData;
import com.ruoyi.system.entity.datashow.AdvertDayStatisticData;
import com.ruoyi.system.mapper.datashow.AdvertDayDataMapper;
import com.ruoyi.system.req.advertiser.AdvertiserCpcDataReq;
import com.ruoyi.system.req.advertiser.AdvertiserDaySumDataReq;
import com.ruoyi.system.service.datasource.AdvertDayDataService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 广告日数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-08-25
 */
@Service
public class AdvertDayDataServiceImpl implements AdvertDayDataService {

    @Autowired
    private AdvertDayDataMapper advertDayDataMapper;

    @Override
    public AdvertDayData selectBy(AdvertDayData param) {
        return advertDayDataMapper.selectBy(param);
    }

    /**
     * 查询广告日数据列表
     *
     * @param param 查询条件
     * @return 广告日数据
     */
    @Override
    public List<AdvertDayData> selectAdvertDayDataList(AdvertDayData param) {
        return advertDayDataMapper.selectAdvertDayDataList(param);
    }

    @Override
    public List<AdvertDayDataBo> selectAdvertDayDataBoList(AdvertDayData param) {
        return advertDayDataMapper.selectAdvertDayDataBoList(param);
    }

    @Override
    public AdvertDayStatisticData selectStatisticAdvertDayData(AdvertDayData param) {
        return advertDayDataMapper.selectStatisticAdvertDayData(param);
    }

    @Override
    public List<AdvertDayData> selectAdvertDayDataList(AdvertiserCpcDataReq req) {
        return advertDayDataMapper.selectAdvertCpcDayDataList(req);
    }

    /**
     * 新增广告日数据
     *
     * @param param 广告日数据
     * @return 结果
     */
    @Override
    public int insertAdvertDayData(AdvertDayData param) {
        if (null == param.getCurDate() || null == param.getAdvertId()) {
            return 0;
        }
        return advertDayDataMapper.insertAdvertDayData(param);
    }

    /**
     * 修改广告日数据
     *
     * @param param 广告日数据
     * @return 结果
     */
    @Override
    public int updateAdvertDayData(AdvertDayData param) {
        if (null == param.getId()) {
            return 0;
        }
        return advertDayDataMapper.updateAdvertDayData(param);
    }

    @Override
    public List<AdvertDaySumDataBo> selectAdvertDaySumData(AdvertiserCpcDataReq req) {
        if(Objects.isNull(req) || CollectionUtils.isEmpty(req.getAdvertIds())){
            return Collections.emptyList();
        }
        return advertDayDataMapper.selectAdvertDaySumData(req);
    }

    @Override
    public Map<String, AdvertiserDaySumDataBo> groupByDateAndAdvertiserId(Date startDate, Date endDate, List<Long> advertiserIds) {
        if (CollectionUtils.isEmpty(advertiserIds)) {
            return Collections.emptyMap();
        }
        AdvertiserDaySumDataReq req = new AdvertiserDaySumDataReq();
        req.setStartDate(startDate);
        req.setEndDate(endDate);
        req.setAdvertiserIds(advertiserIds);
        List<AdvertiserDaySumDataBo> list = advertDayDataMapper.selectAdvertiserDaySumData(req);
        return list.stream().collect(Collectors.toMap(
                s -> DateUtil.formatDate(s.getCurDate()) + "_" + s.getAdvertiserId(), Function.identity(), (v1, v2) -> v2));
    }
}
