package com.ruoyi.system.service.privatesphere.impl;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.system.bo.privatesphere.PrivateSphereDataListBO;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.privatesphere.PrivateSphereDataService;
import com.ruoyi.system.entity.privatesphere.PrivateSphereDataEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;
import java.util.Objects;

import com.ruoyi.system.mapper.privatesphere.PrivateSphereDataMapper;

/**
 * 私域数据表 Service
 *
 * <AUTHOR>
 * @date 2023-2-10 14:18:11
 */
@Service
public class PrivateSphereDataServiceImpl implements PrivateSphereDataService {
    @Autowired
    private PrivateSphereDataMapper privateSphereDataMapper;

    @Override
    public Boolean insert(PrivateSphereDataEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return privateSphereDataMapper.insert(entity) > 0;
    }

    @Override
    public Boolean deleteById(Long id) {
        if (Objects.isNull(id)) {
            return false;
        }
        return privateSphereDataMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean updateById(PrivateSphereDataEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return privateSphereDataMapper.updateById(entity) > 0;
    }

    @Override
    public PrivateSphereDataEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return privateSphereDataMapper.selectById(id);
    }

    @Override
    public PrivateSphereDataEntity selectByDateAndProduct(Date curDate, Long accountId, Long productId) {
        if(Objects.isNull(curDate) || NumberUtils.isNullOrLteZero(accountId) || NumberUtils.isNullOrLteZero(productId)){
            return null;
        }
        curDate = DateUtil.beginOfDay(curDate);
        return privateSphereDataMapper.selectByDateAndProduct(curDate, accountId, productId);
    }

    @Override
    public List<PrivateSphereDataEntity> selectListByParam(PrivateSphereDataListBO bo) {
        if(Objects.isNull(bo)){
            bo = new PrivateSphereDataListBO();
        }
        return privateSphereDataMapper.selectListByParam(bo);
    }

    @Override
    public PrivateSphereDataEntity statisticsByParam(PrivateSphereDataListBO bo) {
        if(Objects.isNull(bo)){
            bo = new PrivateSphereDataListBO();
        }
        return privateSphereDataMapper.statisticsByParam(bo);
    }

    @Override
    public List<Long> selectIdsByParam(PrivateSphereDataListBO bo) {
        if(Objects.isNull(bo)){
            bo = new PrivateSphereDataListBO();
        }
        return privateSphereDataMapper.selectIdsByParam(bo);
    }

    @Override
    public int countByProductId(Long productId) {
        if(NumberUtils.isNullOrLteZero(productId)){
            return 0;
        }
        return privateSphereDataMapper.countByProductId(productId);
    }
}
