package com.ruoyi.system.service.advertiser.fiance;

import com.ruoyi.system.entity.advertiser.finance.AdvertiserConsumeRecordEntity;
import com.ruoyi.system.req.advertiser.finance.AdvertiserConsumeListReq;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 广告主消费记录表 Service
 *
 * <AUTHOR>
 * @date 2022-3-18 17:58:07
 */
public interface AdvertiserConsumeRecordService {

    /**
     * 更新广告主消费记录
     *
     * @param accountId 广告主ID
     * @param curDate 日期
     * @param consumeAmount 消费金额(分)
     * @return 影响行数
     */
    int update(Long accountId, Date curDate, Integer consumeAmount);

    /**
     * 根据id获取
     */
    AdvertiserConsumeRecordEntity selectById(Long id);

    /**
     * 根据广告主ID和日期查询消费记录
     *
     * @param accountId 广告主ID
     * @param curDate 日期
     * @return 广告主消费记录
     */
    AdvertiserConsumeRecordEntity selectByAccountIdAndDate(Long accountId, Date curDate);

    /**
     * 查询广告主消费记录
     *
     * @param req 参数
     * @return 广告主消费记录列表
     */
    List<AdvertiserConsumeRecordEntity> selectList(AdvertiserConsumeListReq req);

    /**
     * 查询广告主消费总金额
     *
     * @param accountId 账户id
     * @return 广告主消费总金额
     */
    Integer sumConsumeAmount(Long accountId);

    /**
     * 查询广告主消费总金额
     *
     * @param accountIds 广告主ID列表
     * @return 广告主消费总金额
     */
    Integer sumConsumeAmountByAccountIds(List<Long> accountIds);

    /**
     * 批量查询广告主消费总金额
     *
     * @param accountIds 广告主ID列表
     * @return 广告主ID-消费总金额映射
     */
    Map<Long, Integer> sumConsumeAmountGroupByAccountIds(List<Long> accountIds);

    /**
     * 批量查询指定日期的广告主消费总金额
     *
     * @param accountIds 广告主ID列表
     * @param date 日期
     * @return 广告主ID-消费总金额映射
     */
    Map<Long, Integer> sumConsumeAmountGroupByAccountIdsAndDate(List<Long> accountIds, Date date);
}
