package com.ruoyi.system.service.datasource;

import com.ruoyi.system.entity.datashow.AdvertSlotDayData;

import java.util.List;
import java.util.Map;

/**
 * 广告广告位日数据Service接口
 *
 * <AUTHOR>
 * @date 2021-10-15
 */
public interface AdvertSlotDayDataService {

    /**
     * 查询广告广告位日数据
     *
     * @param param 查询条件
     * @return 广告广告位日数据
     */
    AdvertSlotDayData selectBy(AdvertSlotDayData param);

    /**
     * 查询广告广告位日数据列表
     *
     * @param param 查询条件
     * @return 广告广告位日数据集合
     */
    List<AdvertSlotDayData> selectAdvertSlotDayDataList(AdvertSlotDayData param);

    /**
     * 查询广告广告位日数据汇总
     *
     * @param param 查询条件
     * @return 广告广告位日数据汇总
     */
    AdvertSlotDayData selectStatisticAdvertSlotDayData(AdvertSlotDayData param);

    /**
     * 新增广告广告位日数据
     *
     * @param param 广告广告位日数据
     * @return 结果
     */
    int insertAdvertSlotDayData(AdvertSlotDayData param);

    /**
     * 修改广告广告位日数据
     *
     * @param param 广告广告位日数据
     * @return 结果
     */
    int updateAdvertSlotDayData(AdvertSlotDayData param);

    /**
     * 查询广告位今日的CPC广告消耗
     *
     * @param slotIds 广告位ID列表
     * @return 广告位ID-CPC广告消耗映射
     */
    Map<Long, Integer> selectTodaySlotAdvertCpcConsumeMap(List<Long> slotIds);

    /**
     * 查询广告位今日的CPC广告消耗总和
     *
     * @param slotIds 广告位ID列表
     * @return 广告位今日的CPC广告消耗总和
     */
    Long sumTodaySlotAdvertCpcConsumeMap(List<Long> slotIds);
}
