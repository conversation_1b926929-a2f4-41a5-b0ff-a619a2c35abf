package com.ruoyi.system.service.privatesphere.impl;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.privatesphere.PrivateSphereChannelDataService;
import com.ruoyi.system.entity.privatesphere.PrivateSphereChannelDataEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import com.ruoyi.system.mapper.privatesphere.PrivateSphereChannelDataMapper;

/**
 * 私域渠道数据表 Service
 *
 * <AUTHOR>
 * @date 2023-2-10 15:51:03
 */
@Service
public class PrivateSphereChannelDataServiceImpl implements PrivateSphereChannelDataService {
    @Autowired
    private PrivateSphereChannelDataMapper privateSphereChannelDataMapper;

    @Override
    public Boolean insert(PrivateSphereChannelDataEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return privateSphereChannelDataMapper.insert(entity) > 0;
    }

    @Override
    public Boolean deleteById(Long id) {
        if (Objects.isNull(id)) {
            return false;
        }
        return privateSphereChannelDataMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean updateById(PrivateSphereChannelDataEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return privateSphereChannelDataMapper.updateById(entity) > 0;
    }

    @Override
    public PrivateSphereChannelDataEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return privateSphereChannelDataMapper.selectById(id);
    }

    @Override
    public int batchInsertOrUpdate(List<PrivateSphereChannelDataEntity> entities) {
        if(CollectionUtils.isEmpty(entities)){
            return 0;
        }
        return privateSphereChannelDataMapper.batchInsertOrUpdate(entities);
    }

    @Override
    public List<PrivateSphereChannelDataEntity> selectListByDataIds(List<Long> ids) {
        if(CollectionUtils.isEmpty(ids)){
            return Collections.emptyList();
        }
        return privateSphereChannelDataMapper.selectListByDataIds(ids);
    }

    @Override
    public List<PrivateSphereChannelDataEntity> statisticsByDataIds(List<Long> dataIds) {
        if(CollectionUtils.isEmpty(dataIds)){
            return null;
        }
        return privateSphereChannelDataMapper.statisticsByDataIds(dataIds);
    }
}
