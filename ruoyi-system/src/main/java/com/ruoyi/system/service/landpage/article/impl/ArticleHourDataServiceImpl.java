package com.ruoyi.system.service.landpage.article.impl;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.system.bo.landpage.article.ArticleDataBo;
import com.ruoyi.system.bo.landpage.article.ArticleDataParamBo;
import com.ruoyi.system.bo.landpage.article.ArticleDataUpdateParamBo;
import com.ruoyi.system.entity.landpage.article.ArticleHourDataEntity;
import com.ruoyi.system.mapper.landpage.article.ArticleHourDataMapper;
import com.ruoyi.system.service.landpage.article.ArticleHourDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 文章时段数据表 Service
 *
 * <AUTHOR>
 * @date 2023-12-1 15:14:16
 */
@Slf4j
@Service
public class ArticleHourDataServiceImpl implements ArticleHourDataService {

    @Autowired
    private ArticleHourDataMapper articleHourDataMapper;

    @Override
    public Boolean insert(ArticleHourDataEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return articleHourDataMapper.insert(entity) > 0;
    }

    @Override
    public Boolean updateById(ArticleHourDataEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return articleHourDataMapper.updateById(entity) > 0;
    }

    @Override
    public Boolean update(Long id, Integer requestPv, Integer requestUv) {
        if (null == id) {
            return false;
        }
        try {
            ArticleDataUpdateParamBo param = new ArticleDataUpdateParamBo();
            param.setId(id);
            param.setRequestPvAdd(requestPv);
            param.setRequestUvAdd(requestUv);
            return articleHourDataMapper.update(param) > 0;
        } catch (Exception e) {
            log.error("文章数据更新异常, id={}", id, e);
        }
        return false;
    }

    @Override
    public ArticleHourDataEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return articleHourDataMapper.selectById(id);
    }

    @Override
    public ArticleHourDataEntity selectBy(Date curDate, Integer curHour, Long linkId, Long articleId) {
        return articleHourDataMapper.selectBy(curDate, curHour, linkId, articleId);
    }

    @Override
    public ArticleDataBo selectSumBy(ArticleDataParamBo param) {
        return articleHourDataMapper.selectSumBy(param);
    }

    @Override
    public List<ArticleDataBo> selectDataBy(ArticleDataParamBo param) {
        if (null == param || (null == param.getLinkId() && CollectionUtils.isEmpty(param.getArticleIds()))) {
            return Collections.emptyList();
        }
        return articleHourDataMapper.selectDayDataBy(param);
    }

    @Override
    public Map<Long, ArticleDataBo> selectTodayDataByArticleIds(List<Long> articleIds) {
        if (CollectionUtils.isEmpty(articleIds)) {
            return Collections.emptyMap();
        }
        Date today = DateUtil.beginOfDay(new Date());
        ArticleDataParamBo param = new ArticleDataParamBo();
        param.setArticleIds(articleIds);
        param.setStartDate(today);
        param.setEndDate(today);
        List<ArticleDataBo> list = articleHourDataMapper.selectDayDataBy(param);
        return list.stream().collect(Collectors.toMap(ArticleDataBo::getArticleId, Function.identity(), (v1, v2) -> v2));
    }
}
