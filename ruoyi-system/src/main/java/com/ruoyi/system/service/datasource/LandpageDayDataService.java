package com.ruoyi.system.service.datasource;

import com.ruoyi.system.entity.datashow.LandpageDayData;
import com.ruoyi.system.req.landpage.LandpageDataReq;

import java.util.List;

/**
 * 落地页维度日数据表 Service
 *
 * <AUTHOR>
 * @date 2022-9-21 16:17:57
 */
public interface LandpageDayDataService {

    /**
     * 查询列表
     *
     * @param param 参数
     * @return 结果
     */
    List<LandpageDayData> selectList(LandpageDataReq param);

    /**
     * 新增记录
     */
    int insert(LandpageDayData entity);

    /**
     * 根据id更新
     */
    int updateById(LandpageDayData entity);

    /**
     * 根据id获取
     */
    LandpageDayData selectById(Long id);

    /**
     * 根据条件获取
     *
     * @param param 参数
     * @return 结果
     */
    LandpageDayData selectBy(LandpageDayData param);
}
