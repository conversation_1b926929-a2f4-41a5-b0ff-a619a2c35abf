package com.ruoyi.system.service.advertiser.impl;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.system.bo.advertiser.finance.AdvertiserConsumeOffsetBo;
import com.ruoyi.system.entity.advertiser.DspAdvertiserConsumeRecordEntity;
import com.ruoyi.system.mapper.advertiser.DspAdvertiserConsumeRecordMapper;
import com.ruoyi.system.service.advertiser.DspAdvertiserConsumeRecordService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * DSP广告主消费记录 Service
 *
 * <AUTHOR>
 * @date 2022-7-13 17:30:15
 */
@Service
public class DspAdvertiserConsumeRecordServiceImpl implements DspAdvertiserConsumeRecordService {

    @Autowired
    private DspAdvertiserConsumeRecordMapper dspAdvertiserConsumeRecordMapper;

    @Override
    public Boolean insert(DspAdvertiserConsumeRecordEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return dspAdvertiserConsumeRecordMapper.insert(entity) > 0;
    }

    @Override
    public Boolean updateById(DspAdvertiserConsumeRecordEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return dspAdvertiserConsumeRecordMapper.updateById(entity) > 0;
    }

    @Override
    public DspAdvertiserConsumeRecordEntity selectByAdvertiserIdAndDate(Long advertiserId, Date curDate) {
        if (null == advertiserId || null == curDate) {
            return null;
        }
        return dspAdvertiserConsumeRecordMapper.selectByAdvertiserIdAndDate(advertiserId, curDate);
    }

    @Override
    public List<Date> selectInvisibleDateList(Long advertiserId) {
        if (null == advertiserId) {
            return new ArrayList<>();
        }
        return dspAdvertiserConsumeRecordMapper.selectInvisibleDateList(advertiserId);
    }

    @Override
    public Map<Long, List<Date>> selectInvisibleDateMap(List<Long> advertiserIds) {
        if (CollectionUtils.isEmpty(advertiserIds)) {
            return Collections.emptyMap();
        }
        List<DspAdvertiserConsumeRecordEntity> list = dspAdvertiserConsumeRecordMapper.batchSelectInvisibleDateList(advertiserIds);
        return list.stream().collect(Collectors.groupingBy(DspAdvertiserConsumeRecordEntity::getAdvertiserId,Collectors.mapping(DspAdvertiserConsumeRecordEntity::getCurDate, Collectors.toList())));
    }

    @Override
    public Map<Date, DspAdvertiserConsumeRecordEntity> selectVisibleDataMap(Date startDate, Date endDate, Long advertiserId) {
        List<DspAdvertiserConsumeRecordEntity> list = dspAdvertiserConsumeRecordMapper.selectList(startDate, endDate, Collections.singletonList(advertiserId), 1);
        return list.stream().collect(Collectors.toMap(DspAdvertiserConsumeRecordEntity::getCurDate, Function.identity(), (v1, v2) -> v2));
    }

    @Override
    public Map<String, DspAdvertiserConsumeRecordEntity> selectMap(Date startDate, Date endDate, List<Long> advertiserIds) {
        if (CollectionUtils.isEmpty(advertiserIds)) {
            return Collections.emptyMap();
        }
        List<DspAdvertiserConsumeRecordEntity> list = dspAdvertiserConsumeRecordMapper.selectList(startDate, endDate, advertiserIds, null);
        return list.stream().collect(Collectors.toMap(s -> DateUtil.formatDate(s.getCurDate()) + "_" + s.getAdvertiserId(), Function.identity(), (v1, v2) -> v2));
    }

    @Override
    public Integer consumeOffset(Long advertiserId) {
        if (null == advertiserId) {
            return 0;
        }
        return dspAdvertiserConsumeRecordMapper.consumeOffset(advertiserId);
    }

    @Override
    public Integer consumeOffsetForCrm(Long advertiserId) {
        if (null == advertiserId) {
            return 0;
        }
        return dspAdvertiserConsumeRecordMapper.consumeOffsetForCrm(advertiserId);
    }

    @Override
    public Map<Long, Integer> batchConsumeOffsetForCrm(List<Long> advertiserIds) {
        if (CollectionUtils.isEmpty(advertiserIds)) {
            return Collections.emptyMap();
        }
        List<AdvertiserConsumeOffsetBo> list = dspAdvertiserConsumeRecordMapper.batchConsumeOffsetForCrm(advertiserIds);
        return list.stream().collect(Collectors.toMap(AdvertiserConsumeOffsetBo::getAdvertiserId, AdvertiserConsumeOffsetBo::getOffset, (v1, v2) -> v2));
    }

    @Override
    public Map<String, AdvertiserConsumeOffsetBo> batchConsumeOffset(Date startDate, Date endDate, List<Long> advertiserIds) {
        List<AdvertiserConsumeOffsetBo> list = dspAdvertiserConsumeRecordMapper.batchConsumeOffset(startDate, endDate, advertiserIds);
        return list.stream().collect(Collectors.toMap(s -> DateUtil.formatDate(s.getCurDate()) + "_" + s.getAdvertiserId(), Function.identity(), (v1, v2) -> v2));
    }

    @Override
    public Map<Long, List<AdvertiserConsumeOffsetBo>> batchConsumeOffset(Date endDate, List<Long> advertiserIds) {
        List<AdvertiserConsumeOffsetBo> list = dspAdvertiserConsumeRecordMapper.batchConsumeOffset(null, endDate, advertiserIds);
        return list.stream().collect(Collectors.groupingBy(AdvertiserConsumeOffsetBo::getAdvertiserId));
    }

    @Override
    public Map<Long, Integer> advertiserConsumeOffsetForCrm(List<Long> advertiserIds) {
        if (CollectionUtils.isEmpty(advertiserIds)) {
            return Collections.emptyMap();
        }
        List<AdvertiserConsumeOffsetBo> list = dspAdvertiserConsumeRecordMapper.advertiserConsumeOffsetForCrm(advertiserIds);
        return list.stream().collect(Collectors.toMap(AdvertiserConsumeOffsetBo::getAdvertiserId, AdvertiserConsumeOffsetBo::getOffset, (v1, v2) -> v2));
    }

    @Override
    public Long sumAdvertiserConsumeOffsetForCrm(List<Long> advertiserIds, List<Long> excludeAccountIds) {
        return dspAdvertiserConsumeRecordMapper.sumAdvertiserConsumeOffsetForCrm(advertiserIds, excludeAccountIds);
    }

    @Override
    public Map<Long, Integer> advertiserConsumeOffset(List<Long> advertiserIds) {
        if (CollectionUtils.isEmpty(advertiserIds)) {
            return Collections.emptyMap();
        }
        List<AdvertiserConsumeOffsetBo> list = dspAdvertiserConsumeRecordMapper.advertiserConsumeOffset(advertiserIds);
        return list.stream().collect(Collectors.toMap(AdvertiserConsumeOffsetBo::getAdvertiserId, AdvertiserConsumeOffsetBo::getOffset, (v1, v2) -> v2));
    }

    @Override
    public Integer advertiserConsumeOffsetSum(List<Long> advertiserIds) {
        if (CollectionUtils.isEmpty(advertiserIds)) {
            return 0;
        }
        return dspAdvertiserConsumeRecordMapper.advertiserConsumeOffsetSum(advertiserIds);
    }
}
