package com.ruoyi.system.service.datasource.impl;

import com.ruoyi.system.entity.datashow.AdvertiserConsumeData;
import com.ruoyi.system.mapper.datashow.AdvertiserConsumeDataMapper;
import com.ruoyi.system.service.datasource.AdvertiserConsumeDataService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 广告主日消耗数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-08-19
 */
@Service
public class AdvertiserConsumeDataServiceImpl implements AdvertiserConsumeDataService {

    @Autowired
    private AdvertiserConsumeDataMapper advertiserConsumeDataMapper;

    /**
     * 查询广告主日消耗数据列表
     *
     * @param param 广告主消耗请求参数
     * @return 广告主日消耗数据
     */
    @Override
    public List<AdvertiserConsumeData> selectAdvertiserConsumeDataList(AdvertiserConsumeData param) {
        return advertiserConsumeDataMapper.selectAdvertiserConsumeDataList(param);
    }

    @Override
    public AdvertiserConsumeData selectByDateAndAdvertiserId(Date curDate, Long advertiserId) {
        return advertiserConsumeDataMapper.selectByDateAndAdvertiserId(curDate, advertiserId);
    }

    /**
     * 新增广告主日消耗数据
     *
     * @param advertiserConsumeData 广告主日消耗数据
     * @return 结果
     */
    @Override
    public int insertAdvertiserConsumeData(AdvertiserConsumeData advertiserConsumeData) {
        return advertiserConsumeDataMapper.insertAdvertiserConsumeData(advertiserConsumeData);
    }

    /**
     * 修改广告主日消耗数据
     *
     * @param advertiserConsumeData 广告主日消耗数据
     * @return 结果
     */
    @Override
    public int updateAdvertiserConsumeData(AdvertiserConsumeData advertiserConsumeData) {
        return advertiserConsumeDataMapper.updateAdvertiserConsumeData(advertiserConsumeData);
    }

    @Override
    public int updateBudget(Long id, Long budget) {
        if (null == id) {
            return 0;
        }
        return advertiserConsumeDataMapper.updateBudget(id, budget);
    }

    @Override
    public int addConsumeData(AdvertiserConsumeData advertiserConsumeData) {
        return advertiserConsumeDataMapper.addConsumeData(advertiserConsumeData);
    }

    @Override
    public Map<Long, Long> selectConsumeMapByAdvertiserIdAndDate(List<Long> accountIds, Date curDate) {
        if (CollectionUtils.isEmpty(accountIds) || null == curDate) {
            return Collections.emptyMap();
        }
        List<AdvertiserConsumeData> list = advertiserConsumeDataMapper.selectListByAdvertiserIdAndDate(accountIds, curDate);
        return list.stream().collect(Collectors.toMap(AdvertiserConsumeData::getAdvertiserId, AdvertiserConsumeData::getConsume, (v1, v2) -> v2));
    }
}
