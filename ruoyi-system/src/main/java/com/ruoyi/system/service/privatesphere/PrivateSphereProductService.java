package com.ruoyi.system.service.privatesphere;

import com.ruoyi.system.entity.privatesphere.PrivateSphereProductEntity;

import java.util.List;
import java.util.Map;

/**
 * 私域产品表 Service
 *
 * <AUTHOR>
 * @date 2023-2-8 10:19:20
 */
public interface PrivateSphereProductService {
    /**
     * 新增记录
     */
    Boolean insert(PrivateSphereProductEntity entity);

    /**
     * 根据id删除
     */
    Boolean deleteById(Long id);

    /**
     * 根据id更新
     */
    Boolean updateById(PrivateSphereProductEntity entity);

    /**
     * 根据id获取
     */
    PrivateSphereProductEntity selectById(Long id);

    /**
     * 根据账号id和产品名称获取
     * @param accountId
     * @param productName
     * @return
     */
    PrivateSphereProductEntity selectByAccountIdAndName(Long accountId,String productName);

    /**
     * 根据id列表查询产品列表
     * @param ids
     * @return
     */
    List<PrivateSphereProductEntity> selectListByIds(List<Long> ids);

    /**
     * 根据id列表查询产品名称Map
     * @param ids
     * @return
     */
    Map<Long,String> selectProductNameMapByIds(List<Long> ids);

    /**
     * 根据账号id列表查询产品列表
     * @param accountIds
     * @return
     */
    List<PrivateSphereProductEntity> selectListByAccountIds(List<Long> accountIds);

    /**
     * 根据产品名称获取产品id列表
     * @param productName
     * @return
     */
    List<Long> selectProductIdsByName(String productName);

    /**
     * 查询产品列表
     * @return
     */
    List<PrivateSphereProductEntity> selectProductList();


}
