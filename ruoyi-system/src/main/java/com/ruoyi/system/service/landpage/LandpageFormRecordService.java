package com.ruoyi.system.service.landpage;

import com.ruoyi.system.entity.landpage.LandpageFormAreaCount;
import com.ruoyi.system.entity.landpage.LandpageFormFullRecord;
import com.ruoyi.system.req.landpage.LpManualAssignReq;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 落地页表单完整记录Service接口
 *
 * <AUTHOR>
 * @date 2021-08-31
 */
public interface LandpageFormRecordService {

    /**
     * 查询落地页表单完整记录列表
     *
     * @param param 查询条件
     * @return 落地页表单完整记录集合
     */
    List<LandpageFormFullRecord> selectList(LandpageFormFullRecord param);

    /**
     * 统计广告位的成功落地页表单数量
     *
     * @param startDate 起始日期
     * @param endDate 结束日期
     * @param slotIds 广告位ID列表
     * @return 广告位ID_日期-成功落地页表单数量映射
     */
    Map<String, Integer> countByDateAndSlotId(Date startDate, Date endDate, List<Long> slotIds);

    /**
     * 统计广告位的成功落地页表单数量总和
     *
     * @param startDate 起始日期
     * @param endDate 结束日期
     * @param slotIds 广告位ID列表
     * @return 成功落地页表单数量总和
     */
    Integer sumByDateAndSlotId(Date startDate, Date endDate, List<Long> slotIds);

    /**
     * 统计广告位-广告维度的成功落地页表单数量
     *
     * @param date 日期
     * @return 广告位ID_广告位-成功落地页表单数量映射
     */
    Map<String, Integer> countBySlotIdAndAdvertId(Date date);

    /**
     * 分配表单并提交到Redis延时队列
     *
     * @param req 参数
     */
    void manualAssign(LpManualAssignReq req);

    /**
     * 统计广告主的落地页表单数量
     *
     * @param startDate 起始日期
     * @param endDate 结束日期
     * @param advertiserIds 广告主ID列表
     * @param isSuccess 是否为成功表单
     * @return 日期_广告主ID-成功落地页表单数量映射
     */
    Map<String, Integer> countByDateAndAdvertiserId(Date startDate, Date endDate, List<Long> advertiserIds, Integer isSuccess);

    /**
     * 根据订单id列表查询落地页表单
     *
     * @param orderIds 订单号列表
     * @return 表单列表
     */
    List<LandpageFormFullRecord> selectByOrderIds(List<String> orderIds);

    /**
     * 表单数据地域分布
     *
     * @param advertiserIds 广告主ID立碑
     * @param startDate 起始日期
     * @param endDate 结束日期
     * @param areaList 地域列表
     * @return 表单数据地域分布
     */
    List<LandpageFormAreaCount> selectFormCountGroupByArea(List<Long> advertiserIds, Date startDate, Date endDate,List<String> areaList);

    /**
     * 表单汇总数据
     *
     * @param advertiserIds 广告主ID立碑
     * @param startDate 起始日期
     * @param endDate 结束日期
     * @param areaList 地域列表
     * @return 表单汇总数据
     */
    LandpageFormAreaCount selectFormCountSummary(List<Long> advertiserIds, Date startDate, Date endDate,List<String> areaList );

    /**
     * 查询所有的广告ID列表
     *
     * @return 广告ID列表
     */
    List<Long> selectTotalAdvertIds();

    /**
     * 查询所有的媒体ID列表
     *
     * @return 广告ID列表
     */
    List<Long> selectTotalAppIds();


    /**
     * 查询所有的广告位ID列表
     *
     * @return 广告ID列表
     */
    List<Long> selectTotalSlotIds();
}
