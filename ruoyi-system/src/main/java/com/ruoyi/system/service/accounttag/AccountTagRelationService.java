package com.ruoyi.system.service.accounttag;

import com.ruoyi.system.entity.accounttag.AccountTagRelationEntity;

import java.util.List;

/**
 * 账号标签关联表 Service
 *
 * <AUTHOR>
 * @date 2023-3-6 10:53:35
 */
public interface AccountTagRelationService {
    /**
     * 新增记录
     */
    Boolean insert(AccountTagRelationEntity entity);

    /**
     * 根据id删除
     */
    Boolean deleteById(Long id);

    /**
     * 根据id更新
     */
    Boolean updateById(AccountTagRelationEntity entity);

    /**
     * 根据id获取
     */
    AccountTagRelationEntity selectById(Long id);

    /**
     * 根据账号id和标签类型删除标签
     * @param accountId
     * @param type
     * @return
     */
    Boolean deleteByAccountIdAndType(Long accountId,Integer type);

    /**
     * 批量新增
     * @param entities
     * @return
     */
    Boolean batchInsert(List<AccountTagRelationEntity> entities);

    /**
     * 根据账号id和类型获取账号标签
     * @param accountId
     * @param tagType
     * @return
     */
    List<AccountTagRelationEntity> selectListByAccountType(Long accountId,Integer tagType);

    /**
     * 根据标签id列表查询账号
     * @param tagIds
     * @return
     */
    List<AccountTagRelationEntity> selectListByTagIds(List<Long> tagIds);

    /**
     * 根据账号和标签类型获取关联的标签id列表
     * @param accountId
     * @param tagType
     * @return
     */
    List<Long> selectTagIdsByAccountType(Long accountId,Integer tagType);



}
