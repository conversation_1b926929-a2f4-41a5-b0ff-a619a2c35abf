package com.ruoyi.system.service.tagmanager;

import com.ruoyi.system.entity.tagmanager.TagManagerEntity;

import java.util.List;

/**
 * 媒体标签表 Service
 *
 * <AUTHOR>
 * @date 2022-9-23 10:52:10
 */
public interface TagManagerService {
    /**
     * 新增记录
     */
    Boolean insert(TagManagerEntity entity);

    /**
     * 根据id删除
     */
    Boolean deleteById(Long id);

    /**
     * 根据id更新
     */
    Boolean updateById(TagManagerEntity entity);

    /**
     * 根据id获取
     */
    TagManagerEntity selectById(Long id);

    /**
     * 查询所有标签
     * @return
     */
    List<TagManagerEntity> selectAllTag();

    /**
     * 根据标签类型查询所有标签
     * @param type
     * @return
     */
    List<TagManagerEntity> selectAllTagByType(Integer type);

    /**
     * 根据id列表删除
     * @param ids
     * @return
     */
    Boolean deleteByIds(List<Long> ids);

    /**
     * 批量更新标签列表
     * @param updateList
     * @return
     */
    Boolean batchUpdate(List<TagManagerEntity> updateList);

    /**
     * 批量新增标签列表
     * @param insertList
     * @return
     */
    Boolean batchInsert(List<TagManagerEntity> insertList);

    /**
     * 根据id列表查询
     * @param ids
     * @return
     */
    List<TagManagerEntity> selectByIds(List<Long> ids);

    /**
     * 根据一级标签名称获取二级标签id列表
     *
     * @param type
     * @param tagName
     * @return
     */
    List<Long> selectTagIdsByFirstTagName(Integer type,String tagName);

    /**
     * 根据标签名称和类型获取二级标签ID列表
     *
     * @param type 标签
     * @param tagNames 标签名称列表
     * @return 标签ID列表
     */
    List<Long> selectTagIdsBySecondTagNames(Integer type, List<String> tagNames);

    /**
     * 根据父标签id列表查询子标签id列表
     *
     * @param parentIds 父标签ID列表
     * @return 子标签ID列表
     */
    List<Long> selectIdsByParentIds(List<Long> parentIds);
}
