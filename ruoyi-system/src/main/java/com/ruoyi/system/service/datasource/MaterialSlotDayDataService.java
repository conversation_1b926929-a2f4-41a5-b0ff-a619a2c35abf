package com.ruoyi.system.service.datasource;

import java.util.List;
import com.ruoyi.system.entity.datashow.MaterialSlotDayData;

/**
 * 素材广告位日数据Service接口
 *
 * <AUTHOR>
 * @date 2021-08-25
 */
public interface MaterialSlotDayDataService {

    /**
     * 查询素材广告位日数据
     *
     * @param param 查询条件
     * @return 素材广告位日数据
     */
    MaterialSlotDayData selectBy(MaterialSlotDayData param);

    /**
     * 查询素材广告位日数据列表
     *
     * @param param 查询条件
     * @return 素材广告位日数据集合
     */
    List<MaterialSlotDayData> selectMaterialSlotDayDataList(MaterialSlotDayData param);

    /**
     * 新增素材广告位日数据
     *
     * @param param 素材广告位日数据
     * @return 结果
     */
    int insertMaterialSlotDayData(MaterialSlotDayData param);

    /**
     * 修改素材广告位日数据
     *
     * @param param 素材广告位日数据
     * @return 结果
     */
    int updateMaterialSlotDayData(MaterialSlotDayData param);
}
