package com.ruoyi.system.service.landpage;

import com.ruoyi.system.bo.landpage.QwtfLandpageFormRecordSelectBo;
import com.ruoyi.system.entity.landpage.QwtfLandpageFormRecordEntity;

import java.util.List;

/**
 * 企微囤粉表单记录 Service
 *
 * <AUTHOR>
 * @date 2023-9-22 15:38:47
 */
public interface QwtfLandpageFormRecordService {

    /**
     * 新增记录
     */
    Boolean insert(QwtfLandpageFormRecordEntity entity);

    /**
     * 查询表单列表
     *
     * @param param 查询条件
     * @return 表单列表
     */
    List<QwtfLandpageFormRecordEntity> selectList(QwtfLandpageFormRecordSelectBo param);

    /**
     * 根据id更新
     */
    Boolean updateById(QwtfLandpageFormRecordEntity entity);

    /**
     * 批量更新好友状态
     *
     * @param unionIds unionId列表
     * @return 影响行数
     */
    int batchUpdateFriendStatus(List<String> unionIds);

    /**
     * 批量更新好友状态
     *
     * @param orderIds orderId列表
     * @return 影响行数
     */
    int batchUpdateFriendStatusByOrderIds(List<String> orderIds);

    /**
     * 根据id获取
     */
    QwtfLandpageFormRecordEntity selectById(Long id);

    /**
     * 根据unionId获取表单记录
     *
     * @param unionId 微信unionId
     * @return 表单记录
     */
    QwtfLandpageFormRecordEntity selectByUnionId(String unionId);

    /**
     * 根据unionId获取未加好友的表单记录
     *
     * @param unionIds 微信unionId列表
     * @return 表单记录
     */
    List<QwtfLandpageFormRecordEntity> selectNotFriendByUnionIds(List<String> unionIds);

    /**
     * 根据orderId获取未加好友的表单记录
     *
     * @param orderIds 微信orderId列表
     * @return 表单记录
     */
    List<QwtfLandpageFormRecordEntity> selectNotFriendByOrderIds(List<String> orderIds);

    /**
     * 查询所有的广告ID列表
     *
     * @return 广告ID列表
     */
    List<Long> selectTotalAdvertIds();

    /**
     * 查询所有的媒体ID列表
     *
     * @return 广告ID列表
     */
    List<Long> selectTotalAppIds();

    /**
     * 查询所有的广告位ID列表
     *
     * @return 广告ID列表
     */
    List<Long> selectTotalSlotIds();
}
