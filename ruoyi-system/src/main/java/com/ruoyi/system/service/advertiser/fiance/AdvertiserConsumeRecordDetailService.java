package com.ruoyi.system.service.advertiser.fiance;

import com.ruoyi.system.entity.advertiser.finance.AdvertiserConsumeDetailRecordEntity;

import java.util.List;

/**
 * 广告主消费记录明细表 Service
 *
 * <AUTHOR>
 * @date 2022-3-25
 */
public interface AdvertiserConsumeRecordDetailService {

    /**
     * 新增广告主消费记录明细
     */
    int insert(AdvertiserConsumeDetailRecordEntity record);

    /**
     * 更新广告主消费记录明细
     */
    int update(AdvertiserConsumeDetailRecordEntity record);

    /**
     * 查询广告主消费记录明细
     *
     * @param param 参数
     * @return 广告主消费记录明细
     */
    List<AdvertiserConsumeDetailRecordEntity> selectList(AdvertiserConsumeDetailRecordEntity param);
}
