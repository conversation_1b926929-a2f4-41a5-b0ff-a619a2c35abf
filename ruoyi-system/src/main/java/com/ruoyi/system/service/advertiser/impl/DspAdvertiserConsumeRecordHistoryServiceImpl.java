package com.ruoyi.system.service.advertiser.impl;

import org.springframework.stereotype.Service;
import com.ruoyi.system.service.advertiser.DspAdvertiserConsumeRecordHistoryService;
import com.ruoyi.system.entity.advertiser.DspAdvertiserConsumeRecordHistoryEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

import com.ruoyi.system.mapper.advertiser.DspAdvertiserConsumeRecordHistoryMapper;

/**
 * DSP广告主消费修改记录表 Service
 *
 * <AUTHOR>
 * @date 2022-7-13 17:28:38
 */
@Service
public class DspAdvertiserConsumeRecordHistoryServiceImpl implements DspAdvertiserConsumeRecordHistoryService {

    @Autowired
    private DspAdvertiserConsumeRecordHistoryMapper dspAdvertiserConsumeRecordHistoryMapper;

    @Override
    public Boolean insert(DspAdvertiserConsumeRecordHistoryEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return dspAdvertiserConsumeRecordHistoryMapper.insert(entity) > 0;
    }

    @Override
    public Boolean updateById(DspAdvertiserConsumeRecordHistoryEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return dspAdvertiserConsumeRecordHistoryMapper.updateById(entity) > 0;
    }

    @Override
    public DspAdvertiserConsumeRecordHistoryEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return dspAdvertiserConsumeRecordHistoryMapper.selectById(id);
    }
}
