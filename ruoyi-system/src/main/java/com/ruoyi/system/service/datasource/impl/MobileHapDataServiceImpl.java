package com.ruoyi.system.service.datasource.impl;

import com.ruoyi.system.entity.datashow.MobileHapDataEntity;
import com.ruoyi.system.mapper.datashow.MobileHapDataMapper;
import com.ruoyi.system.service.datasource.MobileHapDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 设备维度快应用数据表 Service
 *
 * <AUTHOR>
 * @date 2022-11-14 11:45:23
 */
@Service
public class MobileHapDataServiceImpl implements MobileHapDataService {

    @Autowired
    private MobileHapDataMapper mobileHapDataMapper;

    @Override
    public MobileHapDataEntity selectBy(MobileHapDataEntity param) {
        return mobileHapDataMapper.selectBy(param);
    }

    @Override
    public List<MobileHapDataEntity> selectList(MobileHapDataEntity param) {
        return mobileHapDataMapper.selectList(param);
    }

    @Override
    public int insert(MobileHapDataEntity entity) {
        if (Objects.isNull(entity)) {
            return 0;
        }
        return mobileHapDataMapper.insert(entity);
    }

    @Override
    public int updateById(MobileHapDataEntity entity) {
        if (Objects.isNull(entity)) {
            return 0;
        }
        return mobileHapDataMapper.updateById(entity);
    }

    @Override
    public MobileHapDataEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return mobileHapDataMapper.selectById(id);
    }
}
