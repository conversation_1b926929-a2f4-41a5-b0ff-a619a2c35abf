package com.ruoyi.system.service.datasource.impl;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import com.ruoyi.system.bo.slotactivitydata.SlotActivityDataSumBo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.datashow.SlotActivityDataMapper;
import com.ruoyi.system.entity.datashow.SlotActivityData;
import com.ruoyi.system.service.datasource.SlotActivityDataService;

/**
 * 广告位维度活动数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-07-21
 */
@Service
public class SlotActivityDataServiceImpl implements SlotActivityDataService {

    @Autowired
    private SlotActivityDataMapper slotActivityDataMapper;

    @Override
    public SlotActivityData selectBy(Long slotId, Long activityId, Date curDate) {
        return slotActivityDataMapper.selectBy(slotId, activityId, curDate);
    }

    /**
     * 查询广告位维度活动数据
     *
     * @param id 广告位维度活动数据ID
     * @return 广告位维度活动数据
     */
    @Override
    public SlotActivityData selectSlotActivityDataById(String id) {
        return slotActivityDataMapper.selectSlotActivityDataById(id);
    }

    /**
     * 查询广告位维度活动数据列表
     *
     * @param slotActivityData 广告位维度活动数据
     * @return 广告位维度活动数据
     */
    @Override
    public List<SlotActivityData> selectSlotActivityDataList(SlotActivityData slotActivityData) {
        return slotActivityDataMapper.selectSlotActivityDataList(slotActivityData);
    }

    /**
     * 新增广告位维度活动数据
     *
     * @param slotActivityData 广告位维度活动数据
     * @return 结果
     */
    @Override
    public int insertSlotActivityData(SlotActivityData slotActivityData) {
        return slotActivityDataMapper.insertSlotActivityData(slotActivityData);
    }

    /**
     * 修改广告位维度活动数据
     *
     * @param slotActivityData 广告位维度活动数据
     * @return 结果
     */
    @Override
    public int updateSlotActivityData(SlotActivityData slotActivityData) {
        if (null == slotActivityData.getId()) {
            return 0;
        }
        return slotActivityDataMapper.updateSlotActivityData(slotActivityData);
    }

    @Override
    public List<SlotActivityDataSumBo> selectSumSlotActivityDataGroupBySlot(List<Long> slotIds, Date startDate, Date endDate) {
        if(CollectionUtils.isEmpty(slotIds) || Objects.isNull(startDate) || Objects.isNull(endDate)){
            return Collections.emptyList();
        }
        return slotActivityDataMapper.selectSumSlotActivityDataGroupBySlot(slotIds, startDate, endDate);
    }

    @Override
    public SlotActivityDataSumBo selectSlotActivityDataSum(List<Long> slotIds, Date startDate, Date endDate) {
        if (CollectionUtils.isEmpty(slotIds)) {
            return null;
        }
        return slotActivityDataMapper.selectSlotActivityDataSum(slotIds, startDate, endDate);
    }

    @Override
    public List<SlotActivityData> selectSlotActivityData(List<Long> slotIds, Date startDate, Date endDate) {
        if(CollectionUtils.isEmpty(slotIds) || Objects.isNull(startDate) || Objects.isNull(endDate)){
            return Collections.emptyList();
        }
        return slotActivityDataMapper.selectSlotActivityData(slotIds, startDate, endDate);
    }

    @Override
    public List<SlotActivityData> selectDataByAppIdAndDate(Long appId, Date curDate) {
        return slotActivityDataMapper.selectDataByAppIdAndDate(appId, curDate);
    }
}
