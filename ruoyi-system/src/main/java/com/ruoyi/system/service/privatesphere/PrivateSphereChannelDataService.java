package com.ruoyi.system.service.privatesphere;

import com.ruoyi.system.entity.privatesphere.PrivateSphereChannelDataEntity;

import java.util.List;

/**
 * 私域渠道数据表 Service
 *
 * <AUTHOR>
 * @date 2023-2-10 15:51:03
 */
public interface PrivateSphereChannelDataService {
    /**
     * 新增记录
     */
    Boolean insert(PrivateSphereChannelDataEntity entity);

    /**
     * 根据id删除
     */
    Boolean deleteById(Long id);

    /**
     * 根据id更新
     */
    Boolean updateById(PrivateSphereChannelDataEntity entity);

    /**
     * 根据id获取
     */
    PrivateSphereChannelDataEntity selectById(Long id);

    /**
     * 批量新增更新
     * @param entities
     * @return
     */
    int batchInsertOrUpdate(List<PrivateSphereChannelDataEntity> entities);

    /**
     * 根据渠道id列表查询渠道数据
     * @param ids
     * @return
     */
    List<PrivateSphereChannelDataEntity> selectListByDataIds(List<Long> ids);

    /**
     * 根据数据id列表查询统计
     * @param dataIds
     * @return
     */
    List<PrivateSphereChannelDataEntity> statisticsByDataIds(List<Long> dataIds);




}
