package com.ruoyi.system.service.landpage;

import com.ruoyi.system.bo.landpage.PlayletLandpageFormRecordSelectBo;
import com.ruoyi.system.entity.landpage.PlayletLandpageFormRecordEntity;

import java.util.List;

/**
 * 短剧落地页表单记录 Service
 *
 * <AUTHOR>
 * @date 2023-8-2 16:11:47
 */
public interface PlayletLandpageFormRecordService {

    /**
     * 查询落地页表单记录列表
     *
     * @param param 查询条件
     * @return 落地页表单记录集合
     */
    List<PlayletLandpageFormRecordEntity> selectList(PlayletLandpageFormRecordSelectBo param);

    /**
     * 查询落地页表单汇总数据
     *
     * @param param 查询条件
     * @return 落地页表单汇总数据
     */
    PlayletLandpageFormRecordEntity selectStatistic(PlayletLandpageFormRecordSelectBo param);

    /**
     * 新增记录
     */
    Boolean insert(PlayletLandpageFormRecordEntity entity);

    /**
     * 根据id删除
     */
    Boolean deleteById(Long id);

    /**
     * 根据id更新
     */
    Boolean updateById(PlayletLandpageFormRecordEntity entity);

    /**
     * 根据id获取
     */
    PlayletLandpageFormRecordEntity selectById(Long id);

    /**
     * 根据tradeNo获取
     */
    PlayletLandpageFormRecordEntity selectByTradeNo(String tradeNo);

    /**
     * 查询所有的广告ID列表
     *
     * @return 广告ID列表
     */
    List<Long> selectTotalAdvertIds();

    /**
     * 查询所有的媒体ID列表
     *
     * @return 广告ID列表
     */
    List<Long> selectTotalAppIds();

    /**
     * 查询所有的广告位ID列表
     *
     * @return 广告ID列表
     */
    List<Long> selectTotalSlotIds();
}
