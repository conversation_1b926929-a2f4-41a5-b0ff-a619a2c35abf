package com.ruoyi.system.service.datasource.impl;

import com.ruoyi.system.entity.datashow.AdvertSlotDayData;
import com.ruoyi.system.mapper.datashow.AdvertSlotDayDataMapper;
import com.ruoyi.system.service.datasource.AdvertSlotDayDataService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 广告广告位日数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-10-15
 */
@Service
public class AdvertSlotDataServiceImpl implements AdvertSlotDayDataService {

    @Autowired
    private AdvertSlotDayDataMapper advertSlotDayDataMapper;

    @Override
    public AdvertSlotDayData selectBy(AdvertSlotDayData param) {
        return advertSlotDayDataMapper.selectBy(param);
    }

    /**
     * 查询广告广告位日数据列表
     *
     * @param param 查询条件
     * @return 广告广告位日数据
     */
    @Override
    public List<AdvertSlotDayData> selectAdvertSlotDayDataList(AdvertSlotDayData param) {
        return advertSlotDayDataMapper.selectAdvertSlotDayDataList(param);
    }

    @Override
    public AdvertSlotDayData selectStatisticAdvertSlotDayData(AdvertSlotDayData param) {
        return advertSlotDayDataMapper.selectStatisticAdvertSlotDayData(param);
    }

    /**
     * 新增广告广告位日数据
     *
     * @param param 广告广告位日数据
     * @return 结果
     */
    @Override
    public int insertAdvertSlotDayData(AdvertSlotDayData param) {
        if (null == param.getCurDate() || null == param.getAdvertId()) {
            return 0;
        }
        return advertSlotDayDataMapper.insertAdvertSlotDayData(param);
    }

    /**
     * 修改广告广告位日数据
     *
     * @param param 广告广告位日数据
     * @return 结果
     */
    @Override
    public int updateAdvertSlotDayData(AdvertSlotDayData param) {
        if (null == param.getId()) {
            return 0;
        }
        return advertSlotDayDataMapper.updateAdvertSlotDayData(param);
    }

    @Override
    public Map<Long, Integer> selectTodaySlotAdvertCpcConsumeMap(List<Long> slotIds) {
        if (CollectionUtils.isEmpty(slotIds)) {
            return Collections.emptyMap();
        }
        List<AdvertSlotDayData> list = advertSlotDayDataMapper.selectTodaySlotAdvertCpcConsumeMap(slotIds);
        return list.stream().collect(Collectors.toMap(AdvertSlotDayData::getSlotId, AdvertSlotDayData::getConsume, (v1, v2) -> v2));
    }

    @Override
    public Long sumTodaySlotAdvertCpcConsumeMap(List<Long> slotIds) {
        if (CollectionUtils.isEmpty(slotIds)) {
            return 0L;
        }
        return advertSlotDayDataMapper.sumTodaySlotAdvertCpcConsume(slotIds);
    }
}
