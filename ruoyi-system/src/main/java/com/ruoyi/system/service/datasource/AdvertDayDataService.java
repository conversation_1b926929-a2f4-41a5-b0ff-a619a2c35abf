package com.ruoyi.system.service.datasource;

import com.ruoyi.system.bo.advert.AdvertDayDataBo;
import com.ruoyi.system.bo.advert.AdvertDaySumDataBo;
import com.ruoyi.system.bo.advertiser.finance.AdvertiserDaySumDataBo;
import com.ruoyi.system.entity.datashow.AdvertDayData;
import com.ruoyi.system.entity.datashow.AdvertDayStatisticData;
import com.ruoyi.system.req.advertiser.AdvertiserCpcDataReq;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 广告日数据Service接口
 *
 * <AUTHOR>
 * @date 2021-08-25
 */
public interface AdvertDayDataService {

    /**
     * 查询广告日数据
     *
     * @param param 查询条件
     * @return 广告日数据
     */
    AdvertDayData selectBy(AdvertDayData param);

    /**
     * 查询广告日数据列表
     *
     * @param param 查询条件
     * @return 广告日数据集合
     */
    List<AdvertDayData> selectAdvertDayDataList(AdvertDayData param);

    /**
     * 查询广告日数据列表
     *
     * @param param 查询条件
     * @return 广告日数据集合
     */
    List<AdvertDayDataBo> selectAdvertDayDataBoList(AdvertDayData param);

    /**
     * 查询广告日数据汇总
     *
     * @param param 查询条件
     * @return 广告日数据汇总
     */
    AdvertDayStatisticData selectStatisticAdvertDayData(AdvertDayData param);

    /**
     * 查询广告日数据列表
     *
     * @param req 查询条件
     * @return 广告日数据列表
     */
    List<AdvertDayData> selectAdvertDayDataList(AdvertiserCpcDataReq req);

    /**
     * 新增广告日数据
     *
     * @param param 广告日数据
     * @return 结果
     */
    int insertAdvertDayData(AdvertDayData param);

    /**
     * 修改广告日数据
     *
     * @param param 广告日数据
     * @return 结果
     */
    int updateAdvertDayData(AdvertDayData param);

    /**
     * 根据广告id列表查询广告日数据统计数据
     *
     * @param req 请求参数
     * @return 广告日数据统计数据
     */
    List<AdvertDaySumDataBo> selectAdvertDaySumData(AdvertiserCpcDataReq req);

    /**
     * 查询广告主日维度统计数据
     *
     * @param startDate 起始日期
     * @param endDate 结束日期
     * @param advertiserIds 广告主ID列表
     * @return <日期-广告主ID,广告主日数据>
     */
    Map<String, AdvertiserDaySumDataBo> groupByDateAndAdvertiserId(Date startDate, Date endDate, List<Long> advertiserIds);
}
