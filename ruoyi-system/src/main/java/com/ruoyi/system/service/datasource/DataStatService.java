package com.ruoyi.system.service.datasource;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.ruoyi.common.core.local.RequestThreadLocal;
import com.ruoyi.common.enums.DataDimensionEnum;
import com.ruoyi.common.enums.InnerLogType;
import com.ruoyi.common.enums.advert.ChargeTypeEnum;
import com.ruoyi.common.utils.GlobalThreadPool;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.UserAgentUtils;
import com.ruoyi.system.domain.datashow.DataStatContext;
import com.ruoyi.system.req.datashow.DataStatReq;
import com.ruoyi.system.service.datasource.processor.DataProcessor;
import com.ruoyi.system.util.LandpageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据统计服务
 *
 * <AUTHOR>
 * @date 2021/9/29
 */
@Slf4j
@Service
public class DataStatService {

    /**
     * 数据处理器映射
     */
    private static final Map<DataDimensionEnum, DataProcessor> processorMap = new HashMap<>();

    /**
     * 数据处理(同步)
     *
     * @param type 业务类型
     * @param param 数据参数
     * @return true.所有更新都正常执行,false.存在更新失败或者异常
     */
    public boolean handle(final InnerLogType type, JSONObject param) {
        return handle(type, param, false);
    }

    /**
     * 数据处理(异步)
     *
     * @param type 业务类型
     * @param param 数据参数
     */
    public void handleAsync(final InnerLogType type, JSONObject param) {
        handle(type, param, true);
    }

    /**
     * 数据处理器注册
     *
     * @param processor 数据处理器
     */
    public static void register(DataProcessor processor) {
        if (null != processor) {
            processorMap.put(processor.getDimension(), processor);
        }
    }

    /**
     * 数据处理
     *
     * @param type 业务类型
     * @param param 数据参数
     * @param isAsync 是否异步处理
     * @return true.所有更新都正常执行,false.存在更新失败或者异常
     */
    private boolean handle(final InnerLogType type, JSONObject param, boolean isAsync) {
        List<DataDimensionEnum> dimensions = DataDimensionEnum.getByInnerLogType(type);
        if (CollectionUtils.isEmpty(dimensions)) {
            return false;
        }

        // 构造参数
        // 注:因为参数里有时间，所以构造参数是同步执行
        DataStatContext context = new DataStatContext();
        context.setType(type);
        context.setReq(buildDataRequest(param));
        // 处理数据
        if (isAsync) {
            GlobalThreadPool.statExecutorService.execute(() -> doHandle(type, dimensions, context));
            return true;
        } else {
            return doHandle(type, dimensions, context);
        }
    }

    /**
     * 遍历 DataProcessor 更新数据
     */
    private boolean doHandle(final InnerLogType type, final List<DataDimensionEnum> dimensions, final DataStatContext context) {
        boolean success = true;
        for (DataDimensionEnum dimension : dimensions) {
            DataProcessor processor = processorMap.get(dimension);
            if (null == processor) {
                continue;
            }
            try {
                boolean result = processor.validate(context) && processor.process(context);
                if (!result) {
                    log.error("数据统计失败, dimension={}, type={}, req={}", dimension.getDesc(), type.getType(), JSON.toJSONString(context.getReq()));
                }
                success &= result;
            } catch (Exception e) {
                log.error("数据统计异常, dimension={}, type={}, req={}", dimension.getDesc(), type.getType(), JSON.toJSONString(context.getReq()), e);
                success = false;
            }
        }
        return success;
    }

    /**
     * 数据处理
     *
     * @param param 数据参数
     * @return 构造数据请求参数
     */
    private DataStatReq buildDataRequest(JSONObject param) {
        DataStatReq req = new DataStatReq();
        req.setDate(DateUtil.beginOfDay(new Date()));
        req.setDateStr(DateUtil.formatDate(req.getDate()));
        req.setHour(DateUtil.thisHour(true));
        req.setQuarter(req.getHour() * 4 + DateUtil.thisMinute() / 15);
        req.setConsumerId(param.getLong("consumerId"));
        req.setAccountId(NumberUtils.defaultLong(RequestThreadLocal.get().getAccountId(), param.getLong("accountId"))); // TODO 后面去掉ThreadLocal获取
        req.setAppId(param.getLong("appId"));
        req.setSlotId(param.getLong("slotId"));
        req.setActivityId(param.getLong("activityId"));
        req.setPluginId(param.getLong("pluginId"));
        req.setAdvertId(param.getLong("advertId"));
        req.setOrientId(param.getLong("orientId"));
        req.setMaterialId(param.getLong("materialId"));
        req.setChargeType(NumberUtils.defaultInt(param.getInteger("chargeType"), ChargeTypeEnum.CPC.getType()));
        req.setUnitPrice(param.getInteger("unitPrice"));
        req.setAssessCost(param.getInteger("assessCost"));
        req.setLandpageKey(LandpageUtil.extractLpk(param.getString("landpageUrl")));
        req.setConvType(param.getInteger("convType"));
        req.setConvExt(param.getJSONObject("convExt"));
        req.setOrderId(param.getString("orderId"));
        if (StringUtils.isBlank(req.getLandpageKey()) && StrUtil.startWithAny(param.getString("landpageUrl"), "weixin://", "ifr://")) {
            // 跳转小程序落地页特殊处理
            req.setLandpageKey(LandpageUtil.extractLpk(param.getString("originLandpageUrl")));
        }
        if (LandpageUtil.isQuickApp(param.getString("originLandpageUrl"))) {
            req.setMobileModel(param.getString("model"));
            if (StringUtils.isBlank(req.getMobileModel())) {
                // 快应用才设置手机型号，如果要改动，同步修改MobileHapDataProcessor，过滤掉非快应用的数据
                req.setMobileModel(UserAgentUtils.getModel(param.getString("userAgent")));
            }
        }

        JSONObject otherParam = param.getJSONObject("otherParam");
        req.setOtherParam(otherParam != null ? JSONObject.parseObject(otherParam.toJSONString(), new TypeReference<Map<String, Object>>() {}) : null);
        return req;
    }
}
