package com.ruoyi.system.service.landpage.impl;

import com.ruoyi.system.req.datashow.CreditCardLandpageFromRecordReq;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.landpage.CreditCardLandpageFormRecordService;
import com.ruoyi.system.entity.landpage.CreditCardLandpageFormRecordEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;

import com.ruoyi.system.mapper.landpage.CreditCardLandpageFormRecordMapper;

/**
 * 信用卡落地页表单记录 Service
 *
 * <AUTHOR>
 * @date 2023-6-2 17:14:04
 */
@Service
public class CreditCardLandpageFormRecordServiceImpl implements CreditCardLandpageFormRecordService {

    @Autowired
    private CreditCardLandpageFormRecordMapper creditCardLandpageFormRecordMapper;

    @Override
    public List<CreditCardLandpageFormRecordEntity> selectList(CreditCardLandpageFromRecordReq param) {
        return creditCardLandpageFormRecordMapper.selectList(param);
    }

    @Override
    public Boolean insert(CreditCardLandpageFormRecordEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return creditCardLandpageFormRecordMapper.insert(entity) > 0;
    }

    @Override
    public Boolean updateById(CreditCardLandpageFormRecordEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return creditCardLandpageFormRecordMapper.updateById(entity) > 0;
    }

    @Override
    public CreditCardLandpageFormRecordEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return creditCardLandpageFormRecordMapper.selectById(id);
    }

    @Override
    public List<Long> selectTotalAdvertIds() {
        return creditCardLandpageFormRecordMapper.selectTotalAdvertIds();
    }

    @Override
    public List<Long> selectTotalAdvertiserIds() {
        return creditCardLandpageFormRecordMapper.selectTotalAdvertiserIds();
    }

    @Override
    public List<Long> selectTotalAppIds() {
        return creditCardLandpageFormRecordMapper.selectTotalAppIds();
    }

    @Override
    public List<Long> selectTotalSlotIds() {
        return creditCardLandpageFormRecordMapper.selectTotalSlotIds();
    }
}
