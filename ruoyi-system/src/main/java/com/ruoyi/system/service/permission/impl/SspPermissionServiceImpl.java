package com.ruoyi.system.service.permission.impl;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.permission.SspPermissionService;
import com.ruoyi.system.entity.permission.PermissionEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import com.ruoyi.system.mapper.permission.SspPermissionMapper;

/**
 * 权限表 Service
 *
 * <AUTHOR>
 * @date 2022-6-23 16:24:50
 */
@Service
public class SspPermissionServiceImpl implements SspPermissionService {

    @Autowired
    private SspPermissionMapper sspPermissionMapper;

    @Override
    public Boolean insert(PermissionEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return sspPermissionMapper.insert(entity) > 0;
    }

    @Override
    public Boolean deleteById(Long id) {
        if (Objects.isNull(id)) {
            return false;
        }
        return sspPermissionMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean updateById(PermissionEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return sspPermissionMapper.updateById(entity) > 0;
    }

    @Override
    public PermissionEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return sspPermissionMapper.selectById(id);
    }

    @Override
    public List<PermissionEntity> selectByIds(List<Long> ids) {
        if(CollectionUtils.isEmpty(ids)){
            return Collections.emptyList();
        }
        return sspPermissionMapper.selectByIds(ids);
    }

    @Override
    public List<PermissionEntity> selectAllPermission() {
        return sspPermissionMapper.selectAllPermission();
    }
}
