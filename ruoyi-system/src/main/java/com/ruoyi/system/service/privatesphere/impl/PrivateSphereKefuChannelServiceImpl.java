package com.ruoyi.system.service.privatesphere.impl;

import com.ruoyi.common.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.privatesphere.PrivateSphereKefuChannelService;
import com.ruoyi.system.entity.privatesphere.PrivateSphereKefuChannelEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.ruoyi.system.mapper.privatesphere.PrivateSphereKefuChannelMapper;

/**
 * 私域客服渠道表 Service
 *
 * <AUTHOR>
 * @date 2023-3-6 15:22:11
 */
@Service
public class PrivateSphereKefuChannelServiceImpl implements PrivateSphereKefuChannelService {
    @Autowired
    private PrivateSphereKefuChannelMapper privateSphereKefuChannelMapper;

    @Override
    public Boolean insert(PrivateSphereKefuChannelEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return privateSphereKefuChannelMapper.insert(entity) > 0;
    }

    @Override
    public Boolean deleteById(Long id) {
        if (Objects.isNull(id)) {
            return false;
        }
        return privateSphereKefuChannelMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean updateById(PrivateSphereKefuChannelEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return privateSphereKefuChannelMapper.updateById(entity) > 0;
    }

    @Override
    public PrivateSphereKefuChannelEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return privateSphereKefuChannelMapper.selectById(id);
    }

    @Override
    public PrivateSphereKefuChannelEntity selectByChannel(String channel) {
        if(StringUtils.isBlank(channel)){
            return null;
        }
        return privateSphereKefuChannelMapper.selectByChannel(channel);
    }

    @Override
    public List<PrivateSphereKefuChannelEntity> selectAllList() {
        return privateSphereKefuChannelMapper.selectAllList();
    }

    @Override
    public List<Long> selectIdListByChannel(String channel) {
        return privateSphereKefuChannelMapper.selectIdListByChannel(channel);
    }

    @Override
    public List<PrivateSphereKefuChannelEntity> selectListByIds(List<Long> ids) {
        if(CollectionUtils.isEmpty(ids)){
            return Collections.emptyList();
        }
        return privateSphereKefuChannelMapper.selectListByIds(ids);
    }

    @Override
    public Map<Long, String> selectChannelNameMapByIds(List<Long> ids) {
        if(CollectionUtils.isEmpty(ids)){
            return Collections.emptyMap();
        }
        List<PrivateSphereKefuChannelEntity> channelEntities = selectListByIds(ids);

        return channelEntities.stream().collect(Collectors.toMap(PrivateSphereKefuChannelEntity::getId,PrivateSphereKefuChannelEntity::getChannel,(v1,v2)->v1));
    }
}
