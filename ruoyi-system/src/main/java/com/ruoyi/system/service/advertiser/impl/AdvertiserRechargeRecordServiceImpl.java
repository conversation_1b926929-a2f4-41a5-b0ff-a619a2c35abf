package com.ruoyi.system.service.advertiser.impl;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.system.req.advertiser.finance.AdvertiserRechargeListReq;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.advertiser.fiance.AdvertiserRechargeRecordService;
import com.ruoyi.system.entity.advertiser.finance.AdvertiserRechargeRecordEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import com.ruoyi.system.mapper.advertiser.finance.AdvertiserRechargeRecordMapper;

/**
 * 广告主充值记录表 Service
 *
 * <AUTHOR>
 * @date 2022-3-18 17:57:43
 */
@Service
public class AdvertiserRechargeRecordServiceImpl implements AdvertiserRechargeRecordService {

    @Autowired
    private AdvertiserRechargeRecordMapper advertiserRechargeRecordMapper;

    @Override
    public int insert(AdvertiserRechargeRecordEntity entity) {
        if (Objects.isNull(entity)) {
            return 0;
        }
        return advertiserRechargeRecordMapper.insert(entity);
    }

    @Override
    public int updateById(AdvertiserRechargeRecordEntity entity) {
        if (Objects.isNull(entity)) {
            return 0;
        }
        return advertiserRechargeRecordMapper.updateById(entity);
    }

    @Override
    public AdvertiserRechargeRecordEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return advertiserRechargeRecordMapper.selectById(id);
    }

    @Override
    public List<AdvertiserRechargeRecordEntity> selectListByAccountIdAndDate(AdvertiserRechargeListReq req) {
        req.setEndDate(Optional.ofNullable(req.getEndDate()).map(DateUtil::endOfDay).orElse(null));
        return advertiserRechargeRecordMapper.selectListByAccountIdAndDate(req);
    }

    @Override
    public Long selectStatisticRecharge(AdvertiserRechargeListReq req) {
        req.setEndDate(Optional.ofNullable(req.getEndDate()).map(DateUtil::endOfDay).orElse(null));
        return advertiserRechargeRecordMapper.selectStatisticRecharge(req);
    }

    @Override
    public Integer sumBySourceAccountId(Long sourceAccountId) {
        if (Objects.isNull(sourceAccountId)) {
            return 0;
        }
        return advertiserRechargeRecordMapper.sumBySourceAccountId(sourceAccountId);
    }

    @Override
    public Map<Long, Integer> sumAccountMapBySourceAccountId(Long sourceAccountId, List<Long> accountIds) {
        if (Objects.isNull(sourceAccountId)) {
            return Collections.emptyMap();
        }
        List<AdvertiserRechargeRecordEntity> list = advertiserRechargeRecordMapper.sumAccountBySourceAccountId(sourceAccountId, accountIds);
        return list.stream().collect(Collectors.toMap(AdvertiserRechargeRecordEntity::getAccountId, AdvertiserRechargeRecordEntity::getRechargeAmount, (v1, v2) -> v2));
    }
}
