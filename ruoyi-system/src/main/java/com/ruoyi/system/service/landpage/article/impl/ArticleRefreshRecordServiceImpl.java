package com.ruoyi.system.service.landpage.article.impl;

import com.ruoyi.system.bo.landpage.article.ArticleListParamBo;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.landpage.article.ArticleRefreshRecordService;
import com.ruoyi.system.entity.landpage.article.ArticleRefreshRecordEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

import com.ruoyi.system.mapper.landpage.article.ArticleRefreshRecordMapper;

/**
 * 文章阅读量API接口请求记录 Service
 *
 * <AUTHOR>
 * @date 2024-4-15 16:42:45
 */
@Service
public class ArticleRefreshRecordServiceImpl implements ArticleRefreshRecordService {

    @Autowired
    private ArticleRefreshRecordMapper articleRefreshRecordMapper;

    @Override
    public Boolean insert(ArticleRefreshRecordEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return articleRefreshRecordMapper.insert(entity) > 0;
    }

    @Override
    public Boolean updateById(ArticleRefreshRecordEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return articleRefreshRecordMapper.updateById(entity) > 0;
    }

    @Override
    public ArticleRefreshRecordEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return articleRefreshRecordMapper.selectById(id);
    }

    @Override
    public Integer countBy(ArticleListParamBo param) {
        return articleRefreshRecordMapper.countBy(param);
    }
}
