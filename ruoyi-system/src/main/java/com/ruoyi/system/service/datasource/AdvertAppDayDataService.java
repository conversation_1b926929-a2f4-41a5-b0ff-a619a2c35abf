package com.ruoyi.system.service.datasource;

import com.ruoyi.system.entity.datashow.AdvertAppDayData;

import java.util.List;

/**
 * 广告媒体日数据Service接口
 *
 * <AUTHOR>
 * @date 2021-10-15
 */
public interface AdvertAppDayDataService {

    /**
     * 查询广告媒体日数据
     *
     * @param param 查询条件
     * @return 广告媒体日数据
     */
    AdvertAppDayData selectBy(AdvertAppDayData param);

    /**
     * 查询广告媒体日数据列表
     *
     * @param param 查询条件
     * @return 广告媒体日数据集合
     */
    List<AdvertAppDayData> selectAdvertAppDayDataList(AdvertAppDayData param);

    /**
     * 查询广告媒体日数据汇总
     *
     * @param param 查询条件
     * @return 广告媒体日数据汇总
     */
    AdvertAppDayData selectStatisticAdvertAppDayData(AdvertAppDayData param);

    /**
     * 新增广告媒体日数据
     *
     * @param param 广告媒体日数据
     * @return 结果
     */
    int insertAdvertAppDayData(AdvertAppDayData param);

    /**
     * 修改广告媒体日数据
     *
     * @param param 广告媒体日数据
     * @return 结果
     */
    int updateAdvertAppDayData(AdvertAppDayData param);
}
