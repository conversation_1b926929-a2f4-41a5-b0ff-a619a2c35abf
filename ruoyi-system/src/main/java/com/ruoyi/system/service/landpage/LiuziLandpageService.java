package com.ruoyi.system.service.landpage;

import com.ruoyi.system.req.engine.LiuziLandPageFormReq;

/**
 * 留资落地页接口
 *
 * <AUTHOR>
 * @date 2022/01/24
 */
public interface LiuziLandpageService {

    /**
     * 落地页表单
     *
     * @param req 参数
     */
    void formSubmit(LiuziLandPageFormReq req);

    /**
     * 落地页表单
     *
     * @param orderId 订单号
     * @param phone 手机号
     */
    void phoneFormSubmit(String orderId, String phone);

    /**
     * 落地页表单支付
     *
     * @param outTradeNo 支付订单号
     * @param payAmount 支付金额(分)
     * @param payTime 支付时间(yyyyMMddHHmmss)
     */
    void formPaySubmit(String outTradeNo, String payAmount, String payTime);
}
