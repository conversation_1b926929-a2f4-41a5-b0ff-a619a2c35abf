package com.ruoyi.system.service.advertiser.impl;

import com.ruoyi.system.entity.advertiser.finance.AdvertiserConsumeDetailRecordEntity;
import com.ruoyi.system.mapper.advertiser.finance.AdvertiserConsumeDetailRecordMapper;
import com.ruoyi.system.service.advertiser.fiance.AdvertiserConsumeRecordDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 广告主消费记录明细表 Service
 *
 * <AUTHOR>
 * @date 2022-3-25
 */
@Service
public class AdvertiserConsumeRecordDetailServiceImpl implements AdvertiserConsumeRecordDetailService {

    @Autowired
    private AdvertiserConsumeDetailRecordMapper advertiserConsumeDetailRecordMapper;

    @Override
    public int insert(AdvertiserConsumeDetailRecordEntity record) {
        return advertiserConsumeDetailRecordMapper.insert(record);
    }

    @Override
    public int update(AdvertiserConsumeDetailRecordEntity record) {
        return advertiserConsumeDetailRecordMapper.update(record);
    }

    @Override
    public List<AdvertiserConsumeDetailRecordEntity> selectList(AdvertiserConsumeDetailRecordEntity param) {
        return advertiserConsumeDetailRecordMapper.selectList(param);
    }
}
