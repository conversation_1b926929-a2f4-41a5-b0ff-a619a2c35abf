package com.ruoyi.system.service.slotcharge.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.enums.slot.SlotChargeTypeEnum;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.NumberUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.entity.datashow.SlotData;
import com.ruoyi.system.entity.slotcharge.SlotChargeOperLogEntity;
import com.ruoyi.system.req.slot.SlotChargeDataUpdateReq;
import com.ruoyi.system.req.slot.SlotDataUpdateReq;
import com.ruoyi.system.service.datasource.SlotAppDataService;
import com.ruoyi.system.service.slotcharge.SlotChargeOperLogService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.slotcharge.SlotChargeService;
import com.ruoyi.system.entity.slotcharge.SlotChargeEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import com.ruoyi.system.mapper.slotcharge.SlotChargeMapper;
import org.springframework.transaction.annotation.Transactional;

/**
 * 广告位每日计费方式 Service
 *
 * <AUTHOR>
 * @date 2022-3-7 19:42:45
 */
@Service
public class SlotChargeServiceImpl implements SlotChargeService {

    @Autowired
    private SlotChargeMapper slotChargeMapper;
    @Autowired
    private SlotChargeOperLogService slotChargeOperLogService;
    @Autowired
    private SlotAppDataService slotAppDataService;

    @Override
    public Boolean insert(SlotChargeEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return slotChargeMapper.insert(entity) > 0;
    }

    @Override
    public Boolean updateById(SlotChargeEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return slotChargeMapper.updateById(entity) > 0;
    }

    @Override
    public SlotChargeEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return slotChargeMapper.selectById(id);
    }

    @Override
    public List<SlotChargeEntity> selectListBySlotIdsAndYesterday(List<Long> slotIds) {
        if(CollectionUtils.isEmpty(slotIds)){
            return Collections.emptyList();
        }
        return slotChargeMapper.selectListBySlotIdsAndDate(slotIds, DateUtil.beginOfDay(DateUtils.addDays(new Date(),-1)));
    }

    @Override
    public SlotChargeEntity selectBySlotIdAndDate(Long slotId, Date date) {
        if(NumberUtils.isNullOrLteZero(slotId) || Objects.isNull(date)){
            return null;
        }
        return slotChargeMapper.selectBySlotIdAndDate(slotId,date);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateSlotChargeData(SlotChargeDataUpdateReq req) {
        Date date = req.getCurDate();
        SlotChargeEntity chargeEntity = selectBySlotIdAndDate(req.getSlotId(), date);
        boolean insertLog = false;
        if(Objects.isNull(chargeEntity)){
            //新增
            chargeEntity = new SlotChargeEntity();
            chargeEntity.setChargeType(req.getChargeType());
            chargeEntity.setChargePrice(req.getChargePrice());
            chargeEntity.setCurDate(date);
            chargeEntity.setSlotId(req.getSlotId());
            insert(chargeEntity);
            insertLog = true;
        }else{
            if(!Objects.equals(chargeEntity.getChargeType(),req.getChargeType()) || !Objects.equals(chargeEntity.getChargePrice(),req.getChargePrice())){
                insertLog = true;

                //更新记录
                chargeEntity.setChargePrice(req.getChargePrice());
                chargeEntity.setChargeType(req.getChargeType());
                updateById(chargeEntity);
            }
        }

        if(insertLog){
            //
            //新增操作日志
            SlotChargeOperLogEntity entity = BeanUtil.copyProperties(req,SlotChargeOperLogEntity.class);
            entity.setOperName(SecurityUtils.getUsername());
            slotChargeOperLogService.insert(entity);
        }

        // 更新数据
        SlotDataUpdateReq param = new SlotDataUpdateReq();
        param.setSlotId(req.getSlotId());
        param.setCurDate(date);
        param.setNhCost(req.getNhCost());
        param.setOuterCost(req.getOuterCost());
        param.setSource(3);
        param.setSlotRequestPv(req.getSlotRequestPv());
        param.setSlotRequestUv(req.getSlotRequestUv());
        param.setAppSlotClickPv(req.getAppSlotClickPv());
        param.setAppSlotClickUv(req.getAppSlotClickUv());
        param.setAppSlotExposurePv(req.getAppSlotExposurePv());
        param.setAppSlotExposureUv(req.getAppSlotExposureUv());
        slotAppDataService.updateSlotAppData(param);
        return true;
    }

    @Override
    public long calculateAppRevenue(SlotChargeEntity slotChargeEntity, SlotData slotData) {
        if(Objects.isNull(slotChargeEntity) || Objects.isNull(slotData)){
            return 0;
        }
        if(Objects.equals(slotChargeEntity.getChargeType(), SlotChargeTypeEnum.RATIO.getType())){
            return slotChargeEntity.getChargePrice() * (slotData.getNhCost() + slotData.getOuterCost()) / 100;
        }else if(Objects.equals(slotChargeEntity.getChargeType(),SlotChargeTypeEnum.CPC.getType())){
            return slotChargeEntity.getChargePrice() * Long.valueOf(slotData.getSlotRequestPv());
        }else if(Objects.equals(slotChargeEntity.getChargeType(),SlotChargeTypeEnum.UV.getType())){
            return slotChargeEntity.getChargePrice() * Long.valueOf(slotData.getSlotRequestUv());
        }else if(Objects.equals(slotChargeEntity.getChargeType(),SlotChargeTypeEnum.CPT.getType())){
            return slotChargeEntity.getChargePrice();
        } else if(Objects.equals(slotChargeEntity.getChargeType(),SlotChargeTypeEnum.CPM_PV.getType())){
            return slotChargeEntity.getChargePrice() * Long.valueOf(slotData.getSlotRequestPv()) / 1000;
        } else if(Objects.equals(slotChargeEntity.getChargeType(),SlotChargeTypeEnum.CPM_UV.getType())){
            return slotChargeEntity.getChargePrice() * Long.valueOf(slotData.getSlotRequestUv()) / 1000;
        }
        return 0;
    }

    @Override
    public List<SlotChargeEntity> selectListBySlotIdsAndDateList(List<Long> slotIds, List<Date> dates) {
        if(CollectionUtils.isEmpty(slotIds) || CollectionUtils.isEmpty(dates)){
            return Collections.emptyList();
        }
        return slotChargeMapper.selectListBySlotIdsAndDateList(slotIds,dates);
    }

    @Override
    public List<SlotChargeEntity> selectListBySlotIdsAndDateRange(List<Long> slotIds, Date startDate, Date endDate) {
        if(CollectionUtils.isEmpty(slotIds)){
            return Collections.emptyList();
        }

        return slotChargeMapper.selectListBySlotIdsAndDateRange(slotIds,startDate,endDate);
    }

    @Override
    public List<SlotChargeEntity> selectListByDate(Long id, Integer pageSize, Date date) {
        if(Objects.isNull(id) || NumberUtils.isNullOrLteZero(pageSize) || Objects.isNull(date)){
            return Collections.emptyList();
        }
        return slotChargeMapper.selectListByDate(id, pageSize, date);
    }

    @Override
    public int batchInsertOrUpdate(List<SlotChargeEntity> entities) {
        if(CollectionUtils.isEmpty(entities)){
            return 0;
        }
        return slotChargeMapper.batchInsertOrUpdate(entities);
    }
}
