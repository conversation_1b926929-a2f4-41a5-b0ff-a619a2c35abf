package com.ruoyi.system.service.datasource.impl;

import cn.hutool.core.date.DateUtil;
import com.github.pagehelper.PageHelper;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.PageDomain;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.enums.account.AccountMainType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.sql.SqlUtil;
import com.ruoyi.system.entity.app.App;
import com.ruoyi.system.entity.datashow.AppData;
import com.ruoyi.system.mapper.datashow.AppDataMapper;
import com.ruoyi.system.mapper.manager.AppMapper;
import com.ruoyi.system.service.datasource.AppDataService;
import com.ruoyi.system.util.SlotReqUvCalculateUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 媒体数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-07-04
 */
@Service
public class AppDataServiceImpl implements AppDataService {

    @Autowired
    private AppDataMapper appDataMapper;
    @Autowired
    private AppMapper appMapper;

    /**
     * 查询媒体数据
     *
     * @param id 媒体数据ID
     * @return 媒体数据
     */
    @Override
    public AppData selectAppDataById(Long id) {
        return appDataMapper.selectAppDataById(id);
    }

    @Override
    public AppData selectByAppIdAndDate(Long appId, Date curDate) {
        return appDataMapper.selectByAppIdAndDate(appId, curDate);
    }

    /**
     * 查询媒体数据列表
     *
     * @param appData 媒体数据
     * @return 媒体数据
     */
    @Override
    public List<AppData> selectAppDataList(AppData appData, boolean isExport) {
        // 获取当前账户信息
        LoginUser user = SecurityUtils.getLoginUser();

        // 非CRM用户限制查询账号以及时间
        if (!Objects.equals(user.getMainType(), AccountMainType.CRM.getType())) {
            appData.setAccountId(user.getCrmAccountId());
            Date yesterday = DateUtil.beginOfDay(DateUtil.yesterday());
            if (null == appData.getEndDate() || !appData.getEndDate().before(yesterday)) {
                appData.setEndDate(yesterday);
            }
        }

        // 媒体模糊查询
        if (StringUtils.isNotBlank(appData.getSearchValue())) {
            App app = new App();
            app.setAccountId(user.getCrmAccountId());
            app.setSearchValue(appData.getSearchValue());
            List<Long> appIds = appMapper.selectAppIdList(app);
            if (CollectionUtils.isEmpty(appIds)) {
                return Collections.emptyList();
            }
            appData.setAppIds(appIds);
        }

        // 媒体名称
        App app = new App();
        app.setAccountId(user.getCrmAccountId());
        List<App> apps = appMapper.selectSimpleAppList(app);
        Map<Long, String> appNameMap = new HashMap<>(apps.size());
        if (CollectionUtils.isNotEmpty(apps)) {
            apps.forEach(ele -> appNameMap.put(ele.getId(), ele.getAppName()));
        }

        if (!isExport) {
            startPage();
        }

        List<AppData> dataList = selectAppDataList(appData);
        if (CollectionUtils.isNotEmpty(dataList)) {
            dataList.forEach(data -> {
                data.setAppName(appNameMap.get(data.getAppId()));
            });
        }
        return dataList;
    }

    /**
     * 新增媒体数据
     *
     * @param appData 媒体数据
     * @return 结果
     */
    @Override
    public int insertAppData(AppData appData) {
        return appDataMapper.insertAppData(appData);
    }

    /**
     * 修改媒体数据
     *
     * @param appData 媒体数据
     * @return 结果
     */
    @Override
    public int updateAppData(AppData appData) {
        return appDataMapper.updateAppData(appData);
    }

    @Override
    public int updateAppNhCostData(AppData appData) {
        return appDataMapper.updateAppNhCostData(appData);
    }

    @Override
    public Map<Long, AppData> groupByAccountId(List<Long> accountIds, Date date) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return Collections.emptyMap();
        }

        AppData req = new AppData();
        req.setCurDate(date);
        req.setAccountIds(accountIds);
        List<AppData> list = selectAppDataList(req);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        Map<Long, AppData> map = new HashMap<>();
        for (AppData appData : list) {
            AppData sumData = map.get(appData.getAccountId());
            if (sumData == null) {
                sumData = new AppData();
                sumData.setSlotRequestUv(0);
                sumData.setAppRevenue(0L);
            }
            sumData.setSlotRequestUv(sumData.getSlotRequestUv() + appData.getSlotRequestUv());
            sumData.setAppRevenue(sumData.getAppRevenue() + appData.getAppRevenue());
            map.put(appData.getAccountId(), sumData);
        }

        return map;
    }

    @Override
    public Map<Long, AppData> groupByAppId(List<Long> appIds, Date date) {
        if (CollectionUtils.isEmpty(appIds)) {
            return Collections.emptyMap();
        }

        AppData req = new AppData();
        req.setCurDate(date);
        req.setAppIds(appIds);
        List<AppData> list = selectAppDataList(req);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        return list.stream().collect(Collectors.toMap(AppData::getAppId, Function.identity(), (oldVal, newVal) -> newVal));
    }

    @Override
    public List<AppData> selectAppDataList(AppData appData) {
        if(Objects.isNull(appData)){
            return Collections.emptyList();
        }
        List<AppData> dataList = appDataMapper.selectAppDataList(appData);
        //重新计算广告位访问uv
        calculateSlotReqUv(dataList);
        return dataList;
    }

    @Override
    public List<Long> selectDistinctAppIdByDate(Date startDate, Date endDate) {
        if(Objects.isNull(startDate) || Objects.isNull(endDate)){
            return Collections.emptyList();
        }
        return appDataMapper.selectDistinctAppIdByDate(startDate,endDate);
    }

    @Override
    public boolean batchInsertOrUpdate(List<AppData> datas) {
        if(CollectionUtils.isEmpty(datas)){
            return false;
        }
        return appDataMapper.batchInsertOrUpdateAppData(datas) > 0;
    }

    /**
     * 设置请求分页数据
     */
    protected void startPage()
    {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        if (StringUtils.isNotNull(pageNum) && StringUtils.isNotNull(pageSize))
        {
            String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
            PageHelper.startPage(pageNum, pageSize, orderBy);
        }
    }

    /**
     * 重新统计广告位访问uv
     * @param dataList 媒体数据
     */
    private void calculateSlotReqUv(List<AppData> dataList){
        if(CollectionUtils.isEmpty(dataList)){
            return;
        }
        dataList.forEach(appData -> {
            appData.setSlotRequestUvOriginal(appData.getSlotRequestUv());
            Integer slotReqUv = SlotReqUvCalculateUtil.calculateSlotReqUv(appData.getCurDate(),appData.getAppId(), appData.getSlotRequestUv());
            appData.setSlotRequestUv(slotReqUv);
        });
    }
}
