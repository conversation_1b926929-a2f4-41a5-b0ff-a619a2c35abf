package com.ruoyi.system.service.datasource;

import com.ruoyi.system.entity.datashow.AdvertDayBudgetData;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 广告日预算数据Service接口
 *
 * <AUTHOR>
 * @date 2021-10-19
 */
public interface AdvertDayBudgetDataService {

    /**
     * 查询广告预算数据
     *
     * @param curDate 日期
     * @param advertId 广告ID
     * @return 广告预算数据
     */
    AdvertDayBudgetData selectByDateAndAdvertId(Date curDate, Long advertId);

    /**
     * 查询广告日预算数据列表
     *
     * @param advertDayBudgetData 广告日预算数据
     * @return 广告日预算数据集合
     */
    List<AdvertDayBudgetData> selectAdvertDayBudgetDataList(AdvertDayBudgetData advertDayBudgetData);

    /**
     * 查询广告预算数据
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param advertIds 广告ID列表
     * @return 广告预算数据
     */
    Map<String, Integer> selectBudgetByDateAndAdvertIds(Date startDate, Date endDate, List<Long> advertIds);

    /**
     * 新增广告日预算数据
     *
     * @param advertDayBudgetData 广告日预算数据
     * @return 结果
     */
    int insertAdvertDayBudgetData(AdvertDayBudgetData advertDayBudgetData);

    /**
     * 更新广告日预算数据
     *
     * @param param 广告日预算数据
     * @return 结果
     */
    int updateBudget(AdvertDayBudgetData param);
}
