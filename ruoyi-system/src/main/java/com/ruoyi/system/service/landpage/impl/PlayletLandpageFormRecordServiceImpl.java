package com.ruoyi.system.service.landpage.impl;

import com.ruoyi.system.bo.landpage.PlayletLandpageFormRecordSelectBo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.landpage.PlayletLandpageFormRecordService;
import com.ruoyi.system.entity.landpage.PlayletLandpageFormRecordEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;

import com.ruoyi.system.mapper.landpage.PlayletLandpageFormRecordMapper;

/**
 * 短剧落地页表单记录 Service
 *
 * <AUTHOR>
 * @date 2023-8-2 16:11:47
 */
@Service
public class PlayletLandpageFormRecordServiceImpl implements PlayletLandpageFormRecordService {

    @Autowired
    private PlayletLandpageFormRecordMapper playletLandpageFormRecordMapper;

    @Override
    public List<PlayletLandpageFormRecordEntity> selectList(PlayletLandpageFormRecordSelectBo param) {
        return playletLandpageFormRecordMapper.selectList(param);
    }

    @Override
    public PlayletLandpageFormRecordEntity selectStatistic(PlayletLandpageFormRecordSelectBo param) {
        return playletLandpageFormRecordMapper.selectStatistic(param);
    }

    @Override
    public Boolean insert(PlayletLandpageFormRecordEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return playletLandpageFormRecordMapper.insert(entity) > 0;
    }

    @Override
    public Boolean deleteById(Long id) {
        if (Objects.isNull(id)) {
            return false;
        }
        return playletLandpageFormRecordMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean updateById(PlayletLandpageFormRecordEntity entity) {
        if (Objects.isNull(entity)) {
            return false;
        }
        return playletLandpageFormRecordMapper.updateById(entity) > 0;
    }

    @Override
    public PlayletLandpageFormRecordEntity selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return playletLandpageFormRecordMapper.selectById(id);
    }

    @Override
    public PlayletLandpageFormRecordEntity selectByTradeNo(String tradeNo) {
        if (StringUtils.isBlank(tradeNo)) {
            return null;
        }
        return playletLandpageFormRecordMapper.selectByTradeNo(tradeNo);
    }

    @Override
    public List<Long> selectTotalAdvertIds() {
        return playletLandpageFormRecordMapper.selectTotalAdvertIds();
    }

    @Override
    public List<Long> selectTotalAppIds() {
        return playletLandpageFormRecordMapper.selectTotalAppIds();
    }

    @Override
    public List<Long> selectTotalSlotIds() {
        return playletLandpageFormRecordMapper.selectTotalSlotIds();
    }
}
