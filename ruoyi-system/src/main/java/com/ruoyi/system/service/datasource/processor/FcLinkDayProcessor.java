package com.ruoyi.system.service.datasource.processor;

import com.ruoyi.common.enums.DataDimensionEnum;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.datashow.DataStatContext;
import com.ruoyi.system.req.datashow.DataStatReq;
import com.ruoyi.system.service.datasource.DataStatService;
import com.ruoyi.system.service.datasource.FcLinkDayDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

import static com.ruoyi.common.enums.DataDimensionEnum.FC_LINK_DAY;

/**
 * 丰巢链接维度日数据处理器
 *
 * <AUTHOR>
 * @date 2024/03/21
 */
@Slf4j
@Service
public class FcLinkDayProcessor implements DataProcessor, InitializingBean {

    public static final String FC_LINK_KEY= "fcLinkKey";

    public static final String USER_ID= "userId";



    @Autowired
    private FcLinkDayDataService fcLInkDayDataService;

    @Override
    public DataDimensionEnum getDimension() {
        return FC_LINK_DAY;
    }

    @Override
    public boolean validate(DataStatContext context) {
        DataStatReq req = context.getReq();
        return !StringUtils.isEmpty(req.getDateStr()) && Objects.nonNull(req.getOtherParam())
                && !StringUtils.isEmpty((String) (req.getOtherParam().get(USER_ID)))
                && !StringUtils.isEmpty((String) (req.getOtherParam().get(FC_LINK_KEY)));
    }

    @Override
    public boolean process(DataStatContext context) {
        DataStatReq req = context.getReq();

        fcLInkDayDataService.stataDayDate(req.getDateStr(), (String) (req.getOtherParam().get(FC_LINK_KEY)), (String) (req.getOtherParam().get(USER_ID)));

        return true;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        DataStatService.register(this);
    }
} 