package com.ruoyi.system.service.antiblock;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.config.AntiBlockConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Random;

/**
 * 防风控跳链业务URL生成工具类
 * 基于业务代码生成固定的跳转地址
 *
 * <AUTHOR>
 * @date 2025/07/29
 */
@Slf4j
@Component
public class AntiBlockBizUrlGenerator {

    @Autowired
    private AntiBlockService antiBlockService;

    @Autowired
    private AntiBlockConfig antiBlockConfig;

    /**
     * 业务入口路径模板
     */
    private static final String BIZ_ENTRY_PATH = "/antiblock/entry/{bizCode}";

    /**
     * 从配置的URL集合中选择一个基础URL
     * 使用随机选择策略
     *
     * @return 选中的基础URL
     */
    private String selectEntryBaseUrl() {
        List<String> entryBaseUrls = antiBlockConfig.getEntryBaseUrl();

        if (CollectionUtils.isEmpty(entryBaseUrls)) {
            throw new IllegalStateException("未配置entry-base-url，请检查配置文件");
        }

        // 随机选择策略
        int index = new Random().nextInt(entryBaseUrls.size());
        String selectedUrl = entryBaseUrls.get(index);

        log.debug("从{}个配置的URL中选择: {}", entryBaseUrls.size(), selectedUrl);
        return selectedUrl;
    }

    /**
     * 生成业务跳转链接
     *
     * @param bizCode 业务代码（如：weixin等）
     * @return 生成的跳转链接
     */
    public String generateBizEntryUrl(String bizCode) {
        return generateBizEntryUrl(bizCode, null);
    }

    /**
     * 生成业务跳转链接（带targetCode）
     *
     * @param bizCode 业务代码（如：weixin等）
     * @param targetCode 目标代码（可选）
     * @return 生成的跳转链接
     */
    public String generateBizEntryUrl(String bizCode, String targetCode) {
        if (StringUtils.isBlank(bizCode)) {
            throw new IllegalArgumentException("bizCode不能为空");
        }

        try {
            // 从配置中选择基础URL
            String baseUrl = selectEntryBaseUrl();

            // 移除baseUrl末尾的斜杠
            String cleanBaseUrl = baseUrl.endsWith("/") ? baseUrl.substring(0, baseUrl.length() - 1) : baseUrl;

            // 构建URL
            StringBuilder urlBuilder = new StringBuilder();
            urlBuilder.append(cleanBaseUrl).append(BIZ_ENTRY_PATH.replace("{bizCode}", bizCode));

            // 添加targetCode到路径中
            if (StringUtils.isNotBlank(targetCode)) {
                urlBuilder.append("/").append(targetCode);
            }

            String generatedUrl = urlBuilder.toString();
            log.debug("生成业务跳转链接: baseUrl={}, bizCode={}, targetCode={}, result={}", baseUrl, bizCode, targetCode, generatedUrl);

            return generatedUrl;

        } catch (Exception e) {
            log.error("生成业务跳转链接失败: bizCode={}, targetCode={}", bizCode, targetCode, e);
            throw new RuntimeException("生成跳转链接失败", e);
        }
    }

    /**
     * 生成直接URL跳转链接
     *
     * @param targetUrl 目标URL（原始URL）
     * @param encrypt 是否加密
     * @return 生成的跳转链接
     */
    public String generateDirectEntryUrl(String targetUrl, boolean encrypt) {
        if (StringUtils.isBlank(targetUrl)) {
            throw new IllegalArgumentException("targetUrl不能为空");
        }

        try {
            // 从配置中选择基础URL
            String baseUrl = selectEntryBaseUrl();

            // 移除baseUrl末尾的斜杠
            String cleanBaseUrl = baseUrl.endsWith("/") ? baseUrl.substring(0, baseUrl.length() - 1) : baseUrl;

            // 根据encrypt参数决定是否加密targetUrl
            String processedTargetUrl;
            if (encrypt) {
                String encryptedUrl = antiBlockService.encryptTargetUrl(targetUrl);
                processedTargetUrl = StringUtils.isNotBlank(encryptedUrl) ? encryptedUrl : targetUrl;
            } else {
                // 不加密，直接使用原始URL
                processedTargetUrl = targetUrl;
            }

            // 构建URL
            String generatedUrl = cleanBaseUrl + "/antiblock/entry?targetUrl=" +
                    java.net.URLEncoder.encode(processedTargetUrl, "UTF-8");

            log.debug("生成直接URL跳转链接: baseUrl={}, targetUrl={}, encrypt={}, result={}", baseUrl, targetUrl, encrypt, generatedUrl);

            return generatedUrl;

        } catch (Exception e) {
            log.error("生成直接URL跳转链接失败: targetUrl={}, encrypt={}", targetUrl, encrypt, e);
            throw new RuntimeException("生成跳转链接失败", e);
        }
    }

}
