package com.ruoyi.system.bo.landpage.article;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 文章缓存BO
 *
 * <AUTHOR>
 * @date 2023-12-6
 */
@Data
public class ArticleCacheBo implements Serializable {
    private static final long serialVersionUID = -7535278536511277056L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 目标阅读量
     */
    private Integer targetRequestPv;

    /**
     * 阅读量补量
     */
    private Integer compensateRequestPv;

    /**
     * 链接
     */
    private String url;

    /**
     * 跳转链接
     */
    private String jumpUrl;

    /**
     * 文章创建时间
     */
    private Date gmtCreate;

    /**
     * 聚合链接ID
     */
    private Long linkId;
}

